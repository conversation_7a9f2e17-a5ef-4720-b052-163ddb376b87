import { GET } from '../../../request';

const actions = {
  getPopUpMessage(context) {
    return GET('/notice/web/getPopUpMessage').then((res) => {
      if (res.state === 'OK') {
        const uuid = res.body.uuid;
        const messageObj = {
          title: res.body.title,
          content: res.body.content,
        };
        const sessionKey = `hideSystemPopup_${uuid}`;
        context.commit('UPDATE_SESSION_KEY', sessionKey);
        context.commit('UPDATE_MESSAGE_OBJ', messageObj);
        return { sessionKey, messageObj };
      }
    });
  },
};

export default actions;
