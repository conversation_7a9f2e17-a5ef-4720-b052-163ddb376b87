import { GET, POST } from '@/request';

// 课程模块 接口
export default {
  // 查询课程所有分类 course-list
  getCourseCategoryList: (data) => GET('/course_model/web/courseCategory/list', data),
  // 查询课程所有级别 course-list
  getCourseLevelList: (data) => GET('/course_model/web/courseLevel/list', data),
  // 查询精品课程列表 course
  getBoutiqueList: (data) => GET('/course_model/web/course/boutique', data),
  // 查询课程详情 course-detail
  getCourseDetail: (data) => POST(`/course_model/web/course/detail`, data),
  // 查询课程详情目录 course-detail
  getCourseDetailCatalogs: (data) => POST('/course_model/web/course/detail/catalogs', data),
  // 查询相关课程 course-detail
  getCourseRelate: (data) => POST('/course_model/web/course/detail/relate', data),
  // 查询课程详情教师团队 course-detail
  getCourseDetailteachers: (data) => GET('/course_model/web/course_student/course/teacherTeam', data),
  // 获取已有课程数量 course-list
  getCourseExistedNum: (data) => GET('/course_model/web/course/existed/num', data),
  // 分页分类查询课程 course-list
  getCourseList: (data) => POST('/course_model/web/course/list', data),
  // AI路线图课程列表 course
  getCourseSearchModelAI: (data) => POST('/course_model/web/course/searchModelAI', data),
  // 获取明星老师 course
  getExcellentTeacherList: (data) => GET('/course_model/web/excellent/teacher/list', data),
  // 获取合作学校 course
  getSchoolList: (data) => GET('/course_model/web/school/list', data),
  // 获取课程图片
  getImage: (data) => GET('/course_model/web/image', data),
  // 添加登录人学习课程
  getInstanceNum: (data) => GET('/course_model/web/instance/num/create', data),
  // 登出 清除JSESSIONID
  logoutClearCookies: (data) => GET('/course_model/web/logout', data),
  // 首页 -- 学习课程
  getHomeBoutique: (data) => GET('/course_model/web/home/<USER>/boutique', data),
  // 首页 -- 精品内容
  getHomeCourseList: (data) => GET('/recommend/home', data),
};
