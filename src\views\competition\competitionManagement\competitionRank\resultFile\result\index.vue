<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <div ref="subtitle">
      <a-space>
        <a-button v-if="!clearSettingsDisabled" @click="handleClearSettings" :disabled="clearSettingsDisabled">清空设置</a-button>
        <a-button type="primary" :disabled="listTotal === undefined" @click="gotoEdit"><EditOutlined /> 编辑 </a-button>
      </a-space>
    </div>
    <resultContentVue ref="resultContent" />
    <resultTableVue @getListTotal="(e) => (listTotal = e)" ref="resultTable" />
  </div>
</template>

<script>
import resultContentVue from './resultContent.vue';
import resultTableVue from './resultTable.vue';
import { clearSettings } from '../../common';

const settingsType = '结果文件提交';

export default {
  components: {
    resultContentVue,
    resultTableVue,
  },
  data() {
    return {
      listTotal: undefined,
      cid: '',
    };
  },
  computed: {
    clearSettingsDisabled() {
      // 选取一个必填项的字段来判断是否清空过
      return !this.$store.state.competition.resultContentData.teamSubmitMax;
    },
  },
  mounted() {
    this.$emit('getSubtitle', this.$refs.subtitle);
    this.cid = this.$route.params.competitionId;
  },
  methods: {
    gotoEdit() {
      const { competitionId } = this.$route.params;
      this.$router.push({
        path: `${competitionId}/result-edit`,
        query: {
          listTotal: this.listTotal,
        },
      });
    },
    handleClearSettings() {
      clearSettings(this.cid, settingsType, () => {
        this.$refs.resultContent.getData();
        this.$refs.resultTable.getTableData();
      });
    },
  },
};
</script>
