<template>
  <div class="empty-container">
    <a-empty v-if="empty" class="empty-content" :image="image" :image-style="imageStyle">
      <template #description>
        <slot name="empty-description"></slot>
      </template>
      <slot name="empty-content"></slot>
    </a-empty>
    <slot v-else></slot>
  </div>
</template>

<script>
export default {
  props: {
    image: {
      default: require('@/assets/image/emptys2x.png'),
      type: String,
    },
    imageStyle: {
      default() {
        return {
          width: '416px',
          height: '416px',
          'margin-bottom': '0px',
        };
      },
      type: Object,
    },
    empty: <PERSON><PERSON><PERSON>,
  },
};
</script>

<style lang="less" scoped>
.empty-container {
  flex: 1;
}
.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  text-align: center;
  background: #fff;
  position: relative;
  :deep(.ant-empty-description) {
    position: absolute;
    top: calc(50% + 72px);
  }
  :deep(.empty-title) {
    font-size: 18px;
    font-weight: 600;
    color: #121f2c;
  }
}
</style>
