/** middletable的统一样式  */
.common-middile-table-style() {
  :deep(.ant-table-body),
  :deep(.ant-table-body-outer) {
    .ant-table-thead > tr > th {
      color: #121F2C;
      background: #f7f9fa;
      border-bottom-color: #e6ebf5;
      font-size: 12px;
    }
    .ant-table-tbody > tr > td {
        border-bottom-color: #e6ebf5;
        font-size: 12px;
        color: #606972;
      }
      .ant-table-tbody > tr {
        &:hover {
          td {
            background: #f6f9fc;
          }
        }
      }
      tr.ant-table-row-hover{
          td{
            background: #f6f9fc;
          }
      }
  }
}
