<template>
  <div>
    <div class="content">
      <div v-for="(info, i) in basicInfoColumn" :key="i" :class="{ cover: info.type === 'courseImage', columnItem: true }">
        <span class="content-title"> {{ info.title }}: </span>

        <span class="content-content" v-if="info.type === 'courseImage'">
          <img :src="submitData[info.type]" style="width: 160px; height: 120px" alt="" />
        </span>

        <span class="content-content" v-else>{{ basicInfoMaps[submitData[info.type]] ? basicInfoMaps[submitData[info.type]] : submitData[info.type] }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { basicInfoColumn, basicInfoMaps } from '../constants';
export default {
  name: 'CoursePreview',
  data() {
    return {
      basicInfoColumn,
      basicInfoMaps,
    };
  },
  props: {
    submitData: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  text-align: left;
  .columnItem {
    margin-bottom: 24px;
  }
  .cover {
    display: flex;
  }
  .content-title {
    display: inline-block;
    width: 100px;
    text-align: right;
  }
  .content-content {
    display: inline-block;
    margin-left: 16px;
  }
}
</style>
