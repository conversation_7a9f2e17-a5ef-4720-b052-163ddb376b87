<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <div class="section-steps">
      <a-steps :current="currentStep">
        <a-step v-for="item in stepsTitle" :key="item" :title="item" />
      </a-steps>
    </div>
    <jt-common-content :loading="loading">
      <div class="section-form">
        <!-- 移动云账号开通显示 -->
        <div v-if="accountOpenCurrent" class="top-tips-text">
          <p>
            {{ ORDER_ECLOUD_AUDIT_TEXT_STATE[currentState].tips }}
            <a v-if="isUnAudited" target="_blank" rel="noopener noreferrer" style="color: #0082ff" @click="goEcloudRealName">实名认证</a>

            <template v-if="isFailed">
              <a target="_blank" rel="noopener noreferrer" style="color: #0082ff" @click="goEcloudRealName">移动云官网，</a>
              <span>完成实名认证</span>
            </template>
          </p>
          <p v-if="isUnAudited">
            {{ ORDER_ECLOUD_AUDIT_TEXT_STATE[currentState].secondTips }}
          </p>
        </div>
        <!-- 深度学习订购显示 -->
        <div v-if="!accountOpenCurrent && deepLearnCurrent" class="top-tips-text">
          <p>
            {{ DEEP_LEARN_AUDIT_TEXT_STATE[currentState].tips }}
            <template v-if="isUnSubscribe">
              <a rel="noopener noreferrer" style="color: #0082ff" @click="goEcloudRealName">“移动云深度学习平台”</a>
              <span>{{ DEEP_LEARN_AUDIT_TEXT_STATE[currentState].secondTips }}</span>
            </template>
          </p>
        </div>
        <!-- 未注册并且是移动云账号开通显示 -->
        <a-form v-if="isUnRegister && accountOpenCurrent" ref="form" :colon="false" :rules="rules" :label-col="{ span: 6, offset: 1 }" :wrapper-col="wrapperCol" :model="formData">
          <a-form-item label="注册手机号" name="phone">
            <a-input v-model:value="formData.phone" class="phoneNum-input" placeholder="请输入手机号" disabled style="width: 260px" />
          </a-form-item>

          <a-form-item
            class="phoneNum-item"
            label="短信验证码"
            :rules="[
              {
                required: true,
                message: '',
              },
            ]"
          >
            <a-space>
              <a-form-item name="code">
                <a-input v-model:value="formData.code" placeholder="请输入验证码" style="width: 260px" />
              </a-form-item>
              <a-form-item>
                <a-button :disabled="timmer > 0" :class="{ 'input-disabled': timmer > 0 }" @click="handleSendCode">{{ timmer > 0 ? `重新获取 ${timmer}` : `${sended ? '重新获取' : '获取验证码'}` }}</a-button>
              </a-form-item>
            </a-space>
            <p class="code-tips">您将收到【移动云】发送的短信验证码，用于为您注册移动云账号</p>
          </a-form-item>

          <a-form-item :wrapper-col="{ span: 17, offset: 7 }">
            <a-button type="primary" :disabled="isBtnLoading" class="wth100" @click="unRegisteredNext">下一步</a-button>
            <a-button style="margin-left: 10px" class="wth100" @click="goUserCenter"> 稍后注册 </a-button>
          </a-form-item>
        </a-form>
        <!-- 
          移动云账号开通（除未注册）和深度学习订购显示 
          不是未注册并且未完成显示
        -->
        <div v-if="!isUnRegister && !finishCurrent">
          <div class="reg-success">
            <ul class="user-information-list">
              <li><span class="title">移动云用户名:</span>{{ checkTextEmpty(formData.ecloudUserName) }}</li>
              <li><span class="title">手机号:</span>{{ checkTextEmpty(formData.phone) }}</li>
              <li v-if="accountOpenCurrent">
                <span class="title">实名认证:</span><strong class="status" :class="getEcloudStatusClass">{{ checkTextEmpty(getEcloudStatusText) }}</strong>
              </li>
              <template v-if="deepLearnCurrent">
                <li>
                  <span class="title">实名认证:</span><strong class="status status-complete">{{ checkTextEmpty(formData.realStaText) }}</strong>
                </li>
                <li>
                  <span class="title">深度学习平台:</span><strong class="status" :class="isUnSubscribe ? 'status-unaudited' : 'status-complete'">{{ isUnSubscribe ? '未订购' : '已订购' }}</strong>
                </li>
              </template>
            </ul>
          </div>

          <div class="handle-btn">
            <!-- 移动云账号开通显示 -->
            <div v-if="accountOpenCurrent">
              <a-button type="primary" :class="isPass ? 'wth100' : 'wth158'" @click="goEcloudRealName"> {{ ORDER_ECLOUD_AUDIT_TEXT_STATE[currentState].upStep }} </a-button>
              <a-button v-if="!isAuditing" style="margin-left: 10px" class="wth100" @click="goUserCenter"> {{ ORDER_ECLOUD_AUDIT_TEXT_STATE[currentState].nextStep }} </a-button>
            </div>
            <!-- 深度学习订购显示 -->
            <div v-if="!accountOpenCurrent && deepLearnCurrent">
              <a-button type="primary" class="wth158" @click="goEcloudRealName"> {{ DEEP_LEARN_AUDIT_TEXT_STATE[currentState].upStep }} </a-button>
              <!-- 深度学习订购 -- 未订购成功显示 -->
              <a-button v-if="isUnSubscribe" style="margin-left: 10px" @click="goUserCenter"> {{ DEEP_LEARN_AUDIT_TEXT_STATE[currentState].nextStep }} </a-button>
            </div>
          </div>
        </div>
        <!-- 移动云账号开通或深度学习订购显示 -->
        <confirm-modal v-if="!finishCurrent" v-model="orderModalVisible" :title="ORDER_MODAL_TEXT[currentStep].title" :ok-text="ORDER_MODAL_TEXT[currentStep].okText" :cancel-text="ORDER_MODAL_TEXT[currentStep].cancelText" @cancel="goUserCenter" @ok="orderModalHandleOk">
          <template #icon>
            <ExclamationCircleFilled class="invited-icon" style="color: #0082ff" />
          </template>
          <div class="popup-title">{{ ORDER_MODAL_TEXT[currentStep].firstText }}</div>
          <div class="popup-title" style="margin-bottom: 18px">{{ ORDER_MODAL_TEXT[currentStep].secondText }}</div>
        </confirm-modal>
        <!-- 报名审核完成显示 -->
        <div v-if="finishCurrent" class="section-success">
          <a-result status="success" title="成功参加活动">
            <template #subTitle>
              <p class="success-text">
                <span
                  >恭喜，您已成功获得<span class="num">{{ suanLiState.beannum }}个</span>算力豆（有效期<span class="num">{{ suanLiState.daynum }}天</span>）</span
                >
              </p>

              <a-button type="primary" style="margin-top: 32px; margin-right: 8px; border: 0px" @click="ecloudSSOCheck">前往移动云深度学习平台</a-button>
              <a-button @click="goUserCenter">查看算力信息</a-button>
            </template>
          </a-result>
        </div>
      </div>
    </jt-common-content>
  </div>
</template>

<script>
import { mapState } from 'vuex';

import confirmModal from '@/components/confirmModal';
import { telephoneNumberRegex, vetifyCodeRegex } from '@/utils/regex';
import { ECLOUD_URL_CONFIG, ECLOUD_URL, ORDER_ECLOUD_JOIN_LIST, ORDER_ECLOUD_AUDIT_TEXT_STATE, ORDER_ECLOUD_AUDIT, ORDER_ECLOUD_AUDIT_CLASS, ORDER_ECLOUD_AUDIT_TEXT, ORDER_ECLOUD_JOIN_TYPE, ORDER_ECLOUD_STEPS_TYPE, DEEP_LEARN_AUDIT, DEEP_LEARN_AUDIT_TEXT_STATE, ORDER_ECLOUD_JOIN_STEP_TYPE, ORDER_MODAL_TEXT } from '@/common/ecloud';
import { openInNewTab, addUrlParams, checkTextEmpty } from '@/utils/utils';

const VERIFIED_ECLOUD_URL = `${ECLOUD_URL}/api/page/op-usercenter-static/#/securitysetting/verified`;
const VERIFIED_DEEP_LEARN_URL = `${ECLOUD_URL}/api/page/deepLearning/web#/order/on-order`;
const VERIFIED_DEEP_LEARN_CONTROL_URL = `${ECLOUD_URL}${ECLOUD_URL_CONFIG.CONSOLE_DEEP_LEARNING_URL}`;

export default {
  name: 'RegisterForm',
  components: { confirmModal },
  props: {
    current: [Number, String],
  },
  data() {
    return {
      ORDER_ECLOUD_AUDIT_TEXT_STATE,
      ORDER_ECLOUD_AUDIT,
      DEEP_LEARN_AUDIT_TEXT_STATE,
      ORDER_MODAL_TEXT,
      stepsTitle: ['移动云账号开通', '深度学习平台订购', '完成'],
      formData: {
        phone: '',
        code: '',
        realStaText: '已认证',
      },
      registerEcloudData: {
        smsCaptcha: '',
        telephone: '',
        userId: '',
      },
      timmer: 0, //  验证码时间
      sended: false,
      wrapperCol: { span: 12 },
      currentState: ORDER_ECLOUD_AUDIT.UNREGISTER, // 当前状态，处于第几步
      orderModalVisible: false,
      isEcloudRegister: false, // 移动云是否已注册账号
      isBtnLoading: true,
      loading: false,
      currentStep: 0,
    };
  },
  mounted() {
    this.getEcloudInfo();
    if (ORDER_ECLOUD_JOIN_LIST.includes(this.suanLiState.joinSta)) {
      this.currentStep = ORDER_ECLOUD_JOIN_STEP_TYPE[this.suanLiState.joinSta];
    }
  },
  computed: {
    isPass() {
      return this.currentState === ORDER_ECLOUD_AUDIT.PASS;
    },
    isFailed() {
      return this.currentState === ORDER_ECLOUD_AUDIT.FAILED;
    },
    isAuditing() {
      return this.currentState === ORDER_ECLOUD_AUDIT.AUDITING;
    },
    isUnAudited() {
      return this.currentState === ORDER_ECLOUD_AUDIT.UNAUDITED;
    },
    isUnRegister() {
      return this.currentState === ORDER_ECLOUD_AUDIT.UNREGISTER;
    },
    isUnSubscribe() {
      return this.currentState === DEEP_LEARN_AUDIT.UNSUBSCRIBE;
    },
    isSubscribed() {
      return this.currentState === DEEP_LEARN_AUDIT.SUBSCRIBED;
    },
    accountOpenCurrent() {
      return this.currentStep === ORDER_ECLOUD_STEPS_TYPE.ACCOUNT_OPEN;
    },
    deepLearnCurrent() {
      return this.currentStep === ORDER_ECLOUD_STEPS_TYPE.DEEP_LEARN_ORDER;
    },
    finishCurrent() {
      return this.currentStep === ORDER_ECLOUD_STEPS_TYPE.FINISH;
    },
    getEcloudStatusClass() {
      return ORDER_ECLOUD_AUDIT_CLASS[this.currentState];
    },
    getEcloudStatusText() {
      return ORDER_ECLOUD_AUDIT_TEXT[this.currentState];
    },
    rules() {
      return {
        phone: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { pattern: telephoneNumberRegex, message: '请输入正确格式手机号', trigger: ['blur', 'change'] },
        ],
        code: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { pattern: vetifyCodeRegex, message: '请输入正确验证码', trigger: ['blur', 'change'] },
        ],
      };
    },
    ...mapState(['userInfo']),
    ...mapState(['suanLiState']),
  },
  watch: {
    // 下一步按钮disabled 没输入验证码和防止多次点击
    'formData.code'(val) {
      val.length == 6 ? (this.isBtnLoading = false) : (this.isBtnLoading = true);
    },
  },
  methods: {
    checkTextEmpty,
    // 移动云承接的比赛报名状态查询
    getEcloudInfo(tips = false) {
      this.loading = true;
      this.$GET('/marketing/web/ecloud/get', {}, { useError: false }).then((res) => {
        if (res.state === 'OK') {
          this.$store.commit('SET_SUANLISTATE_DATA', res.body);

          const { phone, ecloudUserName, realSta, orderSta, ecloudRegistSta, joinSta } = res.body;
          this.currentStep = ORDER_ECLOUD_JOIN_STEP_TYPE[joinSta];
          this.isEcloudRegister = ecloudRegistSta; // 移动云是否已注册账号

          if (ecloudRegistSta) {
            this.formData.phone = phone;
            this.formData.ecloudUserName = ecloudUserName;
            this.formData.realSta = realSta;
            this.formData.orderSta = orderSta;
          } else {
            this.formData.phone = phone; //手机号
            this.currentState = ORDER_ECLOUD_AUDIT.UNREGISTER;
          }

          if (joinSta === ORDER_ECLOUD_JOIN_TYPE.ACCOUNT_OPEN) {
            this.currentState = realSta;
            if (tips && this.isFailed) {
              this.$message.error('抱歉，我们检测到您的实名认证状态为审核失败，请再次提交认证');
            }
            if (tips && this.isUnAudited) {
              this.$message.error('抱歉，我们检测到您的实名认证状态为未认证，请再次提交认证');
            }
          }

          if (joinSta === ORDER_ECLOUD_JOIN_TYPE.DEEP_LEARN_ORDER) {
            this.currentState = orderSta;
            if (tips && this.isUnSubscribe) {
              this.$message.error('抱歉，我们检测到您尚未完成深度学习平台订购，请再次尝试订购');
            }
          }
        } else if (res.state === 'ERROR' && res.errorCode == '-509') {
          this.$message.error('系统繁忙，请稍后重试');
        }
        this.loading = false;
      });
    },

    // 移动云承接的比赛报名提交
    addEcloudInfo() {
      const ecloudJoinRequest = {};
      const { phone, code } = this.formData;
      this.registerEcloudData.telephone = phone;
      this.registerEcloudData.smsCaptcha = code;
      this.registerEcloudData.userId = this.userInfo.id;
      ecloudJoinRequest.ecloudUserInfo = this.isEcloudRegister ? this.formData : this.registerEcloudData;

      ecloudJoinRequest.joinSta = this.suanLiState.joinSta;

      this.isBtnLoading = true;
      this.loading = true;

      this.$POST('/marketing/web/ecloud/add', ecloudJoinRequest, { useError: false }).then((res) => {
        if (res.state === 'OK') {
          if (this.isUnRegister) {
            this.getEcloudInfo();
          }
          if (this.isPass) {
            this.currentStep = ORDER_ECLOUD_STEPS_TYPE.DEEP_LEARN_ORDER;
            this.getEcloudInfo();
          }
          if (this.isSubscribed) {
            this.currentStep = ORDER_ECLOUD_STEPS_TYPE.FINISH;
            this.getEcloudInfo();
          }
        } else if (res.state === 'ERROR' && res.errorCode == '-509') {
          this.$message.error('系统繁忙，请稍后重试');
        } else {
          this.$message.error(res.errorMessage);
        }
        this.isBtnLoading = false;
        this.loading = false;
      });
    },

    goEcloudRealName() {
      if (this.accountOpenCurrent) {
        if (this.isFailed || this.isUnAudited) {
          this.ecloudSSOCheck();
          this.orderModalVisible = true;
        } else if (this.isPass) {
          this.addEcloudInfo();
        } else if (this.isAuditing) {
          this.goUserCenter();
        }
      } else if (this.deepLearnCurrent) {
        if (this.isUnSubscribe) {
          this.orderModalVisible = true;
          this.ecloudSSOCheck();
        } else if (this.isSubscribed) {
          this.addEcloudInfo();
        }
      }
    },
    // 前往移动云 自动登录
    ecloudSSOCheck() {
      this.loading = true;
      let destUrl = '';
      destUrl = this.accountOpenCurrent ? window.escape(VERIFIED_ECLOUD_URL) : this.deepLearnCurrent ? window.escape(VERIFIED_DEEP_LEARN_URL) : window.escape(VERIFIED_DEEP_LEARN_CONTROL_URL);

      this.$GET('/ecloud/proxy/web/getToken', {}, { useError: false }).then((res) => {
        this.loading = false;
        const url = `${ECLOUD_URL}${ECLOUD_URL_CONFIG.ECLOUD_SSO_CHECK_URL}`;
        const urlParams = {
          token: res.body,
          destUrl: destUrl,
          systemSource: 'BiSheng',
        };
        const deepLearnUrl = addUrlParams(url, urlParams);
        openInNewTab(deepLearnUrl);
      });
    },
    orderModalHandleOk() {
      this.getEcloudInfo(true);
    },
    goUserCenter() {
      this.$router.push({
        path: '/user-center?activeTab=2',
      });
    },
    unRegisteredNext() {
      this.$refs.form
        .validate()
        .then(() => {
          this.addEcloudInfo();
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
    handleSendCode() {
      this.$GET('/marketing/web/ecloud/smsCaptcha', { phone: this.formData.phone }).then((res) => {
        if (res.state === 'OK') {
          this.sended = true;
          this.timmer = 60;
          this.$message.success('发送成功');
          this.timmerDecrease();
        } else if (res.state !== 'OK' && res.errorCode === '-117') {
          this.$message.error('验证码发送过于频繁');
        } else {
          this.$message.error(res.errorMessage || '发送失败');
        }
      });
    },
    timmerDecrease() {
      if (this.timmer > 0) {
        this.timmer--;
        setTimeout(() => {
          this.timmerDecrease();
        }, 1000);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.top-tips-text {
  font-size: @jt-font-size-base;
  text-align: center;
  color: @jt-text-color-primary;
  margin-bottom: 32px;
}
.mgb0 {
  margin-bottom: 0px;
}

.code-tips {
  font-size: @jt-font-size-base;
  color: @jt-text-color-secondary;
  margin-top: 4px;
}

.reg-success {
  width: 434px;
  background: #f9fafb;
  border-radius: @jt-border-radius;
  margin: auto;
  padding: 24px 24px 1px;

  .user-information-list {
    li {
      margin-bottom: 16px;
      color: @jt-text-color;
    }

    .title {
      display: inline-block;
      min-width: 88px;
      height: 18px;
      line-height: 18px;
      text-align: right;
      font-size: @jt-font-size-base;
      color: @jt-text-color-primary;
      margin-right: 16px;
    }
    .status {
      font-weight: @jt-font-weight;
    }
    .status-unaudited {
      color: @jt-error-color;
    }
    .status-complete {
      color: #17bb85;
    }
    .status-auditing {
      color: @jt-warn-color;
    }
  }
}
.handle-btn {
  width: 434px;
  margin: 32px auto 20px;
  display: flex;
  justify-content: center;
}

.phoneNum-item {
  :deep(.ant-form-item-children) {
    display: flex;
    flex-direction: column;
  }
  :deep(.ant-form-item-label) {
    line-height: 32px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 0;
  }

  .phoneNum-input,
  .email-input {
    width: 232px;
  }

  :deep(.ant-space-item) {
    height: 33px;
  }
}

.agree-btn {
  color: @jt-primary-color;
  cursor: pointer;
  margin-left: -8px;
}

.popup-title {
  font-size: 14px;
}

// 步骤条
.section-steps {
  display: flex;
  justify-content: center;
  margin: 48px 0;
  :deep(.ant-steps) {
    width: 784px;
  }
}

:deep(.ant-result-success .ant-result-icon > .anticon) {
  color: #17bb85;
}
:deep(.ant-result-success .ant-result-icon > .anticon svg) {
  width: 48px;
  height: 48px;
}
:deep(.ant-result-icon) {
  margin-bottom: 16px;
}
.success-text {
  margin-top: 7px;
  font-size: @jt-font-size-base;
  font-weight: @jt-font-weight;
  color: @jt-text-color;
  line-height: 22px;
}
.num {
  color: #f17506;
}
.wth100 {
  width: 100px;
}
.wth158 {
  width: 158px;
}

:deep(.ant-result) {
  padding: 16px 32px 48px;
}
</style>
