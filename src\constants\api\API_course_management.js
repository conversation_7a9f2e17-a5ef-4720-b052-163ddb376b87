import { GET } from '@/request';
// 控制台接口
export default {
  //课程管理页面，获取课程基本信息
  getCourseBasic: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getCourseBasic', data),
  //课程管理页面，获取学生列表
  getCourseStudent: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getCourseStudent', data),
  //删除学生信息
  deleteStudent: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/deleteStudent', data),
};
