<template>
  <div class="teacher-box">
    <div class="inner">
      <h1 class="portal-title">明星讲师</h1>
      <h2 class="sub-title">名师大咖 倾囊相授</h2>
      <div class="teacher-content">
        <jt-skeleton :loading="loading" :rows="4" :rowStyle="{ width: '284px', height: '100%' }" :skeletonStyle="{ height: '326px', display: 'flex', 'justify-content': 'space-between' }">
          <div class="teacher-list swiper-container">
            <div class="swiper-wrapper">
              <div class="recommended-item swiper-slide teacher-hover" v-for="x in excellentTeacherList" :key="x.id" style="width: 285px">
                <img :src="x.teacherImage" alt />
                <p>{{ x.teacherName }}</p>
                <p>{{ x.teacherDesc }}</p>
              </div>
            </div>
          </div>
        </jt-skeleton>
        <div class="teacher-buttons">
          <div class="button pre iconfont iconjiantouzuo"></div>
          <div class="button next iconfont iconjiantouyou"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import API from '@/constants/api/API.js';
import Swiper from 'swiper';
import 'swiper/css/swiper.css';
export default {
  name: 'teachers',
  data() {
    return {
      excellentTeacherList: [],
      loading: false,
    };
  },
  mounted() {
    new Swiper('.swiper-container', {
      slidesPerView: 4,
      // slidesPerGroup: 3,
      hideOnClick: true,
      observer: true, //修改swiper自己或子元素时，自动初始化swiper
      observeParents: true, //修改swiper的父元素时，自动初始化swiper

      // 如果需要前进后退按钮
      navigation: {
        nextEl: '.next',
        prevEl: '.pre',
      },
    });
    this.getExcellentTeacherList();
  },
  methods: {
    getExcellentTeacherList() {
      this.loading = true;
      API.course_model.getExcellentTeacherList({ requestId: this.$store.state.requestId }).then((res) => {
        this.loading = false;
        if (res.state === 'OK') {
          this.excellentTeacherList = res.body;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import './common.less';
.teacher-hover {
  margin-right: 15px;
  margin-top: 10px;
}

.teacher-hover:nth-of-type(4) {
  margin-right: -20px;
}
.teacher-hover:nth-of-type(1) {
  margin-left: 10px;
}

.teacher-hover:hover {
  box-shadow: 0px 3px 24px 4px rgba(0, 0, 0, 0.03), 0px 5px 5px 0px rgba(0, 0, 0, 0.05), 0px 3px 8px -4px rgba(0, 0, 0, 0.08);
}

.teacher-list {
  display: flex;
  justify-content: space-between;
  height: 326px;
  margin-top: 48px;
  .recommended-item {
    height: 305px;
    width: 285px !important;
    padding: 48px 0;
    img {
      width: 120px;
      height: 120px;
      margin: auto;
      display: block;
    }
    p {
      margin-top: 32px;
      text-align: center;
      &:nth-of-type(2) {
        margin-top: 12px;
        display: block;
      }
    }
  }
}

.teacher-content {
  margin-top: 48px;
}
.teacher-buttons {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  .button {
    width: 28px;
    height: 28px;
    background: #cbcfd2;
    border-radius: 50%;
    &:nth-of-type(1) {
      margin-right: 20px;
    }
  }
}
.pre,
.next {
  opacity: 0.4;
}
.pre:hover,
.next:hover {
  opacity: 1;
  transition: all 0.8s;
}
.teacher-box {
  padding: 40px 0;
  background: #fff;
}
</style>
