export const basicSteps = [
  {
    title: '基本信息',
  },
  {
    title: '课程封面',
  },
  {
    title: '预览',
  },
  {
    title: '完成',
  },
];
export const basicCreateInfo = {
  courseFlag: [
    { name: '公开课', key: '1' },
    { name: '封闭课', key: '2' },
  ],
  categoryCode: [
    {
      name: '机器学习/深度学习',
      key: 'AI',
    },
    {
      name: '工具与框架',
      key: 'TF',
    },
    {
      name: '计算机视觉',
      key: 'CMV',
    },
    {
      name: '自然语言处理',
      key: 'NLP',
    },
    {
      name: '智能数据分析',
      key: 'IDA',
    },
    {
      name: '智能语音',
      key: 'IV',
    },
  ],
  levelCode: [
    {
      name: '入门',
      key: 'BASE',
    },
    {
      name: '进阶',
      key: 'MORE',
    },
    {
      name: '实战',
      key: 'MOST',
    },
  ],
};
export const basicInfoColumn = [
  { title: '封面预览', type: 'courseImage' },
  { title: '课程名称', type: 'courseName' },
  { title: '开课范围', type: 'courseFlag' }, // 1:公开课; 2:封闭课
  { title: '开课时间', type: 'time' },
  { title: '课程分类', type: 'categoryCode' },
  { title: '能力分级', type: 'levelCode' },
  { title: '一句话简介', type: 'courseIntroduce' },
  { title: '开课机构', type: 'instituteName' },
];

export const basicInfoMaps = {
  BASE: '入门',
  MORE: '进阶',
  MOST: '实战',
  1: '公开课',
  2: '封闭课',
  AI: '机器学习/深度学习',
  TF: '工具与框架',
  CMV: '计算机视觉',
  NLP: '自然语言处理',
  IDA: '智能数据分析',
  IV: '智能语音',
};
