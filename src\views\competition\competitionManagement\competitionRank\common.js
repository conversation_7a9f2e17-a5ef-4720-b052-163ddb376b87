import { ClearType } from './constants';
import { Modal, message } from 'ant-design-vue';
import { POST } from '@/request';

export const clearSettings = (cid, settingsType, cb) => {
  const handleClear = (cb) => {
    const obj = {
      cid,
      type: ClearType[settingsType],
    };

    POST('/competiton/web/manage/submit/settings/delete', obj).then((res) => {
      if (res.state === 'OK') {
        message.success(`${settingsType}设置已成功清空`);
        cb && cb();
      } else {
        message.error(`${settingsType}清空失败，请稍后重试`);
      }
    });
  };

  Modal.confirm({
    title: `确定清空${settingsType}设置吗？`,
    content: '清空后设置内容将丢失，请谨慎操作',
    okText: '清空',
    cancelText: '取消',
    okType: 'danger',
    onOk: () => {
      handleClear(cb);
    },
  });
};
