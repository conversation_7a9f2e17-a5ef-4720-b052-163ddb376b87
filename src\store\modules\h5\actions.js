import API from '@/constants/api/API.js';
import { message } from 'ant-design-vue';

const actions = {
  getCityList({ commit }) {
    commit('updateCityLoading', true);
    API.h5_campus.getProvince().then((res) => {
      if (res && res.state === 'OK') {
        commit('initCityList', res.body);
        setTimeout(() => {
          commit('updateCityLoading', false);
        }, 300);
      } else {
        message.error('数据错误');
        setTimeout(() => {
          commit('updateCityLoading', false);
        }, 300);
      }
    });
  },
  searchCityList({ commit }, params = {}) {
    commit('updateCityLoading', true);
    API.h5_campus.getSearchProvince(params).then((res) => {
      if (res && res.state === 'OK') {
        commit('updateSchoolcity', res.body);
        setTimeout(() => {
          commit('updateCityLoading', false);
        }, 300);
      } else {
        message.error('数据错误');
        setTimeout(() => {
          commit('updateCityLoading', false);
        }, 300);
      }
    });
  },

  getSchoolList({ commit }, params = {}) {
    commit('updateSchoolLoading', true);
    API.h5_campus.getSchool(params).then((res) => {
      if (res && res.state === 'OK') {
        commit('updateSchool', res.body);
        setTimeout(() => {
          commit('updateSchoolLoading', false);
        }, 300);
      } else {
        message.error('数据错误');
        setTimeout(() => {
          commit('updateSchoolLoading', false);
        }, 300);
      }
    });
  },
  searchSchoolList({ commit }, params = {}) {
    commit('updateSchoolLoading', true);
    API.h5_campus.getSearchSchool(params).then((res) => {
      if (res && res.state === 'OK') {
        commit('updateSchool', res.body);
        setTimeout(() => {
          commit('updateSchoolLoading', false);
        }, 300);
      } else {
        message.error('数据错误');
        setTimeout(() => {
          commit('updateSchoolLoading', false);
        }, 300);
      }
    });
  },
};

export default actions;
