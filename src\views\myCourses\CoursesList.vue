<template>
  <jt-common-content :loading="loading" :empty-style="emptyStyle" :empty="total === 0" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text">
    <template #empty-operation>
      <p v-if="!isFilter" class="empty-operation">请立即 <span @click="hanldeCreateCourse">创建课程</span></p>
    </template>
    <div>
      <a-list class="courses-list" item-layout="horizontal" :data-source="courseList" :pagination="false">
        <template #renderItem="item">
          <a-list-item class="course-item">
            <template #actions>
              <a-button type="link" @click="handleCourseManage(item)">
                课程管理
                <jt-icon type="iconright" />
              </a-button>
            </template>
            <a-list-item-meta>
              <template #title>
                <div class="course-tags">
                  <span class="course-title">{{ item.courseName }}</span>
                  <div class="course-type">{{ item.courseFlag == '1' ? '公开课' : '封闭课' }}</div>
                  <div :class="item.courseStatus == '0' ? 'starting' : item.courseStatus == '1' ? 'running' : 'ended'">{{ item.courseStatus == '0' ? '即将开始' : item.courseStatus == '1' ? '进行中' : '已结束' }}</div>
                  <div :class="item.coursePublish == '1' ? 'published' : item.coursePublish == '2' ? 'verifying' : 'unpublish'">{{ item.coursePublish == '1' ? '已发布' : item.coursePublish == '2' ? '审核中' : '未发布' }}</div>
                </div>
              </template>
              <template #description>
                <div style="color: #606972">
                  <div style="margin-top: 20px">
                    <div style="width: 700px">{{ item.courseIntroduce || 暂无课程介绍 }}</div>
                  </div>
                  <div style="margin-top: 20px">
                    <a-space>
                      <span>{{ item.instituteName }}</span>
                      <span>|</span>
                      <span>开课时间：{{ item.startTime }} - {{ item.endTime }}</span>
                      <span style="padding-left: 26px; color: #a0a6ab">{{ item.courseStudyNum ? item.courseStudyNum : 0 }}人学习</span>
                    </a-space>
                  </div>
                </div>
              </template>
              <template #avatar>
                <div class="course-prev">
                  <img :src="item.courseImage" alt="" />
                </div>
              </template>
            </a-list-item-meta>
            <div>共{{ item.catalogNum || 0 }}节</div>
          </a-list-item>
        </template>
      </a-list>
      <div class="jt-pagination">
        <a-space size="large">
          <span>共{{ total }}条记录</span>
          <span>
            每页显示
            <a-select default-value="10" style="width: 65px" @change="changePageSize">
              <a-select-option value="5"> 5 </a-select-option>
              <a-select-option value="10"> 10 </a-select-option>
              <a-select-option value="15"> 15 </a-select-option>
              <a-select-option value="20"> 20 </a-select-option>
            </a-select>
            条
          </span>
        </a-space>
        <a-pagination show-quick-jumper :page-size="pageSize" :default-current="1" :total="total" @change="changePageNum" />
      </div>
    </div>
  </jt-common-content>
</template>

<script>
import { getCourseList } from '@/apis/teaching.js';
import { filterTip, searchTip } from '@/common/text';

export default {
  name: 'CoursesList',
  props: {
    courseStatus: {
      type: String,
      default: '3',
    },
    searchValue: {
      type: String,
      default: '',
    },
    courseTotal: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      loading: false,
      pageSize: 10, // 每页的条数
      pageNum: 1, // 第几页
      total: 0, // 总条数
      courseList: [],
      emptyStyle: {
        'min-height': '416px',
        height: 'calc(100vh - 818px)', // 根据头部及底部高度动态计算空态高度撑满
      },
    };
  },
  computed: {
    isFilter() {
      return this.searchValue || this.courseStatus != '3';
    },
    emptyStatus() {
      if (this.isFilter) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关课程',
          text: this.searchValue ? searchTip : filterTip,
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '您暂未开设任何课程',
          text: '',
        };
      }
    },
  },
  watch: {
    courseStatus() {
      this.pageNum = 1;
      this.initData();
    },
    searchValue() {
      this.pageNum = 1;
      this.initData();
    },
  },
  mounted() {
    this.initData();
  },
  methods: {
    handleCourseManage(item) {
      this.$router.push(`/course/teaching/mycourses/course-manage/info/${item.courseId}`);
    },
    hanldeCreateCourse() {
      this.$store.commit('course/SET_CURRENTACTIVECOURSE_DATA', {});
      this.$router.push('/course/teaching/mycourses/create-course');
    },
    changePageSize(size) {
      this.pageSize = Number(size);
      this.pageNum = 1;
      this.initData();
    },
    changePageNum(num) {
      this.pageNum = num;
      this.initData();
    },
    /* 初始化请求数据 */
    async initData(data = {}) {
      this.loading = true;

      data = Object.assign(data, {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        courseStatus: this.courseStatus,
        courseName: this.searchValue,
      });

      const res = await getCourseList(data);
      if (res.state === 'OK') {
        if (res.body && res.body.data) {
          this.courseList = res.body.data;
          this.total = res.body.total;
        } else {
          this.courseList = [];
          this.total = 0;
        }
      } else {
        this.courseList = [];
        this.total = 0;
      }
      this.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.empty-operation {
  font-size: 14px;
  color: #606972;
  span {
    color: @jt-primary-color;
    cursor: pointer;
  }
}
.courses-list {
  .course-item {
    height: 176px;
    padding: 0px 32px;
    &:hover {
      background: #f8f9fa;
      .course-title {
        color: #0082ff;
      }
    }
    .course-title {
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      font-size: 18px;
      padding-right: 8px;
    }
  }
}

.course-prev {
  height: 112px;
  width: 148px;
  margin-right: 12px;
  img {
    width: 100%;
    height: 100%;
  }
}
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 0px 32px;
  border-top: 1px solid #e8e8e8;
  height: 92px;

  :deep(.ant-pagination) {
    display: flex;
    align-items: center;
  }
}

.course-tags {
  display: flex;
  align-items: center;
  div {
    height: 24px;
    line-height: 23px;
    padding: 0px 12px;
    margin-right: 8px;
    border-radius: 2px;
    font-size: 12px;
  }
  .course-type,
  .running {
    border: 1px solid #0082ff;
    color: #0082ff;
  }
  .starting {
    border: 1px solid #0cb0d4;
    color: #0cb0d4;
  }
  .ended {
    background: linear-gradient(270deg, #cbcfd2 0%, #cbcfd2 100%);
    color: #ffffff;
  }
  .published {
    border: 1px solid #17bb85;
    color: #17bb85;
  }
  .verifying {
    border: 1px solid #ff8415;
    color: #ff8415;
  }

  .unpublish {
    border: 1px solid #a0a6ab;
    color: #606972;
  }
}
</style>
