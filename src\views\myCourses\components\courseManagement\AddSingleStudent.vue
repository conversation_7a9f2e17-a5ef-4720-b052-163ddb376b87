<template>
  <div class="add-single">
    <h3 class="title">单独新增学生</h3>
    <div>
      <a-form :form="form" :colon="false" @submit="handleSubmit">
        <a-form-item v-bind="formItemLayout" label="姓名">
          <a-input
            v-decorator="[
              'name',
              {
                rules: [
                  { required: true, message: '请输入姓名！', whitespace: true },
                  {
                    validator: validateName,
                    message: '请输入合法姓名！',
                  },
                ],
              },
            ]"
          />
        </a-form-item>
        <a-form-item v-bind="formItemLayout" label="学号">
          <a-input
            v-decorator="[
              'id',
              {
                rules: [
                  { required: true, message: '请输入学号！' },
                  {
                    validator: validateId,
                    message: '请输入合法学号！',
                  },
                ],
              },
            ]"
          >
          </a-input>
        </a-form-item>
        <a-form-item v-bind="formItemLayout" label="手机">
          <a-input
            v-decorator="[
              'phone',
              {
                rules: [
                  { required: true, message: '请输入手机号！', whitespace: true },
                  {
                    validator: validatePhoneNumber,
                    message: '请输入合法手机号！',
                  },
                ],
              },
            ]"
          >
          </a-input>
        </a-form-item>
        <a-form-item v-bind="formItemLayout" label="邮箱">
          <a-input
            v-decorator="[
              'email',
              {
                rules: [
                  {
                    type: 'email',
                    message: '请输入合法邮箱!',
                  },
                  {
                    required: true,
                    message: '请输入邮箱!',
                    whitespace: true,
                  },
                ],
              },
            ]"
          />
        </a-form-item>

        <a-form-item v-bind="tailFormItemLayout">
          <a-button type="primary" html-type="submit"> 确定 </a-button>
          <a-button @click="goBack"> 取消 </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AddSingleStudent',
  data() {
    return {
      formItemLayout: {
        labelCol: {
          xs: { span: 4 },
          sm: { span: 2 },
        },
        wrapperCol: {
          xs: { span: 20 },
          sm: { span: 8 },
        },
      },
      tailFormItemLayout: {
        wrapperCol: {
          xs: {
            span: 8,
            offset: 0,
          },
          sm: {
            span: 16,
            offset: 2,
          },
        },
      },
    };
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'register' });
  },
  methods: {
    handleSubmit(e) {
      e.preventDefault();
      this.form.validateFieldsAndScroll((err, values) => {
        if (!err) {
          console.log('Received values of form: ', values);
          this.goBack();
        }
      });
    },
    validateName(rule, value, callback) {
      console.log(value, '===>name');
      callback();
    },
    validateId(rule, value, callback) {
      console.log(value, '===>id');
      callback();
    },
    validatePhoneNumber(rule, value, callback) {
      console.log(value, '===>phone');
      callback();
    },
    goBack() {
      window.history.back(-1);
    },
  },
};
</script>

<style lang="less" scoped>
.add-single {
  min-height: 300px;
  margin: 20px 80px;
  background: aliceblue;
  .title {
    margin: 10px;
  }
}
</style>
