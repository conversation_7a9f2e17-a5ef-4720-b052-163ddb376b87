<template>
  <section>
    <div ref="subtitle">
      <a-tooltip>
        <template #title> 排行榜页面可显示全部团队排名，参赛团队可查看自己的排名 </template>
        <jt-icon type="iconbangzhu" class="open-rank-icon"></jt-icon>
      </a-tooltip>
      公开排行榜：
      <a-switch checked-children="是" un-checked-children="否" :checked="showRank" @click="clickSwitch" />
    </div>
    <headTitle title="排行榜" class="head-title">
      <div class="subtitle">
        <search v-model="formData.searchValue" placeholder="团队名称/提交人用户名/姓名" class="search-input" @handSearch="handSearch" />
        <a-button class="button" type="primary" ghost :disabled="tableData.length == 0" @click="download">
          <jt-icon type="icondaochu2"></jt-icon>
          导出
        </a-button>
      </div>
    </headTitle>
    <a-config-provider>
      <template #renderEmpty>
        <jt-common-content :empty="true" :loading="loading" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text"> </jt-common-content>
      </template>
      <a-table class="table" size="middle" :loading="tableData.length > 0 ? loading : undefined" :columns="columns" :data-source="tableData" :scroll="{ x: columns.filter((x) => x.isAjaxRow).length * 250 + 300 }" :pagination="false" @change="tableChange">
        <!-- 根据接口返回自动生成的title -->
        <template v-for="x in columns.filter((z) => z.isAjaxRow)" :key="x.dataIndex" #[x.slots.title]>
          <p :title="x.dataIndex" :style="ajaxTitleStyle">{{ x.titleName }}</p>
        </template>
      </a-table>
    </a-config-provider>
    <div v-if="!tableEmpty" class="jt-pagination">
      <a-space size="large">
        <span>共{{ page.total }}条记录</span>
        <span>
          每页显示
          <a-select default-value="10" style="width: 65px" @change="changePageSize">
            <a-select-option value="5"> 5 </a-select-option>
            <a-select-option value="10"> 10 </a-select-option>
            <a-select-option value="15"> 15 </a-select-option>
            <a-select-option value="20"> 20 </a-select-option>
          </a-select>
          条
        </span>
      </a-space>
      <a-pagination :page-size="page.pageSize" show-quick-jumper :default-current="1" :total="page.total" @change="changePageNum" />
    </div>
    <confirm-modal v-if="modelVisible && showRank" v-model="modelVisible" :confirm-loading="confirmLoading" type="danger" ok-text="隐藏" cancel-text="取消" title="确定隐藏排行榜吗？" @ok="(e) => confirmModalHandleOk(false)">
      <template #icon>
        <jt-icon type="iconwarning-circle-fill" style="font-size: 18px; color: #ff454d" />
      </template>
      <div>隐藏后，排行榜页面不再显示全部团队排名，参赛团队无法查看自己的排名</div>
    </confirm-modal>
    <confirm-modal v-if="modelVisible && !showRank" v-model="modelVisible" :confirm-loading="confirmLoading" ok-text="公开" cancel-text="取消" title="确定公开排行榜吗？" @ok="(e) => confirmModalHandleOk(true)">
      <template #icon>
        <jt-icon type="iconwarning-circle-fill" style="font-size: 18px; color: #0082ff" />
      </template>
      <div>公开后，排行榜页面可显示全部团队排名，参赛团队可查看自己的排名</div>
    </confirm-modal>
  </section>
</template>

<script>
import headTitle from '@/components/headTitle';
import search from '@/components/search';
import confirmModal from '@/components/confirmModal/index.vue';
import API from '@/constants/api/API';
import { getTableRow, setTableDataByRow } from '@/views/competition/competitionManagement/competitionRank/utils';
import { ajaxTitleStyle } from '@/views/competition/competitionManagement/competitionRank/constants';
import { downloadFileWithToken } from '@/utils/file';

export default {
  components: {
    headTitle,
    search,
    confirmModal,
  },
  emits: ['getSubtitle'],
  data() {
    const cid = this.$route.params.competitionId;
    return {
      ajaxTitleStyle,
      cid,
      tableData: [],
      columns: [
        {
          dataIndex: 'rankNum',
          key: 'rankNum',
          title: '排名',
          fixed: 'left',
          width: 100,
        },
        {
          dataIndex: 'teamName',
          key: 'teamName',
          title: '团队名称',
          ellipsis: true,
          width: 200,
          fixed: 'left',
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          dataIndex: 'userName',
          key: 'userName',
          title: '提交人用户名',
          ellipsis: true,
          fixed: 'left',
          width: 200,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          dataIndex: 'fullName',
          key: 'fullName',
          title: '提交人姓名',
          ellipsis: true,
          fixed: 'left',
          width: 100,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          title: '提交时间',
          key: 'createtime',
          dataIndex: 'createtime',
          sorter: true,
          width: 200,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
      ],
      filter: false,
      loading: true,
      showRank: false,
      modelVisible: false,
      confirmLoading: false,
      formData: {
        keyword: '',
        submitStatus: '',
        sortKey: '',
        sortRule: '',
      },
      page: {
        pageNum: 1,
        total: 0,
        pageSize: 10,
      },
    };
  },
  computed: {
    tableEmpty() {
      return this.tableData.length === 0;
    },
    emptyStatus() {
      if (this.formData.keyword || this.formData.submitStatus) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关排名信息',
          text: '您可以换一个关键词试试哦～',
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '暂无排名信息',
          text: '',
        };
      }
    },
  },
  async created() {
    this.loading = true;
    const rankDisplay = await API.competition_model.getRankingStatus({ cid: this.cid });
    if (rankDisplay.state === 'OK') {
      this.showRank = rankDisplay.body.rankSta;
    }

    const scoreName = await API.competition_model.getCompetitionGetScoreName({ cid: this.cid });
    if (scoreName.state === 'OK') {
      const asyncTableRow = await getTableRow({ scoreName: scoreName.body, sorter: true });
      this.columns.splice(4, 0, ...asyncTableRow);
      if (this.columns.filter((x) => x.isAjaxRow).length === 0) {
        this.columns
          .filter((x, i) => i > this.columns.length / 3)
          .map((x) => {
            delete x.width;
            delete x.fixed;
          });
      }
      this.getTableData();
    }
  },
  mounted() {
    this.$emit('getSubtitle', this.$refs.subtitle);
  },
  methods: {
    getTableData() {
      const params = { cid: this.cid, ...this.formData, ...this.page };
      this.loading = true;
      API.competition_model.getRankingList(params).then((res) => {
        this.loading = false;
        if (res.state === 'OK') {
          this.tableData = res.body.list;
          setTableDataByRow(this.tableData, 'scorestr');
          this.page.total = res.body.total;
        }
      });
    },
    handSearch(val) {
      this.formData.keyword = val;
      this.getTableData();
    },
    handleSelectClick(setSelectedKeys, selectedKeys, confirm, index) {
      this.filter = index == '' ? false : true;
      this.page.pageNum = 1;
    },
    changePageSize(size) {
      this.page.pageSize = Number(size);
      this.page.pageNum = 1;
      this.getTableData();
    },
    changePageNum(num) {
      this.page.pageNum = num;
      this.getTableData();
    },
    getStatusObj(status) {
      switch (status) {
        case 2:
        case null:
        case undefined:
          return '失败';
        case 1:
          return '成功';
        case 3:
          return '评分中';
        case 5:
          return '提交中';
      }
    },
    clickSwitch() {
      this.modelVisible = true;
    },
    confirmModalHandleOk(status) {
      this.confirmLoading = true;
      API.competition_model.rankingStatusUpdate({ cid: this.cid, rankSta: status }).then((res) => {
        this.confirmLoading = false;
        this.modelVisible = false;
        if (res.state === 'OK') {
          this.showRank = status;
          if (status) {
            this.getTableData();
          } else {
            this.formData.keyword = '';
          }
        }
      });
    },
    download() {
      downloadFileWithToken({ url: `/competiton/web/manage/edit/ranking/export?cid=${this.cid}` });
    },
    tableChange(pagination, filters, sorter) {
      this.formData.sortKey = sorter.field;
      this.formData.sortRule = sorter.order === 'ascend' ? 'asc' : 'desc';
      this.getTableData();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import '~@/views/competition/competitionManagement/competitionRank/table.less';
.open-rank-icon {
  color: #c2c5cf;
  &:hover {
    color: #7f828f;
  }
}
.head-title {
  :deep(.jt-head-title) {
    margin-left: 0;
    &:before {
      display: none !important;
    }
  }
}
section {
  margin-top: 32px;
  padding-bottom: 48px;
}
.subtitle {
  display: flex;
}
.search-input {
  width: 240px;
  margin-right: 9px;
  :deep(.ant-input) {
    padding-right: 20px;
  }
}
.button {
  width: 80px;
}
</style>
