<template>
  <div class="register">
    <van-overlay :show="!ifUsernameReady || !invitedStateReady" @overlay-background-color="rgba(0, 0, 0, 0.2)">
      <div class="notready-loading" @click.stop>
        <van-loading type="spinner" color="#1989fa" />
      </div>
    </van-overlay>

    <div class="register-header">
      <img src="@/assets/image/home/<USER>" alt="logo" />
    </div>
    <div class="register-container">
      <h1>欢迎注册九天·毕昇</h1>
      <p class="sub-header">
        您的好友{{ friendName }}正在参加“邀请注册赢算力”活动，立即注册赢取<span class="orange">{{ inviteeBeanCount }}</span
        >个算力豆，并助TA赢取<span class="orange">{{ inviterBeanCount }}</span
        >个算力豆
      </p>
      <van-form class="register-form" validate-trigger="onChange" @submit="onSubmit">
        <div class="register-form-item" @input="handleRegisterformChange">
          <van-field
            v-model="userName"
            class="item"
            name="username"
            label=""
            placeholder="请输入用户名"
            maxlength="20"
            :rules="[{ required: true, validator: userValidator }]"
            @input="
              () => {
                userError = '';
              }
            "
          />
          <div class="item-errormsg">{{ userError }}</div>
          <p :class="validatedUser ? 'item-info' : 'item-info wrong'">6-20个字符，只能包含字母、下划线和数字，不支持全数字，用户名不可修改，请谨慎设置</p>

          <!-- { require: true, message: '密码过于简单或存在安全风险，请修改', validator: (value) => value && zxcvbn(value).score >= 2 }, -->
          <van-field v-model="password" class="item" :type="passwordStyle.passwordType" name="password" label="" placeholder="请设置密码" maxlength="20" :rules="[{ required: true, validator: passwordValidator }]">
            <template #button>
              <van-icon :name="passwordStyle.passwordIcon" style="margin-right: 5px" size="16" @click="handleShowPassword(1)" />
            </template>
          </van-field>
          <p :class="validatedPassword ? 'item-info' : 'item-info wrong'">8-20个字符，必须包含大、小写字母和数字</p>

          <van-field v-model="passwordConfirm" class="item" :type="passwordStyle.passwordConfirmType" name="confirmpassword" label="" placeholder="请再次输入密码" maxlength="20" :rules="[{ required: true, validator: confirmpasswordValidator }]">
            <template #button>
              <van-icon :name="passwordStyle.passwordConfirmIcon" style="margin-right: 5px" size="16" @click="handleShowPassword(2)" />
            </template>
          </van-field>
          <div class="item-errormsg">{{ passwordConfirmError }}</div>

          <van-field v-model="phoneNum" class="item" name="phonenum" label="" placeholder="请输入11位手机号" type="tel" :rules="[{ required: true, validator: telPhoneValidator }]" @input="handlePhoneInput" />
          <div class="item-errormsg">{{ phoneError }}</div>
          <van-field
            v-model="validateNumber"
            class="item"
            name="code"
            center
            label=""
            placeholder="验证码"
            maxlength="6"
            type="digit"
            :rules="[{ required: true }]"
            @input="
              () => {
                codeError = '';
              }
            "
          >
            <template #button>
              <span class="validate-divider"></span>
              <span v-if="showCountdown">
                <van-count-down ref="countDown" class="validate-code" :time="countdownTime" :auto-start="false" format="重新获取ss秒" @finish="resetCountdown" />
              </span>
              <a v-else :class="validateSms ? 'validate-code' : 'validate-code invalid'" @click="sendSms">获取验证码</a>
            </template>
          </van-field>
          <div v-if="activityAvailable" class="checkbox-item">
            <van-checkbox v-model="registEcloud" class="register-ecloud-label" name="registEcloud">
              {{ `同时注册移动云账号，算力豆+${ecloudRegisterBeanBount}` }}
            </van-checkbox>
          </div>
          <div class="item-errormsg sendcode-error">{{ codeError }}</div>

          <div class="school-item">
            <div>
              <van-field readonly label="" :value="cityValue" name="city" placeholder="地区" :rules="[{ required: true }]" @click="handleCityList" />
              <van-popup v-model="showCityPicker" round position="bottom" @close="handleCityCancel">
                <van-search v-model="inputCity" name="search" placeholder="请输入搜索学校所在地区" @input="searchCity" />
                <div v-if="schoolCityList && schoolCityList.length === 0 && !schoolLoading" class="emptySelect">
                  <img src="@/assets/image/h5/empty.png" alt="empty" />
                </div>
                <van-picker v-else show-toolbar :columns="schoolCityList" :loading="cityLoading" @cancel="handleCityCancel" @change="handleRegisterformChange" @confirm="handleCity" />
              </van-popup>
            </div>
            <div>
              <div v-if="showCustome">
                <van-field v-model="schoolValue" class="item" name="school" label="" placeholder="地区" maxlength="50" :rules="[{ required: true, validator: schoolnameValidator }]" @change="handleRegisterformChange" />
                <p v-if="!validatedSchoolname" class="item-info wrong">50个字符以内中英文，可包含空格</p>
              </div>
              <div v-else>
                <van-field readonly label="" :value="schoolValue" name="school" placeholder="在读学校/就职学校/毕业学校" @click="handleSchoolList" />
                <van-popup v-model="showSchoolPicker" round position="bottom" @close="handleSchoolCancel">
                  <van-search v-model="inputSchool" name="search" placeholder="请输入搜索学校名称" @input="searchSchool" />
                  <div v-if="schoolList && schoolList.length === 0 && !schoolLoading" class="emptySelect">
                    <img src="@/assets/image/h5/empty.png" alt="empty" />
                  </div>
                  <van-picker v-else show-toolbar :columns="schoolList" :loading="schoolLoading" @cancel="handleSchoolCancel" @change="handleRegisterformChange" @confirm="handleSchools" />
                </van-popup>
              </div>
            </div>
          </div>
          <div class="checkbox-item">
            <van-checkbox v-model="checkTrims" name="checkTrims" @change="handleRegisterformChange"> 我已阅读并同意九天人工智能平台 <a href="/portal/common-helpcenter#/h5/trims" target="_blank" rel="noopenner noreferrer" style="color: #2567f5">服务条款</a> </van-checkbox>
          </div>
        </div>
        <div class="register-form-submit">
          <van-button block type="info" :loading="submitLoading" loading-text="提交中..." native-type="submit" :disabled="disbaledSubmit">注册</van-button>
        </div>
      </van-form>
      <div class="register-intro">
        <p>活动须知：</p>
        <p>1、注册成功并登录后，您将赢取{{ inviteeBeanCount }}个算力豆，您的好友将赢取{{ inviterBeanCount }}个算力豆，算力豆有效期均为{{ period }}天</p>
        <template v-if="activityAvailable">
          <p>2、如勾选“同时注册移动云账号”，您将额外赢取{{ ecloudRegisterBeanBount }}个算力豆</p>
          <p>3、本活动最终解释权归九天·毕昇所有</p>
          <p>点击<a @click="showInvite = true">了解九天·毕昇平台</a></p>
          <p>点击<a @click="openEcloud">了解移动云</a></p>
        </template>
        <template v-else>
          <p>2、本活动最终解释权归九天·毕昇所有</p>
          <p>点击<a @click="showInvite = true">了解九天·毕昇平台</a></p>
        </template>
      </div>
    </div>

    <van-popup v-model="showResult" class="popup" :close-on-click-overlay="ifSuccess ? false : true">
      <div class="popup-header">
        <img :src="closeicon" alt="" @click="closePopup" />
      </div>
      <div v-if="ifSuccess" class="popup-content-success">
        <img :src="successicon" alt="" />
        <p class="success-title">{{ successMessage }}</p>
        <p class="success-text">在电脑端登录使用九天·毕昇平台</p>
        <p id="successLink" class="success-link" @click="pasteLink('successLink')">https://jiutian.10086.cn/edu</p>
        <p class="success-text">开启AI之旅，赢取更多福利</p>
      </div>
      <div v-else class="popup-content-fail">
        <img :src="failicon" alt="" />
        <p class="fail-title">{{ failMessage || '很遗憾，注册失败' }}</p>
        <p class="fail-info">请您稍后重试</p>
      </div>
    </van-popup>
    <van-popup v-model="showInvite" closeable close-icon="cross" class="invitePop" :close-on-click-overlay="true">
      <div class="invite-content-success">
        <img :src="invitepopHeader" alt="" />
        <p class="success-title">在电脑端体验九天•毕昇平台</p>
        <p id="successLink" class="success-link" @click="pasteLink('successLink')">https://jiutian.10086.cn/edu</p>
        <van-divider />
        <p class="success-text">开启AI之旅，赢取更多福利</p>
      </div>
    </van-popup>
    <deny-invite v-if="(!marketingFeature || !inviteLimitFeature) && invitedStateReady" :message="denyInviteMessage"></deny-invite>
    <van-popup v-model="verifyVisible" class="verify-popup" closeable :close-on-click-overlay="false">
      <rotate-captch ref="rotateVerify" :img-url="verifyImgUrl" :result="verifyResult" @onSliding="onSliding" @end="onEnd"></rotate-captch>
    </van-popup>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
import API from '@/constants/api/API.js';
import { pasteContent, debounce } from '@/utils/utils.js';
import { Notify } from 'vant';
import denyInvite from './denyInvite';
import { POST, GET } from '@/request';
import rotateCaptch from '@/components/rotateCaptchaH5.vue';
// const zxcvbn = require('zxcvbn');

export default {
  name: 'InviteRegister',
  components: {
    denyInvite,
    rotateCaptch,
  },
  data() {
    return {
      marketingFeature: true,
      inviteLimitFeature: true,
      invitedStateReady: false,
      ifUsernameReady: false,
      token: '',
      friendName: '',
      userName: '',
      password: '',
      passwordConfirm: '',
      phoneNum: '',
      phoneError: '',
      codeError: '',
      userError: '',
      validatedUser: true,
      validatedPassword: true,
      validatedSchoolname: true,
      passwordError: '',
      passwordConfirmError: '',
      showPassword: false,
      showConfirmPassword: false,
      passwordStyle: {
        passwordType: 'password',
        passwordIcon: 'closed-eye',
        passwordConfirmType: 'password',
        passwordConfirmIcon: 'closed-eye',
      },
      validateNumber: '',
      validateSms: false,
      phoneValidator: /^[1](([3][0-9])|([4][0,1,4-9])|([5][0-3,5-9])|([6][2,5,6,7])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/,
      passwordformatValidator: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[0-9a-zA-Z!@#$%^&*()-_=+{}[\]\\|;:'",.<>/?]{8,20}$/,
      usernameValidator: /^([A-Za-z0-9_]){6,20}$/,
      usernameNumberValidator: /^\d+$/,
      schoolNameValidator: /^([\u4e00-\u9fa5_A-Za-z0-9 ]){1,50}$/,
      countdownTime: 60 * 1000,
      showCountdown: false,
      ifSuccess: false,
      showResult: false,
      showInvite: false,
      disbaledSubmit: true,
      successicon: require('@/assets/image/h5/successinvite.png'),
      invitepopHeader: require('@/assets/image/h5/invitepop-header.png'),
      failicon: require('@/assets/image/h5/fail.png'),
      closeicon: require('@/assets/image/h5/close.png'),
      cityValue: '',
      schoolValue: '',
      inputCity: '',
      inputSchool: '',
      showCustome: false,
      showCityPicker: false,
      showSchoolPicker: false,
      schoolColumns: [],
      cityColumns: [],
      submitLoading: false,
      inviteeBeanCount: 0,
      inviterBeanCount: 0,
      period: 0,
      registEcloud: false,
      activityAvailable: false,
      successMessage: '',
      failMessage: '',
      eCloudCodeAvailable: false,
      eCloudRegistered: false,
      ecloudRegisterBeanBount: 0,
      verifyVisible: false,
      verifyImgUrl: '',
      verifyResult: undefined,
      verifyId: '',
      angle: undefined,
      needVerify: false,
      refreshTimmer: null,
      codeSended: false,
      formData: {},
      checkTrims: false,
    };
  },
  unmounted() {
    this.clearRefreshTimmer();
  },
  created() {
    this.token = this.$route.query.token || '';
    this.initRegisterFeature();
    this.getUsername();
    this.getInviteeBeanMessage();
    this.getActivityStatus();
    this.getEcloudRegisterBeanMessage();
  },
  computed: {
    ...mapState('h5', ['cityLoading', 'schoolLoading', 'schoolCityList', 'schoolList', 'currentProvince']),
    denyInviteMessage() {
      return this.marketingFeature ? '抱歉，邀请注册链接已失效' : '抱歉，邀请注册赢算力活动暂未开放';
    },
  },
  watch: {
    cityValue(newValue) {
      this.schoolValue = '';
      this.handleRegisterformChange();
      if (newValue === '其他') {
        this.showCustome = true;
      } else {
        this.showCustome = false;
      }
    },
    showPassword(newValue) {
      if (newValue) {
        this.passwordStyle.passwordType = '';
        this.passwordStyle.passwordIcon = 'eye-o';
      } else {
        this.passwordStyle.passwordType = 'password';
        this.passwordStyle.passwordIcon = 'closed-eye';
      }
    },
    showConfirmPassword(newValue) {
      if (newValue) {
        this.passwordStyle.passwordConfirmType = '';
        this.passwordStyle.passwordConfirmIcon = 'eye-o';
      } else {
        this.passwordStyle.passwordConfirmType = 'password';
        this.passwordStyle.passwordConfirmIcon = 'closed-eye';
      }
    },
    passwordConfirm(newValue) {
      if (newValue !== this.password) {
        this.passwordConfirmError = '两次输入密码不一致';
      } else {
        this.passwordConfirmError = '';
      }
    },
  },
  methods: {
    // zxcvbn,
    ...mapMutations('h5', ['updateCurrentCity']),
    ...mapActions('h5', ['getCityList', 'searchCityList', 'getSchoolList', 'searchSchoolList']),
    handleShowPassword(type) {
      type === 1 ? (this.showPassword = !this.showPassword) : (this.showConfirmPassword = !this.showConfirmPassword);
    },

    initRegisterFeature() {
      Promise.all([API.h5_campus.getInvitationFeature(), API.h5_campus.getInviteLimitFeature({ token: this.token })])
        .then((result) => {
          if (result[0].state === 'OK') {
            this.marketingFeature = result[0].body;
          } else {
            this.marketingFeature = false;
          }

          if (result[1].state === 'OK') {
            this.inviteLimitFeature = result[1].body;
          } else {
            this.inviteLimitFeature = false;
          }
          this.invitedStateReady = true;
        })
        .catch(() => {
          this.invitedStateReady = true;
          this.marketingFeature = false;
          this.inviteLimitFeature = false;
        });
    },
    getInviteeBeanMessage() {
      API.h5_campus.getInviteeBeanMessage().then((res) => {
        if (res.state === 'OK') {
          const { inviteeBeanCount, inviterBeanCount, period } = res.body;
          this.inviteeBeanCount = inviteeBeanCount;
          this.inviterBeanCount = inviterBeanCount;
          this.period = period;
        }
      });
    },
    getUsername() {
      API.h5_campus
        .getInviterName({ token: this.token })
        .then((res) => {
          if (res.state === 'OK' && res.body) {
            this.friendName = res.body;
          } else {
            console.warn('无法获取邀请人');
          }
        })
        .catch(() => {
          console.warn('无法获取邀请人');
        });
      setTimeout(() => {
        this.ifUsernameReady = true;
      }, 300);
    },
    handleCityList() {
      this.showCityPicker = true;
      this.getCityList();
    },
    handleSchoolList() {
      this.showSchoolPicker = this.cityValue !== '' ? true : false;
      this.getSchoolList({ province: this.currentProvince });
    },
    searchCity: debounce(function (city) {
      this.searchCityList({ keyword: city });
    }),
    searchSchool: debounce(function (school) {
      this.searchSchoolList({ province: this.currentProvince, keyword: school });
    }),
    handleCityCancel() {
      this.showCityPicker = false;
      this.inputCity = '';
    },
    handleCity(value) {
      this.cityValue = value;
      this.showCityPicker = false;
      this.updateCurrentCity(value);
      this.handleRegisterformChange();
    },
    handleSchoolCancel() {
      this.showSchoolPicker = false;
      this.inputSchool = '';
    },
    handleSchools(value) {
      this.schoolValue = value;
      this.showSchoolPicker = false;
      this.handleRegisterformChange();
    },
    telPhoneValidator(val) {
      const isValidated = this.phoneValidator.test(val);
      this.phoneError = isValidated ? '' : '手机号码格式不正确';
      return isValidated;
    },
    userValidator(val) {
      const nameValidator = this.usernameValidator.test(val);
      const numberValidator = this.usernameNumberValidator.test(val);
      if (numberValidator) {
        this.validatedUser = false;
      } else if (!nameValidator) {
        this.validatedUser = false;
      } else {
        this.validatedUser = true;
      }
      return nameValidator && !numberValidator;
    },
    passwordValidator(val) {
      const isvalidated = this.passwordformatValidator.test(val);
      this.validatedPassword = isvalidated;
      return isvalidated;
    },
    confirmpasswordValidator(val) {
      return val === this.password ? true : false;
    },
    schoolnameValidator(val) {
      const isvalidated = this.schoolNameValidator.test(val);
      this.validatedSchoolname = isvalidated ? true : false;
      return isvalidated;
    },
    checkEcloudCodeAvailable() {
      if (this.codeSended && this.eCloudCodeAvailable !== this.registEcloud) {
        if (this.registEcloud) {
          Notify({ type: 'danger', message: '您已勾选同时注册移动云，请重新获取移动云验证码' });
        } else {
          Notify({ type: 'danger', message: '您已取消注册移动云，移动云验证码失效，请重新获取验证码' });
        }
        return;
      }
      return true;
    },
    submitted() {
      this.showResult = true;
      this.ifSuccess = true;
      this.successMessage = '恭喜，您已成功注册';
    },
    onSubmit(values) {
      if (!this.checkEcloudCodeAvailable()) {
        return;
      }
      this.submitLoading = true;
      let params = {};
      Object.keys(values).forEach((key) => {
        if (key && values[key] && values[key].length !== 0) {
          params[key] = values[key];
        }
      });
      this.formData = params;
      this.postData();
    },

    postData() {
      let params = this.formData;
      params.token = this.token;
      params.channel = '0'; // 标识是h5注册渠道
      delete params.city;
      delete params.search;
      delete params.confirmpassword;
      params.channel = 0; //0=H5端自主注册
      params.registEcloud = +this.registEcloud;
      params.angle = this.angle !== undefined ? parseInt(this.angle) : undefined;
      params.uuid = this.verifyId;
      setTimeout(() => {
        this.submitLoading = false;
      }, 300);
      const url = this.registEcloud && !this.eCloudRegistered ? '/marketing/web/ecloud/inviteeRegistration' : '/marketing/web/inviteeRegistration';
      POST(url, params)
        .then((res) => {
          if (res.state === 'OK') {
            if (this.needVerify) {
              this.verifyResult = '正确';
              setTimeout(() => {
                this.onSubmited();
              }, 1000);
            } else {
              this.onSubmited();
            }
          } else {
            // 立即注册接口：
            // WRONG_SMS_CODE("1020", "验证码有误，请重新输入"),
            // BAD_SMS_CODE("1021", "验证码已过期，请重新获取"),
            // BAD_FORMAT_USERNAME("1022", "用户名格式不正确"),
            // BAD_FORMAT_PASSWORD("1023", "密码格式不正确"),
            // REGIST_FAIL("1040", "用户名已被注册，请重新输入");
            // 调用keycloak注册接口返回的几个错误码：
            // CREATE_USER_ERROR("-106", "keycloak创建用户失败"),
            // PHONE_USED_ERROR("-104", "该手机号已绑定至其他账号，请解绑后再试"),
            // QUERY_USER_ERROR("-107", "keycloak查询用户失败"),
            if (res.errorCode === '1020' || res.errorCode === '1021') {
              this.phoneError = '';
              this.codeError = res.errorMessage;
              this.userError = '';
              this.passwordError = '';
              this.verifyVisible = false;
            } else if (res.errorCode === '1022' || res.errorCode === '1040') {
              this.codeError = '';
              this.phoneError = '';
              this.userError = res.errorMessage;
              this.passwordError = '';
              this.verifyVisible = false;
            } else if (res.errorCode === '1023') {
              this.codeError = '';
              this.phoneError = '';
              this.userError = '';
              this.passwordError = res.errorMessage;
              this.verifyVisible = false;
            } else if (res.errorCode === '-104') {
              this.codeError = '';
              this.phoneError = res.errorMessage;
              this.userError = '';
              this.passwordError = '';
              this.verifyVisible = false;
            } else if (res.errorCode === '1111') {
              this.failMessage = '毕昇平台账号注册失败';
              this.ifSuccess = false;
              this.showResult = true;
              this.verifyVisible = false;
            }
            // 需要图形验证码
            else if (res.errorCode === '1121') {
              this.needVerify = true;
              this.openVerify();
            }
            // 图形验证码错误
            else if (res.errorCode === '-702') {
              this.verifyResult = '错误';
              this.needVerify = true;
              setTimeout(() => {
                this.openVerify();
              }, 1000);
            }
            // 图形验证码过期
            else if (res.errorCode === '-701') {
              this.verifyResult = '已过期';
              this.needVerify = true;
              setTimeout(() => {
                this.openVerify();
              }, 1000);
            } else {
              this.verifyVisible = false;
              this.showResult = true;
              this.ifSuccess = false;
              this.failMessage = res.errorMessage;
            }
          }
          this.submitLoading = false;
        })
        .catch(() => {
          this.submitLoading = false;
        });
    },
    handleRegisterformChange() {
      //  && zxcvbn(this.password).score >= 2
      const ifValidedForm = this.validateNumber !== '' && this.userName !== '' && this.password !== '' && this.passwordConfirm !== '' && this.phoneNum !== '' && this.validatedSchoolname && this.cityValue !== '' && this.schoolValue !== '' && this.checkTrims;
      this.disbaledSubmit = !ifValidedForm;
    },
    handlePhoneInput() {
      const validedPhone = this.telPhoneValidator(this.phoneNum);
      this.validateSms = validedPhone;
    },
    sendSms() {
      const ifPhoneValidated = this.telPhoneValidator(this.phoneNum);
      if (!this.validateSms) {
        return;
      }
      if (!ifPhoneValidated) {
        this.phoneError = '手机号码格式不正确';
        return;
      }
      this.codeSended = true;
      this.eCloudCodeAvailable = this.registEcloud;
      if (this.registEcloud) {
        this.sendEcloudCode();
      } else {
        this.sendBiShengCode();
      }
    },

    sendEcloudCode() {
      const url = '/marketing/web/ecloud/sendSmsCode';
      const param = { phoneNum: this.phoneNum };
      GET(url, param).then((res) => {
        if (res.state === 'OK') {
          this.startCountdown();
          this.eCloudRegistered = false;
          Notify({ type: 'success', message: '验证码已成功发送' });
        } else {
          if (res.errorCode === '1020' || res.errorCode === '1021') {
            this.phoneError = '';
            this.codeError = res.errorMessage;
          } else if (res.errorCode === '1109') {
            this.eCloudRegistered = true;
            this.sendBiShengCode();
          } else {
            this.codeError = '';
            this.phoneError = res.errorMessage;
          }
        }
      });
    },

    sendBiShengCode() {
      const url = '/marketing/web/sendSmsCode';
      const param = { phoneNum: this.phoneNum };
      GET(url, param).then((res) => {
        if (res.state === 'OK') {
          this.startCountdown();
          Notify({ type: 'success', message: '验证码已成功发送' });
        } else {
          if (res.errorCode === '1020' || res.errorCode === '1021') {
            this.phoneError = '';
            this.codeError = res.errorMessage;
          } else {
            this.codeError = '';
            this.phoneError = res.errorMessage;
          }
        }
      });
    },
    resetCountdown() {
      this.showCountdown = false;
      this.$refs.countDown.reset();
    },
    startCountdown() {
      this.showCountdown = true;
      this.$nextTick(() => {
        this.$refs.countDown.start();
      });
    },
    closePopup() {
      this.showResult = false;
    },
    pasteLink(id) {
      const paste = document.getElementById(id).innerHTML;
      pasteContent(paste);
      Notify({ type: 'success', message: '复制成功' });
    },
    getActivityStatus() {
      GET('/marketing/web/ecloud/activityStatus').then((res) => {
        if (res.state === 'OK') {
          this.activityAvailable = res.body;
          this.registEcloud = this.activityAvailable;
        }
      });
    },
    openEcloud() {
      window.open('https://ecloud.10086.cn');
    },
    getEcloudRegisterBeanMessage() {
      GET('/marketing/web/ecloud/getRegisterBeanMessage').then((res) => {
        if (res.state === 'OK') {
          this.ecloudRegisterBeanBount = (res.body && res.body.ecloudRegisterBeanBount) || 0;
        }
      });
    },
    openVerify() {
      this.verifyVisible = true;
      this.$nextTick(() => {
        this.refreshVerify();
        this.setRefreshTimmer();
      });
    },
    onEnd(angle) {
      this.angle = angle;
      this.verifyResult = '验证中';
      this.postData();
    },
    refreshVerify() {
      this.verifyResult = undefined;
      this.getVerifyInfo();
      this.$refs.rotateVerify.resetSlider();
    },
    getVerifyInfo() {
      GET('/messaging/web/sendImageCaptcha').then((res) => {
        if (res.state === 'OK') {
          this.verifyImgUrl = res.body.imageBase64;
          this.verifyId = res.body.uuid;
        } else {
          Notify({ type: 'danger', message: res.errorMessage || '获取图片验证码失败，请稍后重试' });
        }
      });
    },
    setRefreshTimmer() {
      if (this.refreshTimmer) {
        this.clearRefreshTimmer();
      }
      this.refreshTimmer = setInterval(() => {
        if (this.verifyVisible) {
          this.refreshVerify();
        } else {
          this.clearRefreshTimmer();
        }
      }, 1000 * 60);
    },
    clearRefreshTimmer() {
      clearInterval(this.refreshTimmer);
      this.refreshTimmer = null;
    },
    onSliding() {
      // 如果开始拖动了，要把定时器给停掉
      this.clearRefreshTimmer();
    },
  },
};
</script>

<style lang="less">
@import '~@/assets/styles/index.less';

.register {
  .notready-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
  .register-header {
    width: 100%;
    min-height: 50px;
    background: #ffffff;
    box-shadow: 0px 1px 14px 0px rgba(102, 118, 153, 0.16);
    border: 1px solid #edf1f3;
    padding: 15px 20px;
    img {
      width: 150px;
    }
  }
  .register-container {
    padding: 30px 15px 0px;
    h1 {
      text-align: center;
      font-size: 25px;
      font-family: PingFangSC-Medium, PingFang SC, sans-serif;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
    }
    .sub-header {
      padding: 15px 13px 8px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC, sans-serif;
      font-weight: 400;
      color: #606972;
      .orange {
        color: #f17506;
      }
    }
    .register-form {
      width: 100%;
      text-align: center;
      padding: 0px 5px;
      img {
        width: 200px;
        margin-top: -1px;
      }
      .register-form-item {
        text-align: left;
        .validate-divider {
          height: 20px;
          margin-right: 10px;
          border-left: 1px solid #ddd;
        }
        .validate-code {
          white-space: nowrap;
          font-size: 14px;
          font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
          font-weight: 400;
          color: #0082ff;
          margin-right: 10px;
        }
        .invalid {
          color: #d8d8d8;
          pointer-events: none;
          cursor: default;
        }

        .school-item {
          margin-left: 15px;
          .van-icon {
            line-height: 2;
            font-size: 20px;
          }
          .van-cell {
            padding-left: 0;
            margin: 10px 0;
          }
          .item-info {
            margin-left: 0;
          }
          .emptySelect {
            height: 308px;
            overflow: hidden;
            img {
              width: 100%;
            }
            .empty-text {
              position: absolute;
              bottom: 15%;
              left: 50%;
              transform: translateX(-50%);
              text-align: center;
              font-size: 18px;
              font-weight: @jt-font-weight-medium;
              color: #121f2c;
            }
          }
        }
        .item-errormsg {
          margin-left: 15px;
          font-size: 11px;
          font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
          font-weight: 400;
          color: #f63f48;
          &.sendcode-error {
            margin-top: 4px;
            margin-bottom: -10px;
          }
        }
        .item-info {
          margin: 0px 17px 10px;
          text-align: left;
          font-size: 11px;
          font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
          font-weight: 400;
          color: #a0a6ab;
        }
        .wrong {
          color: #f63f48;
        }
      }
      .register-form-submit {
        margin: 25px 16px 16px;
        background: #178cf9;
        border-radius: 3px;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
        font-weight: @jt-font-weight-medium;
        color: #ffffff;
      }
      .van-cell {
        border-radius: 4px;
        background: inherit;
        padding-bottom: 3px;
        margin-top: 10px;
      }
    }
    .register-intro {
      padding: 15px 20px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC, sans-serif;
      font-weight: 400;
      color: #606972;
      p {
        margin-bottom: 15px;
        a {
          color: #0082ff;
        }
      }
    }
  }

  .invitePop {
    width: 75%;
    background: #ffffff;
    box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
    border-radius: 6px;
    text-align: center;
    .invite-content-success {
      img {
        width: 100%;
      }
      .success-title {
        margin-top: 12px;
        font-size: 15px;
        font-family: PingFangSC-Regular, PingFang SC, sans-serif;
        font-weight: 400;
        color: #121f2c;
        line-height: 22px;
      }
      .success-link {
        color: #0082ff;
      }
      .van-divider {
        color: #d1d4d7 !important;
        border-color: inherit;
        margin: 15px 35px;
      }
      .success-text {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC, sans-serif;
        font-weight: 400;
        color: #606972;
        line-height: 20px;
        padding: 0 25px 18px;
      }
    }
    .van-popup__close-icon--top-right {
      color: #fff;
    }
  }
  .popup {
    width: 80%;
    background: #ffffff;
    box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
    border-radius: 6px;
    border: 2px solid #ffffff;
    text-align: center;
    padding: 15px 15px 30px;
    .popup-header {
      text-align: right;
      img {
        width: 24px;
      }
    }

    .popup-content-success {
      color: #41497a;
      font-size: 17px;
      text-align: center;
      font-family: PingFangSC-Regular, PingFang SC, sans-serif;
      img {
        width: 80px;
        margin-bottom: 20px;
      }
      .success-title {
        margin-bottom: 12px;
        font-size: 18px;
        font-weight: 400;
        color: #121f2c;
      }

      .success-text {
        line-height: 1.8;
        font-size: 12px;
        color: #606972;
      }
      .success-link {
        color: #0082ff;
      }
      .success-info {
        margin-bottom: 15px;
        font-weight: 600;
      }
    }
    .popup-content-fail {
      font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
      font-weight: 600;
      img {
        width: 72px;
        margin: 16px;
      }
      .fail-title {
        font-size: 22px;
        color: #f63f48;
        margin-bottom: 7px;
      }
      .fail-info {
        font-size: 17px;
        color: #41497a;
        margin-bottom: 25px;
      }
    }
  }
}
</style>
<style lang="less">
.van-field__button {
  display: flex !important;
  align-items: center;
}
.van-field__value .van-field__body {
  border-radius: 3px;
  border: 1.5px solid #cbcfd2;
  background: #ffffff !important;
}
.van-field__value .van-field__body:visited {
  border-color: #178cf9;
}
.van-field__value .van-field__body:hover {
  border-color: #178cf9;
}
.van-field__value .van-field__body input {
  padding: 10px;
  border-radius: 4px;
  font-size: 14px !important;
}
.van-field__error-message {
  font-size: 14px;
  font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
  font-weight: 400;
  color: #f63f48;
}
.van-field__value {
  .van-field__body {
    input {
      font-size: 14px;
    }
    input::-webkit-input-placeholder {
      color: #abafc5;
      font-size: 14px;
    }
  }
}
.van-cell::after {
  border: none;
}

.van-button--normal {
  font-size: 17px;
}

.checkbox-item {
  padding: 0 16px;
}

.van-checkbox__icon .van-icon {
  border-color: #2567f5;
}

.register-ecloud-label {
  .van-checkbox__label {
    color: #a0a6ab;
  }
}
.verify-popup {
  width: 72%;
  border-radius: 12px;
}
</style>
