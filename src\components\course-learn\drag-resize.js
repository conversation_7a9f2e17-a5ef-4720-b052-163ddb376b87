import localStorage from '../../utils/localStorage';

export const InitialWidth = '528';
export const getMinWidth = () => {
  return 0.3 * document.body.clientWidth;
};
export const getMaxWidth = () => {
  return 0.7 * document.body.clientWidth;
};

export const getAdaptiveHeight = (width) => {
  return (width * 4) / 5;
};

export const setWidth = (width) => {
  localStorage.set('course-learn-width', width);
};

export const getWidth = () => {
  return localStorage.get('course-learn-width') || InitialWidth;
};
