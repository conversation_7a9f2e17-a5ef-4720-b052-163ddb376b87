<template>
  <div class="form">
    <div class="logo"><img :src="formLogo" alt="" /></div>
    <div class="content">
      <div class="title">您已成功注册九天·毕昇平台</div>
      <div class="link"><span id="linkUrl" class="link-url" @click="pasteLink('linkUrl')">https://jiutian.10086.cn/edu</span> 复制链接到电脑端登录体验</div>
      <div class="form">
        <div class="form-title">
          <p class="top">继续完善个人信息</p>
          <p class="bottom">赢取<span>10元</span>话费权益</p>
        </div>
        <van-form @submit="submitInfo">
          <div v-for="(item, i) in formInput" :key="i" class="form-item">
            <p class="form-item-title"><span v-if="item.required" class="required">*</span>{{ item.name }}</p>
            <van-field v-model="item.value" :name="item.key" maxlength="30" label="" :placeholder="item.placeholder" :rules="[{ required: item.required, message: item.message }]" @input="handleFormChange" />
          </div>
          <div class="form-item">
            <p class="form-item-title">人工智能水平</p>
            <div v-for="(level, i) in levelText" :key="level.key" :class="levelSelected[i] ? 'form-item-notselet form-item-seleted' : 'form-item-notselet'" @click="toggleLevel(i, level.key)">{{ level.level }}<img :src="selecticon" :class="levelSelected[i] ? 'selected' : 'selected dispear'" alt="" /></div>
          </div>
          <div class="form-item">
            <p class="form-item-title">人工智能学习意愿</p>
            <div v-for="(will, i) in willText" :key="will.key" :class="willSelected[i] ? 'form-item-notselet form-item-seleted' : 'form-item-notselet'" @click="toggleWill(i, will.key)">{{ will.will }}<img :src="selecticon" :class="willSelected[i] ? 'selected' : 'selected dispear'" alt="" /></div>
          </div>
          <div class="form-agree">
            <img class="form-agree-icon" :src="ifAgree ? agreeicon : notagreeicon" alt="" @click="toggleAgree" />
            <span>同意九天·毕昇平台使用您的信息为您提供服务</span>
          </div>
          <van-button class="form-submit" native-type="submit" :disabled="buttonDisabled">提交信息，赢取话费权益</van-button>
        </van-form>
      </div>
      <div class="form-info">话费权益说明：每月 25 日之前完成注册及个人信息完善，当月账单自动抵扣 10 元话费；每月 26 日及以 后完成注册及个人信息完善，次月账单自动抵扣 10 元话费。此外，登录并使用平台，次月账单再自动抵 扣 10 元话费。活动有效期截至 2021 年 12 月 31 日。</div>
    </div>
    <van-popup v-model="showResult" class="popup" :close-on-click-overlay="ifSuccess ? false : true">
      <div class="popup-header">
        <img v-if="!ifSuccess" :src="closeicon" alt="" @click="closePopup" />
      </div>
      <div v-if="ifSuccess" class="popup-content-success">
        <img :src="successicon" alt="" />
        <p class="success-title">恭喜，您已成功提交信息</p>
        <p class="success-subtitle">{{ isCurrentdate ? '当月账单将自动抵扣' : '次月账单将自动抵扣' }}<span class="red">10元</span>话费</p>
        <van-divider />
        <p class="success-text">在电脑端登录使用九天·毕昇平台</p>
        <p id="successLink" class="success-link" @click="pasteLink('linkUrl')">https://jiutian.10086.cn/edu</p>
        <p class="success-info">次月账单再自动抵扣10元话费，开启AI之旅，赢取更多福利</p>
      </div>
      <div v-else class="popup-content-fail">
        <img :src="failicon" alt="" />
        <p class="fail-title">很遗憾，提交失败</p>
        <p class="fail-info">请您稍后重试</p>
      </div>
    </van-popup>
    <div v-if="showLoading" class="loading">
      <van-loading type="spinner" size="40" class="loading-spinner" />
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { Notify } from 'vant';
import { levelText, willText, formInput } from './constants';
import API from '@/constants/api/API.js';
import { pasteContent } from '@/utils/utils.js';

export default {
  name: 'Question',
  data() {
    return {
      formLogo: require('../../assets/image/h5/logo.png'),
      notagreeicon: require('../../assets/image/h5/notselect.png'),
      agreeicon: require('../../assets/image/h5/selectedall.png'),
      selecticon: require('../../assets/image/h5/selected.png'),
      successicon: require('../../assets/image/h5/success.png'),
      failicon: require('../../assets/image/h5/fail.png'),
      closeicon: require('../../assets/image/h5/close.png'),
      ifAgree: false,
      ifSuccess: true,
      levelSelected: [false, false, false, false],
      willSelected: [false, false, false],
      showResult: false,
      showLoading: false,
      isCurrentdate: false,
      info: {
        level: '',
        will: '',
      },
      formInput,
      levelText,
      willText,
      userPhonenum: '',
      buttonDisabled: true,
    };
  },
  created() {
    this.currentDate();
  },
  mounted() {
    if (this.$route.query.phoneNum) {
      this.userPhonenum = this.$route.query.phoneNum;
    } else {
      Notify({ type: 'danger', message: '请先填写注册信息' });
    }
  },
  methods: {
    handleFormChange() {
      this.checkSubmit();
    },
    handleClose() {
      console.log('--->');
    },
    checkSubmit() {
      let ifValidatedSelect = false;
      if (this.info.will !== '' && this.info.level !== '') {
        ifValidatedSelect = true;
      }
      let checkedArr = [];
      this.formInput.map((item) => {
        if (item.required) {
          checkedArr.push(item);
        }
      });
      const isValidatedItem = checkedArr.every((form) => {
        return form.value !== '';
      });
      if (isValidatedItem && ifValidatedSelect && this.ifAgree) {
        this.buttonDisabled = false;
      } else {
        this.buttonDisabled = true;
      }
    },
    currentDate() {
      const date = moment().format('DD');
      this.isCurrentdate = Number(date) < 25 ? true : false;
    },
    pasteLink(id) {
      const paste = document.getElementById(id).innerHTML;
      pasteContent(paste);
      Notify({ type: 'success', message: '复制成功' });
    },
    toggleAgree() {
      this.ifAgree = !this.ifAgree;
      this.checkSubmit();
    },
    toggleLevel(index, key) {
      this.levelSelected = [false, false, false, false];
      this.levelSelected[index] = true;
      this.info.level = key;
      this.checkSubmit();
    },
    toggleWill(index, key) {
      this.willSelected = [false, false, false];
      this.willSelected[index] = true;
      this.info.will = key;
      this.checkSubmit();
    },
    closePopup() {
      this.showResult = false;
    },
    submitInfo(values) {
      const params = Object.assign({}, values, { will: this.info.will }, { ability: this.info.level }, { phonenum: this.userPhonenum });
      if (!this.userPhonenum) {
        Notify({ type: 'danger', message: '请先注册用户' });
        return;
      }
      if (this.ifAgree) {
        this.showLoading = true;
        API.h5_campus.userDetails(params).then((res) => {
          if (res.state === 'OK') {
            this.showResult = true;
            this.ifSuccess = true;
          } else {
            this.showResult = true;
            this.ifSuccess = false;
          }
          setTimeout(() => {
            this.showLoading = false;
          }, 300);
        });
      } else {
        Notify({ type: 'danger', message: '请先勾选用户协议' });
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.form {
  background-image: url('~@/assets/image/h5/formbg.png');
  background-size: 100%;
  background-repeat: no-repeat;
  background-color: #8bb6f9;
  position: relative;
  padding: 30px 15px;
  .logo {
    width: 120px;
    img {
      width: 100%;
    }
  }
  .content {
    margin-top: 32px;
    .title {
      text-align: center;
      font-size: 27px;
      font-family: SourceHanSansCN-Bold, SourceHanSansCN, sans-serif;
      font-weight: bold;
      color: #ffffff;
    }
    .link {
      text-align: center;
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
      font-weight: 400;
      color: #ffffff;
      .link-url {
        font-weight: @jt-font-weight-medium;
        cursor: pointer;
      }
    }
    .form {
      background: #ffffff;
      box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
      border-radius: 16px;
      border: 2px solid #ffffff;
      margin: 23px 0 30px;
      padding: 15px;
      text-align: center;
      .form-title {
        margin-bottom: 30px;
        font-weight: 600;
        color: #41497a;

        .top {
          font-size: 22px;
        }
        .bottom {
          font-size: 36px;
          span {
            color: #ff5058;
          }
        }
      }
      .form-item {
        margin-bottom: 20px;
        .form-item-title {
          margin-bottom: 4px;
          text-align: left;
          font-size: 17px;
          font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
          font-weight: @jt-font-weight-medium;
          color: #41497a;
          .required {
            font-size: 12px;
            font-family: MicrosoftYaHei, sans-serif;
            color: #f04134;
            margin-right: 8px;
          }
        }
        .form-item-notselet {
          width: 100%;
          padding: 10px;
          margin: 15px 0;
          border-radius: 4px;
          font-size: 17px;
          font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
          font-weight: 400;
          background: #e7e8ec;
          color: #41497a;
          .selected {
            width: 14px;
            float: right;
            margin-top: 4px;
          }
          .dispear {
            opacity: 0;
          }
        }
        .form-item-seleted {
          background: rgba(76, 119, 243, 0.26);
          color: #4576ff;
        }
      }
      .form-agree {
        margin-top: 10px;
        font-size: 14px;
        font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
        font-weight: 400;
        text-align: left;
        color: #9ca0b6;
        .form-agree-icon {
          width: 18px;
          margin-right: 5px;
        }
      }
      .form-submit {
        width: 100%;
        margin-top: 20px;
        padding: 10px 0;
        text-align: center;
        background: linear-gradient(180deg, #487bfd 0%, #4056ff 100%);
        border-radius: 4px;
        font-size: 17px;
        font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
        font-weight: @jt-font-weight-medium;
        color: #ffffff;
      }
      .van-button--disabled {
        background: #bbb;
        opacity: 1;
      }
    }
    .form-info {
      font-size: 13px;
      font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
      font-weight: 400;
      color: #3f6ebf;
    }
  }
  .popup {
    width: 85%;
    background: #ffffff;
    box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
    border-radius: 12px;
    border: 2px solid #ffffff;
    text-align: center;
    padding: 24px;
    .popup-header {
      text-align: right;
      img {
        width: 24px;
      }
    }
    .popup-content-success {
      color: #41497a;
      font-size: 17px;
      img {
        width: 180px;
      }
      .success-title {
        font-size: 22px;
        color: #f63f48;
        margin-bottom: 7px;
        font-weight: 600;
      }
      .success-subtitle {
        font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
        font-weight: 600;
        .red {
          color: #f63f48;
        }
      }
      .van-divider {
        color: #d8d8d8 !important;
        border-color: inherit;
      }
      .success-text {
        font-weight: 400;
      }
      .success-link {
        margin: 8px;
        font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
        font-weight: @jt-font-weight-medium;
        color: #4777fd;
      }
      .success-info {
        margin-bottom: 15px;
        font-weight: 600;
      }
    }
    .popup-content-fail {
      font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
      font-weight: 600;
      img {
        width: 72px;
        margin: 16px;
      }
      .fail-title {
        font-size: 22px;
        color: #f63f48;
        margin-bottom: 7px;
      }
      .fail-info {
        font-size: 17px;
        color: #41497a;
        margin-bottom: 25px;
      }
    }
  }
  .loading {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    .loading-spinner {
      top: 50%;
      left: 50%;
      transform: translate(-5%, -50%);
      color: rgba(76, 119, 243, 1);
      font-size: 20px;
    }
  }
}
</style>
<style lang="less">
.van-cell {
  padding: 0;
}
.van-field__value .van-field__body input {
  padding: 10px;
  border: 1px solid #abafc5;
  border-radius: 4px;
}
.van-field__error-message {
  font-size: 14px;
  font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
  font-weight: 400;
  color: #f63f48;
}
.van-field__control {
  position: relative;
  color: rgba(65, 73, 122, 1) !important;
}
.van-field__value {
  .van-field__body {
    input {
      font-size: 17px !important;
    }
    input::-webkit-input-placeholder {
      color: #abafc5;
      font-size: 17px;
    }
  }
}
</style>
