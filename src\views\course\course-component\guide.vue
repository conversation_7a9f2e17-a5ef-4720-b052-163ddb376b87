<template>
  <div class="introduce-box">
    <div class="inner">
      <h1 class="portal-title">立即开始AI学习之路</h1>
      <div class="introduce-progress">
        <div class="item" v-for="(x, index) in AiStudy" :key="index">
          <div class="img"></div>
          <p>{{ x.text }}</p>
        </div>
      </div>
      <div class="introduce-list">
        <div class="item">
          <h6>优质公开课</h6>
          <p>适合不同阶段 AI 爱好者</p>
          <button class="button btnhover" @click="toAllCourse">立即学习</button>
        </div>

        <div class="item">
          <h6>{{ itemConfig.title }}</h6>
          <p>{{ itemConfig.text }}</p>
          <button v-if="haspPublicCoursePermission" class="button btnhover" @click="toTeachingCourse">{{ itemConfig.btn }}</button>
          <a-tooltip v-else>
            <template #title>敬请期待</template>
            <button class="button disabled">开课申请</button>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { checkPublishCoursePermission } from './auth';
import { checkLogin } from '@/keycloak';
export default {
  name: 'guide',
  data() {
    return {
      AiStudy: [
        {
          text: '定制化AI学习公开课',
          // img: "/assets/image/course/file-text.png",
        },
        {
          text: '交互式AI实训环境',
          // img: "/assets/image/course/code-library.png",
        },
        {
          text: '高性能GPU算力资源',
          // img: "@/assets/image/course/sever.png",
        },
      ],
    };
  },
  computed: {
    haspPublicCoursePermission() {
      return checkLogin() && checkPublishCoursePermission();
    },
    itemConfig() {
      return {
        title: this.haspPublicCoursePermission ? '开设公开课' : '成为老师',
        text: this.haspPublicCoursePermission ? '实现公开课配置和管理' : '在平台上开设你的公开课',
        btn: this.haspPublicCoursePermission ? '立即管理' : '开课申请',
        img: '@/assets/image/course/course-item1.png',
      };
    },
  },
  methods: {
    toAllCourse() {
      this.$router.push({
        path: '/course/my-course',
      });
    },
    toTeachingCourse() {
      this.$router.push({
        path: '/course/teaching/mycourses',
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import './common.less';
.introduce-box {
  padding: 64px 0;
}
.introduce-progress {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 65px;
  position: relative;
  &:before {
    content: '';
    // width: 100%;
    height: 1px;
    background: #0082ff;
    position: absolute;
    left: 0;
    top: 56px;
  }
  .item {
    .img {
      width: 56px;
      height: 56px;
      background: #0082ff;
      margin: auto;
      margin-bottom: 19px;
    }
    &:nth-of-type(1) .img {
      background: url('~@/assets/image/course/filetext.png') no-repeat center;
      background-size: 56px 56px;
    }
    &:nth-of-type(2) .img {
      background: url('~@/assets/image/course/code-library.png') no-repeat center;
      background-size: 56px 56px;
    }
    &:nth-of-type(3) .img {
      background: url('~@/assets/image/course/sever.png') no-repeat center;
      background-size: 56px 56px;
    }

    span {
      display: block;
      width: 8px;
      height: 8px;
      background: #0082ff;
      border-radius: 50%;
      margin: 17px auto 21px;
    }
    p {
      text-align: center;
      &:nth-of-type(1) {
        font-size: 18px;
        font-weight: bold;
        line-height: 25px;
      }
      &:nth-of-type(2) {
        font-size: 16px;
        color: #606972;
        line-height: 22px;
        margin-top: 6px;
      }
    }
  }
}
.introduce-list {
  display: flex;
  margin-top: 92px;
  justify-content: space-between;

  .item {
    width: 590px;
    height: 154px;
    background: #178cf9;
    border-radius: 2px;
    padding: 45px 0 0 109px;
    position: relative;

    &:nth-of-type(1) {
      background: #178cf9 url('~@/assets/image/course/course-item1.png') no-repeat center;
      background-size: 590px 154px;
    }
    &:nth-of-type(2) {
      background: #20b7dc url('~@/assets/image/course/course-item2.png') no-repeat center;
      background-size: 590px 154px;
    }

    h6 {
      color: #ffffff;
      line-height: 33px;
      font-size: 24px;
    }
    p {
      font-size: 16px;
      color: #ffffff;
      line-height: 22px;
    }
    .button {
      width: 128px;
      height: 40px;
      border-radius: 2px;
      border: 1px solid rgba(255, 255, 255, 0.65);
      font-size: 16px;
      color: #ffffff;
      line-height: 22px;
      position: absolute;
      right: 32px;
      top: 57px;
      bottom: 57px;
      background-color: transparent;
      &.disabled {
        cursor: no-drop;
        border: 1px solid rgba(255, 255, 255, 0.325);
        color: rgba(255, 255, 255, 0.5);
      }
    }
    .btnhover:hover {
      border-radius: 2px;
      background: #ffffff;
      font-weight: 400;
      color: #178cf9;
    }
  }
}
</style>
