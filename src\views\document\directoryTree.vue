<template>
  <div>
    <a-menu mode="inline" :selected-keys="selectedKeys" :open-keys="openKeys" @click="handleClickItem">
      <template v-for="item of treeData">
        <a-menu-item v-if="item.type === 'page'" :key="item.id" :item="item">
          {{ item.name }}
        </a-menu-item>
        <a-sub-menu v-if="item.type === 'catalog'" :key="item.id" :item="item" @titleClick="handleClickSubMenu">
          <template #title>
            <span>{{ item.name }}</span>
          </template>
          <a-menu-item v-for="subItem of item.children" :key="subItem.id">
            {{ subItem.name }}
          </a-menu-item>
        </a-sub-menu>
      </template>
    </a-menu>
  </div>
</template>

<script>
export default {
  name: 'DirectoryTree',
  props: {
    treeData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    activeKey: {
      type: String,
      default: '',
    },
  },
  emits: ['clickItem'],
  data() {
    return {
      selectedKeys: [this.activeKey],
      openKeys: [],
    };
  },
  watch: {
    treeData: {
      handler: function (val) {
        this.selectedKeys = [this.activeKey];
        this.openKeys = val.filter((item) => item.type === 'catalog').map((item) => item.id);
      },
    },
    activeKey: {
      handler: function (val) {
        this.selectedKeys = [val];
      },
    },
  },
  methods: {
    handleClickItem({ key }) {
      this.selectedKeys = [key];
      this.$emit('clickItem', key);
    },
    handleClickSubMenu({ key }) {
      this.toggleLoop(this.openKeys, key);
    },
    toggleLoop(data, key) {
      if (data.includes(key)) {
        data.splice(data.indexOf(key), 1);
      } else {
        data.push(key);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

:deep(.ant-menu) {
  background: transparent;
  border: none;
}
:deep(.ant-menu-item-selected) {
  background: transparent !important;
  color: #0082ff !important;
}
:deep(.ant-menu-item-selected::after) {
  opacity: 0 !important;
}
:deep(.ant-menu-item),
:deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title) {
  font-size: 16px;
  font-weight: @jt-font-weight-medium;
  color: #121f2c;
  &:hover {
    color: #0082ff;
  }
}
:deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item) {
  font-size: 14px;
  font-weight: 400;
  color: #121f2c;
  &:hover {
    color: #0082ff;
  }
}
:deep(.ant-menu-submenu-selected) {
  color: #121f2c;
  &:hover {
    color: #0082ff;
  }
}
</style>
