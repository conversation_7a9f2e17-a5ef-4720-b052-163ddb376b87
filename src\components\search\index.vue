<template>
  <div class="tab-extra-container">
    <a-input v-model:value="keywords" allow-clear class="search" :placeholder="placeholder">
      <template #prefix>
        <jt-icon type="iconsousuo" />
      </template>
    </a-input>
  </div>
</template>

<!-- 
  1.带清除
  2.支持修改placeholder
  3.节流
  <Search 
     @handSearch="handSearch" 
     placeholder="搜索比赛"   // 支持修改placeholder
  >
  </Search>
 -->

<script>
import _ from 'lodash';

export default {
  props: {
    placeholder: {
      type: String,
      default: '请输入内容',
    },
    value: {
      type: String,
      default: '',
    },
  },
  emits: ['handSearch'],
  data() {
    return {
      keywords: '',
    };
  },
  watch: {
    value(val) {
      this.keywords = val;
    },
    keywords(val) {
      this.changeSearch(val);
    },
  },
  methods: {
    changeSearch: _.debounce(function (value) {
      this.$emit('handSearch', value);
    }, 500),
  },
};
</script>

<style lang="less" scoped>
.tab-extra-container {
  .search {
    width: 240px;
  }
  :deep(.ant-input-affix-wrapper .ant-input:not(:last-child)) {
    padding-right: 22px;
  }
}
</style>
