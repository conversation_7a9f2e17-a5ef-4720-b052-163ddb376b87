/**
 * axios默认参数配置文件
 */
import Axios from 'axios';
import { notification } from 'ant-design-vue';
import { BrowserDetect } from '../utils/browserdetect';
import { getProxyPrefix } from '../config';
import store from '../store';

// 线上环境关掉全局报错
const isDev = process.env.NODE_ENV === 'development';

const proxyPrefix = getProxyPrefix();
const baseURL = isDev ? `/api/${proxyPrefix}` : './';

//关闭H5页面的报错
const currentOs = BrowserDetect.init().OS;
const showDefaultError = currentOs === 'iPhone' || currentOs === 'Android' ? false : true;

export const axios = Axios.create({
  baseURL: baseURL, // 本地
  withCredentials: true, // set cross-origin
  headers: {
    'Content-Type': 'application/json',
  },
});

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (err) => {
    if (err.response?.status === 403 || err.response?.status === 401) {
      // 经常容易导致重复跳转，所以先不要这个逻辑
      // return location.reload();
      return;
    }

    if (isDev && showDefaultError) {
      notification.error({
        message: '系统错误',
        description: err.response ? err.response.config.url : err,
      });
    }
    throw err;
  }
);

export function GET(url, params, options, paramsSerializerValue) {
  params = params || {};
  options = options || {};
  let paramsSerializerObj = {};
  if (paramsSerializerValue) {
    paramsSerializerObj = {
      paramsSerializer(params) {
        var yourNewParams = params[paramsSerializerValue]
          .map(function (_) {
            return `${paramsSerializerValue}=`.concat(_);
          })
          .join('&');
        return yourNewParams;
      },
    };
  }
  const { useError = true } = options;
  const { useLoading = false } = options;
  if (useLoading) {
    store.state.globalLoading = true;
  }
  return new Promise((resolve, reject) => {
    axios
      .get(
        url,
        {
          params,
          ...paramsSerializerObj,
          headers: {},
        },
        { ...options }
      )
      .then((res) => {
        if (isDev && useError && res.data.errorCode && showDefaultError) {
          notification.error({
            message: '系统错误',
            description: res.data.errorMessage || '系统出错，请联系相应人员',
          });

          if (res.data.errorCode === '-500') {
            reject(res.data);
          }
        }
        resolve(res.data);
      })
      .finally(() => {
        if (useLoading) {
          store.state.globalLoading = false;
        }
      });
  });
}

export function POST(url, data, options = {}, params = {}) {
  const { useError = true } = options;
  const { useLoading = false } = options;
  if (useLoading) {
    store.state.globalLoading = true;
  }
  return new Promise((resolve, reject) => {
    axios.post(url, data, params, { ...options }).then((res) => {
      if (isDev && useError && res.data.errorCode && showDefaultError) {
        notification.error({
          message: '系统错误',
          description: res.data.errorMessage || '系统出错，请联系相应人员',
        });
        if (res.data.errorCode === '-500') {
          reject(res.data);
        }
      }
      resolve(res.data);
    });
  }).finally(() => {
    if (useLoading) {
      store.state.globalLoading = false;
    }
  });
}

export function PUT(url, data, options = {}, params = {}) {
  const { useError = true } = options;
  const { useLoading = false } = options;
  if (useLoading) {
    store.state.globalLoading = true;
  }
  return new Promise((resolve, reject) => {
    axios.put(url, data, params, { ...options }).then((res) => {
      if (isDev && useError && res.data.errorCode && showDefaultError) {
        notification.error({
          message: '系统错误',
          description: res.data.errorMessage || '系统出错，请联系相应人员',
        });
        if (res.data.errorCode === '-500') {
          reject(res.data);
        }
      }
      resolve(res.data);
    });
  }).finally(() => {
    if (useLoading) {
      store.state.globalLoading = false;
    }
  });
}

export default { POST, GET };

// 文件上传和下载相关接口，不需要登录态
export const axiosWithNoToken = Axios.create({
  baseURL: baseURL, // 本地
  withCredentials: true, // set cross-origin
});
