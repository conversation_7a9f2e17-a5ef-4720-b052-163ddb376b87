import { GET, POST } from '@/request';

export default {
  // 数据集分类列表
  getDatasetClassList: (data) => POST('dataset/web/datasetClassList', data),
  // 九天校园数据集数据计量
  getDataCount: (data) => POST('dataset/web/dataCount', data),
  // 九天校园数据集数据详情页文件列表
  getDataFileList: (data) => POST('dataset/web/dataFileList', {}, { params: data }),
  // 九天校园数据集查询
  getDataList: (data) => POST('dataset/web/dataList', null, { params: data }),
  // 九天校园数据集数据类型列表
  getDataTypeList: (data) => POST('dataset/web/dataTypeList', {}, { params: data }),
  //使用人数统计接口 /dataset/web/instance/num/insert
  getInsert: (data) => GET('dataset/web/instance/num/insert', data),
  // 求职模块
  // /jt_education/job/jList  九天校园求职查询
  getJobList: (data) => POST('/job/jList', null, {}, { params: data }),
  //求职使用人数统计接口  /InstanceNum/getInstanceNumList
  getInsertNum: (data) => GET('/job/getInstanceNumList', data),
};
