import { GET, POST } from '../request';
// 我举办的比赛卡片信息
export const cardCompetitionInfo = () => GET('/competiton/web/manage/myself/create/competition/info', {}, { useError: false });

// 我举办的比赛列表
export const getHeldCompetitions = (data) => GET('/competiton/web/manage/myself/create/competitions', data);

// 获取比赛管理详情信息
export const getCompetitionDetail = (data) => GET('/competiton/web/manage/info', data, { useError: false });

// 获取常见问题接口
export const getCompetitionCommonQuestion = (data) => GET('/competiton/web/question', data);

// 赛制介绍及赛题说明
export const getCompetitionDesAndIntro = (data) => GET('/competiton/web/desc/info', data);

// 我举办的正式赛和练习赛总数
export const getManageCompetitionCount = () => GET('/competiton/web/manage/get/total');

// 团队管理
export const getTeamManageList = (data) => GET('/competiton/web/manage/edit/join/team/list', data);

// 报名及用户管理
export const getUserManageList = (data) => GET('/competiton/web/manage/edit/join/user/list', data);

// 上传文本
export const textUpload = (data) => POST('/object/web/storage/text/upload', data, { useError: false }); // baseString
// type 0 赛题说明 1赛制介绍 2 常见问题
export const updateCompetitionRefData = (data) => POST('/competiton/web/manage/edit/description/update', data);

// 创建比赛
export const createCompetition = (data) => POST('/competiton/web/manage/create', data);

// 比赛是否重名检查
export const checkCompetitionByName = (data) => GET('/competiton/web/manage/checkCompetitionByName', data);
