import { GET, POST } from '../request';

// 添加课程
export const addCourse = (data) => POST('/course_model/web/teaching/course/add', data, { useError: false });

// 更新课程基本信息 加一个courseId
export const updateCourse = (data) => POST('/course_model/web/teaching/course/update', data, { useError: false });

// 获取所有课程列表
export const getCourseList = (data) => POST('/course_model/web/teaching/list/my/course', data);

// 获取课程分类别个数
export const courseCount = (data) => POST('/course_model/web/teaching/count/my/course', data);

// 默认图片列表
export const defaultPictures = () => POST('/course_model/web/teaching/list/picture');

// 根据id获取课程详情信息
export const getCourseDetail = (data) => GET('/course_model/web/teaching/course/detail', data, { useError: false });

// 新增课节
export const addCatalogByCourseId = (data) => POST('/course_model/web/teaching/catalog/add', data, { useError: false });

// 修改课节
export const updateCatalogById = (data) => POST('/course_model/web/teaching/catalog/update', data, { useError: false });

// 删除课节
export const deleteCatalogById = (data) => GET('/course_model/web/teaching/catalog/delete', data, { useError: false });

// 根据课程id获取全量课节信息
export const getAllCatalogById = (data) => GET('/course_model/web/teaching/catalog/list/courseId', data);

// 获取课节及资源（所有的）
export const getCatalogAndResourceById = (data) => GET('/course_model/web/teaching/catalog/resource/list/courseId', data);

// 新增文档或视频 resourceType 资源类型：1:项目；2：视频 3：文档
export const addVideoOrDoc = (data) => POST('/course_model/web/teaching/catalog/resourse/add', data, { useError: false });

// 修改文档或视频 resourceType 资源类型：1:项目；2：视频 3：文档
export const editVideoOrDoc = (data) => POST('/course_model/web/teaching/catalog/resourse/update', data, { useError: false });

// 删除
export const deleteVideoOrDoc = (data) => GET('/course_model/web/teaching/catalog/resourse/delete', data, { useError: false });

// 发布资源
export const publishResource = (data) => GET('/course_model/web/teaching/catalog/resourse/publish', data, { useError: false });

// 分页查询模型实例列表
export const getInstanceList = (data) => POST('/dp_platform/resource/index/instance_list', data);

// 验证实例目录的大小是否符合要求
export const validInstanceSize = (data) => GET('/course_model/web/teaching/catalog/project/valid', data, { useError: false });

// 给课节新建项目
export const createProjectForCatalog = (data) => POST('/course_model/web/teaching/catalog/project/add', data, { useError: false });

/* 学生跟踪 */

// 查询课程的学生总数
export const totalStudentByCourseId = (data) => POST('/course_model/web/teaching/course/student/count', data);

// 根据课程名称查询课程
export const findCourseByName = (data) => GET('/course_model/web/teaching/course/detail/byname', data);

// 根据课程id查询这个课程的所有学生
export const getAllStudentByCourseId = (data) => POST('/course_model/web/teaching/course/student/list', data);

// 根据课程删除学生
export const deleteStudentByCourseId = (data) => POST('/course_model/web/teaching/course/student/delete', data, { useError: false });

/* 学生管理 */

// 单独新增学生
export const addStudentByCourse = (data) => POST('/course_model/web/teaching/course/student/add', data, { useError: false });

// 根据课程获取学生列表
export const getStudentsByCourse = (data) => POST('/course_model/web/teaching/course/student/list', data);

// 查询学生总数
export const studentsCountByCourse = (data) => POST('/course_model/web/teaching/course/student/count', data);

// 查询课节对应的学生学习情况
export const getStudentByCatalogId = (data) => POST('/course_model/web/teaching/catalog/student/list', data);

// 查询课节的已学习学生
export const countStudentStudyedByCatalogId = (data) => POST('/course_model/web/teaching/catalog/student/count', data);

/* 课程介绍及资源 */

// 富文本上传
export const textUpload = (data) => POST('/object/web/storage/text/upload', data, { useError: false }); // baseString

// 富文本查询
export const textFind = (data) => GET('/object/web/storage/findById', data); // id

// 查询课程介绍
export const getCourseIntroduction = (data) => GET('/course_model/web/teaching/course/detail/introduce', data);

// 添加或修改课程介绍
export const updateOrSaveCourseIntro = (data) => POST('/course_model/web/teaching/course/content/save', data, { useError: false });

// 添加或修改课程资源
export const updateOrSaveCourseResource = (data) => POST('/course_model/web/teaching/course/resourse/save', data, { useError: false });

// 查询课程资源
export const getCourseResource = (data) => GET('/course_model/web/teaching/course/detail/resource', data);

export const publishIntroOrResource = (data) => POST('/course_model/web/teaching/course/content/publish', data);

// 学生信息查询
export const getUserInfo = (data) => GET('/keycloak/web/user/getuserinfo', data);

// 发布课程
export const publishCourse = (data, options) => GET('/course_model/web/teaching/course/publish', data, options);

// 下架课程
export const deleteCourse = (data) => GET('/course_model/web/teaching/course/remove', data, { useError: false });

// 下载课程列表
export const downloadStudentList = (data) => GET('/course_model/web/teaching/course/student/export', data);

// 删除课节对应的项目
export const deleteCatalogProject = (data) => POST('/course_model/web/teaching/catalog/project/detele', data, { useError: false });

// 修改课节对应的项目
export const updateCatalogProject = (data) => POST('/course_model/web/teaching/catalog/project/update', data, { useError: false });

// 获取本人开课数和学习人数
export const getCourseNumAndStudentNum = () => GET('/course_model/web/teaching/data/my/course');

// 下载默认模板
export const downloadModel = () => GET('/course_model/web/teaching/file/model');

// 获取失败学生的下载列表 group
export const exportFailStudentList = (data) => GET('/course_model/web/teaching/fail/student/export', data);

// 对课节进行排序
export const sortCatalog = (data) => POST('/course_model/web/teaching/catalog/sort', data, { useError: false });

// 对课节的资源进行排序
export const sortResource = (data) => POST('/course_model/web/teaching/catalog/resourse/sort', data, { useError: false });

// 根据项目id查询项目发布状态
export const getProjectStatusById = (data) => GET('/course_model/web/teaching/catalog/resourse/status', data);

// 查询希冀教学平台课程列表
export const getXJCourseList = (data) => GET('/course_model/web/cg_proxy/courses', data);

// 获取希冀教学升级状态
export const getTeachingStatus = () => GET('/notice/web/getTeachingStatus', {}, { useError: false });
