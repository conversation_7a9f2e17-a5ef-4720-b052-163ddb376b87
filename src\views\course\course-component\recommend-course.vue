<template>
  <div class="recomment-course">
    <div class="inner">
      <h1 class="portal-title">推荐公开课</h1>
      <h2 class="sub-title" style="margin-bottom: 48px">分领域优质课程，持续上架</h2>
      <div class="recommend-course-tabs">
        <div class="tab-item" @click="courseCateCode = item.val" v-for="item in recommendCats" :key="item.val" :class="{ active: item.val === courseCateCode }">
          <img v-if="item.val !== courseCateCode" :src="item.img" alt="" />
          <img v-else :src="item.activeImg" alt="" />
          {{ item.name }}
        </div>
      </div>
      <div class="recommend-course-list">
        <!-- <jt-common-content :loading="recommendListLoading" :empty="recommendCourseList.length === 0" :emptyStyle="{ height: '532px' }"> -->

        <jt-skeleton :loading="recommendListLoading" :rows="2" :rowStyle="{ height: '132px' }" :skeletonStyle="{ height: '268px', display: 'flex', 'flex-direction': 'column', 'justify-content': 'space-between' }">
          <div @click="handleNavigateToDetail(item.id)" class="content-item list-item flex" v-for="item in recommendCourseList.slice(0, 2)" :key="item.id">
            <div class="left flex">
              <div class="info">
                <div>
                  <span class="title">{{ item.name }}</span>
                  <a-tag v-if="item.courseFlag == 1">公开课</a-tag>
                  <a-tag v-else>封闭课</a-tag>
                  <a-tag :class="courseStatusTag(item.courseStatus).class">{{ courseStatusTag(item.courseStatus).text }}</a-tag>
                </div>
                <p class="text description ellipsis-text">{{ item.courseIntroduce }}</p>
                <p>
                  <span class="number">{{ `${item.studyNum || 0}人学习` }}</span>
                </p>
              </div>
            </div>
            <div class="right">
              <span class="text ellipsis-text" style="width: 480px; text-align: right">
                {{ item.instituteName }}
                <a-divider type="vertical" />
                {{ item.catalogNum || 0 }}节
                <a-divider type="vertical" />
                {{ `开课时间：${item.startTime} - ${item.endTime}` }}
              </span>
            </div>
          </div>
        </jt-skeleton>
        <!-- </jt-common-content> -->
      </div>
      <div class="link-button">
        <a @click="handleNavigateToList" class="link-arrow-right">更多公开课</a>
      </div>
    </div>
  </div>
</template>

<script>
import { courseStudentApi } from '@/apis';
export default {
  name: 'recomment-course',
  data() {
    return {
      recommendCats: [
        { name: '机器学习/深度学习', val: 'AI', img: require('@/assets/image/course/tabs0.png'), activeImg: require('@/assets/image/course/tabs0-active.png') },
        { name: '工具与框架', val: 'TF', img: require('@/assets/image/course/tabs1.png'), activeImg: require('@/assets/image/course/tabs1-active.png') },
        { name: '计算机视觉', val: 'CMV', img: require('@/assets/image/course/tabs2.png'), activeImg: require('@/assets/image/course/tabs2-active.png') },
        { name: '自然语言处理', val: 'NLP', img: require('@/assets/image/course/tabs3.png'), activeImg: require('@/assets/image/course/tabs3-active.png') },
        { name: '智能数据分析', val: 'IDA', img: require('@/assets/image/course/tabs4.png'), activeImg: require('@/assets/image/course/tabs4-active.png') },
        { name: '智能语音', val: 'IV', img: require('@/assets/image/course/tabs5.png'), activeImg: require('@/assets/image/course/tabs5-active.png') },
      ],
      recommendTitles: ['机器学习/深度学习', '工具与框架', '计算机视觉', '自然语言处理', '智能数据分析', '智能语音'],
      courseCateCode: 'AI',
      recommendCourseList: [],
      recommendListLoading: true,
    };
  },
  mounted() {
    this.getCourseRecommend();
  },
  watch: {
    courseCateCode() {
      this.getCourseRecommend();
    },
  },
  methods: {
    courseStatusTag(val) {
      const tagMap = {
        0: { text: '即将开始', class: 'toBegin' },
        1: { text: '进行中' },
        2: { text: '已结束', class: 'ended' },
      };
      const tag = tagMap[val];
      return tag || {};
    },
    handleNavigateToList() {
      this.$router.push({
        path: '/course/course-list',
        query: {
          key: 'courseCateCode',
          value: this.courseCateCode,
        },
      });
    },
    getCourseRecommend() {
      const obj = { courseCateCode: this.courseCateCode };
      this.recommendCourseList = [{}, {}];
      this.recommendListLoading = true;
      courseStudentApi.getCourseRecommend(obj).then((res) => {
        if (!res.errorCode) {
          this.recommendCourseList = res.body;
        }
        this.recommendListLoading = false;
      });
    },
    handleNavigateToDetail(id) {
      this.$router.push({
        path: '/course/course-detail',
        query: {
          courseId: id,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import './common.less';
.recommend-course {
  background-color: #f4f8fa;
}
.recommend-course-tabs {
  display: flex;
  justify-content: space-between;
  .tab-item {
    width: 201px;
    height: 112px;
    &:not(:last-child) {
      border-right: 1px solid #d8e0e5;
    }
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #606972;
    line-height: 24px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    margin-bottom: 2px;
    padding: 20px 20px 24px 16px;
    background-color: #fff;
    cursor: pointer;
    img {
      width: 32px;
      height: 32px;
      display: block;
      margin-bottom: 10px;
    }
    &:hover {
      background: #f4f9ff;
    }
    &.active {
      background: linear-gradient(136deg, #0185ff 0%, #77beff 100%);
      color: #ffffff;
    }
  }
}
.flex {
  display: flex;
}
.recommend-course-list {
  .list-item {
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 134px;
    padding: 24px 32px;
    margin-bottom: 1px;
    background: #ffffff;
    box-shadow: 0px 1px 0px 0px #e0e1e1;
    cursor: pointer;

    &:hover {
      background: #f8f9fa;
    }

    .img-container {
      margin-right: 32px;
      img {
        width: 148px;
        height: 112px;
      }
    }
    .title {
      margin-right: 16px;
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
    }
    &:hover {
      .title {
        color: #0082ff;
      }
    }
    .ant-tag {
      color: #0082ff;
      border: 1px solid #0082ff;
      &.toBegin {
        color: #0cb0d4;
        border: 1px solid #0cb0d4;
      }
      &.ended {
        border: none;
        color: #fff;
        background: linear-gradient(270deg, #cbcfd2 0%, #cbcfd2 100%);
      }
    }
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #606972;
    }
    .description {
      padding-top: 16px;
      display: inline-block;
      width: 600px;
    }
    .number {
      font-size: 14px;
      font-weight: 400;
      color: #a0a6ab;
    }
    .right {
      display: flex;
      height: 100%;
      align-items: flex-start;
      .progress {
        margin-right: 24px;
        font-size: 14px;
        font-weight: 400;
        color: #606972;
      }
      a {
        font-size: 16px;
        font-weight: @jt-font-weight-medium;
      }
      .badge-container {
        margin-right: 36px;
      }
    }
  }
  .item-title {
    display: flex;
    justify-content: space-between;
    h3 {
      font-size: 18px;
      display: flex;
      align-items: center;
      span {
        border-radius: 10px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 60px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #337dff;
        border: 1px solid #337dff;
        text-align: center;
        margin-left: 30px;
        &.running {
          color: #0082ff;
        }
        &.ready {
          border-color: #0cb0d4;
          color: #0cb0d4;
        }
        &.over {
          background: linear-gradient(270deg, #cbcfd2 0%, #cbcfd2 100%);
          border: none;
          color: #fff;
        }
      }
    }
  }
  .item-body {
    padding: 10px 0;
  }
  .item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    p:nth-of-type(2) {
      color: #337dff;
      cursor: pointer;
    }
  }
}
.link-arrow-right {
  font-size: 16px;
  position: relative;
  padding-right: 15px;
  &:before {
    content: '';
    width: 6px;
    height: 6px;
    border-top: 1px solid #121f2c;
    border-right: 1px solid #121f2c;
    transform: rotate(45deg);
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
  &:hover {
    transition: all 0.3s;
    &:before {
      border-color: @jt-primary-color;
    }
  }
}
.recomment-course {
  padding-top: 40px;
}
</style>
