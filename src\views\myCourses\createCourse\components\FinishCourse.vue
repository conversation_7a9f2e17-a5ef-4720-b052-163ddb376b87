<template>
  <div v-if="Object.keys(currentActiveCourse).length > 0">
    <img src="@/assets/image/teaching/success.png" alt="" class="result-icon" />
    <p class="success-msg">修改课程信息成功！</p>
    <a-button type="primary" @click="jumpToCourseManage">返回课程管理</a-button>
  </div>
  <!-- 公开课 -->
  <div v-else-if="submitData.courseFlag == '1'">
    <img src="@/assets/image/teaching/success.png" alt="" class="result-icon" />
    <p class="success-msg">您已经成功创建课程！</p>
    <p class="next-tip">请在教学专区完善内容后提交审核，审核通过后方可发布</p>
    <a-button type="primary" @click="jumpToMyCourses">进入我开设的课程</a-button>
  </div>
  <div v-else>
    <img src="@/assets/image/teaching/success.png" alt="" class="result-icon" />
    <p class="success-msg">您已经成功创建并发布课程！</p>
    <a-button type="primary" @click="jumpToMyCourses">进入我开设的课程</a-button>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'FinishCourse',
  props: {
    submitData: {
      type: Object,
    },
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
  },
  methods: {
    jumpToMyCourses() {
      this.$router.push({
        path: '/course/teaching/mycourses',
        query: {
          noLeaveConfirm: true,
        },
      });
    },
    // 返回到课程管理的-基本信息
    jumpToCourseManage() {
      // eslint-disable-next-line no-unused-vars
      const { subPage, ...params } = this.$route.params;
      this.$router.replace({
        name: '课程管理',
        params,
        query: {
          noLeaveConfirm: true,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.success-msg {
  font-size: 20px;
  margin: 10px 0px;
  font-weight: @jt-font-weight-medium;
}
.result-icon {
  width: 48px;
}
.next-tip {
  color: #606972;
  margin-bottom: 20px;
}
</style>
