<template>
  <a-skeleton :loading="competitionDetailSkeletonLoading">
    <jt-list-item class="content-information">
      <template #center>
        <div class="content-information-left">
          <p class="competition-name">
            <span class="competition-title">{{ formDataInfo.typeName }}</span>
            <jt-tag :type="getTagClass(formDataInfo.flag)" class="tag-s0">
              {{ runningStatusMaps[formDataInfo.flag] }}
            </jt-tag>
            <jt-tag type="normal" class="tag-s1">{{ competitionTypeMaps[formDataInfo.typeId] }}</jt-tag>
          </p>
          <p class="competition-details">{{ formDataInfo.summary }}</p>
          <div class="competition-label-box">
            <span class="label-blod pr5">时间 :</span>
            <span class="label-blod pr24">
              {{ formDataInfo.startTime }} -
              {{ formDataInfo.endTime }}
            </span>
            <span class="label">标签 :</span>
            <span class="label-content">{{ formDataInfo.tag }}</span>
            <span class="label">举办方 :</span>
            <img v-cloak v-for="(items, indexs) in formDataInfo.leader" :key="indexs" :src="items" class="leader-img" alt="" />
          </div>
        </div>
      </template>
      <template #right>
        <div class="content-information-right">
          <div class="information-reward" v-if="formDataInfo.amount != '特别礼包'">奖池 ¥{{ handlePrize(formDataInfo.amount) }}</div>
          <div v-else class="information-reward">奖池 特别礼包</div>
          <div class="information-num">{{ formDataInfo.number }}人参赛</div>
        </div>
      </template>
    </jt-list-item>
  </a-skeleton>
</template>

<script>
import JtTag from '@/components/tag/index.vue';
import JtListItem from '@/components/listItem/index.vue';
import { runningStatusMaps, competitionTypeMaps } from '@/views/competition/competitionConfig/index';
import { handlePrize } from '@/utils/utils';

export default {
  name: 'competition-detail-info',
  components: {
    JtTag,
    JtListItem,
  },
  props: {
    formDataInfo: {
      type: Object,
      require: true,
    },
    competitionDetailSkeletonLoading: Boolean,
  },
  data() {
    return {
      runningStatusMaps,
      competitionTypeMaps,
    };
  },
  methods: {
    handlePrize,
    getTagClass(flag) {
      return flag == 1 ? 'tobegin' : flag == 2 ? 'running' : 'end';
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
:deep(.ant-skeleton-content .ant-skeleton-paragraph > li + li) {
  margin-top: 8px;
}

.content-information {
  .content-information-left {
    flex: 1;
    .competition-name {
      font-size: 24px;
      color: @jt-text-color-primary;
      margin-right: 8px;

      .competition-title {
        font-weight: 600;
        padding-right: 8px;
        vertical-align: middle;
      }
    }
    .competition-details {
      font-size: @jt-font-size-base;
      color: @jt-text-color;
      line-height: 22px;
      margin: 16px 0;
      max-width: calc(1200px - 159px);
    }
    .competition-label-box {
      font-size: @jt-font-size-base;
      color: @jt-text-color-secondary;
      line-height: 20px;

      .label-blod {
        font-size: @jt-font-size-lger;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
      }

      .pr5 {
        padding-right: 5px;
      }
      .pr24 {
        padding-right: 24px;
      }
      .label {
        color: @jt-text-color-secondary;
        padding-right: 5px;
      }
      .label-content {
        color: @jt-text-color;
        padding-right: 16px;
      }
    }

    .leader-img {
      height: 23px;
      margin-right: 16px;
      object-fit: contain;
    }
  }
  .content-information-right {
    .information-reward {
      text-align: right;
      height: 33px;
      font-size: 24px;
      font-weight: @jt-font-weight-medium;
      color: #ff4945;
      line-height: 33px;
      margin-bottom: 8px;
    }
    .information-num {
      height: 22px;
      font-size: @jt-font-size-lg;
      color: @jt-text-color;
      line-height: 18px;
      text-align: right;
    }
  }
}
.tag-s0 {
  vertical-align: middle;
}
.tag-s1 {
  margin: 0 8px 0;
  vertical-align: middle;
}
</style>
