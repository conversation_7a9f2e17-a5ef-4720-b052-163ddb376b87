<script setup>
import { FilterFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <div class="student-total">
      <span class="description"
        >学生总数（人）<span class="number">{{ studentTotal }}</span></span
      >
    </div>
    <div class="study-track-content">
      <ul v-if="catalogList && catalogList.length > 0" class="course-list">
        <li v-for="(item, index) in catalogList" :key="item.id" :class="{ selectItem: selectCatalogId === item.id }" @click="handleSelectCatalog(item.id)">课节{{ toChinesNum(index + 1) }}：{{ item.catalogName }}</li>
      </ul>
      <div v-else class="course-list empty-holder">
        <div style="text-align: center">
          <jt-icon type="iconzanwushuju" style="font-size: 48px; color: #e0e1e1"></jt-icon>
          <p style="margin: 16px 0px 10px">本课程暂无课节</p>
          <!-- 去教学内容部分 -->
          <div>去教学内容中<span style="color: #0082ff; cursor: pointer" @click="jumpToCourseManage()"> 新增课节</span></div>
        </div>
      </div>

      <div v-if="tableData.length === 0 && searchValue == '' && studyStatus == ''" class="student-table empty-tooltip" style="padding-left: 170px">
        <div class="empty">
          <div>暂无学生</div>
          <span>去课程管理中<span style="color: #0082ff; cursor: pointer" @click="jumpToCourseManagement"> 新增学生</span></span>
        </div>
      </div>

      <div v-else class="student-table">
        <div class="table-header">
          <span
            >已学习学生<span style="color: #0082ff; padding-left: 5px">{{ studyedTotal }}</span>
            <span v-if="oldTotal"
              >（其中<span style="color: #0082ff; padding-left: 5px">{{ oldTotal }}</span
              >人学非最新）</span
            >
          </span>
          <a-input allow-clear placeholder="搜索姓名/学号" style="width: 240px; height: 32px" @change="handleCourseSearch">
            <template #prefix>
              <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
            </template>
          </a-input>
        </div>
        <a-table :columns="columns" :data-source="tableData" :loading="loading" :scroll="{ y: 430 }" :pagination="false">
          <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm }">
            <div v-if="catalogList && catalogList.length > 0" class="filter-status-select">
              <div class="all" :class="{ select: studyStatus == '' }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, '')">全部类型</div>
              <div class="already-study" :class="{ select: studyStatus == '1' }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, '1')">已学习</div>
              <div class="old-study" :class="{ select: studyStatus == '2' }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, '2')">已学非最新</div>
              <div class="un-study" :class="{ select: studyStatus == '0' }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, '0')">未学习</div>
            </div>
          </template>
          <template #filterIcon>
            <FilterFilled v-if="catalogList && catalogList.length > 0" :style="{ color: filter ? '#108ee9' : undefined }" class="status-filter-icon" />
          </template>
          <template #status-slot="{ status }">
            <a-space v-if="selectCatalogId">
              <div class="status-icon" :class="status == '1' ? 'green' : status == '0' || status == null ? 'orange' : 'blue'"></div>
              {{ status == '1' ? '已学习' : status == '0' || status == null ? '未学习' : '已学非最新' }}
            </a-space>
            <div v-else>
              <div class="status-icon grey"></div>
              <span style="padding-left: 5px">无法学习</span>
            </div>
          </template>
        </a-table>
        <div class="jt-pagination">
          <a-space size="large">
            <span>共{{ total }}条记录</span>
            <span>
              每页显示
              <a-select default-value="10" style="width: 65px" @change="changePageSize">
                <a-select-option value="5"> 5 </a-select-option>
                <a-select-option value="10"> 10 </a-select-option>
                <a-select-option value="15"> 15 </a-select-option>
                <a-select-option value="20"> 20 </a-select-option>
              </a-select>
              条
            </span>
          </a-space>
          <a-pagination :page-size="pageSize" show-quick-jumper :default-current="1" :total="total" @change="changePageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { studentsCountByCourse, getAllCatalogById, getStudentByCatalogId, getStudentsByCourse, countStudentStudyedByCatalogId } from '@/apis/teaching.js';
import _ from 'lodash';
import toChinesNum from '@/lib/toChinesNum';

export default {
  name: 'StudyTrack',
  emits: ['changeParentTb', 'changeTab'],
  data() {
    return {
      searchValue: '',
      loading: false,
      pageSize: 10, // 每页的条数
      pageNum: 1, // 第几页
      total: 0, // 总条数
      courseId: this.$route.params.courseId,
      selectCatalogId: '',
      studentTotal: '',
      catalogList: [],
      tableData: [],
      studyedTotal: 0,
      oldTotal: 0,
      columns: [
        {
          title: '姓名',
          dataIndex: 'studentName',
        },
        {
          title: '学号',
          dataIndex: 'studentNum',
        },
        {
          title: '学习状态',
          dataIndex: 'status',
          slots: {
            filterDropdown: 'filterDropdown',
            filterIcon: 'filterIcon',
            customRender: 'status-slot',
          },
        },
      ],
      filter: false,
      studyStatus: '',
    };
  },
  mounted() {
    this.getStudentNumber();
    this.getAllCatalogById();
  },
  methods: {
    toChinesNum,
    handleSelectClick(setSelectedKeys, selectedKeys, confirmCallBack, index) {
      confirmCallBack();
      this.studyStatus = index;
      this.filter = index == '' ? false : true;
      this.pageNum = 1;
      this.getStudentByCatalogId();
    },
    // 跳转到课程管理
    jumpToCourseManagement() {
      this.$emit('changeParentTb');
    },
    // 跳转到教学内容
    jumpToCourseManage() {
      this.$emit('changeTab', '2');
    },
    changePageSize(size) {
      this.pageSize = Number(size);
      this.pageNum = 1;
      if (this.selectCatalogId) {
        this.getStudentByCatalogId();
      } else {
        this.getStudentsByCourse();
      }
    },
    changePageNum(num) {
      this.pageNum = num;
      if (this.selectCatalogId) {
        this.getStudentByCatalogId();
      } else {
        this.getStudentsByCourse();
      }
    },
    // 关键字搜索更新，根据是否有课节列表判断是按照课程搜索还是按照课节搜索
    handleCourseSearch: _.debounce(function (e) {
      this.searchValue = e.target.value;
      if (this.selectCatalogId) {
        this.getStudentByCatalogId();
      } else {
        this.getStudentsByCourse();
      }
    }, 500),
    // 获取课程对应的学生总数
    async getStudentNumber() {
      const res = await studentsCountByCourse({ courseId: this.courseId });
      if (res.state === 'OK') {
        this.studentTotal = res.body;
      } else {
        this.studentTotal = 0;
      }
    },
    // 根据课节查询所有学生
    async getStudentByCatalogId() {
      // 每次需要更新下已学学生的数量
      this.countStudentStudyedByCatalogId();
      const res = await getStudentByCatalogId({
        catalogId: this.selectCatalogId,
        courseId: this.courseId,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        studentName: this.searchValue,
        studyStatus: this.studyStatus,
      });
      if (res.state === 'OK') {
        this.tableData = res.body.data;
        this.total = res.body.total;
      } else {
        this.tableData = [];
        this.total = 0;
      }
    },
    // 根据课程获取学生
    async getStudentsByCourse() {
      const res = await getStudentsByCourse({
        courseId: this.courseId,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        studentName: this.searchValue,
      });
      if (res.state === 'OK') {
        this.tableData = res.body.data;
        this.total = res.body.total;
      } else {
        this.tableData = [];
        this.total = 0;
      }
    },
    // 根据课程id获取课节
    async getAllCatalogById() {
      const res = await getAllCatalogById({
        courseId: this.courseId,
      });
      if (res.state === 'OK') {
        if (res.body && res.body.length > 0) {
          this.catalogList = res.body;
          this.selectCatalogId = this.catalogList[0].id;
          // 根据第一个id请求学生数据
          this.getStudentByCatalogId();
        } else {
          this.catalogList = [];
          this.selectCatalogId = '';
          this.getStudentsByCourse();
        }
      }
    },

    // 查询课节的已学习人数
    async countStudentStudyedByCatalogId() {
      const res = await countStudentStudyedByCatalogId({
        catalogId: this.selectCatalogId,
      });
      if (res.state === 'OK') {
        this.studyedTotal = res.body[0];
        this.oldTotal = res.body[1];
      } else {
        this.studyedTotal = 0;
        this.oldTotal = 0;
      }
    },
    handleSelectCatalog(id) {
      this.selectCatalogId = id;
      this.pageNum = 1;
      this.studyStatus = '';
      this.filter = false;
      this.getStudentByCatalogId();
    },
  },
};
</script>

<style lang="less" scoped>
.student-total {
  position: absolute;
  top: 0px;
  right: 32px;
  .description {
    color: #606972;
  }
  .number {
    color: #121f2c;
    font-size: 24px;
    padding-left: 16px;
  }
}
.study-track-content {
  display: flex;
  margin-top: 12px;
  border: 1px solid #e8e8e8;
  .course-list {
    width: 360px;
    height: 628px;
    background-color: #f7f9fa;
    color: #121f2c;
    overflow: auto;
    li {
      padding: 12px 16px;
      cursor: pointer;
      &:hover {
        color: #0082ff;
      }
    }
    .selectItem {
      color: #0082ff;
      background-color: white;
      border-left: 4px solid #0082ff;
    }
  }
  .empty-holder {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #606972;
  }
  .student-table {
    // flex-grow: 1;
    height: 500px;
    padding: 20px;
    width: calc(100% - 360px);
    .table-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .status-icon {
      display: inline-block;
      width: 7px;
      height: 7px;
      border-radius: 8px;
    }
    .status-filter-icon {
      right: auto;
    }
    .orange {
      background-color: #faad14;
    }
    .green {
      background-color: #1dca94;
    }
    .blue {
      background-color: #0082ff;
    }
    .grey {
      background-color: #c2c5cf;
    }
  }
}

.empty-tooltip {
  margin: auto;
  .empty {
    width: 416px;
    height: 416px;
    background-image: url('~@/assets/image/emptys2x.png');
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 60px;
    div {
      font-size: 18px;
      color: #121f2c;
      margin-bottom: 8px;
      font-weight: 600;
    }
  }
}

.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px 40px;
}
/* 屏蔽antd头部出滚动条问题 */
:deep(.ant-table-header.ant-table-hide-scrollbar) {
  overflow: hidden !important;
}
</style>

<style lang="less">
.filter-status-select {
  div {
    cursor: pointer;
    width: 120px;
    height: 40px;
    font-size: 12px;
    color: #606972;
    line-height: 40px;
    &:hover {
      background-color: #f0f8ff;
    }
  }
  .all {
    padding-left: 10px;
  }
  .already-study,
  .old-study,
  .un-study {
    position: relative;
    padding-left: 30px;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 8px;
      position: absolute;
      left: 10px;
      top: 18px;
    }
  }
  .already-study::before {
    background-color: #1dca94;
  }
  .old-study::before {
    background-color: #0082ff;
  }
  .un-study::before {
    background-color: #faad14;
  }

  .select {
    background-color: #f0f8ff;
  }
}
</style>
