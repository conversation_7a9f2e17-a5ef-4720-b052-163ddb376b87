<template>
  <div class="my-competition-container">
    <jt-bread-crumb :value="breadCrumbs"></jt-bread-crumb>
    <div class="tabs-container flex">
      <a-tabs v-model:activeKey="formdata.TypeId" class="tabs inner" default-active-key="1" @change="changeTabs">
        <a-tab-pane key="1" :tab="`正式赛 ${formalCount}`"></a-tab-pane>
        <a-tab-pane key="2" :tab="`练习赛 ${practiceCount}`"></a-tab-pane>
      </a-tabs>
      <div class="jt-box-shadow">
        <div class="tabs-button-filter flex inner">
          <jt-button-filter v-model="competitionStatus" class="filter-row" :options="filterList" @change="changeFilterValue"></jt-button-filter>
          <jt-search v-model="formdata.keyword" placeholder="搜索比赛" @handSearch="handSearch"></jt-search>
        </div>
      </div>
    </div>
    <div class="my-competition-main-content flex">
      <div class="my-competition-list content-container flex">
        <jt-common-content :loading="loading" :empty="list.length === 0" :empty-style="emptyStyle" :empty-image="emptyImage" :empty-title="emptyDescription.title" :empty-text="emptyDescription.text">
          <jt-list-item v-for="item in list" :key="item.cid" :img="item.imageUrl">
            <template #center>
              <div class="my-competition-info">
                <p class="my-competition-name" @click="toCompetitionDetile(item)">
                  <span class="competition-name">{{ item.typeName }}</span>
                  <jt-tag :type="getTagClass(item.flag)" style="margin: 0 8px 0 0">
                    {{ runningStatusMaps[item.flag] }}
                  </jt-tag>
                  <jt-tag type="normal">{{ competitionTypeMaps[item.typeId] }}</jt-tag>
                </p>
                <p class="my-competition-detail">{{ item.summary }}</p>
                <div class="my-competition-label-box">
                  <span class="tag-s0 tag-s1">参赛人数：</span>
                  <span class="tag-s0 tag-s2" style="color: #606972">{{ item.number }}</span>
                  <span class="tag-s0 tag-s1">时间 :</span>
                  <span class="label">{{ dateConvert(item.startTime) }} - {{ dateConvert(item.endTime) }}</span>
                  <span class="tag-s0 tag-s1">举办方 : &nbsp;</span>
                  <span v-for="(items, indexs) in item.leader" v-cloak :key="indexs">
                    <img class="leader-img" :src="items.imageUrl" alt="" />
                  </span>
                </div>
              </div>
            </template>
            <template #right>
              <div class="my-competition-info-right">
                <a-button type="link" @click="toCompetitionDetile(item)">
                  前往比赛
                  <jt-icon type="iconright" />
                </a-button>
              </div>
              <div v-if="formdata.TypeId === '1'" class="ranking">排名：{{ item.ranking || '--' }}</div>
            </template>
          </jt-list-item>
        </jt-common-content>
        <jt-pagination v-if="list.length !== 0" class="pagination-box" :page-size="formdata.pageSize" :page-num="formdata.pageNum" :total="total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import API from '@/constants/api/API.js';

import JtBreadCrumb from '../../components/breadCrumb';
import JtSearch from '../../components/search/index.vue';
import JtListItem from '../../components/listItem/index.vue';
import JtTag from '@/components/tag/index.vue';
import { dateConvert } from '@/utils/utils.js';
import { runningStatusMaps, competitionTypeMaps, EMPTY_STATE_TEXT_MAPS, EMPTY_STATE_TEXT_TYPE_MAPS, competitionTypeIdMaps, competitionStatusMaps } from '@/views/competition/competitionConfig/index';

export default {
  components: { JtBreadCrumb, JtSearch, JtListItem, JtTag },
  data() {
    return {
      breadCrumbs: [{ name: '比赛', path: '/competition' }, { name: '我参加的比赛' }],
      filterList: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '2',
          label: '进行中',
        },
        {
          value: '3',
          label: '已结束',
        },
      ],
      formdata: {
        pageNum: 1,
        pageSize: 5,
        TypeId: '1', // 正式赛1 练习赛2
        flag: '', // ''全部 2进行中 3结束
        keyword: '',
      },
      competitionStatus: '', // ''全部 1即将开始 2进行中 3结束
      practiceCount: 0, // 练习赛
      formalCount: 0, // 正式赛
      list: [],
      total: 5,
      loading: false,
      emptyStateTextType: 'NO_OFFICIAL_COMPETITION',
      joinCompetitionListLength: 0,
      competitionTypeMaps,
      runningStatusMaps,
      emptyStyle: {
        'min-height': '416px',
        height: 'calc(100vh - 753px)',
      },
    };
  },
  computed: {
    emptyImage() {
      return this.formdata.keyword ? require('@/assets/image/empty2x.png') : require('@/assets/image/emptys2x.png');
    },
    emptyDescription() {
      const description = {};
      description.title = this.formdata.keyword ? '抱歉，没有找到相关比赛' : `${EMPTY_STATE_TEXT_MAPS[this.emptyStateTextType].title} `;
      description.text = this.formdata.keyword ? '您可以换一个关键词试试哦~' : `${EMPTY_STATE_TEXT_MAPS[this.emptyStateTextType].text} `;
      return description;
    },
    isEmptyCompetitionList() {
      return this.joinCompetitionListLength === 0;
    },
    isOfficialCompetition() {
      const { TypeId } = this.formdata;
      return TypeId === competitionTypeIdMaps.officialCompetition;
    },
    isPracticeCompetition() {
      const { TypeId } = this.formdata;
      return TypeId === competitionTypeIdMaps.practiceCompetition;
    },
    isALLCompetition() {
      return this.competitionStatus === competitionStatusMaps.ALL;
    },
    isRunningCompetition() {
      return this.competitionStatus === competitionStatusMaps.RUNNING;
    },
    isEndedCompetition() {
      return this.competitionStatus === competitionStatusMaps.ENDED;
    },
    joinCompetitionListAll() {
      return (this.isOfficialCompetition && this.isALLCompetition) || (this.isPracticeCompetition && this.isALLCompetition) ? true : false;
    },
  },
  mounted() {
    this.getMyCompetition();
    this.getCompetitionCount();
  },
  methods: {
    handSearch(val) {
      this.formdata.pageNum = 1;
      this.formdata.pageSize = 5;
      this.formdata.keyword = val;
      this.getMyCompetition();
    },
    changeTabs(key) {
      this.formdata.TypeId = key;
      this.formdata.flag = '';
      this.formdata.pageNum = 1;
      this.formdata.pageSize = 5;
      this.getMyCompetition();
      this.competitionStatus = '';
    },
    getMyCompetition() {
      let reqData = this.formdata;
      this.loading = true;
      API.competition_model.getMyCompetitionList(reqData).then((res) => {
        if (res.state === 'OK') {
          this.loading = false;
          this.total = res.body.total;
          res.body.data.forEach((item, index) => {
            res.body.data[index].leader = JSON.parse(item.leader);
          });
          this.list = res.body.data;

          if (this.joinCompetitionListAll) {
            this.joinCompetitionListLength = this.list.length;
          }

          if (this.isEmptyCompetitionList && this.isOfficialCompetition) {
            this.emptyStateTextType = EMPTY_STATE_TEXT_TYPE_MAPS.NO_OFFICIAL_COMPETITION;
          } else if (this.isEmptyCompetitionList && this.isPracticeCompetition) {
            this.emptyStateTextType = EMPTY_STATE_TEXT_TYPE_MAPS.NO_PRACTICE_COMPETITION;
          }

          if (!this.isEmptyCompetitionList && this.isOfficialCompetition && this.isRunningCompetition) {
            this.emptyStateTextType = EMPTY_STATE_TEXT_TYPE_MAPS.NO_RUNNING_OFFICIAL_COMPETITION;
          } else if (!this.isEmptyCompetitionList && this.isOfficialCompetition && this.isEndedCompetition) {
            this.emptyStateTextType = EMPTY_STATE_TEXT_TYPE_MAPS.NO_ENDED_OFFICIAL_COMPETITION;
          }

          if (!this.isEmptyCompetitionList && this.isPracticeCompetition && this.isRunningCompetition) {
            this.emptyStateTextType = EMPTY_STATE_TEXT_TYPE_MAPS.NO_RUNNING_PRACTICE_COMPETITION;
          } else if (!this.isEmptyCompetitionList && this.isPracticeCompetition && this.isEndedCompetition) {
            this.emptyStateTextType = EMPTY_STATE_TEXT_TYPE_MAPS.NO_ENDED_PRACTICE_COMPETITION;
          }
        }
      });
    },
    getCompetitionCount() {
      API.competition_model.getMyCompetitionCount().then((res) => {
        if (res.state === 'OK') {
          this.formalCount = res.body[1];
          this.practiceCount = res.body[2];
        } else {
          this.formalCount = '0';
          this.practiceCount = '0';
        }
      });
    },
    changeFilterValue(value) {
      const filterCompetitionStatusMaps = {
        '': competitionStatusMaps.ALL,
        2: competitionStatusMaps.RUNNING,
        3: competitionStatusMaps.ENDED,
      };
      this.formdata.flag = filterCompetitionStatusMaps[value];
      this.formdata.pageNum = 1;
      this.formdata.pageSize = 5;
      this.competitionStatus = value;
      this.getMyCompetition();
    },
    pageSizeChange(pageSize) {
      this.formdata.pageSize = pageSize;
      this.formdata.pageNum = 1;
      this.getMyCompetition();
    },
    pageNumChange(pageNum) {
      this.formdata.pageNum = pageNum;
      this.getMyCompetition();
    },
    toCompetitionDetile(item) {
      this.$router.push({
        path: '/competition/competition-detail',
        query: {
          id: item.cid, // 当前比赛id
          name: item.typeName, // 产品需求 进入详情不显空白
          all: true, // 是否为我参加的比赛tab进入详情
        },
      });
    },
    dateConvert,
    getTagClass(flag) {
      return flag == 1 ? 'tobegin' : flag == 2 ? 'running' : 'end';
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.flex {
  display: flex;
}

.my-competition-main-content {
  justify-content: center;
  margin-top: 20px;
  padding-bottom: 48px;
  background: @jt-main-bg-color;
  .content-container {
    width: 1200px;
  }
}
.my-competition-list {
  flex-direction: column;
  background: @jt-color-white;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  li {
    align-items: center;
    width: 100%;
    padding: 32px;
    margin-bottom: 1px;
    background: @jt-color-white;
    box-shadow: 0px 1px 0px 0px @jt-line-color;
    position: relative;

    &:hover {
      background: #f8f9fa;
      transition: 0.3s;
    }

    .img-container {
      img {
        width: 128px;
        height: 128px;
      }
    }

    .my-competition-info-right {
      width: 177px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .ranking {
      width: 83px;
      height: 24px;
      line-height: 24x;
      background: #ff7b00;
      padding-left: 16px;
      border-radius: 0px 0px 0px 100px;
      color: @jt-color-white;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .my-competition-info {
    flex: 1;

    .my-competition-name {
      font-size: 20px;
      font-weight: @jt-font-weight-medium;
      color: @jt-title-color;
      line-height: 28px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      margin-right: 9px;
      .competition-name {
        cursor: pointer;
        margin-right: 8px;
        &:hover {
          color: @jt-primary-color;
          transition: 0.3s;
        }
      }
    }

    .my-competition-detail {
      height: 22px;
      font-size: @jt-font-size-base;
      font-weight: @jt-font-weight;
      color: @jt-text-color;
      line-height: 22px;
      margin-bottom: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 786px;
    }

    .my-competition-label-box {
      .label {
        color: @jt-text-color;
        padding-right: 24px;
        font-weight: @jt-font-weight;
      }
    }

    .tag-s0 {
      height: 20px;
      line-height: 20px;
      font-size: @jt-font-size-base;
      font-weight: @jt-font-weight;
    }
    .tag-s1 {
      padding-right: 5px;
      color: @jt-text-color-secondary;
    }
    .tag-s2 {
      color: @jt-text-color;
      padding-right: 15px;
    }

    .leader-img {
      height: 23px;
      margin-right: 16px;
      object-fit: contain;
    }
  }
}
.pagination-box {
  display: flex;
  align-items: center;
  height: 92px;
  padding: 0 32px;
  box-sizing: border-box;
  z-index: 1;
  margin-top: 0;
}
.tabs-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding-top: 16px;
  background: @jt-color-white;

  .tabs-button-filter {
    height: @jt-header-height;
    align-items: center;
    justify-content: space-between;
  }

  .tabs {
    width: 1200px;
  }
  :deep(.ant-tabs-nav-container) {
    font-size: 18px;
  }

  .filter-row {
    display: flex;
    align-items: center;
    &:last-of-type {
      margin-bottom: 0;
    }
    > p {
      width: 76px;
      line-height: 20px;
      margin-right: 13px;
    }
    :deep(.ant-radio-button-wrapper) {
      margin: 24px 0;
    }
  }
}
</style>
