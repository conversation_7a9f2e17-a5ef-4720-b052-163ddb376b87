<template>
  <div class="main-container">
    <bread-crumb class="bread-crumb-container" :value="breadcrumb">
      <div>
        <a-button :disabled="!havePre" size="small" type="primary" ghost class="btn mr16" @click="handleNavigate(-1)"> <jt-icon type="iconleft" /> 上一次 </a-button>
        <a-button :disabled="!haveNext" size="small" type="primary" ghost class="ml16" @click="handleNavigate(1)"> 下一次 <jt-icon type="iconright" /> </a-button>
      </div>
    </bread-crumb>
    <div class="content">
      <a-spin :spinning="loading">
        <div class="item-container">
          <jupyter-viewer v-if="projectInfo.projectId" :project-info="projectInfo" class="large-size" type="homework">
            <template #extraToolbar>
              <a-space>
                <a-space class="end-time" :class="getEndTimeClass">
                  <jt-icon type="icontime-circle" style="font-size: 16px" />
                  <span v-if="outdated">提交已经截止：</span>
                  <span v-else>提交截止时间：</span>
                  <span>{{ detail.endTime }}</span>
                </a-space>
                <!-- <p>
                  <a-tag>已提交</a-tag>
                  <span>2022-03-09 15:55</span>
                </p> -->
                <a-space>
                  <!-- <a-button @click="handleDownloadAttachment">下载附件</a-button> -->
                  <a-button :disabled="true" type="primary" @click="handleCheckScore">提交作业</a-button>
                </a-space>
              </a-space>
            </template>
            <template #popoverContent>
              <p class="popover-container">页面关闭后10分钟作业实例将自动停止；</p>
              <p class="popover-container">最多只能同时运行4个课节或作业实例；</p>
              <p class="popover-container">当启动多于4个实例时，则自动关闭启动时间较久的已运行实例。</p>
            </template>
          </jupyter-viewer>
        </div>
      </a-spin>
    </div>
    <submit-dialog :visible="submitDialogVisible" :assignment-id="assignmentId" :need-attachment="needAttachment" @ok="submitDialogVisible = false" @cancel="submitDialogVisible = false"></submit-dialog>
    <score-detail :level="level" :score="score" :remark="remark" :visible="scoreDetailVisible" @cancel="scoreDetailVisible = false"></score-detail>
  </div>
</template>

<script>
import { checkLogin } from '@/keycloak';
import breadCrumb from '../../components/breadCrumb';
import jupyterViewer from '../../components/jupyterViewer';
import submitDialog from '@/components/homework/submitDialog.vue';
import scoreDetail from '@/components/course-overview/score-detail.vue';
import { GET } from '@/request';
import { PUBLISH_STATUS } from '../../components/course-overview/homework-maps';
import { checkAuth } from '@/utils/utils';

const breadcrumbData = [
  // { name: '教学', path: '/teaching' },
  // { name: '我开设的课程', path: '/course/mycourse' },
];

export default {
  components: {
    breadCrumb,
    jupyterViewer,
    submitDialog,
    scoreDetail,
  },
  data() {
    return {
      courseId: null,
      breadcrumb: [],
      loading: false,
      detail: {},
      loaded: false,
      homeworkList: [],
      assignmentId: null,
      assignmentName: '',
      submitDialogVisible: false,
      scoreDetailVisible: false,
      score: 90,
      level: 'good',
      remark: '做的不错',
    };
  },
  computed: {
    haveNext() {
      return this.currentIndex < this.homeworkList.length - 1;
    },
    havePre() {
      return this.currentIndex > 0;
    },
    currentIndex() {
      return this.homeworkList.findIndex((item) => +item.id === +this.assignmentId);
    },
    needAttachment() {
      return false;
    },
    projectInfo() {
      return {
        projectId: this.detail.projectId,
        spec: this.detail.spec,
        instanceModel: this.detail.instanceModel,
        instanceName: this.detail.projectName,
      };
    },
    outdated() {
      return PUBLISH_STATUS[this.detail.status] === '已截止';
    },
    getEndTimeClass() {
      return PUBLISH_STATUS[this.detail.status] === '已截止' ? 'end-time-outdated' : '';
    },
  },
  watch: {
    // 如果路由有变化，会再次执行该方法
    $route: 'init',
  },
  created() {
    if (!checkLogin()) {
      return;
    }
    this.init();
  },
  methods: {
    async init() {
      this.courseName = this.$route.query.courseName;
      this.courseId = this.$route.params.courseId;
      this.assignmentId = this.$route.params.assignmentId;
      this.assignmentName = this.$route.query.assignmentName;
      this.breadcrumb = [...breadcrumbData];
      this.breadcrumb.push({
        name: '课程主页',
        path: `/course/teaching/course-preview/${this.courseId}`,
      });
      this.loading = true;
      await this.getHomeworkList();
      await this.getHomeworkDetail();
      this.breadcrumb.push({
        name: `${this.courseName}：${this.detail.name}`,
      });
      this.loading = false;
    },

    async getHomeworkDetail() {
      const res = await GET(
        '/course_model/web/course_student/student/teacherAssignmentCase',
        {
          courseId: this.courseId,
          assignmentId: this.assignmentId,
        },
        { useError: false }
      );
      if (res.state === 'OK') {
        this.detail = res.body;
      } else {
        if (!checkAuth(res.errorCode, '-802', '/course/teaching')) {
          return;
        }
      }
    },
    handleNavigate(offset) {
      if (this.loading) return; //避免连续点击出现的bug
      const target = this.homeworkList[this.currentIndex + offset];
      this.$router.push(`/course/teaching/homework-detail-preview/${this.courseId}/${target.id}?courseId=${this.courseId}&courseName=${this.courseName}`);
      this.detail = {};
    },
    async getHomeworkList() {
      const res = await GET('/course_model/web/course_student/course/teacherCourseAssignment', { courseId: this.courseId });
      this.homeworkList = res.body.filter((item) => PUBLISH_STATUS[item.status] !== '未发布');
    },
    handleDownloadAttachment() {
      console.log('handleDownloadAttachment');
    },
    handleCheckScore() {
      this.scoreDetailVisible = true;
    },
    handleSubmit() {
      this.submitDialogVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
@import './homework-detail.less';
</style>
