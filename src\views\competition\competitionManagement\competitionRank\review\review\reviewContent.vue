<template>
  <section>
    <headTitle title="审查及答辩材料提交设置" />
    <div class="descriptions">
      <div class="descriptions-item">
        <div class="title">提交开放时间：</div>
        <div v-if="details.replyStartTime || details.replyEndTime" class="content">{{ formatDate(details.replyStartTime) }} - {{ formatDate(details.replyEndTime) }}</div>
        <div v-else class="content">- -</div>
      </div>
      <div class="descriptions-item">
        <div class="title">提交方式：</div>
        <div v-if="details.replySubmitType" class="content" style="word-break: break-word">{{ getSubmitType(+details.replySubmitType) }}</div>
        <div v-else class="content">- -</div>
      </div>
      <div class="descriptions-item html-viewer">
        <div class="title">提交要求：</div>
        <div v-if="details.replyNarrate" class="content">
          <htmlViewerVue class="markdown-body" :text-url="!previewDetails ? details.replyNarrate : ''" :value="previewDetails ? details.replyNarrate : ''" @onError="htmlViewerError" />
        </div>
        <div v-else class="content">- -</div>
      </div>
      <div class="descriptions-item table-title">
        <div class="title">提交开放范围：</div>
        <div v-if="details.replyCreateSta !== undefined && details.replyCreateSta !== null && details.replyCreateSta !== ''" class="content">
          <p v-if="previewDetails">{{ details.replyCreateSta ? '所有团队' : '指定团队' }} {{ details.teamIds.length }}</p>
          <p v-else>{{ details.replyCreateSta ? '所有团队' : '指定团队' }} {{ details.teamNum }}</p>
          <search placeholder="团队名称/队长用户名/队长姓名" class="search-input" @handSearch="(val) => $emit('getSearchValue', val)" />
        </div>
        <div v-else class="content">- -</div>
      </div>
    </div>
  </section>
</template>

<script>
import headTitle from '@/components/headTitle';
import search from '@/components/search';
import API from '@/constants/api/API';
import { getSubmitType } from '@/views/competition/competitionManagement/competitionRank/constants';
import { formatDate } from '@/views/competition/competitionManagement/competitionRank/utils';
import htmlViewerVue from '@/views/competition/competitionManagement/components/htmlViewer.vue';

export default {
  components: {
    headTitle,
    search,
    htmlViewerVue,
  },
  // previewDetails 预览时的内容
  props: {
    previewDetails: { type: Object, default: () => ({}) },
  },
  emits: ['updateCleared', 'getSearchValue'],
  data() {
    return {
      searchValue: '',
      details: {},
    };
  },
  async created() {
    if (this.previewDetails) {
      this.details = { ...this.previewDetails };
    } else {
      this.getData();
    }
  },
  methods: {
    formatDate,
    getSubmitType,
    htmlViewerError() {
      this.replyNarrate = '';
    },
    async getData() {
      const fileReply = await API.competition_model.getFileReply({ cid: this.$route.params.competitionId });
      if (fileReply.state === 'OK') {
        this.details = fileReply.body;
        this.checkField();
      }
      const fileReplyScope = await API.competition_model.getFileReplyScope({ cid: this.$route.params.competitionId });
      if (fileReplyScope.state === 'OK') {
        this.details.replyCreateSta = fileReplyScope.body.replyCreateSta;
        this.details.teamNum = fileReplyScope.body.teamNum;
      }
    },
    checkField() {
      // 选取一个必填项的字段来判断是否清空过
      const cleared = !this.details.replyNarrate;
      return this.$emit('updateCleared', cleared);
    },
  },
};
</script>

<style lang="less" scoped>
section {
  margin-top: 32px;
}
.descriptions {
  margin-top: 24px;
  display: grid;
  justify-content: space-between;
  font-size: 14px;
  line-height: 20px;
  grid-template-columns: repeat(2, 50%);
  color: #121f2c;
  .descriptions-item {
    display: flex;
    margin-bottom: 24px;
    &.html-viewer {
      grid-column-end: span 2;
      .content {
        width: 100%;
      }
    }
    &.table-title {
      flex-shrink: 0;
      grid-column-end: span 2;
      align-items: center;
      margin-bottom: 20px;
      .content {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
      }
    }
    .title {
      text-align: right;
      margin-right: 16px;
      min-width: 130px;
    }
    &:nth-of-type(2n + 1),
    &.table-title {
      .title {
        min-width: 98px;
      }
    }
    .content-tag {
      display: block;
      text-align: center;
      width: 56px;
      height: 20px;
      border-radius: 2px;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      border: 1px solid;
      border-color: #abb9ca;
      background-color: #edf3fd;
      color: #698097;
      &.active {
        color: #00b155;
        border-color: #72d69e;
        background-color: #eafdeb;
      }
    }
  }
}
.tab-extra-container {
  .search-input {
    width: 240px;
    margin-right: 9px;
  }
  :deep(.ant-input-affix-wrapper .ant-input:not(:last-child)) {
    padding-right: 10px;
  }
}
</style>
