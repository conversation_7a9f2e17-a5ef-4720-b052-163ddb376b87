<template>
  <div class="course-overview-container">
    <bread-crumb :value="breadcrumbs"></bread-crumb>
    <div class="content">
      <div class="side">
        <div class="thumb">
          <img :src="courseInfo.imageUrl || emptyImg" alt="" />
          <p class="title">{{ courseInfo.name }}</p>
          <p class="institute">{{ courseInfo.instituteName }}</p>
          <p class="time">{{ `${courseInfo.startTime}-${courseInfo.endTime}` }}</p>
        </div>
        <a-menu class="navigator" :selected-keys="[currentKey]" mode="vertical" @click="handleNavigateTo">
          <a-menu-item key="课节">
            <jt-icon type="iconkejie" class="tab-icon" />
            课节
          </a-menu-item>
          <a-menu-item key="作业" :disabled="homeworkDisabled">
            <jt-icon type="iconzuoye" class="tab-icon" />
            作业
          </a-menu-item>
          <a-menu-item key="课程介绍">
            <jt-icon type="iconkecheng" class="tab-icon" />
            课程介绍
          </a-menu-item>
          <a-menu-item key="课程资源">
            <jt-icon type="iconkechengziyuan" class="tab-icon" />
            课程资源
          </a-menu-item>
          <a-menu-item key="教师团队">
            <jt-icon type="iconjiaoshituandui" class="tab-icon" />
            教师团队
          </a-menu-item>
        </a-menu>
      </div>
      <course-contents-preview v-if="currentKey === '课节'" :course-flag="courseInfo.courseFlag" :course-name="courseInfo.name" :course-id="courseId" :finished="finished" :course-contents="courseContents" :catalog-num="courseInfo.catalogNum" :map-flag="courseInfo.mapFlag" @reload="getCourseInfo"></course-contents-preview>
      <course-homework-preview v-if="currentKey === '作业'" :course-id="courseId" :course-name="courseInfo.name"></course-homework-preview>
      <course-introduction v-if="currentKey === '课程介绍'" :course-desc="courseInfo.courseDesc" :course-front-knowledge="courseInfo.courseFrontKnowledge" :course-goal="courseInfo.courseGoal"></course-introduction>
      <course-resource v-if="currentKey === '课程资源'" :preview="true" :course-id="courseId"></course-resource>
      <course-teacher v-if="currentKey === '教师团队'" :course-id="courseId"></course-teacher>
    </div>
  </div>
</template>

<script>
// import { defineComponent } from '@vue/composition-api';
import breadCrumb from '../../components/breadCrumb';
import courseContentsPreview from '../../components/course-overview/course-contents-preview.vue';
import courseIntroduction from '../../components/course-overview/course-introduction.vue';
import courseResource from '../../components/course-overview/course-resource.vue';
import courseTeacher from '../../components/course-overview/course-teacher.vue';
import { checkAuth } from '@/utils/utils';
import { checkLogin } from '@/keycloak';
import courseHomeworkPreview from '../../components/course-overview/course-homework-preview.vue';
import { FINISHED, COURSE_FLAG } from './course-overview';

export default {
  components: { breadCrumb, courseContentsPreview, courseIntroduction, courseResource, courseTeacher, courseHomeworkPreview },
  data() {
    return {
      courseId: '',
      courseName: '',
      breadcrumbs: [{ name: '课程主页' }],
      courseInfo: {
        instituteDto: {},
      },
      currentKey: '课节',
      courseContents: [],
      emptyImg: require('@/assets/image/empty2x.png'),
      confirmVisible: false,
    };
  },
  computed: {
    finished() {
      return FINISHED[this.courseInfo.courseStudentStudyFlag] === '已完成';
    },
    homeworkDisabled() {
      return COURSE_FLAG[this.courseInfo.courseFlag] === '公开课';
    },
  },
  mounted() {
    if (!checkLogin(true)) {
      return;
    }
    this.courseId = this.$route.params.courseId;
    this.getCourseInfo();
    this.initTab();
  },
  methods: {
    getCourseInfo() {
      const obj = {
        courseId: this.courseId,
      };
      return this.$GET('/course_model/web/course_student/course/courseTeachingFindByCourseId', obj, { useError: false }).then((res) => {
        this.loading = false;
        if (!checkAuth(res.errorCode, '-801', '/course')) {
          return;
        }
        if (!res.errorCode) {
          this.courseInfo = res.body;
        }
      });
    },
    handleNavigateTo(val) {
      if (this.currentKey === val.key) {
        return;
      }
      this.currentKey = val.key;
      this.$router.replace({ query: { ...this.$route.query, currentKey: val.key } });
    },
    initTab() {
      this.currentKey = this.$route.query.currentKey || '课节';
    },
  },
};
</script>

<style lang="less" scoped>
@import './course-overview.less';
</style>
