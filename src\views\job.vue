<script setup>
import { CaretDownOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="home" style="width: 100%; padding-bottom: 20px">
    <div class="content-box">
      <div style="width: 100%; background: #fff" class="jt-box-shadow">
        <div class="headerWrapper">
          <div class="box-left">求职题库</div>
          <div class="right">
            <span v-if="$store.state.course.examUrl" style="background: #ff9d00; color: #fff; border: none" @click="navigateToExam"> 移动在线考试 </span>
            <span class="primary" @click="goApplication"> 移动招聘职位 </span>
          </div>
        </div>
        <div class="tabs">
          <ul class="tab-tilte">
            <li v-for="(item, index) in tabTitle" :id="cur == index ? 'liWidth' : ''" :key="index" :class="{ active: cur == index }" class="tab-item" @click="getTopic(index, item.value)">
              {{ item.label }}
            </li>

            <div class="risk-active-line risk-active-line-l" :style="`width:${width}px;transform:translateX(${offsetW}px);`"></div>
          </ul>
        </div>
      </div>

      <div class="job-question-bank">
        <div v-if="$store.state.course.examUrl" class="alert-bar">
          <jt-icon type="iconxiaoxiguanli" style="color: #ff9d00; font-size: 16px; margin-right: 8px" />
          <span style="color: #333333">移动研究院2024年度校招开始啦！</span>
          <a-button type="link" style="padding: 0; font-size: 12px" @click="navigateToExam">
            立即进入在线考试
            <jt-icon type="iconjiantouyou-copy" style="margin: 0" />
          </a-button>
        </div>
        <!-- 最小高度416px，height为100vh减去头部、底部及导航部分 -->
        <jt-common-content :loading="jobListLoading" :empty="jobList.length === 0" :empty-style="{ 'min-height': '416px', height: 'calc(100vh - 674px)' }">
          <ul class="question">
            <li v-for="item in jobList" :key="item.id" @click="!logined ? gotoLogin() : item.hasInstance ? gotoInstanceListPage(item) : gotoCreatePage(item)">
              <div class="questionleft">
                <h3>
                  {{ item.jname }}
                  <template v-for="(items, index) in item.topictype" :key="index">
                    <span v-show="Object.prototype.toString.call(item.topictype) === '[object Array]'" type="primary" class="tag">{{ items }}</span>
                  </template>
                </h3>
                <p style="color: #606972">{{ item.describe }}</p>
                <div style="color: #a0a6ab">
                  <span> {{ item.pageview }}人答题</span>
                  <span style="margin-left: 14px">{{ item.issuetime }}</span>
                  <span style="margin-left: 24px">{{ item.topicsize }}道题</span>
                </div>
              </div>
              <div class="questionright">
                <div v-if="!logined">
                  <span @click="gotoLogin">立即答题<span class="iconfont iconjiantouyou" style="margin-left: 12px"></span></span>
                </div>
                <div v-else>
                  <a-button v-if="item.hasInstance" type="link" :loading="item.loading" @click.stop="gotoInstanceListPage(item)">继续答题<span class="iconfont iconjiantouyou" style="margin-left: 12px"></span></a-button>
                  <a-button v-else type="link" :loading="item.loading" @click.stop="gotoCreatePage(item)">立即答题<span class="iconfont iconjiantouyou" style="margin-left: 12px"></span></a-button>
                </div>
              </div>
            </li>
          </ul>
        </jt-common-content>
        <!-- 分页 -->
        <jt-row v-if="totalCount != 0 && !jobListLoading" class="pagination-box">
          <jt-col :span="6">
            共 {{ totalCount }} 条
            <span class="pageOption" style="margin-left: 15px">每页显示</span>
            <jt-select v-model="formData.size" :default-value="formData.size" style="min-width: 50px; margin: 0 5px" @change="pageSizeChange">
              <template #suffixIcon>
                <CaretDownOutlined :style="{ color: '#606266', marginRight: '-7px' }" />
              </template>
              <jt-select-option :value="5">5</jt-select-option>
              <jt-select-option :value="10">10</jt-select-option>
              <jt-select-option :value="20">20</jt-select-option>
            </jt-select>
            <span>条</span>
          </jt-col>
          <jt-col :span="18">
            <jt-pagination v-model:page-size="formData.size" v-model="formData.page" show-quick-jumper :default-current="2" :total="totalCount" style="text-align: right" @change="getJob" />
          </jt-col>
        </jt-row>
      </div>
    </div>
  </div>
</template>
<script>
import { Row as JtRow, Col as JtCol, Select as JtSelect, Pagination as JtPagination } from 'ant-design-vue';
import API from '@/constants/api/API.js';
import { getLocalConfig } from '@/config';
import { checkLogin, login } from '@/keycloak';
import { getSessionKey } from '@/components/systemNotice';
import { gotoConslePlatform, openInNewTab } from '@/utils/utils';
const YearOffset = 0;

export default {
  components: {
    JtRow,
    JtCol,
    JtSelect,
    JtSelectOption: JtSelect.Option,
    JtPagination,
  },
  data() {
    return {
      tabTitle: [
        {
          value: '',
          label: '全部',
        },
      ],
      timer: '',
      jobList: [],
      topicTypelist: [],
      cur: 0, //默认选中第一个tabTitle
      width: '',
      offsetW: '11',
      typeACount: 0,
      typeACurrentPage: 1,
      typeAPageSize: 10,
      formData: {
        page: 1,
        size: 10,
      },
      projectId: '',
      totalCount: 0,
      creatlist: {},
      hasInstance: false, // 实例是否存在
      jobListLoading: false,
    };
  },
  computed: {
    CONSOLE_URL() {
      return getLocalConfig('CONSOLE_URL');
    },
    logined() {
      return checkLogin();
    },
  },
  created() {
    this.getTime();
    this.getJob();
  },
  methods: {
    getTime() {
      let year = new Date().getFullYear();
      let yearVal = new Date().getFullYear();
      for (let index = 0; index < YearOffset; index++) {
        this.tabTitle.push({
          value: --year + '',
          label: --yearVal + '',
        });
      }
      yearVal = yearVal - 1;
      // 不需要了
      // this.tabTitle.push({
      //   value: '-' + yearVal,
      //   label: '其他',
      // });
    },
    // getJobList
    getTopic(key, timer) {
      this.jobList = [];
      this.timer = timer;
      this.cur = key;
      this.curs = 0;
      this.$nextTick(() => {
        this.width = 60;
        this.offsetW = document.getElementById('liWidth').offsetLeft + 12;
      });
      if (timer) {
        this.formData.nowTime = timer;
      } else {
        delete this.formData.nowTime;
      }
      this.formData.page = 1;
      this.getJob();
    },
    // 获取答题列表数据
    getJob() {
      this.jobListLoading = true;
      API.dasteset_model.getJobList(this.formData).then((res) => {
        this.jobListLoading = false;
        const jobPromiseList = [];
        res.body.list.map((x) => {
          x.dataUserNumber = 0;
          x.hasInstance = false;
          x.loading = true;
          if (this.$keycloak && this.$keycloak.authenticated) {
            jobPromiseList.push(API.dp_platform.getJobInstance_repeat({ projectId: x.projectId }));
          }
        });
        this.jobList = res.body.list;
        this.creatlist = res.body.list[0];
        this.totalCount = res.body.totalCount;
        if (this.$keycloak && this.$keycloak.authenticated) {
          Promise.all(jobPromiseList)
            .then((_res) => {
              res.body.list.map((x, index) => {
                x.loading = false;
                x.hasInstance = _res[index].code === 200;
              });
            })
            .finally(() => {
              this.jobList = res.body.list;
              res.body.list.forEach((element) => {
                let arr = [];
                arr = Object.assign(element.topictype.split('、'));
                element.topictype = arr;
              });
              this.creatlist = res.body.list[0];
              this.totalCount = res.body.totalCount;
            });
        }
      });
    },

    // 分页
    onTypeAShowSizeChange(showSizeSelected) {
      this.typeAPageSize = showSizeSelected;
    },
    // 跳至xx页
    onTypeAPageChange(pageIndex) {
      this.typeACurrentPage = pageIndex;
      this.getTableInfo();
    },
    // 重置分页和搜索数据参数
    resetPage() {
      this.typeACount = 0;
      this.typeACurrentPage = 1;
      this.searchText = '';
      this.searchInputShow = false;
    },
    pageSizeChange() {
      var totalPage = Math.floor((this.totalCount + this.formData.size - 1) / this.formData.size);
      if (this.formData.page > totalPage) {
        this.formData.page = totalPage;
      }
      this.getJob();
    },
    /**
     * 跳转登录页
     */
    gotoLogin() {
      login();
    },
    /**
     * 跳转 实例列表页面显示当前单条实例
     */
    gotoInstanceListPage(item) {
      this.getUserNum(item, 'gotoInstanceListPage');
    },
    gotoCreatePage(item) {
      this.getUserNum(item, 'gotoCreatePage');
    },
    // 跳转移动招聘网页
    goApplication() {
      openInNewTab('http://www.hotjob.cn/wt/CMRI/web/index');
    },
    navigateToExam() {
      getSessionKey().then((res) => {
        openInNewTab(`${location.origin}/${location.pathname}#/job/online-exam?hideSystemPopup=${encodeURIComponent(!!window.localStorage.getItem(res))}`);
      });
    },
    // 求职使用人数统计 getInsertNum
    async getUserNum(item, type) {
      if (item.loading) {
        return;
      }
      await API.dasteset_model.getInsertNum({ projectId: item.projectId });
      if (type === 'gotoCreatePage') {
        gotoConslePlatform(`${this.CONSOLE_URL}/home/<USER>/instance-form?projectId=${item.projectId}&projectName=${item.jname}`);
      } else {
        gotoConslePlatform(`${this.CONSOLE_URL}/home/<USER>/instance-info/${item.projectId}`);
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.home {
  overflow: hidden;
  background-color: #f8f9fa;
}
.headerWrapper {
  width: 1200px;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 32px;
  box-sizing: border-box;
  background-color: #fff;
  .right {
    display: flex;
    justify-content: space-between;
    span {
      width: 128px;
      height: 40px;
      background: #ffffff;
      color: #0082ff;
      border: 1px solid #0082ff;
      text-align: center;
      line-height: 38px;
      cursor: pointer;
      &.primary {
        margin-left: 8px;
        background: #0082ff;
        color: #fff;
      }
    }
  }
}
.box-left {
  font-size: 32px;
  font-weight: @jt-font-weight-medium;
  padding-left: 12px;
}

.tabs {
  width: 1200px;
  margin: auto;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  padding-top: 30px;
  overflow: hidden;
  .tab-tilte {
    position: relative;
    .tab-item {
      width: 84px;
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      line-height: 25px;
      padding-bottom: 16px;
      transition: color 1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
    .filter-item {
      margin-right: 12px;
      display: block;
      padding: 2px 11px;
      line-height: 20px;
      height: auto;
      font-size: 14px;
      &.ant-btn-default {
        border-color: transparent;
        box-shadow: none;
      }
    }
    :deep(.ant-btn-background-ghost.ant-btn-primary) {
      margin-left: 8px;
    }
    // 下划线
    .risk-active-line {
      width: 60px;
      height: 2px;
      background: #0082ff;
      position: absolute;
      top: 39px;
    }
    .risk-active-line-l {
      transition-duration: 0.5s;
    }
  }
}
.tab-tilte > li {
  float: left;
  text-align: center;
  cursor: pointer;
  background: #ffffff;
}
.tab-tilte .active {
  color: #0082ff;
}
.job-question-bank {
  width: 1200px;
  margin: 20px auto 0px;
  background: #fff;
}
.question {
  padding: 0px;
  box-sizing: border-box;
}
.question li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
  height: 160px;
  border-bottom: 1px solid #e0e1e1;
  background-color: #fff;
  box-sizing: border-box;
  cursor: pointer;
}
.question li:hover {
  background: #f8f9fa;
}
.question li h3 {
  font-size: 20px;
}
.question li:hover h3 {
  color: #0082ff;
}
.question .questionleft {
  p {
    margin: 16px 0px;
  }
  h3 {
    display: flex;
    align-items: center;
  }
}

.question .questionright {
  color: #0082ff;
  cursor: pointer;
  font-weight: @jt-font-weight-medium;
  line-height: 26px;
  .iconfont {
    font-size: 16px;
    vertical-align: middle;
  }
}
.pagination-box {
  display: flex;
  align-items: center;
  height: 92px;
  background-color: #fff;
  /* margin: 0 120px; */
  margin: 0 10px;
  padding: 0 32px;
  box-sizing: border-box;
}
.alert-bar {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 40px;
  background: #fef6e7;
  border-radius: 2px;
  border: 1px solid #ffd666;
  font-size: 12px;
}
.tag {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 24px;
  border-radius: 2px;
  border: 1px solid #0082ff;
  font-size: 12px;
  font-weight: 400;
  color: #0082ff;
  margin-left: 8px;
}
</style>
