<template>
  <section :style="{ paddingBottom: previewDetails ? '12px' : '40px', borderBottom: previewDetails ? 'none' : 'border-bottom: 1px solid #efefef;' }">
    <headTitle title="结果文件提交设置" />
    <div class="descriptions">
      <div class="descriptions-item">
        <div class="title">提交开放时间：</div>
        <div class="content" v-if="details.resultStartTime || details.resultEndTime">{{ formatDate(details.resultStartTime) }} - {{ formatDate(details.resultEndTime) }}</div>
        <div class="content" v-else>- -</div>
      </div>
      <div class="descriptions-item">
        <div class="title">提交方式：</div>
        <div class="content" style="word-break: break-word" v-if="details.submitType">{{ getSubmitType(details.submitType) }}</div>
        <div class="content" v-else>- -</div>
      </div>
      <div class="descriptions-item">
        <div class="title">自动评分：</div>
        <div class="content" v-if="details.markType !== undefined && details.markType !== null && details.markType !== ''">
          <span class="content-tag" :class="{ active: details.markType }"> {{ details.markType ? '开启' : '关闭' }} </span>
        </div>
        <div class="content" v-else>- -</div>
      </div>
      <div class="descriptions-item">
        <div class="title">每日提交次数上限：</div>
        <div class="content" v-if="details.teamSubmitMax !== undefined && details.teamSubmitMax !== null && details.teamSubmitMax !== ''">{{ details.teamSubmitMax }}</div>
        <div class="content" v-else>- -</div>
      </div>
      <div class="descriptions-item" v-if="details.markType">
        <div class="title">指标名称：</div>
        <div class="content" style="word-break: break-word" v-if="details.scoreName !== undefined && details.scoreName !== null && details.scoreName !== ''">
          {{ details.scoreName ? details.scoreName.join('；') : '-' }}
        </div>
        <div class="content" v-else>- -</div>
      </div>
      <div class="descriptions-item" v-if="details.markType">
        <div class="title">排序方式：</div>
        <div class="content" v-if="details.scoreFlag !== undefined && details.scoreFlag !== null && details.scoreFlag !== ''">
          {{ details.scoreFlag ? '指标越高越靠前' : '指标越低越靠前' }}
        </div>
        <div class="content" v-else>- -</div>
      </div>
      <div class="descriptions-item html-viewer">
        <div class="title">提交要求：</div>
        <div class="content" v-if="details.resultNarrate !== undefined && details.resultNarrate !== null && details.resultNarrate !== ''">
          <htmlViewerVue class="markdown-body" :textUrl="!previewDetails ? details.resultNarrate : ''" :value="previewDetails ? details.resultNarrate : ''" />
        </div>
        <div class="content" v-else>- -</div>
      </div>
    </div>
  </section>
</template>

<script>
import headTitle from '@/components/headTitle';
import { getSubmitType } from '@/views/competition/competitionManagement/competitionRank/constants';
import { formatDate } from '@/views/competition/competitionManagement/competitionRank/utils';
import htmlViewerVue from '@/views/competition/competitionManagement/components/htmlViewer.vue';
import API from '@/constants/api/API';

export default {
  props: ['previewDetails'],
  components: {
    headTitle,
    htmlViewerVue,
  },
  data() {
    return {
      details: {},
    };
  },
  watch: {
    '$store.state.competition.resultContentData': function () {
      this.details = this.$store.state.competition.resultContentData;
    },
  },
  created() {
    if (this.previewDetails) {
      this.details = { ...this.previewDetails };
    } else {
      this.details = this.$store.state.competition.resultContentData;
    }
  },
  methods: {
    formatDate,
    getSubmitType,
    getData() {
      API.competition_model.fileSubmitGet({ cid: this.$route.params.competitionId }).then((res) => {
        if (res.state === 'OK') {
          this.$store.state.competition.resultContentData = res.body;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import url('./resultContent.less');
</style>
