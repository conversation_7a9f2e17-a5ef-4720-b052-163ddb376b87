<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <confirm-modal v-model="inviteVisibleCopy" :cancel-text="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].cancelText" :ok-text="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].okText" :title="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].title" :show-cancel="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].cancelText !== ''" @ok="handleOk" @cancel="inviteCancel">
    <template #icon>
      <ExclamationCircleFilled
        class="invited-icon"
        :style="{
          color: MYTEAM_JOIN_TEAM_MODAL_TEXT[current].title !== '团队邀请' ? '#ff454d' : '#0082ff',
        }"
      />
    </template>
    <div class="dlg-body">
      <p class="invite-content">{{ joinTeamModalContent || MYTEAM_JOIN_TEAM_MODAL_TEXT[current].content }}</p>
    </div>
  </confirm-modal>
</template>

<script>
import confirmModal from '@/components/confirmModal/index.vue';
import { MYTEAM_JOIN_TEAM_MODAL_TEXT } from '../competitionConfig/index';
export default {
  components: { confirmModal },
  props: {
    current: [Number, String],
    inviteVisible: Boolean,
    joinTeamModalContent: {
      type: String,
      default: '',
    },
  },
  emits: ['inviteOk', 'inviteCancel'],
  data() {
    return {
      MYTEAM_JOIN_TEAM_MODAL_TEXT,
      inviteVisibleCopy: !!this.inviteVisible,
    };
  },
  watch: {
    inviteVisible(val) {
      this.inviteVisibleCopy = !!val;
    },
  },
  methods: {
    handleOk() {
      this.$emit('inviteOk', this.MYTEAM_JOIN_TEAM_MODAL_TEXT[this.current].okText);
    },
    inviteCancel() {
      this.$emit('inviteCancel');
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.invited-icon {
  font-size: @jt-font-size-lger;
}
.invite-content {
  margin-bottom: 24px;
  font-size: @jt-font-size-base;
}
</style>
