<template>
  <div class="contents">
    <div class="contents-header">
      <div>
        <span class="title">作业</span>
        <span class="progress">
          {{ `共${data.length}次` }}
        </span>
      </div>
    </div>
    <jt-common-content :empty="data.length === 0" :loading="loading">
      <ul class="contents-list">
        <li v-for="(item, i) in data" :key="item.id">
          <div class="left" :title="item.name">
            <span class="catlog-name">{{ `第${toChinesNum(i + 1)}次：${item.name}` }}</span>
            <a-tag class="filled-tag" :class="getHomeworkTypeClass(item)">{{ getHomeworkType(item) }}</a-tag>
          </div>
          <div class="right">
            <a-space class="end-time" :class="getEndTimeClass(item)">
              <jt-icon type="icontime-circle" style="font-size: 16px" />
              <span>
                {{ getOutDatedStatus(item) ? '提交已经截止' : '提交截止时间' }}
              </span>
              {{ item.endTime }}
            </a-space>
            <div class="flex-align-center">
              <a-tooltip v-if="getPublishedStatus(item)">
                <template #title>
                  {{ getTooltip(item) }}
                </template>
                <a-button @click="handleCheck(item)" :disabled="getPublishedStatus(item)" type="link">查看作业</a-button>
              </a-tooltip>
              <a-button v-else @click="handleCheck(item)" type="link">查看作业</a-button>
            </div>
          </div>
        </li>
      </ul>
    </jt-common-content>
  </div>
</template>

<script>
import toChinesNum from '@/lib/toChinesNum';
import { GET } from '@/request';
import { TYPE, PUBLISH_STATUS, OUTDATED_STATUS } from './homework-maps';
import { checkAuth } from '@/utils/utils';

export default {
  name: 'homework',
  components: {},
  data() {
    return {
      loading: false,
      data: [],
      currentScore: 0,
      currentRemark: '',
      currentLevel: '',
      currentItem: {},
    };
  },
  props: {
    courseId: {
      type: [Number, String],
      default: 0,
    },
    courseName: {
      type: [String],
      required: true,
    },
  },
  computed: {},
  methods: {
    toChinesNum,
    getHomework() {
      this.loading = true;

      GET('/course_model/web/course_student/course/teacherCourseAssignment', { courseId: this.courseId }, { useError: false })
        .then((res) => {
          if (res.state === 'OK') {
            this.data = res.body;
          } else {
            if (!checkAuth(res.errorCode, '-802', '/course')) {
              return;
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getPublishedStatus({ status }) {
      return PUBLISH_STATUS[status] === '发布中' || PUBLISH_STATUS[status] === '未发布';
    },
    getHomeworkTypeClass({ assignmentType }) {
      return TYPE[assignmentType] === '项目' ? 'item' : 'test';
    },
    getHomeworkType({ assignmentType }) {
      return TYPE[assignmentType];
    },
    handleCheck(item) {
      this.currentItem = item;
      this.navigateToDetail();
    },
    getOutDatedStatus({ abort }) {
      return OUTDATED_STATUS[abort] === '已截止';
    },
    getEndTimeClass({ abort }) {
      return OUTDATED_STATUS[abort] === '已截止' ? 'end-time-outdated' : '';
    },
    getTooltip({ status }) {
      if (PUBLISH_STATUS[status] === '发布中') {
        return '作业发布中，请稍后查看';
      } else if (PUBLISH_STATUS[status] === '已发布') {
        return '改作业尚未发布，请发布后查看';
      } else if (PUBLISH_STATUS[status] === '未发布') {
        return '该作业尚未发布，请发布后查看';
      }
      return '';
    },
    navigateToDetail() {
      this.$router.push({
        path: `/course/teaching/homework-detail-preview/${this.courseId}/${this.currentItem.id}/`,
        query: {
          courseName: this.courseName,
        },
      });
    },
  },
  mounted() {
    this.getHomework();
  },
};
</script>

<style lang="less" scoped>
@import './course-homework.less';
</style>
