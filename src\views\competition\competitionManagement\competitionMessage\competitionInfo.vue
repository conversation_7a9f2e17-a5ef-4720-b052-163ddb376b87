<template>
  <div class="competition-info">
    <a-row v-for="itemKey in Object.keys(competitionInfoMaps)" :key="itemKey" :gutter="[24, 24]">
      <a-col :span="3" style="text-align: right"> {{ competitionInfoMaps[itemKey] }}： </a-col>
      <a-col :span="20" v-if="itemKey === 'time'"> {{ handleTime(currentManageCompetition.StartTime) }} - {{ handleTime(currentManageCompetition.EndTime) }} </a-col>
      <a-col :span="20" v-else-if="itemKey === 'competitionTraining'">{{ competitionTrainningMaps[currentManageCompetition.competitionTraining] }}</a-col>
      <a-col :span="20" v-else-if="itemKey === 'amount'" class="amount">￥{{ handlePrize(currentManageCompetition.amount) }}</a-col>
      <a-col :span="20" v-else-if="itemKey === 'competitionType'">{{ competitionRealTypeMaps[currentManageCompetition.competitionType] }}</a-col>
      <a-col :span="20" v-else-if="itemKey === 'imageUrl'">
        <img style="height: 129px" v-if="currentManageCompetition[itemKey]" :src="currentManageCompetition[itemKey]" :alt="competitionInfoMaps[itemKey]" />
        <span v-else>--</span>
      </a-col>
      <a-col :span="20" v-else-if="itemKey === 'banner'">
        <img style="height: 160px" v-if="currentManageCompetition[itemKey]" :src="currentManageCompetition[itemKey]" :alt="competitionInfoMaps[itemKey]" />
        <span v-else>--</span>
      </a-col>
      <a-col :span="20" v-else-if="itemKey === 'leader'">
        <div style="display: flex; flex-wrap: wrap" v-if="currentManageCompetition[itemKey] && currentManageCompetition[itemKey].length > 0">
          <img style="height: 30px; margin: 0 24px 8px 0" v-for="url in currentManageCompetition[itemKey]" :src="url" :key="url" :alt="competitionInfoMaps[itemKey]" />
        </div>
        <span v-else>--</span>
      </a-col>
      <a-col :span="20" v-else>
        {{ currentManageCompetition[itemKey] || '--' }}
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { competitionInfoMaps, competitionTrainningMaps, competitionRealTypeMaps } from './../../competitionConfig/index.js';
import { mapState } from 'vuex';
import { dateConvert, handlePrize } from '@/utils/utils';
export default {
  name: 'competitionInfo',
  data() {
    return {
      competitionInfoMaps,
      competitionTrainningMaps,
      competitionRealTypeMaps,
      dateConvert,
      handlePrize,
    };
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
  },
  methods: {
    handleTime(t) {
      return this.dateConvert(t).replace(/\//g, '.');
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.competition-info {
  color: #121f2c;
  .amount {
    font-size: 20px;
    font-weight: @jt-font-weight-medium;
    color: #ff454d;
    line-height: 19px;
  }
}
</style>
