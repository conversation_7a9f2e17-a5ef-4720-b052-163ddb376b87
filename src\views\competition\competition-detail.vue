<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div class="competition-detail">
    <competition-breadcrumb :competition-name="formDataInfo.typeName"></competition-breadcrumb>
    <jt-common-content :loading="loading">
      <div class="competition-main inner">
        <div class="competition-detail-content">
          <competition-detail-info :competition-detail-skeleton-loading="loading" :form-data-info="formDataInfo"></competition-detail-info>
          <div v-if="competitionModalShow" class="content-information-bottom">
            <Alarm v-if="isMobileCloud && !isCompleteRegister" :title="ecloudAlarm" style="margin-bottom: 24px" />
            <a-modal v-model:open="modalForm.visible" class="sing-modal" :mask="true" :closable="false">
              <template #title>
                <ExclamationCircleFilled class="sing-title-icon" />
                <span class="sing-title-text">{{ modalForm.title }}</span>
              </template>
              <p class="sing-content">您已报名“{{ rejectName }}”，不可同时报名本比赛</p>
              <template #footer>
                <a-button type="primary" @click="modalForm.visible = false">知道了</a-button>
              </template>
            </a-modal>
            <competition-detail-button :loading="btnLoading" :competition-status="competitionStatus" :is-mobile-cloud="isMobileCloud" :signup-end="signupEnd" :logined="logined" :mobile-cloud-status="mobileCloudStatus" :mobile-cloud-registered="mobileCloudRegistered" :sign-up="signUp" :has-instance="hasInstance" :is-official="isOfficial" :reject-name="rejectName" :ecloud-error-code="ecloudErrorCode" @handleClick="handleClick" />
          </div>
        </div>
        <div v-if="formDataInfo.banner" class="cpt-banner">
          <img :src="formDataInfo.banner" alt />
        </div>
        <div class="cpt-detail">
          <a-tabs v-model:activeKey="activeKey" @change="tabChange">
            <a-tab-pane :key="COMPETITION_DETAIL_TABS.INTRODUCE" tab="赛制介绍">
              <competition-introduce :cpt-introduce="cptIntroduce" :introduce-loading="introduceLoading"></competition-introduce>
            </a-tab-pane>
            <a-tab-pane :key="COMPETITION_DETAIL_TABS.DESCRIPTION" tab="赛题说明">
              <competition-introduce :cpt-introduce="cptExplain" :introduce-loading="introduceLoading"></competition-introduce>
            </a-tab-pane>
            <a-tab-pane v-if="signUp || isCompleteRegister" :key="COMPETITION_DETAIL_TABS.SUBMIT_RESULT" tab="提交结果">
              <submit-result :active-key="activeKey" :is-mobile-cloud="isMobileCloud" :type-id="formDataInfo.typeId" @tabChange="tabChange"></submit-result>
            </a-tab-pane>
            <a-tab-pane v-if="signUp || isCompleteRegister" :key="COMPETITION_DETAIL_TABS.MY_TEAM" tab="我的团队">
              <my-team :active-key="activeKey" :ecloud-type-id="ecloudTypeId"></my-team>
            </a-tab-pane>
            <a-tab-pane v-if="isOfficial && formDataInfo.rankSta" :key="COMPETITION_DETAIL_TABS.RANKINGS" tab="排行榜">
              <competition-ranking :show-ranking="formDataInfo.rankSta" :ranking-title="formDataInfo.typeName"></competition-ranking>
            </a-tab-pane>
            <a-tab-pane :key="COMPETITION_DETAIL_TABS.COMMON_QUESTION" tab="常见问题">
              <common-question></common-question>
            </a-tab-pane>
          </a-tabs>
        </div>
        <competition-invite-modal :form-data-info="formDataInfo"></competition-invite-modal>
        <mobile-abnormal-confirm :current="mobileCurrent" :abnormal-mobile-current-body="abnormalMobileCurrentBody" :abnormal-modal-visible="abnormalModalVisible" :form-data-info="formDataInfo" :mobile-cloud-status="mobileCloudStatus" @abnormalModalCancel="abnormalModalVisible = false"> </mobile-abnormal-confirm>
      </div>
    </jt-common-content>
  </div>
</template>

<script>
import API from '@/constants/api/API.js';
import { getLocalConfig } from '@/config';
import { checkLogin, login } from '@/keycloak';
import CompetitionDetailButton from './components/CompetitionDetailButton.vue';
import { ECLOUD_URL_CONFIG, ECLOUD_URL, CONSOLE_ECLOUD_URL } from '@/common/ecloud';
import { ECLOUD_COMPETITION_SIGN_STATUS, runningStatusMaps, COMPETITION_SIGN_STATUS, competitionTypeTextGroup, ECLOUD_COMPETITION_STEPS_TYPE, COMPETITION_DETAIL_TABS } from './competitionConfig/index';
import Alarm from '@/components/alarm/index';
import { dateConvert, gotoConslePlatform } from '@/utils/utils';
import CompetitionBreadcrumb from './components/CompetitionBreadcrumb.vue';
import CompetitionIntroduce from './components/CompetitionIntroduce.vue';
import SubmitResult from './components/SubmitResult.vue';
import MyTeam from './components/MyTeam.vue';
import CompetitionRanking from './components/CompetitionRanking.vue';
import CommonQuestion from './components/CommonQuestion.vue';
import CompetitionInviteModal from './components/CompetitionInviteConfirm/index.vue';
import MobileAbnormalConfirm from './components/AbnormalConfirm';
import { mapState } from 'vuex';
import CompetitionDetailInfo from './components/CompetitionDetailInfo';
import { GET } from '@/request';
import { openInNewTab, addUrlParams, isEntranceBySasac } from '@/utils/utils';

import { ECLOUD_UPDATE_INFO_MODAL_STATE, ecloudErrorCodeMaps } from '@/common/ecloud';

export default {
  components: {
    Alarm,
    CompetitionDetailButton,
    CompetitionBreadcrumb,
    CompetitionIntroduce,
    SubmitResult,
    MyTeam,
    CompetitionRanking,
    CommonQuestion,
    CompetitionInviteModal,
    MobileAbnormalConfirm,
    CompetitionDetailInfo,
  },
  data() {
    return {
      isEntranceBySasac,
      sasacLocation: window.location.origin,
      COMPETITION_DETAIL_TABS,
      competitionId: this.$route.query.id,
      introduceLoading: true,
      ecloudAlarm: '应比赛主办方要求，本次比赛需要在移动云深度学习平台进行模型训练，您必须完成移动云账号注册、实名认证及深度学习平台订购，才能参加本比赛',
      cptIntroduce: [],
      cptExplain: [],
      activeKey: COMPETITION_DETAIL_TABS.INTRODUCE,
      formDataInfo: {}, //比赛信息
      competitionStatus: '',
      rejectName: '', //冲突比赛名
      modalForm: {
        title: '报名失败提示',
        visible: false,
      },
      hasInstance: false,
      btnLoading: true,
      signUp: false, //是否已报名
      isOfficial: true, //是否为正式赛
      signupEnd: false, //报名是否结束
      isMobileCloud: false, //是否为移动云比赛
      mobileCloudStatus: '', // 移动云状态
      mobileCloudRegistered: false, //是否已注册移动云
      ecloudTypeId: '', //比赛id 传到my-team判断是否为移动云比赛
      // 比赛对接移动云支持账号异常处理 abnormalVisible mobileCurrent abnormalMobileCurrentBody ecloudErrorCode
      abnormalModalVisible: false,
      mobileCurrent: ECLOUD_UPDATE_INFO_MODAL_STATE.UPDATED, // 移动云账号变更显示对应弹窗
      abnormalMobileCurrentBody: {},
      ecloudErrorCode: null, // 比赛对接移动云支持账号异常处理 按钮显示（“继续报名”），提示用户移动云账号变化，errorcode
      loading: false,
    };
  },
  computed: {
    ...mapState(['userInfo']),
    competitionModalShow() {
      return competitionTypeTextGroup[this.formDataInfo.typeId] !== '正式算法赛' && competitionTypeTextGroup[this.formDataInfo.typeId] !== '正式创意赛';
    },
    CONSOLE_URL() {
      return getLocalConfig('CONSOLE_URL');
    },
    logined() {
      return checkLogin();
    },
    ECLOUD_ERROR_CODE_JOIN() {
      return this.ecloudErrorCode === ecloudErrorCodeMaps.mobileCloudJoin;
    },
    ECLOUD_ERROR_CODE_KNOW() {
      return this.ecloudErrorCode === ecloudErrorCodeMaps.mobileCloudKnow;
    },
    isOfficialMobileCompetition() {
      return competitionTypeTextGroup[this.formDataInfo.typeId] === '正式赛&移动云比赛';
    },
    isCompleteRegister() {
      return this.mobileCloudStatus === '完成报名';
    },
  },
  beforeRouteEnter(to, from, next) {
    API.competition_model.getCompetitionAuthority({ cid: to.query.id }).then((res) => {
      if (res.state === 'OK' && res.body) {
        next();
      } else {
        next('/competition');
      }
    });
  },
  created() {
    this.getCompetitionIntroduce();
    this.getCompetitionDetails();
  },
  methods: {
    handleClick(methodsName) {
      switch (methodsName) {
        case 'gotoLogin':
          this.gotoLogin();
          break;
        case 'gotoMobileCloudSignUp':
          this.gotoMobileCloudSignUp();
          break;
        case 'gotoDeepLearn':
          this.gotoMobileCloudDeepLearn();
          break;
        case 'gotoSignUp':
          this.gotoSignUp();
          break;
        case 'gotoInstanceListPage':
          this.gotoInstanceListPage();
          break;
        case 'gotoCreatePage':
          this.gotoCreatePage();
          break;
      }
    },
    // 比赛状态(报名状态)比赛是否有冲突
    getCompetitionConflictState() {
      return API.competition_model.getCompetitionjoinSta({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.signUp = COMPETITION_SIGN_STATUS[res.body.sta] === '已报名';
          this.isOfficial = res.body.iscpt;
          this.signupEnd = res.body.signupend;

          let activeKey = this.$route.query.activeKey;
          if (activeKey === COMPETITION_DETAIL_TABS.MY_TEAM && this.signUp) {
            this.activeKey = COMPETITION_DETAIL_TABS.MY_TEAM;
          }
          if (res.body.rejectName) {
            this.rejectName = res.body.rejectName?.join('，');
          } else {
            this.rejectName = '';
          }
        } else {
          this.signUp = false;
        }
      });
    },
    getMobilecloudCompetitionState() {
      return API.competition_model.getEcloudInfo({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.mobileCloudStatus = ECLOUD_COMPETITION_SIGN_STATUS[res.body.joinSta];
          this.mobileCloudRegistered = res.body.ecloudRegistSta;
          this.isMobileCloud = true;
        } else {
          this.signUp = false;
          this.isMobileCloud = true;
        }
        if (res.state === 'ERROR') {
          this.ecloudErrorCode = res.errorCode; // 接口返回errorCode 判断移动云账号是否发生变化
          if (this.ECLOUD_ERROR_CODE_KNOW) {
            // -922 弹窗显示移动云账号信息（用户名、用户ID）
            this.abnormalMobileCurrentBody = res.body;
          }
        }
      });
    },

    checkInstanceState() {
      return API.dp_platform.getCompetitionInstance_repeat({ projectId: this.formDataInfo.projectId }).then((res) => {
        this.hasInstance = res.msg === '该实例不存在' ? false : true;
        this.btnLoading = false;
      });
    },

    getCompetitionDetails() {
      this.loading = true;
      API.competition_model.getCompetitionInfo({ cid: this.competitionId }).then(async (res) => {
        if (res.state === 'OK' && res.body) {
          this.formDataInfo = res.body;
          this.competitionStatus = runningStatusMaps[res.body.flag];
          this.formDataInfo.leader = res.body.leader;
          this.formDataInfo.startTime = dateConvert(res.body.startTime);
          this.formDataInfo.endTime = dateConvert(res.body.endTime);
          this.ecloudTypeId = res.body.typeId; //比赛id 传到my-team判断是否为移动云比赛
          this.loading = false;

          await this.getCompetitionConflictState();
          if (this.isOfficialMobileCompetition) {
            await this.getMobilecloudCompetitionState();
          }
          if (this.logined) {
            await this.checkInstanceState();
          } else {
            this.btnLoading = false;
          }
        }
        // 没有权限跳到比赛首页
        if (res.errorCode == '-529') {
          this.$router.push({
            path: this.$route.meta.parentPageUrl,
          });
        }
      });
    },
    getCompetitionIntroduce() {
      API.competition_model.getCompetitionDescInfoUrl({ cid: this.competitionId }).then(async (res) => {
        if (res.state === 'OK' && res.body) {
          this.cptIntroduce = res.body.descriptionPathJson || [];
          this.cptExplain = res.body.instrucationsPathJson || [];
          this.introduceLoading = false;
        }
      });
    },
    tabChange(key) {
      this.activeKey = key;
      if (key === COMPETITION_DETAIL_TABS.SUBMIT_RESULT && this.isOfficialMobileCompetition) {
        this.getMobilecloudCompetitionState();
      }
    },
    gotoLogin() {
      this.getInstanceNumCreate();
      login();
    },
    // 跳转 实例列表页面显示当前单条实例
    async gotoInstanceListPage() {
      await this.getInstanceNumCreate();
      await API.competition_model.getSpecId({ cid: this.competitionId });
      if (isEntranceBySasac()) {
        gotoConslePlatform(`${this.sasacLocation}/edu/console?entrance=sasac#/home/<USER>/instance-info/${this.formDataInfo.projectId}`);
      } else {
        gotoConslePlatform(`${this.CONSOLE_URL}/home/<USER>/instance-info/${this.formDataInfo.projectId}`);
      }
    },
    async gotoCreatePage() {
      await this.getInstanceNumCreate();
      const gzRoute = `${this.sasacLocation}/edu/console?entrance=sasac#/home/<USER>/instance-form?projectId=${this.formDataInfo.projectId}&projectName=${this.formDataInfo.typeName}&hasInstance=${+this.hasInstance}`;
      const url = isEntranceBySasac() ? gzRoute : `${this.CONSOLE_URL}/home/<USER>/instance-form?projectId=${this.formDataInfo.projectId}&projectName=${this.formDataInfo.typeName}&hasInstance=${+this.hasInstance}`;
      gotoConslePlatform(url);
    },
    getInstanceNumCreate() {
      return API.competition_model.getCompetitionListNum({ projectId: this.formDataInfo.projectId });
    },
    // 跳转非移动云报名页
    gotoSignUp() {
      if (this.rejectName != '') {
        this.modalForm.visible = !this.modalForm.visible;
        return;
      }
      const { cid, typeName, typeId } = this.formDataInfo;
      this.$router.push({
        path: '/competition/competition-register',
        query: {
          id: cid,
          name: typeName,
          typeId,
        },
      });
    },
    // 跳转移动云报名页
    gotoMobileCloudSignUp() {
      if (this.rejectName != '') {
        this.modalForm.visible = !this.modalForm.visible;
        return;
      }
      let current = ECLOUD_COMPETITION_STEPS_TYPE.REGISTER;
      if (this.mobileCloudStatus === '继续报名' && this.mobileCloudRegistered) {
        current = ECLOUD_COMPETITION_STEPS_TYPE.ACCOUNT_OPEN;
      } else if (this.mobileCloudStatus === '继续报名') {
        current = ECLOUD_COMPETITION_STEPS_TYPE.DEEPLEARN_SUBSCRIBED;
      }
      if (this.ECLOUD_ERROR_CODE_JOIN) {
        this.mobileCurrent = ECLOUD_UPDATE_INFO_MODAL_STATE.CLOSED_TO_REGISTER;
        this.abnormalModalVisible = true;
      } else if (this.ECLOUD_ERROR_CODE_KNOW) {
        this.mobileCurrent = ECLOUD_UPDATE_INFO_MODAL_STATE.CLOSED_TO_ECLOUD;
        this.abnormalModalVisible = true;
      } else {
        const { cid, typeName, typeId } = this.formDataInfo;
        this.$router.push({
          path: '/competition/competition-register',
          query: {
            id: cid,
            name: typeName,
            typeId,
            current: current,
          },
        });
      }
    },
    // 跳转深度学习页面
    gotoMobileCloudDeepLearn() {
      GET('/competiton/join/web/ecloud/checkUser', { cid: this.competitionId }).then((res) => {
        if (res.state !== 'ERROR') {
          this.ecloudSSOCheck();
        } else {
          this.ecloudErrorCode = res.errorCode;
          // -921","移动云账号已注销" -922","移动云账号发生变化"
          if (this.ECLOUD_ERROR_CODE_KNOW) {
            this.mobileCurrent = ECLOUD_UPDATE_INFO_MODAL_STATE.UPDATED;
            this.abnormalModalVisible = true;
            this.abnormalMobileCurrentBody = res.body; // 发生变化 显示弹窗的手机号及其他信息
          } else if (this.ECLOUD_ERROR_CODE_JOIN) {
            if (this.mobileCloudStatus === '完成报名') {
              //(4) 如移动云手机号已不存在，且在毕昇平台已完成报名
              this.mobileCurrent = ECLOUD_UPDATE_INFO_MODAL_STATE.CLOSED_TO_ECLOUD;
              this.abnormalMobileCurrentBody = this.userInfo;
            } else {
              // (3) 如移动云手机号已不存在，且在毕昇平台已进入报名流程，点击“前往报名”进入立即报名页
              this.mobileCurrent = ECLOUD_UPDATE_INFO_MODAL_STATE.CLOSED_TO_ECLOUD;
            }
            this.abnormalModalVisible = true;
          }
        }
      });
    },
    ecloudSSOCheck() {
      API.competition_model.ecloudSSOCheck().then((res) => {
        if (res.state === 'OK' && res.body) {
          const destUrl = window.escape(`${CONSOLE_ECLOUD_URL}${ECLOUD_URL_CONFIG.CONSOLE_DEEP_LEARNING_URL}`);
          const url = `${ECLOUD_URL}${ECLOUD_URL_CONFIG.ECLOUD_SSO_CHECK_URL}`;
          const urlParams = {
            token: res.body,
            destUrl: destUrl,
            systemSource: 'BiSheng',
          };
          const deepLearnUrl = addUrlParams(url, urlParams);
          openInNewTab(deepLearnUrl);
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.competition-main {
  background: @jt-main-bg-color;
  margin-top: 20px;
  padding-bottom: 20px;
  .inner {
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }
}
.sing-modal {
  :deep(.ant-modal-footer),
  :deep(.ant-modal-header) {
    border: none;
  }
  .sing-title-icon {
    color: #ff454d;
    margin-right: 9px;
  }
  .sing-title-text {
    font-weight: bold;
  }
  .sing-content {
    color: #606972;
    margin: -20px 0 20px 23px;
  }
}
.cpt-detail {
  padding: 0 32px 40px;
  background: @jt-color-white;
}
.competition-detail-content {
  background: @jt-color-white;
  border-bottom: 1px solid @jt-line-color;
  border-radius: @jt-border-radius;
  padding: 32px;
  position: relative;

  .content-information-bottom {
    padding: 24px 0 0;
  }
}
.cpt-banner {
  height: 378px;
  padding: 32px 32px 20px;
  background: @jt-color-white;
  > img {
    width: 100%;
    height: 100%;
  }
}
</style>
