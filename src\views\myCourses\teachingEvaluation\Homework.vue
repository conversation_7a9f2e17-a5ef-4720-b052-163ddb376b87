<script lang="jsx" setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <a-table v-if="tableData.length > 0" row-key="id" :data-source="tableData" :columns="columns" :pagination="false" size="middle" :custom-row="subCustomRow" :custom-header-row="subCustomHeaderRow">
      <template #sort-slot="{ record, index }">
        <div class="sort-btns">
          <jt-icon class="down-btn" :class="!record.sortdown && 'sort-hidden'" type="icondown" @click="handlesortByIndex(index, 'down')" />
          <jt-icon class="up-btn" :class="!record.sortup && 'sort-hidden'" type="iconup" @click="handlesortByIndex(index, 'up')" />
        </div>
      </template>
      <template #name-slot="{ value, record }">
        <span>{{ value }}</span>
        <template v-if="record.spec">
          <a-tag v-for="item in record.spec.split(',')" :key="item" class="resource-tag">
            {{ item == 'cpu' ? 'CPU' : 'vGPU' }}
          </a-tag>
        </template>
        <span v-if="record.instanceModel" :class="record.instanceModel === 'Jupyter' ? 'jupyter-icon' : 'vscode-icon'">{{ record.instanceModel === 'Jupyter' ? 'J' : 'V' }}</span>
      </template>
      <template #assignmentType-slot="{ value }">
        <span :class="value === 'item' ? 'project-item' : 'test-item'">{{ value === 'item' ? '项目' : '测验' }}</span>
      </template>

      <template #status-slot="{ status }">
        <span :class="`${status === HOMEWORKSTATUS.UNPUBLISH ? 'unpublish' : status === HOMEWORKSTATUS.PUBLISHED ? 'published' : status === HOMEWORKSTATUS.PUBLISHING ? 'publishing' : 'ended'}-status`">{{ status === HOMEWORKSTATUS.UNPUBLISH ? '未发布' : status === HOMEWORKSTATUS.PUBLISHED ? '已发布' : status === HOMEWORKSTATUS.PUBLISHING ? '发布中' : '已截止' }}</span>
      </template>
      <template #startTime-slot="{ startTime, record }">
        <span>{{ record.status === HOMEWORKSTATUS.UNPUBLISH || record.status === HOMEWORKSTATUS.PUBLISHING ? '—' : startTime }}</span>
      </template>
      <template #submitNumber-slot="{ submitNumber, record }">{{ record.status === HOMEWORKSTATUS.UNPUBLISH || record.status === HOMEWORKSTATUS.PUBLISHING ? '—' : `${submitNumber}/${record.classNumber}` }}</template>
      <template #operation-slot="{ id, record }">
        <a-space :size="24" class="operation-btns">
          <a-button type="link" @click="() => deleteWork(id, record)">删除</a-button>
          <a-button type="link" :disabled="record.status !== HOMEWORKSTATUS.UNPUBLISH" @click="() => publishWork(id, record)">发布</a-button>
          <a-button v-if="record.status === HOMEWORKSTATUS.ENDED" type="link" @click="reviewWork(id, record)">评阅</a-button>
          <a-button v-else type="link" :disabled="record.status === HOMEWORKSTATUS.PUBLISHING" @click="() => editWork(id, record)">编辑</a-button>
        </a-space>
      </template>
    </a-table>
    <empty v-if="tableData.length == 0" :empty="true" empty-title="您暂未添加任何作业">
      <template #empty-description>
        <p class="work-empty-title">您暂未添加任何作业</p>
        <p class="work-empty-operation">您可以 <span @click="routeToAddHomework">新增作业</span></p>
      </template>
    </empty>
    <confirm-modal v-model="deleteModalVisible" type="danger" ok-text="删除" title="确实删除该作业吗？" @ok="confirmDelete">
      <template #icon>
        <ExclamationCircleFilled style="font-size: 18px; color: #ff454d" />
      </template>
      <div style="line-height: 22px; margin-bottom: 32px">
        <p>
          删除作业：<span style="color: #0082ff">{{ selectworkItem.assignmentName }}</span>
        </p>
        <p>作业删除后无法恢复，无法查看学生提交内容，请谨慎操作；</p>
        <p>如作业已发布，学生将收到提示，可自行同步删除。</p>
      </div>
    </confirm-modal>
    <confirm-modal v-model="publishModalVisible" title="发布作业提示" ok-text="发布" :spinning="publishBtnDisabled" :confirm-loading="publishBtnDisabled" @ok="confirmPublish">
      <template #icon>
        <jt-icon type="iconwarning-circle-fill" style="font-size: 18px; color: #fa8014" />
      </template>
      <div style="margin-bottom: 32px; line-height: 22px">
        <p>发布前请务必确保作业名称、类型、实例信息、提交附件等均已正确设置。</p>
        <p>发布后仅可修改提交截止日期，作业开始时间即为发布时间。</p>
      </div>
    </confirm-modal>
  </div>
</template>

<script lang="jsx">
import confirmModal from '@/components/confirmModal/index.vue';
import empty from '@/components/contentWithEmpty.vue';
import { homeworkApi } from '@/apis';
import moment from 'moment';
let intervalMap = new Map();

export default {
  name: 'Homework',
  components: {
    confirmModal,
    empty,
  },
  data() {
    return {
      isDestroying: false,
      deleteModalVisible: false,
      publishModalVisible: false,
      publishBtnDisabled: false,
      emptyImg: require('@/assets/image/emptys2x.png'),
      selectworkItem: {},
      tableData: [],
      columns: [
        {
          key: 'sort',
          title: '',
          dataIndex: 'sort',
          slots: { customRender: 'sort-slot' },
        },
        {
          key: 'assignmentName',
          title: '名称',
          dataIndex: 'assignmentName',
          // width: '240px',
          slots: { customRender: 'name-slot' },
        },
        {
          key: 'assignmentType', // item 项目 test 测验
          title: '类型',
          dataIndex: 'assignmentType',
          slots: { customRender: 'assignmentType-slot' },
        },
        {
          // 0 未发布 1发布中  2 已发布
          key: 'status',
          title: '状态',
          dataIndex: 'status',
          slots: { customRender: 'status-slot' },
        },
        {
          key: 'startTime',
          title: '作业开始时间',
          dataIndex: 'startTime',
          slots: { customRender: 'startTime-slot' },
        },
        {
          key: 'endTime',
          title: '提交截止时间',
          dataIndex: 'endTime',
          slots: { customRender: 'endTime-slot' },
        },
        // submitNumber classNumber
        {
          key: 'submitNumber',
          title: '已提交/总人数',
          dataIndex: 'submitNumber',
          slots: { customRender: 'submitNumber-slot' },
        },
        {
          key: 'operation',
          title: '操作',
          dataIndex: 'id',
          slots: { customRender: 'operation-slot' },
        },
      ],
      HOMEWORKSTATUS: {
        UNPUBLISH: '0',
        PUBLISHING: '1',
        PUBLISHED: '2',
        ENDED: '3', // 已截止
      },
    };
  },
  beforeUnmount() {
    this.isDestroying = true;
    this.destroyAllIntervals();
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    // 覆盖table的样式
    subCustomHeaderRow() {
      return {
        style: {
          'font-size': '12px',
          'background-color': '#EDF1F3;',
          color: '#121F2C',
          height: '40px',
        },
      };
    },
    subCustomRow() {
      return {
        style: {
          height: '44px',
          color: '#121F2C',
          'font-size': '12px',
        },
      };
    },
    async getTableData() {
      const courseId = this.$route.params.courseId;
      const res = await homeworkApi.homeworkList(courseId);
      if (res.state === 'OK') {
        this.tableData = this.formateTableData(res.body || []);
        // 如果有发布中状态的作业，则需要定时查询、更新作业状态
        this.tableData.forEach((homeworkItem) => {
          if (homeworkItem.status === this.HOMEWORKSTATUS.PUBLISHING) {
            this.createIntervalByHomeworkItem(homeworkItem);
          }
        });
      }
    },
    // 发布项目后根据需要轮循查询项目的状态
    createIntervalByHomeworkItem(homeworkItem) {
      if (intervalMap.get(homeworkItem.id)) {
        return;
      }
      createTimeout();
      // 定时查询作业发布的状态
      const self = this;
      const { id, assignmentName } = homeworkItem;
      function createTimeout() {
        const timeoutId = setTimeout(() => {
          homeworkApi.getHomeworkStatus(id).then((res) => {
            if (self.isDestroying) {
              return;
            }
            if (res.state === 'OK') {
              if (res.body?.status !== self.HOMEWORKSTATUS.PUBLISHING) {
                self.clearTimeoutById(id);
                self.getTableData();
                self.$message.success(`作业“${assignmentName}”发布成功`);
              } else {
                createTimeout();
              }
            } else if (res.state === 'ERROR' && res.errorCode == '920') {
              // 发布或创建失败
              const timeoutId = intervalMap.get(id);
              self.getTableData();
              if (timeoutId) {
                self.clearTimeoutById(id);
                intervalMap.delete(id);
                self.$message.error({
                  content: <span style="color: #FF454D;">{`作业“${assignmentName}”发布失败，请勿在发布过程中更改模型训练实例内容`}</span>,
                });
              }
            }
          });
        }, 1000);
        intervalMap.set(id, timeoutId);
      }
    },
    // 根据数据序列确定是否有上下箭头
    formateTableData(tableData) {
      const len = tableData.length;
      if (len === 0 || len === 1) {
        return tableData;
      }
      for (let i = 0; i < len; i++) {
        if (i === 0) {
          tableData[i].sortup = false;
          tableData[i].sortdown = true;
        } else if (i === len - 1) {
          tableData[i].sortup = true;
          tableData[i].sortdown = false;
        } else {
          tableData[i].sortup = true;
          tableData[i].sortdown = true;
        }
      }
      return tableData;
    },
    // 根据id清除定时器
    clearTimeoutById(id) {
      const timeoutId = intervalMap.get(id);
      if (timeoutId) {
        clearTimeout(timeoutId);
        intervalMap.delete(id);
      }
    },
    // 打开删除作业弹框
    deleteWork(id, record) {
      this.selectworkItem = record;
      this.deleteModalVisible = true;
    },
    confirmDelete() {
      const id = this.selectworkItem.id;
      this.clearTimeoutById(id);
      homeworkApi.deleteHomeworkItem(id).then((res) => {
        if (res.state === 'OK') {
          this.deleteModalVisible = false;
          this.$message.success('删除作业成功');
          this.getTableData();
        } else {
          this.$message.error('删除作业失败');
        }
      });
    },
    async confirmPublish() {
      const { assignmentName, id } = this.selectworkItem;
      this.publishBtnDisabled = true;
      const res = await homeworkApi.publishHomework(id);
      if (res.state === 'OK') {
        this.$message.success({
          content: <span style="color: #666666;">{`作业“${assignmentName}”发布中，请稍后查看`}</span>,
          icon: <jt-icon style="color: #0082FF;" type="icondengdaizhong"></jt-icon>,
        });
        this.getTableData();
      } else {
        this.$message.error(res.errorCode === '-001' ? res.errorMessage : `作业"${assignmentName}"，请勿在发布过程中更改模型训练实例内容`);
      }
      this.publishModalVisible = false;
      this.publishBtnDisabled = false;
    },
    routeToAddHomework() {
      const courseId = this.$route.params.courseId;
      this.$router.push(`/course/teaching/mycourses/create-work/${courseId}`);
    },
    reviewWork(id, record) {
      this.$router.push({
        path: `/course/teaching/mycourses/course-manage/${this.$route.params.courseId}/${id}`,
        query: { assignmentName: record.assignmentName },
      });
    },
    publishWork(id, record) {
      if (moment().diff(moment(record.endTime)) > 0) {
        this.$message.error('提交截止时间必须晚于当前时间');
        return;
      }
      this.selectworkItem = record;
      this.publishModalVisible = true;
    },
    editWork(workId) {
      // 跳转到编辑页面
      const courseId = this.$route.params.courseId;
      this.$router.push(`/course/teaching/mycourses/create-work/${courseId}/${workId}`);
    },
    // 作业排序
    handlesortByIndex(wokrkIndex, type) {
      const sourceIndex = wokrkIndex;
      const targetIndex = type === 'up' ? wokrkIndex - 1 : wokrkIndex + 1;

      let maps = this.tableData.map((item) => {
        return { id: item.id };
      });
      let items = maps.splice(sourceIndex, 1);
      maps.splice(targetIndex, 0, items[0]);
      const data = maps.map((item, index) => {
        return { id: item.id, sort: index + 1 };
      });
      homeworkApi.sortHomework(data).then((res) => {
        if (res.state === 'OK') {
          this.$message.success('修改排序成功');
          this.getTableData();
        }
      });
    },
    destroyAllIntervals() {
      for (let timeoutId of intervalMap.values()) {
        clearTimeout(timeoutId);
      }
      intervalMap.clear();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.sort-btns {
  display: inline-block;
  margin-left: 12px;
  .down-btn,
  .up-btn {
    font-size: 16px;
    color: #aaacb4;
    &:hover {
      color: @jt-primary-color;
    }
  }
  .sort-hidden {
    visibility: hidden;
  }
}
.work-empty-title {
  margin-bottom: @jt-gap-base;
  color: @jt-text-color-primary;
  font-size: @jt-font-size-lger;
  font-weight: @jt-font-weight-medium;
}

.work-empty-operation {
  color: @jt-text-color;
  span {
    cursor: pointer;
    color: @jt-primary-color;
  }
}
.resource-tag {
  background-color: #e1ecff;
  color: #337dff;
  font-size: @jt-font-size-sm;
  border-radius: 12px;
  border: none;
  margin-left: @jt-gap-base;
  margin-right: 0px;
}
.jupyter-icon {
  margin-left: @jt-gap-base;
  .jt-jupyter-icon();
}
.vscode-icon {
  margin-left: @jt-gap-base;
  .jt-vscode-icon();
}

.project-item {
  display: inline-block;
  width: 52px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: #fef3ea;
  border-radius: 2px;
  color: #f79032;
  font-size: @jt-font-size-sm;
}

.test-item {
  display: inline-block;
  width: 52px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: rgba(56, 155, 255, 0.1);
  border-radius: 2px;
  color: @jt-primary-color;
  font-size: @jt-font-size-sm;
}

.work-status-item(@color;@border-color) {
  display: inline-block;
  width: 52px;
  height: 20px;
  line-height: 18px;
  text-align: center;
  border-radius: 2px;
  border: 1px solid @border-color;
  color: @color;
  font-size: @jt-font-size-sm;
}

.unpublish-status {
  .work-status-item(#606972,#A0A6AB);
}

.published-status {
  .work-status-item(#17bb85,#17bb85);
}

.publishing-status {
  .work-status-item(#389bff,#389bff);
}
.ended-status {
  .work-status-item(#FA9914,#FA9914);
}

.operation-btns {
  .ant-btn.ant-btn-link {
    font-size: @jt-font-size-sm;
    padding: 0px;
  }
}
</style>
