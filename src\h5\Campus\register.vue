<template>
  <div class="register">
    <div class="register-cover-1">
      <img :src="introHeader" alt="" />
    </div>
    <div class="register-cover-2">
      <img :src="giftHeader" alt="" />
    </div>

    <van-form @submit="onSubmit" class="register-form">
      <img :src="registerHeaderbg" alt="" />
      <div class="register-form-title">九天·毕昇平台</div>
      <div class="register-form-item">
        <van-field class="item" v-model="phoneNum" name="phonenum" label="" placeholder="手机号" type="tel" :rules="[{ required: true, validator: telPhoneValidator }]" />
        <div class="item-errormsg">{{ phoneError }}</div>
        <van-field class="item" v-model="validateNumber" name="code" center label="" placeholder="验证码" maxlength="6" type="digit" :rules="[{ required: true }]">
          <template #button>
            <span class="validate-divider"></span>
            <span v-if="showCountdown">
              <van-count-down class="validate-code" ref="countDown" :time="countdownTime" :auto-start="false" format="重新获取ss秒" @finish="resetCountdown" />
            </span>
            <a @click="sendSms" v-else class="validate-code">获取验证码</a>
          </template>
        </van-field>
        <div class="item-errormsg">{{ codeError }}</div>
        <van-field class="item" v-model="userName" name="username" label="" placeholder="用户名" maxlength="20" :rules="[{ required: true, validator: userValidator }]" />
        <div class="item-errormsg">{{ userError }}</div>
        <p class="item-info">6-20个字符，只能包含字母、下划线和数字，不支持全数字，用户名不可修改，请谨慎设置</p>
        <van-field class="item" v-model="password" type="password" name="password" label="" placeholder="密码" maxlength="20" :rules="[{ required: true, validator: passwordValidator }]" />
        <div class="item-errormsg">{{ passwordError }}</div>
        <p class="item-info">8-20个字符，必须包含大、小写字母和数字</p>
      </div>
      <div class="register-form-submit">
        <van-button block type="info" native-type="submit">注册</van-button>
      </div>
    </van-form>
    <div class="register-info">话费权益说明：每月25日之前完成注册及个人信息完善，当月账单自动抵扣10元话费；每月26日及以后完成注册及个人信息完善，次月账单自动抵扣10元话费。此外，登录并使用平台，次月账单再自动抵扣10元话费。活动有效期截至2021年12月31日。</div>
  </div>
</template>

<script>
import API from '@/constants/api/API.js';

import { Notify } from 'vant';

export default {
  data() {
    return {
      userName: '',
      password: '',
      phoneNum: '',
      phoneError: '',
      codeError: '',
      userError: '',
      passwordError: '',
      validateNumber: '',
      phoneValidator: /^[1](([3][0-9])|([4][0,1,4-9])|([5][0-3,5-9])|([6][2,5,6,7])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/,
      passwordformatValidator: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[0-9a-zA-Z!@#$%^&*()-_=+{}[\]\\|;:'",.<>/?]{8,20}$/,
      usernameValidator: /^([A-Za-z0-9_]){6,20}$/,
      usernameNumberValidator: /^\d+$/,
      countdownTime: 60 * 1000,
      showCountdown: false,
      introHeader: require('../../assets/image/h5/develop.png'),
      giftHeader: require('../../assets/image/h5/fuli.png'),
      registerHeaderbg: require('../../assets/image/h5/register-header.png'),
    };
  },
  watch: {
    // eslint-disable-next-line no-unused-vars
    phoneError(newValue, oldValue) {
      console.log(newValue, '--->');
    },
  },
  methods: {
    telPhoneValidator(val) {
      console.log(val, 'phone');
      const isValidated = this.phoneValidator.test(val);
      if (!isValidated) {
        this.phoneError = '手机号码格式不正确';
      } else {
        this.phoneError = '';
      }
      return isValidated;
    },
    userValidator(val) {
      const nameValidator = this.usernameValidator.test(val);
      const numberValidator = this.usernameNumberValidator.test(val);
      if (numberValidator) {
        this.userError = '用户名不可为全数字';
      } else if (!nameValidator) {
        this.userError = '用户名格式不正确';
      } else {
        this.userError = '';
      }
      console.log(this.userError);
      return nameValidator && !numberValidator;
    },
    passwordValidator(val) {
      const isvalidated = this.passwordformatValidator.test(val);
      if (!isvalidated) {
        this.passwordError = '密码格式不正确';
      } else {
        this.passwordError = '';
      }
      return isvalidated;
    },
    onSubmit(values) {
      console.log('submit', values);
      const params = values;
      API.h5_campus.userRegistration(params).then((res) => {
        if (res.state === 'OK') {
          Notify({ type: 'success', message: '注册成功' });
          this.$router.replace({
            path: '/h5/question',
            query: { phoneNum: this.phoneNum },
          });
        } else {
          Notify({ type: 'danger', message: res.errorMessage });
        }
      });
    },
    sendSms() {
      const ifPhoneValidated = this.telPhoneValidator(this.phoneNum);
      if (ifPhoneValidated) {
        const param = { phoneNum: this.phoneNum };
        this.startCountdown();
        API.h5_campus.sendSmsCode(param).then((res) => {
          if (res.state === 'OK') {
            Notify({ type: 'success', message: '验证码已成功发送' });
          } else {
            if (res.errorCode === '1020' || res.errorCode === '1021') {
              this.phoneError = '';
              this.codeError = res.errorMessage;
            } else {
              this.codeError = '';
              this.phoneError = res.errorMessage;
            }
            this.resetCountdown();
          }
        });
      } else {
        this.phoneError = '手机号码格式不正确';
      }
    },
    resetCountdown() {
      this.showCountdown = false;
      this.$refs.countDown.reset();
    },
    startCountdown() {
      this.showCountdown = true;
      this.$nextTick(() => {
        this.$refs.countDown.start();
      });
    },
  },
};
</script>

<style lang="less">
@import '~@/assets/styles/index.less';

.register {
  padding: 16px;
  background: rgba(139, 182, 249, 1);
  background-image: url('~@/assets/image/h5/home.jpg');
  background-size: 100%;
  background-repeat: no-repeat;
  .register-cover-1 {
    margin-top: 115%;
    img {
      width: 100%;
    }
  }
  .register-cover-2 {
    margin: 20px 0;
    img {
      width: 100%;
    }
  }
  .register-form {
    width: 100%;
    background: rgba(255, 255, 255, 0.43);
    box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
    border-radius: 8px;
    border: 1px solid #ffffff;
    text-align: center;
    img {
      width: 200px;
      margin-top: -1px;
    }
    .register-form-title {
      margin: 20px 0;
      text-align: center;
      font-size: 22px;
      font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
      font-weight: 600;
      color: #2567f5;
    }
    .register-form-item {
      text-align: left;
      .validate-divider {
        height: 20px;
        margin-right: 10px;
        border-left: 1px solid #ddd;
      }
      .validate-code {
        white-space: nowrap;
        font-size: 17px;
        font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
        font-weight: 400;
        color: #4576ff;
        margin-right: 10px;
      }
      .item {
        border: none;
      }
      .item-errormsg {
        margin: -10px 15px 10px 15px;
        font-size: 14px;
        font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
        font-weight: 400;
        color: #f63f48;
      }
      .item-info {
        margin: 0px 17px 10px;
        text-align: left;
        font-size: 14px;
        font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
        font-weight: 400;
        color: #41497a;
      }
    }
    .register-form-submit {
      margin: 25px 16px 16px;
      background: linear-gradient(180deg, #487bfd 0%, #4056ff 100%);
      border-radius: 4px;
      font-size: 17px;
      font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
      font-weight: @jt-font-weight-medium;
      color: #ffffff;
    }
    .van-cell {
      border-radius: 4px;
      background: inherit;
    }
  }
  .register-info {
    margin: 30px 0 15px;
    font-size: 13px;
    font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
    font-weight: 400;
    color: #3f6ebf;
  }
}
</style>
<style lang="less">
.van-field__button {
  display: flex !important;
  align-items: center;
}
.van-field__value .van-field__body {
  border-radius: 4px;
  border: none;
  background: #ffffff !important;
}
.van-field__value .van-field__body input {
  padding: 10px;
  border-radius: 4px;
}
.van-field__error-message {
  font-size: 14px;
  font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
  font-weight: 400;
  color: #f63f48;
}
.van-field__value {
  .van-field__body {
    input {
      font-size: 17px;
    }
    input::-webkit-input-placeholder {
      color: #abafc5;
      font-size: 17px;
    }
  }
}
.van-cell::after {
  border: none;
}

.van-button--normal {
  font-size: 17px;
}
</style>
