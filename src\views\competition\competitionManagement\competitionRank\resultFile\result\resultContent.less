section {
    margin-top: 32px;
    padding-bottom: 40px;
    border-bottom: 1px solid #efefef;
  }
  .descriptions {
    margin-top: 24px;
    display: grid;
    justify-content: space-between;
    font-size: 14px;
    line-height: 20px;
    grid-template-columns: repeat(2, 50%);
    color: #121F2C;
    .descriptions-item {
      display: flex;
      margin-bottom: 24px;
      .title {
        text-align: right;
        margin-right: 16px;
        min-width: 130px;
      }
      &:nth-of-type(2n + 1),&.left {
        .title {
          min-width: 98px;
        }
      }
      &.html-viewer{
        grid-column-start: span 2;
      }
      .content-tag {
        display: block;
        text-align: center;
        width: 56px;
        height: 20px;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        border: 1px solid;
        border-color: #abb9ca;
        background-color: #edf3fd;
        color: #698097;
        &.active {
          color: #00b155;
          border-color: #72d69e;
          background-color: #eafdeb;
        }
      }
    }
  }
  .button-group{
    margin-top: 32px;
    .button{
      margin-right: 8px;
    }
  }