<template>
  <section class="content-box">
    <div class="inner">
      <h1 class="portal-title">精品内容</h1>
      <h2 class="sub-title">课程、比赛一网打尽，从 AI 小白到高阶玩家</h2>
      <div class="item-list">
        <recommended-item v-for="(cardItem, i) of cardConfig" :key="cardItem.title" :title="cardItem.title" :bgColor="cardItem.bgColor" :iconImg="cardItem.iconImg" :itemList="itemList.slice(i * number, (i + 1) * number)" :tagColor="cardItem.tagColor" @onClickItem="gotoItemDetail" />
      </div>
    </div>
  </section>
</template>

<script>
import { GET } from '@/request';
import RecommendedItem from './recommendedItem.vue';
const NUMBER = 3;
export default {
  name: 'home-recommended',
  components: { RecommendedItem },
  data() {
    return {
      number: NUMBER,
      itemList: [],
      cardConfig: [
        { title: '入门', iconImg: require('@/assets/image/home/<USER>'), bgColor: 'rgba(56, 155, 255, 0.25)', tagColor: '#389bff' },
        { title: '进阶', iconImg: require('@/assets/image/home/<USER>'), bgColor: 'rgba(247, 169, 45, 0.25)', tagColor: '#f7a92d' },
        { title: '实战', iconImg: require('@/assets/image/home/<USER>'), bgColor: 'rgba(15, 184, 220, 0.25)', tagColor: '#0fb8dc' },
      ],
    };
  },
  mounted() {
    this.getRecommendedList();
  },
  methods: {
    getRecommendedList() {
      GET('/recommend/home').then((res) => {
        if (res.state === 'OK') {
          this.itemList = res.body;
        }
      });
    },
    gotoItemDetail(item) {
      const resourceType = item.resourceType;
      let path, query;
      if (resourceType === 'course') {
        path = '/course/course-detail';
        query = {
          courseId: item.resourceId,
          num: item.studyNum,
        };
      } else if (resourceType === 'dataset') {
        path = '/dataset/dataset-detail';
        query = {
          dataId: item.data.dataId,
          dataName: item.data.dataName,
          dataUserNumber: item.data.dataUserNumber,
          dataDescribe: item.data.dataDescribe,
          dataIntroduce: item.data.dataIntroduce,
          dataTime: item.data.dataTime,
          dataClassificationId: item.data.dataClassificationId,
          projectId: item.data.projectId,
        };
      } else {
        path = '/competition/competition-detail';
        query = {
          id: item.resourceId,
          name: item.resourceName,
          all: 0,
        };
      }
      if (path && query) {
        this.$router.push({
          path,
          query,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import './common.less';

.item-list {
  display: flex;
  margin-top: 56px;
}
</style>
