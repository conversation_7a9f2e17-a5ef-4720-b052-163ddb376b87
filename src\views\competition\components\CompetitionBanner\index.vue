<template>
  <div class="competition-banner">
    <div class="banner-content login" v-if="logined">
      <h2>
        Hi,<span>{{ userName }}</span>
      </h2>
      <div class="content-text">
        <jt-common-content :loading="contentTextLoading" :empty="contentTextLoading" :emptyStyle="{ height: '0px' }">
          <template v-if="isJoinCompetition">
            <p v-if="joinCompetitionTotal > 0">
              您共参加
              <span>{{ joinCompetitionTotal }}</span>
              个比赛
            </p>
            <p v-else>您尚未参加任何比赛</p>

            <ol class="learned-course-container">
              <li v-for="item in joinCompetitionList.slice(0, 3)" :key="item.cid">
                <span @click="() => openToCompetitionDetail(item)">{{ item.typeName }}</span>
              </li>
            </ol>
            <div v-if="joinCompetitionList.length > 3">......</div>
          </template>
          <template v-if="isManageCompetition">
            <p v-if="manageCompetitionNum > 0">
              您共举办
              <span>{{ manageCompetitionNum }}</span>
              个比赛
            </p>
            <p v-else>您尚未举办任何比赛</p>

            <ol class="learned-course-container">
              <li v-for="manageCompetition in manageCompetitionList" :key="manageCompetition.cid">
                <span @click="() => openToCompetitionManage(manageCompetition.cid)">{{ manageCompetition.name }}</span>
              </li>
            </ol>
            <div v-if="manageCompetitionNum > 3">......</div>
          </template>
        </jt-common-content>
      </div>
      <a-button type="primary" v-if="isJoinCompetition && joinCompetitionTotal" :disabled="joinCompetitionTotal === 0" @click="myAttendCompetition" class="button">我参加的比赛</a-button>
      <a-button type="primary" v-if="isManageCompetition" @click="myHoldCompetition" class="button">{{ manageCompetitionNum > 0 ? '我举办的比赛' : '去创建比赛' }}</a-button>
      <div v-if="showCompetitionManage" class="tab-box">
        <div class="tab-item" :class="{ active: isJoinCompetition }" @click="activeTab = 'joinCompetition'"></div>
        <div class="tab-item" :class="{ active: isManageCompetition }" @click="activeTab = 'manageCompetition'"></div>
      </div>
    </div>
    <div class="banner-content" v-else>
      <h2>
        Hi,
        <span style="font-size: 24px">您好！</span>
      </h2>
      <div class="content-text">
        <p>欢迎来到九天 · 毕昇</p>
        <p>您尚未登录，请登录查看比赛</p>
      </div>
      <a-button type="primary" @click="login" class="button">立即登录</a-button>
    </div>
  </div>
</template>
<script>
import API from '@/constants/api/API.js';

import { checkLogin, login, isLogin, checkPublishCompetitionPermission } from '@/keycloak';
import { competitionApi } from '@/apis/index.js';
import { openInNewTab } from '@/utils/utils';

export default {
  data() {
    return {
      showCompetitionManage: isLogin() && checkPublishCompetitionPermission(),
      joinCompetitionList: [],
      joinCompetitionTotal: 0,
      joinningCompetitionLoading: true,
      heldCompetitionLoading: true,
      manageCompetitionNum: 0,
      manageCompetitionList: [],
      activeTab: 'joinCompetition',
    };
  },
  computed: {
    logined() {
      return checkLogin();
    },
    isJoinCompetition() {
      return this.activeTab === 'joinCompetition';
    },
    isManageCompetition() {
      return this.activeTab === 'manageCompetition';
    },
    contentTextLoading() {
      return this.isJoinCompetition ? this.joinningCompetitionLoading : this.heldCompetitionLoading;
    },
    userName() {
      return this.$keycloak ? this.$keycloak.idTokenParsed.preferred_username : '';
    },
  },
  mounted() {
    this.getMyCompetition();
    this.initManageCompetition();
  },
  methods: {
    openToCompetitionDetail(item) {
      const routeUrl = this.$router.resolve({
        path: 'competition/competition-detail',
        query: {
          id: item.cid,
          name: item.typeName,
        },
      });
      openInNewTab(routeUrl.href);
    },
    openToCompetitionManage(id) {
      const routeUrl = this.$router.resolve({
        path: `competition/competition-management/${id}`,
      });
      openInNewTab(routeUrl.href);
    },
    myHoldCompetition() {
      const routeUrl = this.myHoldCompetition === 0 ? '/competition/competition-management/create-competition' : '/competition/held-competition';
      this.$router.push(routeUrl);
    },
    myAttendCompetition() {
      this.$router.push('/competition/my-competition');
    },
    getMyCompetition() {
      if (this.$keycloak && this.$keycloak.authenticated) {
        API.competition_model.getMyCompetitionList({ pageNum: 1, pageSize: 5 }).then((res) => {
          if (res.state === 'OK') {
            this.joinCompetitionList = res.body.data;
            this.joinCompetitionTotal = res.body.total;
          } else {
            this.joinCompetitionList = [];
            this.joinCompetitionTotal = 0;
          }
          this.joinningCompetitionLoading = false;
        });
      }
    },
    initManageCompetition() {
      if (isLogin() && checkPublishCompetitionPermission()) {
        competitionApi.cardCompetitionInfo().then((res) => {
          if (res.state === 'OK') {
            this.manageCompetitionNum = res.body.competitonNum;
            this.manageCompetitionList = res.body.competitonNames;
          } else {
            this.manageCompetitionNum = 0;
            this.manageCompetitionList = [];
          }
          this.heldCompetitionLoading = false;
        });
      }
    },
    login,
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.banner-content {
  width: 280px;
  height: 298px;
  background: @jt-color-white;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  position: absolute;
  right: 0;
  top: 116px;
  padding: 40px;
  padding: 16px 24px 24px;

  h2 {
    font-size: 32px;
    font-weight: @jt-font-weight-medium;
    color: #16161c;
    line-height: 48px;
    margin-top: 0;
    margin-bottom: 21px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    span {
      font-size: 24px;
    }
  }

  .content-text {
    font-size: @jt-font-size-lg;
    font-weight: @jt-font-weight;
    color: @jt-text-color;

    p {
      margin-bottom: 8px;

      span {
        color: @jt-primary-color;
      }
    }

    ul {
      margin-top: 8px;
      line-height: 26px;

      li {
        &:before {
          content: '';
          width: 4px;
          height: 4px;
          margin-right: 5px;
          display: inline-block;
          background-color: @jt-text-color-secondary;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    :deep(.ant-spin-dot) {
      margin-top: 35px;
    }
  }

  .button {
    position: absolute;
    left: 0;
    bottom: 30px;
    right: 0;
    margin: auto;
    width: 232px;
    height: 40px;
    background: @jt-primary-color;
    border-radius: @jt-border-radius;
    color: @jt-color-white;
  }

  &.login {
    > p {
      margin: 0;
    }
  }
}
.learned-course-container {
  margin-bottom: 0;
  li {
    color: @jt-text-color;
    line-height: 26px;
    font-size: @jt-font-size-base;
    list-style: inside;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    span {
      cursor: pointer;
      &:hover {
        color: @jt-primary-color;
      }
    }
  }
}

.tab-box {
  position: absolute;
  left: 0;
  bottom: 14px;
  width: 100%;
  display: flex;
  justify-content: center;

  .tab-item {
    width: 24px;
    height: 4px;
    margin-right: 12px;
    cursor: pointer;
    background: #e0e1e1;

    &.active {
      background: #0082ff;
    }
  }
}
.button {
  position: absolute;
  left: 0;
  bottom: 30px;
  right: 0;
  margin: auto;
  width: 232px;
  height: 40px;
  background: #0082ff;
  border-radius: 2px;
}
</style>
