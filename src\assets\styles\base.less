// 浏览器默认行为覆盖
@import './var.less';

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;

  box-sizing: border-box;
}
:not(font) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
}

html,
body {
  height: 100%;
}

body {
  color: @jt-text-color-primary;
  font-size: @jt-font-size-base;
  font-weight: @jt-font-weight;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

a {
  color: inherit;
  text-decoration: none;

  &:hover {
    color: @jt-primary-color;
  }
  &[disabled] {
    cursor: not-allowed;
    pointer-events: all;
  }
}

ul,
ol {
  list-style: none;
}

p,
ul,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0;
}

// iconfont图标支持颜色修改
svg {
  fill: currentColor;
}

path {
  fill: unset;
} 
