<template>
  <div class="pagination">
    <div class="prve-btn pagination-btn" :class="{ disabled: current === 1 }" @click="prveOrNext(0)"></div>
    <!-- 当总页数小于等于10页时，显示全部页码 -->
    <ul v-if="getPageLength <= 10 || showAllPage" class="page-list">
      <li v-for="(x, index) in getPageLength" :key="index" :class="{ active: current === index + 1 }" @click="changePage(index + 1)">
        {{ index + 1 }}
      </li>
    </ul>
    <!-- 当总页数过多时，动态隐藏页码 -->
    <ul v-else class="page-list">
      <li v-if="current > Math.floor(pageBtnLimit / 2) + 1" @click="changePage(1)">1</li>
      <li v-if="current > Math.floor(pageBtnLimit / 2) + 2" class="pagination-item-ellipsis"></li>

      <li v-for="(x, index) in getCurrentLeft" :key="index" @click="changePage(current - x)">
        {{ current - x }}
      </li>
      <li class="active">
        {{ current }}
      </li>
      <li v-for="x in getCurrentRight" :key="x" @click="changePage(x)">
        {{ x }}
      </li>

      <li v-if="current < getPageLength - Math.floor(pageBtnLimit / 2) - 1" class="pagination-item-ellipsis">>></li>
      <li v-if="current < getPageLength - Math.floor(pageBtnLimit / 2)" @click="changePage(getPageLength)">
        {{ getPageLength }}
      </li>
    </ul>

    <div class="next-btn pagination-btn" :class="{ disabled: current === getPageLength }" @click="prveOrNext(1)">></div>

    <br />current - {{ current }} <br />value - {{ value }}
  </div>
</template>

<style lang="less" scoped>
* {
  transition: all 0.3s;
}
.pagination,
.page-list {
  display: flex;
}
.pagination-btn,
.page-list li {
  min-width: 32px;
  height: 32px;
  color: rgba(0, 0, 0, 0.65);
  font-family: Arial, sans-serif;
  line-height: 32px;
  text-align: center;
  vertical-align: middle;
  list-style: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 20px;
  background-color: #fff;
  outline: none;
  margin-right: 8px;
  user-select: none;
  &:not(.pagination-item-ellipsis) {
    border: 1px solid #d9d9d9;
  }
  &:hover,
  &.active {
    color: #1890ff;
    border-color: #1890ff;
  }
  &.disabled {
    color: rgba(0, 0, 0, 0.25);
    border-color: #d9d9d9;
    cursor: not-allowed;
  }
}
</style>

<script>
export default {
  /**
   * @param { number } value 父组件v-model绑定的字段，对应当前显示的页数
   * @param { boolean } showAllPage 是否显示全部页码，如果为true，则分页器中不会缩略显示
   */
  props: ['value', 'showAllPage'],
  emits: ['change'],
  data() {
    return {
      current: 1, // 当前页数
      pageSize: 10, // 每页条数
      pageSizeOptions: ['10', '20', '30', '40'], // 指定下拉列表中每页可以显示多少条
      total: 101, // 数据总数
      pageBtnLimit: 5, // 页码显示数量限制
    };
  },
  computed: {
    // 通过数据总数total和每页条数pageSize，计算出需要分多少页
    getPageLength() {
      return Math.ceil(this.total / this.pageSize);
    },

    // 多页数时显示的页码的相关方法：
    /**
     * 获取当前页左侧应该显示的页码
     * @returns {number:[]} 页码的数组
     */
    getCurrentLeft() {
      const btnNumber = Math.floor(this.pageBtnLimit / 2);
      const arr = [];
      for (let i = 1; i <= btnNumber && this.current - i > 0; i++) {
        arr.unshift(i);
      }
      return arr;
    },
    /**
     * 获取当前页右侧应该显示的页码
     * @returns {number:[]} 页码的数组
     */
    getCurrentRight() {
      const btnNumber = Math.floor(this.pageBtnLimit / 2);
      const arr = [];
      for (let i = this.current + 1; i <= this.getPageLength && i <= this.current + btnNumber; i++) {
        arr.push(i);
      }
      return arr;
    },
  },
  methods: {
    /**
     * 翻页时触发的方法
     * @param {Number} index 翻到的页数
     *
     * 防止使用了v-model绑定之后忘记赋值导致可以换页码但是数据不变的问题出现，将value重新赋值给current
     * 使用nextTick保证current和绑定到组件的value（v-model）值同步变化
     */
    changePage(index) {
      if (index === this.current) {
        return;
      }
      if (this.value !== undefined) {
        this.$emit('change', index);
        this.$nextTick(() => {
          this.current = this.value;
        });
      } else {
        this.current = index;
      }
    },
    /**
     * 点击上一页或点击下一页
     * @param {Number} type 0上一页 1下一页
     */
    prveOrNext(type) {
      if (type) {
        if (this.current === this.getPageLength) {
          return;
        }
        this.current++;
      } else {
        if (this.current === 1) {
          return;
        }
        this.current--;
      }
      this.$emit('change', this.current);
    },
  },
};
</script>
