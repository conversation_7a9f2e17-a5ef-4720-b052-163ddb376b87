<template>
  <div class="html-content cpt-explain markdown-body" v-html="textHtml"></div>
</template>

<script>
import { GET } from '@/request';

export default {
  name: 'HtmlViewer',

  props: {
    textUrl: {
      type: String,
      default: null,
    },
    textValue: {
      type: String,
      default: null,
    },
    value: {
      type: String,
      default: '',
    },
  },
  emits: ['updateRichLoading', 'onError'],
  data() {
    return {
      textHtml: '',
    };
  },
  watch: {
    textUrl: {
      handler() {
        if (this.textUrl) {
          this.$emit('updateRichLoading', true);
          GET(this.textUrl, {})
            .then((res) => {
              this.textHtml = res;
              this.$emit('updateRichLoading', false);
            })
            .catch((err) => this.$emit('onError', err));
        } else {
          if (this.textValue) {
            this.textHtml = this.textValue;
          }
        }
      },
      immediate: true,
    },
  },
  created() {
    if (this.value) {
      this.textHtml = this.value;
    }
  },
};
</script>

<style lang="less" scoped>
.cpt-explain {
  color: #606972;
  white-space: pre-line;
}
</style>
