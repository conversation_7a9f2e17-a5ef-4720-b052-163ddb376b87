<template>
  <section class="process-section">
    <div class="teach-process">
      <h2 class="process-title">教学流程</h2>
      <div class="process-graph">
        <template v-for="item in process">
          <div class="process-col" :key="item.description">
            <img :src="item.src" class="process-img" alt="" />
            <div class="process-description">
              <span>{{ item.description }}</span>
            </div>
          </div>
          <div :key="item.description + 'img'">
            <img :src="processArror" v-if="item.arror !== false" alt="" style="width: 88px; margin-top: 50%" />
          </div>
        </template>
      </div>

      <div class="course-route" v-if="$keycloak && $keycloak.authenticated && $keycloak.idTokenParsed.DLP_USER_ALLOW_PUBLISH_OPEN_COURSE == '1'">
        <a-button class="course-route-btn" @click="handleCourseClick">我开设的课程</a-button>
      </div>
    </div>
  </section>
</template>

<script>
const teachCreateImg = require('@/assets/image/teaching/teach-create.png');
const importStudentImg = require('@/assets/image/teaching/import-student.png');
const teachContentImg = require('@/assets/image/teaching/teach-content.png');
const teachPublishImg = require('@/assets/image/teaching/teach-publish.png');
const processArror = require('@/assets/image/teaching/process-arror.png');
import { mapState } from 'vuex';
export default {
  name: 'TeachProcess',
  data() {
    return {
      processArror,
      process: [
        {
          description: 'Step1：创建课程',
          src: teachCreateImg,
        },
        {
          description: 'Step2：录入学生',
          src: importStudentImg,
        },
        {
          description: 'Step3：发布教学内容',
          src: teachContentImg,
        },
        {
          description: 'Step4：发布教学评测',
          arror: false,
          src: teachPublishImg,
        },
      ],
    };
  },
  computed: {
    ...mapState(['userInfo']),
  },
  methods: {
    handleCourseClick() {
      if (this.userInfo.fullName && this.userInfo.introduction && this.userInfo.image) {
        this.$router.push({
          path: '/course/teaching/mycourses',
        });
      } else {
        this.$router.push({
          path: '/user-center?edit=true&redirectUrl=course/teaching/mycourses',
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.process-section {
  background-color: #f6f9fb;
  height: 460px;
  padding: 56px 0px 64px 0px;
}
.teach-process {
  margin: 0px auto;
  width: 1200px;
  height: 100%;
  position: relative;
  .process-title {
    margin-bottom: 64px;
    text-align: center;
    font-size: 32px;
    color: #121f2c;
  }
  .process-graph {
    display: flex;
    justify-content: space-between;
    .process-col {
      display: flex;
      flex-direction: column;
      align-items: center;
      .process-img {
        height: 56px;
        width: 56px;
        margin-bottom: 16px;
      }
    }
    .process-description {
      font-size: 18px;
      color: #121f2c;
    }
  }
  .course-route {
    position: absolute;
    bottom: 0px;
    left: 50%;
    margin-left: -72px;
    text-align: center;
    .course-route-btn {
      width: 144px;
      height: 40px;
      border-color: #0082ff;
      color: #0082ff;
      &:hover {
        background-color: #f0f7ff;
      }
    }
  }
}
</style>
