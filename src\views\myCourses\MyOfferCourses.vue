<template>
  <div class="my-courses">
    <user-introduction></user-introduction>

    <div class="mycourses-introduction jt-box-shadow">
      <div class="introduction-header">
        <div class="select-bargroup">
          <div :class="['select-bar', '3' === select ? 'current-bar' : '']" @click="handleTabClick('3')">
            <span>全部 {{ counts[0] }}</span>
          </div>
          <div :class="['select-bar', '1' === select ? 'current-bar' : '']" @click="handleTabClick('1')">
            <span>进行中 {{ counts[2] }}</span>
          </div>
          <div :class="['select-bar', '0' === select ? 'current-bar' : '']" @click="handleTabClick('0')">
            <span>即将开始 {{ counts[1] }}</span>
          </div>
          <div :class="['select-bar', '2' === select ? 'current-bar' : '']" @click="handleTabClick('2')">
            <span>已结束 {{ counts[3] }}</span>
          </div>
          <div :class="`bottom-bar bottom-active-${select}`"></div>
        </div>

        <div>
          <a-space>
            <a-input allow-clear placeholder="搜索课程" style="width: 240px; height: 32px" @change="handleCourseSearch">
              <template #prefix>
                <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
              </template>
            </a-input>
            <a-button type="primary" style="width: 112px; height: 32px" @click="hanldeCreateCourse">
              <PlusOutlined />
              创建课程
            </a-button>
          </a-space>
        </div>
      </div>
      <courses-list :course-total="counts[0]" :course-status="select" :search-value="searchValue" />
    </div>
  </div>
</template>
<script setup>
import { PlusOutlined } from '@ant-design/icons-vue';
</script>

<script>
import _ from 'lodash';
import UserIntroduction from './UserIntroduction.vue';
import CoursesList from './CoursesList.vue';

import { courseCount } from '@/apis/teaching.js';

export default {
  name: 'MyOfferCourses',
  components: {
    UserIntroduction,
    CoursesList,
  },
  data() {
    return {
      select: '3', // 3 全部 1 进行中  0即将开始  2结束
      counts: [0, 0, 0, 0],
      searchValue: '',
    };
  },
  methods: {
    async getCourseCount() {
      const res = await courseCount({});
      if (res.state === 'OK' && res.body.length > 0) {
        this.counts = res.body;
      } else {
        this.counts = [0, 0, 0, 0];
      }
    },
    hanldeCreateCourse() {
      this.$store.commit('course/SET_CURRENTACTIVECOURSE_DATA', {});
      this.$router.push({
        path: '/course/teaching/mycourses/create-course',
      });
    },
    handleCourseSearch: _.debounce(function (e) {
      this.searchValue = e.target.value;
    }, 500),

    handleTabClick(select) {
      this.select = select;
    },
  },
  mounted() {
    // 进入课程列表置空当前选中course
    this.$store.commit('course/SET_CURRENTACTIVECOURSE_DATA', {});
    this.getCourseCount();
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.my-courses {
  .mycourses-introduction {
    width: 1200px;
    margin: 20px auto 0px;
    background-color: white;
    border-radius: 0px 0px 2px 2px;
    .introduction-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 67px;
      border-bottom: 1px solid #e0e1e1;
      margin: 0px 32px;
      .select-bargroup {
        display: flex;
        align-items: center;
        position: relative;
      }
      .select-bar {
        width: 160px;
        text-align: center;
        height: 67px;
        line-height: 67px;
        span {
          font-weight: @jt-font-weight-medium;
          cursor: pointer;
          font-size: 18px;
        }
        &:hover {
          color: #0082ff;
        }
      }
      .current-bar {
        color: #0082ff;
      }
      .bottom-bar {
        width: 160px;
        height: 2px;
        background-color: #0082ff;
        position: absolute;
        bottom: 0px;
        transition: all 0.3s;
      }
      .bottom-active-0 {
        left: 320px;
      }
      .bottom-active-1 {
        left: 160px;
      }
      .bottom-active-2 {
        left: 480px;
      }
      .bottom-active-3 {
        left: 0px;
      }
    }
  }
}
</style>
