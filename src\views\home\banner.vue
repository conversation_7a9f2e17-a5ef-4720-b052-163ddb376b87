<template>
  <div class="dataCenter">
    <div class="window" @mouseover="stop" @mouseleave="play">
      <ul class="container" :style="containerStyle">
        <div v-for="(item, index) in wrappedBanners" :key="index" class="swiper-slides item" :style="{ width: imgWidth + 'px' }">
          <div class="inner">
            <div class="text" :class="item.textClass">
              <p v-if="item.h1">{{ item.h1 }}</p>
              <span v-if="item.h2">{{ item.h2 }}</span>
              <strong v-if="item.p2">
                <p>{{ item.p }}</p>
                <p>{{ item.p2 }}</p>
              </strong>
              <strong v-else>{{ item.p }}</strong>

              <slot v-if="!item.unstarted">
                <button v-if="item.btn" class="action-btn" :class="item.btnClass" @click="toDetail(item)">
                  {{ item.btn }}
                </button>
              </slot>
              <slot v-else>
                <a-tooltip>
                  <template #title>敬请期待</template>
                  <button v-if="item.btn" class="action-btn" :class="item.btnClass" @click="toDetail(item)">
                    {{ item.btn }}
                  </button>
                </a-tooltip>
              </slot>
            </div>
            <div class="img-container" :class="item.imgClass">
              <img :src="item.img" class="img" alt />
            </div>
          </div>
        </div>
      </ul>

      <!-- 小圆点 -->
      <ul v-if="amendmentBanners.length > 1" class="dots">
        <div class="dots-container">
          <li v-for="(dot, i) in amendmentBanners" :key="i" :class="{ dotted: i === currentIndex - 1 }" @click="jump(i + 1)"></li>
          <div class="process">
            <div :style="{ width: health + '%', left: `${linePro}px` }"></div>
          </div>
        </div>
      </ul>
    </div>
  </div>
</template>

<script>
import { getLocalConfig } from '@/config';
import { list } from './banners';
import { mapState } from 'vuex';

const SPEED = 30;
const INTERVAL = 1;

export default {
  name: 'HomeBanner',

  data() {
    return {
      initialInterval: INTERVAL,
      banners: list,
      windowWidth: document.documentElement.clientWidth, //实时屏幕宽度
      windowHeight: document.documentElement.clientHeight, //实时屏幕高度
      imgWidth: 0, // 图片宽度
      currentIndex: 1, // 原点起始位置
      distance: -0, // 外层嵌套的初始移动距离
      transitionEnd: true, // 防止多次快速点击切换出现问题的闸门
      speed: SPEED,
      timer: null, // 定时器
      maskBol: false,
      health: 0,
      eable: false,
      timers: null,
      count: 5,
      isPause: false,
      linePro: 30, //进度条当前的位置
      amendmentBanners: [],
    };
  },
  computed: {
    ...mapState(['giveBeansByCourse']),
    containerStyle() {
      return {
        transform: `translate3d(${this.distance}px, 0, 0)`,
      };
    },
    interval() {
      return this.initialInterval * 5000;
    },
    firstItem() {
      return this.amendmentBanners[0];
    },
    lastItem() {
      return this.amendmentBanners[this.amendmentBanners.length - 1];
    },
    wrappedBanners() {
      return [this.lastItem, ...this.amendmentBanners, this.firstItem];
    },
    bannerCurrentIndex() {
      return this.currentIndex - 1;
    },
  },
  watch: {
    giveBeansByCourse() {
      this.initGiveBeanActivity();
    },
  },
  created() {
    this.amendmentBanners = [...this.banners];
    this.init();
    this.timers = setInterval(this.sub, 10);
    this.initSize();

    this.$nextTick(() => {
      window.addEventListener('resize', () => {
        this.initSize();
      });
    });
  },
  beforeUnmount() {
    clearInterval(this.timers);
  },
  methods: {
    initSize() {
      window.fullHeight = document.documentElement.clientHeight;
      window.fullWidth = document.documentElement.clientWidth;
      this.windowHeight = window.fullHeight; // 高
      this.windowWidth = window.fullWidth; // 宽
      this.imgWidth = this.windowWidth;
      this.distance = -this.windowWidth * this.currentIndex;
    },
    initGiveBeanActivity() {
      this.amendmentBanners = [...this.banners];
      if (this.giveBeansByCourse.feature) {
        this.amendmentBanners[1] = {
          id: '1',
          h1: '',
          h2: '人工智能课程陆续上新啦',
          p: '联手名师名校，新增多门课程',
          p2: '丰富内容，边学边练，更有算力赠送',
          btn: '立即查看',
          img: require('@/assets/image/home/<USER>'),
          jumpPath: '/course/course-list',
        };
      }
    },
    toDetail(item) {
      if (item.flag === 'model-training') {
        window.open(`${getLocalConfig('CONSOLE_URL')}/home/<USER>
        return;
      }
      if (item.jumpPath) {
        if (item.isCrossPlat) {
          window.open(item.jumpPath);
          return;
        }
        this.$router.push({
          path: item.jumpPath,
        });
      }
    },
    sub() {
      if (this.isPause == false) {
        this.health += 1 / this.count;

        if (this.health >= 100) {
          clearInterval(this.timers);
          this.move(this.imgWidth, -1, this.speed);
          this.health = 0;
        }
      }
    },

    init() {
      this.plays();
    },
    move(offset, direction, speed) {
      // 移动一次的距离， 向左还是向右移动， 图片移动速度
      if (this.banners.length <= 1) return;
      if (!this.transitionEnd) return;
      this.transitionEnd = false;
      direction === -1 ? (this.currentIndex += offset / this.imgWidth) : (this.currentIndex -= offset / this.imgWidth);
      if (this.currentIndex > this.banners.length) this.currentIndex = 1;
      if (this.currentIndex < 1) this.currentIndex = this.banners.length;

      const destination = this.distance + offset * direction;
      this.animate(destination, direction, speed);
      clearInterval(this.timers);
      this.health = 0;
      this.timers = setInterval(this.sub, 10);

      if (this.bannerCurrentIndex == 0) {
        this.linePro = 30;
      } else if (this.bannerCurrentIndex == 1) {
        this.linePro = 70;
      } else if (this.bannerCurrentIndex == 2) {
        this.linePro = 110;
      } else if (this.bannerCurrentIndex == 3) {
        this.linePro = 150;
      } else if (this.bannerCurrentIndex == 4) {
        this.linePro = 190;
      } else if (this.bannerCurrentIndex == 5) {
        this.linePro = 230;
      }
    },
    animate(des, direc, speed) {
      // 实际移动距离 想左还是向右 移动速度 负右正左
      if (this.temp) {
        window.clearInterval(this.temp);
        this.temp = null;
      }
      this.temp = window.setInterval(() => {
        if ((direc === -1 && des < this.distance) || (direc === 1 && des > this.distance)) {
          this.distance += speed * direc;
        } else {
          this.transitionEnd = true;
          window.clearInterval(this.temp);
          this.distance = des;
          let allWidth = this.banners.length * this.imgWidth;
          if (des < -allWidth) this.distance = -this.imgWidth;
          if (des > -this.imgWidth) this.distance = -allWidth;
        }
      }, 10);
    },
    jump(index) {
      const direction = index - this.currentIndex >= 0 ? -1 : 1;
      const offset = Math.abs(index - this.currentIndex) * this.imgWidth;
      const jumpSpeed = Math.abs(index - this.currentIndex) === 0 ? this.speed : Math.abs(index - this.currentIndex) * this.speed;
      this.move(offset, direction, jumpSpeed);
    },
    // 已进入页面就开始 动
    plays() {
      if (this.banners.length > 1) {
        if (!this.maskBol) {
          this.isPause = false;
          if (this.timer) {
            window.clearInterval(this.timer);
            this.timer = null;
          }
          this.timer = window.setInterval(() => {
            this.move(this.imgWidth, -1, this.speed);
          }, this.interval);
        }
      }
    },
    // 自动播放函数
    play() {
      if (this.banners.length > 1) {
        this.isPause = false;
        if (!this.maskBol) {
          if (this.timer) {
            window.clearInterval(this.timer);
            this.timer = null;
          }
          if (this.timers) {
            clearInterval(this.timers);
            this.timers = null;
          }
          this.timers = setInterval(this.sub, 10);
        }
      }
    },
    stop() {
      this.isPause = true;
      window.clearInterval(this.timer);
      this.timer = null;
      clearInterval(this.timers);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.dataCenter {
  // text-align: center;
  height: 460px;
  .window {
    position: relative;
    // width: 1200px;
    height: 460px;
    // margin: 0 auto;
    overflow: hidden;
    .container {
      display: flex;
      position: absolute;
    }
    .left,
    .right {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      cursor: pointer;
    }
    .left {
      left: 3%;
      padding-left: 12px;
      padding-top: 10px;
    }
    .right {
      right: 3%;
      padding-right: 12px;
      padding-top: 10px;
    }
    img {
      user-select: none;
    }
  }
  .dots {
    position: absolute;
    bottom: 32px;
    width: 100%;
    // left: 50%;
    // transform: translateX(-50%);
  }
  .dots li {
    display: inline-block;
    width: 32px;
    height: 4px;
    margin-right: 8px;
    background-color: #fff;
    cursor: pointer;
  }
  .mask-div {
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    .mask-img {
      margin-top: calc(50vh - 200px);
    }
  }
}

.process {
  width: 32px;
  height: 4px;
  margin-right: 8px;
  position: absolute;
  top: 10px;
  margin-left: -30px;
  // border: black 4px solid;
}

.process div {
  height: 4px;
  width: 32px;
  position: absolute;
  background: #0082ff;
}

.swiper-slides {
  width: 100%;
  height: 460px;
  padding: 43px 0px 61px 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 80px;
  overflow: hidden;
  .inner {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 1200px;
    margin: 0 auto;
  }
  &:nth-of-type(1) {
    background: #d1efff;
  }
  &:nth-of-type(2) {
    background: rgba(0, 130, 255, 0.18);
  }
  &:nth-of-type(3) {
    background: #d1efff;
  }
  &:nth-of-type(4) {
    background: #d6e7ff;
  }
  &:nth-of-type(5) {
    background: #d1e8ff;
  }
  &:nth-of-type(6) {
    background: rgba(0, 130, 255, 0.18);
  }
  &:nth-of-type(7) {
    background: rgba(0, 130, 255, 0.18);
  }

  .img-container {
    width: 460px;
    height: 216px;
    .img {
      width: 460px;
      height: 100%;
    }
    &.gain-suanli {
      // position: relative;
      width: 1069px !important;
      height: 405px !important;
      position: absolute;
      .text {
        display: flex;
        flex-direction: column;
      }
      .img {
        width: 1069px !important;
        height: 405px !important;
        position: absolute;
        left: 168px;
        object-fit: contain;
      }
    }
    &.automation-modeling {
      width: 503px !important;
      height: 380px !important;
      position: relative;
      top: 30px;
      right: 0px;
      .img {
        width: 503px !important;
        height: 380px !important;
      }
    }
  }

  .text {
    &.gain-suanli {
      display: flex;
      flex-direction: column;
    }

    > p {
      height: 67px;
      font-size: 48px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 67px;
    }
    > span {
      // height: 67px;
      font-size: 48px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 67px;
      display: inline-block;
    }
    > strong {
      width: 626px;
      // height: 82px;
      font-size: 16px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 26px;
      margin-top: 16px;
      margin-bottom: 32px;
      display: inline-block;

      &:nth-of-type(2) {
        color: red;
        margin-top: 0px;
        margin-bottom: 0px;
      }
    }
  }
}

// banner 的立即查看
.action-btn {
  width: 128px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: #0082ff;
  border-radius: 2px;
  color: #ffffff;
  cursor: pointer;
  display: block;
  border: none;
  outline: none;
  z-index: 2;
  &.not-allowed {
    cursor: not-allowed;
  }
}
.slide {
  width: 1024px;
  height: 320px;
  margin: 0 auto;
  margin-top: 50px;
  overflow: hidden;
  position: relative;
}
.slideshow {
  width: 1024px;
  height: 320px;
}

.bar {
  position: absolute;
  width: 100%;
  bottom: 10px;
  margin: 0 auto;
  z-index: 10;
  text-align: center;
}
.bar span {
  width: 20px;
  height: 5px;
  border: 1px solid;
  background: white;
  display: inline-block;
  margin-right: 10px;
}
.active {
  background: red !important;
}
.image-enter-active {
  transform: translateX(0);
  transition: all 1.5s ease;
}
.image-leave-active {
  transform: translateX(-100%);
  transition: all 1.5s ease;
}
.image-enter {
  transform: translateX(100%);
}
.image-leave {
  transform: translateX(0);
}

.dots-container {
  overflow: hidden;
  width: 1200px;
  margin: 0 auto;
}
</style>
