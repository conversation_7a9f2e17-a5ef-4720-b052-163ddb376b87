<template>
  <div>
    <invite-register v-if="isOnH5"></invite-register>
  </div>
</template>

<script>
import inviteRegister from './inviteRegister';
import { BrowserDetect } from '@/utils/browserdetect';

export default {
  components: {
    inviteRegister,
  },
  data() {
    return {
      isOnH5: false,
    };
  },
  created() {
    const isOnH5 = BrowserDetect.onH5();
    if (!isOnH5) {
      this.$router.replace({ path: '/invite-register', query: { token: this.$route.query.token } });
      return;
    }
    this.isOnH5 = isOnH5;
  },
};
</script>
