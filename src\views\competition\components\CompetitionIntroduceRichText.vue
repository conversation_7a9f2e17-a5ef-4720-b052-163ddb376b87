<template>
  <div class="cpt-explain markdown-body" v-html="richText"></div>
</template>
<script>
import API from '@/constants/api/API_competition_model.js';
export default {
  props: {
    url: String,
  },
  emits: ['updateRichLoading'],
  data() {
    return {
      richText: '',
    };
  },
  created() {
    this.getRichText();
  },
  methods: {
    getRichText() {
      this.$emit('updateRichLoading', true);
      API.getCompetitionDescInfo(this.url).then((res) => {
        this.richText = res;
        this.$emit('updateRichLoading', false);
      });
    },
  },
};
</script>
