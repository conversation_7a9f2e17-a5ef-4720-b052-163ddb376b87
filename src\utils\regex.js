// 电话号码
export const telephoneNumberRegex = /^1[3456789]\d{9}$/;
// 6位验证码
export const vetifyCodeRegex = /^\d{6}$/;
// 中英文或空格
export const chineseOrLetterOrBlankRegex = /^[\u4e00-\u9fa5 a-zA-Z]+$/;
// 邮件
export const emailRegex = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
// 数字字母或下划线
export const numberOrLetterOrLineRegex = /^\w+$/;
// 毕晟密码
export const passwordRegex = /^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,20}$/;
// 学校格式的校验
export const schoolInputRegex = /^[\u4e00-\u9fa5a-zA-Z\s()（）]*$/;
