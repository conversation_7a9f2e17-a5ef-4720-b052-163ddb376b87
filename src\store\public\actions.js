import { GET } from '@/request';
import { courseModelApi } from '@/apis';

const actions = {
  getUserCount({ commit }) {
    GET('/keycloak/web/user/countuser').then((res) => {
      if (res.errorCode) {
        return;
      }
      commit('SET_USER_COUNT', res.body);
    });
  },
  setGlobalLoading({ commit }, loading) {
    commit('SET_GLOBAL_LOADING', loading);
  },
  setGiveBeansByCourse({ commit }) {
    courseModelApi.getGiveBeansByCourse().then((res) => {
      if (res.state === 'OK') {
        commit('SET_GIVEBEANSBYCOURSE', res.body);
      }
    });
  },
};
export default actions;
