<template>
  <div class="jt-alarm-banner">
    <svg t="1646385844421" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3519" width="14" height="14"><path d="M492.763429 0C220.745143 0 0 218.477714 0 487.643429c0 269.165714 220.745143 487.570286 492.763429 487.570285 272.018286 0 492.763429-218.404571 492.763428-487.570285S764.781714 0 492.763429 0z m-49.298286 682.642286c0-26.916571 21.869714-48.713143 48.786286-48.713143h1.024a48.786286 48.786286 0 0 1 0 97.499428H492.251429a48.786286 48.786286 0 0 1-48.786286-48.786285z m0-195.510857V293.083429a49.298286 49.298286 0 1 1 98.523428 0V487.131429a49.298286 49.298286 0 1 1-98.523428 0z" fill="#FAAD14" p-id="3520"></path></svg>
    <slot
      ><p>{{ title }}</p></slot
    >
  </div>
</template>
<!--
  橙色告警组件（温馨提示横幅）
  <Alarm :title="温馨提示内容"/>
  OR
  <Alarm>
      <p>{{温馨提示内容}}</p>
  </Alarm>
 -->
<script>
export default {
  name: 'Alarm',
  props: {
    title: String,
  },
};
</script>

<style lang="less" scoped>
.jt-alarm-banner {
  min-height: 40px;
  display: flex;
  font-size: 14px;
  color: #333333;
  border-radius: 2px;
  align-items: center;
  background: #fef6e7;
  box-sizing: border-box;
  border: 1px solid #ffd666;

  .icon {
    margin: 0 9px 0 19px;
    color: #ff9d00;
  }
}

.jt-alarm-banner p {
  flex: 1;
}
</style>
