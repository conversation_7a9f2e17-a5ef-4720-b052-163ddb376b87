<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div class="competition-system-introedit">
    <div class="edit-header">{{ isEdit ? '编辑' : '预览' }}赛制介绍</div>
    <a-spin :spinning="spinning">
      <div class="intro-edit-container">
        <div v-for="(item, index) in introList" v-show="isEdit" :key="item.id" class="intro-edit-item" :class="index !== 0 && 'edit-item-line'">
          <div class="paragraph-name">
            <div>
              <span class="auto-prefix" style="width: 86px; display: inline-block">章节名称</span>
              <validateInput :ref="richtextContainerId + item.id + 'input'" v-model:value="item.inputValue" style="width: 560px" />
            </div>
            <a-button class="delete-btn" ghost style="width: 86px" type="primary" danger @click="() => deleteParagraph(index, item)"><jt-icon type="iconshanchu1" />删除</a-button>
          </div>
          <div class="paragraph-content">
            <span class="auto-prefix" style="width: 86px">章节内容</span>
            <div class="paragraph-content-editor"><richtextEditor :id="richtextContainerId + item.id" :ref="richtextContainerId + item.id + 'editor'" v-model="item.editorValue" :validate="true" :url="item.editorUrl" /></div>
          </div>
        </div>
        <div v-show="isEdit" :class="{ 'add-paragraph-btn': true, mgt40: introList.length === 0 }" @click="handleAddparagraph"><jt-icon type="icontianjia" style="padding-right: 11px" />添加章节</div>
        <div v-if="!isEdit" style="padding-top: 24px">
          <competitionSystemIntro :show-edit-btn="false" :data-list="introList" />
        </div>
        <a-space :style="`margin-top: 32px; padding-left: ${isEdit && introList.length > 0 ? '86px' : '0px'}`">
          <a-button :disabled="!previewEnable" type="primary" style="width: 120px" @click="handleSaveOrPreview">{{ isEdit ? '预览' : hasPublished ? '保存并发布' : '保存' }}</a-button>
          <a-button style="width: 88px" @click="handleCancelOrBack">{{ isEdit ? '取消' : '返回编辑' }}</a-button>
        </a-space>
      </div>
    </a-spin>
    <confir-modal :visible="modalVisible" type="danger" :title="deleteTitle" @ok="handleOk" @cancel="handleCancel">
      <template #icon>
        <ExclamationCircleFilled style="font-size: 18px; color: #ff454d" />
      </template>
      <p style="margin-bottom: 32px; font-size: 13px">删除后将不可恢复，请谨慎操作</p>
    </confir-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import richtextEditor from '@/components/richtextEditor.vue';
import confirModal from '@/components/confirmModal/index.vue';
import validateInput from '../../components/validateInput.vue';
import competitionSystemIntro from '../competitionSystemIntro.vue';
import { competitionApi } from '@/apis/index';
import { publishStatusKeys } from '../../../competitionConfig';
const richtextContainerId = 'competition-systemtroedit-container';
let editIdIndex = 0;
export default {
  name: 'CompetitionSystemIntroEdit',
  components: {
    richtextEditor,
    confirModal,
    validateInput,
    competitionSystemIntro,
  },
  data() {
    return {
      isEdit: true,
      richtextContainerId,
      modalVisible: false,
      selectIndex: undefined,
      selectTitle: '',
      spinning: false,
      introList: [
        {
          id: ++editIdIndex,
          inputValue: '',
          editorValue: '',
          editorUrl: '',
        },
      ],
      previewEnable: true,
    };
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
    hasPublished() {
      return this.currentManageCompetition.releaseSta === publishStatusKeys.PUBLISHED;
    },
    deleteTitle() {
      return this.selectTitle ? `确定删除章节"${this.selectTitle}"吗？` : '确定删除章节吗？';
    },
  },
  watch: {
    introList: {
      handler(val) {
        if (val.length === 0) {
          this.previewEnable = true;
          return;
        }
        const bol = val.every((intro) => {
          return intro.inputValue.length > 0 && intro.inputValue.length < 21 && intro.editorValue;
        });
        this.previewEnable = bol;
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.initData();
  },
  methods: {
    handleAddparagraph() {
      if (this.introList.length > 20) {
        this.$message.error('最多显示20个章节');
        return;
      }
      this.introList.push({
        id: ++editIdIndex,
        inputValue: '',
        editorValue: '',
        editorUrl: '',
      });
    },
    async initData() {
      this.spinning = true;
      const cid = this.$route.params.competitionId;
      const res = await competitionApi.getCompetitionDesAndIntro({ cid });
      if (res.state === 'OK' && res.body.descriptionPathJson && res.body.descriptionPathJson.length > 0) {
        this.introList = res.body.descriptionPathJson.map((item) => {
          return {
            id: ++editIdIndex,
            inputValue: item.title,
            editorValue: '',
            editorUrl: item.content,
          };
        });
      }
      this.spinning = false;
    },
    handleOk() {
      if (typeof this.selectIndex === 'number' && this.selectIndex >= 0) {
        this.introList.splice(this.selectIndex, 1);
      }
      this.modalVisible = false;
      this.selectIndex = undefined;
      this.selectTitle = '';
    },
    handleCancel() {
      this.modalVisible = false;
      this.selectIndex = undefined;
      this.selectTitle = '';
    },
    handleSaveOrPreview() {
      console.log('handleSaveOrPreview', this.introList);
      if (this.isEdit) {
        if (this.validateForm()) {
          this.isEdit = false;
        }
      } else {
        this.publish();
      }
    },
    async publish() {
      this.spinning = true;
      const promises = this.introList.map((item) => {
        return competitionApi.textUpload({ baseString: item.editorValue });
      });
      const resArr = await Promise.all(promises);
      const submitData = [];
      for (let i = 0; i < resArr.length; i++) {
        if (resArr[i].state === 'OK') {
          submitData.push({
            title: this.introList[i].inputValue,
            content: resArr[i].body.url,
          });
        } else {
          this.$message.error('编辑赛制介绍失败');
          this.spinning = false;
          return;
        }
      }
      const updateRes = await competitionApi.updateCompetitionRefData({
        cid: this.$route.params.competitionId,
        type: 1,
        path: submitData.length > 0 ? JSON.stringify(submitData) : '',
      });
      if (updateRes.state === 'OK') {
        this.$message.success('编辑赛制介绍成功');
        this.backToTab(true);
      } else {
        this.$message.error('编辑赛制介绍失败');
      }
      this.spinning = false;
    },
    validateForm() {
      debugger;
      for (let i = 0; i < this.introList.length; i++) {
        const id = this.introList[i].id;
        const inputRef = this.$refs[this.richtextContainerId + id + 'input'][0];
        const editorRef = this.$refs[this.richtextContainerId + id + 'editor'][0];
        const inputValidate = inputRef.validator();
        const editValidate = editorRef.validator();
        if (!inputValidate || !editValidate) {
          return false;
        }
      }
      return true;
    },
    handleCancelOrBack() {
      if (this.isEdit) {
        this.backToTab();
      } else {
        this.isEdit = true;
      }
    },
    deleteParagraph(index, item) {
      this.selectIndex = index;
      this.selectTitle = item.inputValue;
      this.modalVisible = true;
    },
    backToTab(noLeaveConfirm) {
      const { competitionId } = this.$route.params;
      this.$router.push({
        path: `/competition/competition-management/${competitionId}`,
        query: {
          tabId: '2',
          subtabId: '2',
          noLeaveConfirm,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.competition-system-introedit {
  .edit-header {
    .competition-edit-header();
  }
  .intro-edit-container {
    padding: 0px 32px 64px;
  }
  .intro-edit-item {
    padding: 32px 0px 40px;
    .paragraph-name {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .delete-btn {
        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }
    .paragraph-content {
      display: flex;
      margin-top: 32px;
      .paragraph-content-editor {
        width: calc(100% - 80px);
      }
    }
  }
  .edit-item-line {
    border-top: 1px solid #e0e1e1;
  }
  .intro-edit-item.edit-item-line {
    padding-top: 40px;
  }
  .add-paragraph-btn {
    text-align: center;
    height: 40px;
    line-height: 40px;
    border: 1px dashed #0082ff;
    font-size: 14px;
    color: #0082ff;
    cursor: pointer;
    transition: 0.3s all;
    &:hover {
      background-color: #f0f8ff;
    }
  }
  .mgt40 {
    margin-top: 40px;
  }
  .auto-prefix:before {
    content: '* ';
    color: red;
  }
}
</style>
