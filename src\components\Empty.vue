<template>
  <div class="wrap">
    <jt-empty class="empty" :image="emptyImage" :image-style="imageStyle">
      <template #description>
        <span class="description">
          {{ description || '暂无结果' }}
        </span>
      </template>
    </jt-empty>
  </div>
</template>
<script>
import { Empty as JtEmpty } from 'ant-design-vue';
export default {
  components: {
    JtEmpty,
  },
  props: {
    description: { type: String, default: '' },
  },
  data() {
    return {
      emptyImage: require('@/assets/image/empty2x.png'),
      imageStyle: {
        height: '416px',
        'margin-bottom': '0px',
      },
    };
  },
};
</script>
<style lang="less" scoped>
.empty {
  padding: 32px;
  position: relative;
  margin-bottom: 0;
}
.description {
  line-height: 20px;
  font-size: 18px;
  font-weight: bold;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 105px;
  margin: auto;
}
</style>
