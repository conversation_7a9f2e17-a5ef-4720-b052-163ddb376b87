<template>
  <div class="button-filter-container">
    <div>{{ label }}</div>
    <a-radio-group v-model:value="valueData" button-style="solid" @change="onChange">
      <a-radio-button v-for="(item, i) of options" :key="i" class="button-filter-item" :value="item.value"> {{ item.label }} </a-radio-button>
    </a-radio-group>
  </div>
</template>

<script>
export default {
  name: 'ButttonFilter',
  props: {
    value: { type: String, default: '' },
    options: { type: Array, default: () => [] },
    change: { type: Function, default: () => {} },
    label: { type: String, default: '' },
  },
  emits: ['change', 'update:value'],
  data() {
    return {
      valueData: '',
    };
  },
  watch: {
    value(val) {
      this.valueData = val;
    },
  },
  methods: {
    onChange(val) {
      this.$emit('update:value', val.target.value);
      this.$emit('change', val.target.value);
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.ant-radio-button-wrapper) {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 28px;
  margin-left: 12px;
  border: none;
  &::before {
    display: none;
  }
}
:deep(.ant-radio-group) {
  display: flex;
  align-items: center;
}
.button-filter-container {
  display: flex;
  align-items: center;
  .button-filter-item {
    width: auto;
    padding-left: 20px;
    padding-right: 20px;
    height: 32px;
  }
}
</style>
