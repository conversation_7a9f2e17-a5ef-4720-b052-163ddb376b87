<template>
  <div style="display: block">
    <a-spin :spinning="loading" tip="加载中">
      <jt-content-with-empty :loading="loading" :empty-image="emptyImage" :image-style="imageStyle" :empty="empty" :empty-style="emptyStyle" :empty-title="emptyTitle" :empty-text="emptyText">
        <slot></slot>
        <template #empty-operation>
          <slot v-if="!loading" name="empty-operation"></slot>
        </template>
      </jt-content-with-empty>
    </a-spin>
  </div>
</template>

<script>
import empty from '@/assets/image/emptys2x.png';
export default {
  props: {
    loading: Boolean,
    emptyImage: {
      default: empty,
      type: String,
    },
    imageStyle: {
      default() {
        return {
          width: '416px',
          height: '416px',
          'margin-bottom': '0px',
        };
      },
      type: Object,
    },
    empty: Boolean,
    emptyStyle: {
      type: Object,
      default() {
        return {};
      },
    },
    emptyTitle: { type: String, default: '' },
    emptyText: { type: String, default: '' },
  },
};
</script>
