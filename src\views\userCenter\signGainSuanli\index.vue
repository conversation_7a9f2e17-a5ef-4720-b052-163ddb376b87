<template>
  <div class="sign-gain-suanli swiper-slide">
    <div class="sign-gain-suanli-li">
      <div>
        <h6 class="sign-title">签到赢算力</h6>
        <span class="is-sign">
          <img src="@/assets/image/sign-gain-suanli/no-sign-today.png" v-if="!signGainSuanliToday" alt="" />
          <img src="@/assets/image/sign-gain-suanli/sign-today.png" v-else alt="" />
        </span>
      </div>
      <p class="sign-text">连续签到获得更多算力豆</p>
      <a-button type="primary" v-if="!signGainSuanliToday" @click="setSignGainSuanli" class="sign-gain-btn">签到赢算力 </a-button>
      <a-button type="primary" v-else @click="checkSignGainSuanli" class="sign-gain-btn">查看详情</a-button>

      <img src="@/assets/image/sign-gain-suanli/sign-gain-bg.png" class="img" alt="" />
    </div>
    <sign-gain-suanli-modal v-model="signGainVisible" :isCount="isCount" ref="signGainSuanliModal"> </sign-gain-suanli-modal>
  </div>
</template>
<script>
import { GET } from '@/request';
import SignGainSuanliModal from '@/views/userCenter/signGainSuanli/signGainSuanliModal.vue';
import { mapState, mapMutations } from 'vuex';

export default {
  components: {
    SignGainSuanliModal,
  },
  data() {
    return {
      signGainVisible: false,
      isCount: null,
    };
  },

  computed: {
    ...mapState('suanLiBean', ['signGainSuanliSwitch', 'signGainSuanliToday']),
  },
  methods: {
    ...mapMutations('suanLiBean', ['SET_SIGNGAINSUANLITODAY']),
    // 立即签到
    setSignGainSuanli() {
      GET('/marketing/web/checkin/set', {}).then((res) => {
        if (res.state === 'OK') {
          this.signGainVisible = true;
          this.$refs.signGainSuanliModal.getCheckinDetail();
          this.SET_SIGNGAINSUANLITODAY(true);
          this.isCount = true;
        }
      });
    },
    checkSignGainSuanli() {
      this.signGainVisible = true;
      this.isCount = false;
      this.$refs.signGainSuanliModal.getCheckinDetail();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.swiper-slide {
  width: 365px !important;
}

.sign-gain-suanli {
  min-width: 365px;
  height: 142px;
  background: linear-gradient(270deg, #fff0ea 0%, #f9ede4 100%);
  border-radius: 4px;
  padding: 24px;
  margin-right: 21px;
  position: relative;

  .img {
    width: 164px;
    height: 138px;
    position: absolute;
    right: 0;
    top: 2px;
  }

  .sign-title {
    font-size: @jt-font-size-lg;
    font-weight: 600;
    color: @jt-text-color-primary;
    line-height: 22px;
    margin-bottom: 8px;
    display: inline-block;
  }

  .sign-text {
    font-size: @jt-font-size-base;
    font-weight: @jt-font-weight;
    color: @jt-text-color;
    line-height: 20px;
    margin-bottom: 16px;
  }
}

.sign-gain-btn {
  background-color: #ff7029;
  border-color: #ff7029;

  &:hover {
    background: #f6651c;
  }
}

.is-sign {
  width: 76px;
  height: 20px;

  img {
    width: 76px;
    height: 20px;
    margin-left: 8px;
  }
}
</style>
