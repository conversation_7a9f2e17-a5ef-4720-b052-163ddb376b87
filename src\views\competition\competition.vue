<template>
  <div v-if="upgrading" class="upgrading-container">
    <div class="empty-box">
      <img :src="upgradingImage" alt="" />
      <div class="empty-title">
        <h3>{{ upgradingTitle }}</h3>
        <span>{{ upgradingContent }}</span>
      </div>
    </div>
  </div>
  <router-view v-else></router-view>
</template>

<script>
import { keycloak } from '@/keycloak';
import { getCompetitionStatus } from '../../apis/systemNotice.js';

let upgrading = false;
let upgradingTitle = '';
let upgradingContent = '';

export default {
  beforeRouteEnter(to, from, next) {
    // 登录了且无升级中访问权限
    if (keycloak.authenticated && keycloak.idTokenParsed.DLP_USER_ALLOW_ENTER_DURING_UPGRADING != '1') {
      getCompetitionStatus()
        .then((res) => {
          if (res.state === 'OK' && res.body.competitionSwitch) {
            upgrading = true;
            upgradingTitle = res.body.title || '维护升级中';
            upgradingContent = res.body.content || '等会再来看看吧';
          }
          next();
        })
        .catch(() => {
          next();
        });
    } else {
      next();
    }
  },
  data() {
    return {
      upgradingImage: require('@/assets/image/teaching/system-maintenance.png'),
      upgrading, // 比赛升级状态
      upgradingTitle,
      upgradingContent,
    };
  },
  created() {
    if (!this.$keycloak.authenticated || this.upgrading) {
      this.loading = false;
    }
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.upgrading-container {
  padding-top: 80px;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 640px;
  height: calc(100vh - @jt-footer-height + @jt-header-height);

  .empty-box {
    position: relative;
    img {
      width: 416px;
      height: 416px;
    }
    .empty-title {
      position: absolute;
      bottom: 90px;
      left: 0px;
      right: 0px;
      text-align: center;
      h3 {
        font-size: 18px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
      }
      span {
        font-size: 12px;
        color: #606972;
      }
    }
  }
}
</style>
