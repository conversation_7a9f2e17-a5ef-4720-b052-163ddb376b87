<template>
  <div class="competition-register-container">
    <bread-crumb :value="breadcrumbs"></bread-crumb>
    <div class="section">
      <div class="section-title">{{ cname }}报名</div>
      <span class="section-alert" v-if="contestSwitch">
        <alarm>
          <p>
            温馨提示：报名本比赛后，您无法报名 <span>{{ contestRejectList }}</span
            >；报名操作不可撤销，请谨慎选择报名比赛
          </p>
        </alarm>
      </span>
      <register-information></register-information>
    </div>
  </div>
</template>

<script>
import API from '@/constants/api/API.js';

import breadCrumb from '../../components/breadCrumb';
import alarm from '@/components/alarm/index.vue';
import RegisterInformation from './components/RegisterInformation';
export default {
  components: { breadCrumb, alarm, RegisterInformation },
  data() {
    return {
      cid: this.$route.query.id,
      cname: this.$route.query.name || this.formDataInfoName,
      breadcrumbs: [],
      contestSwitch: false, // 互斥是否显示
      contestRejectList: [], // 互斥列表
      formDataInfoName: '',
    };
  },
  beforeRouteEnter(to, from, next) {
    API.competition_model.getCompetitionAuthority({ cid: to.query.id }).then((res) => {
      if (res.state === 'OK' && res.body) {
        next();
      } else {
        next('/competition');
      }
    });
  },
  mounted() {
    this.competitionRejectList(); // 获取是否有互斥比赛
    this.getCompetitionInfo(); // 获取比赛名称 解决面包屑没有比赛名称的bug
  },
  methods: {
    // 报名页排斥比赛信息列表
    competitionRejectList() {
      API.competition_model.getCompetitionRejectInfo({ cid: this.cid }).then((res) => {
        if (res.state === 'OK') {
          if (res.body.sta) {
            this.contestRejectList = res.body.value?.join(',');
          }
          this.contestSwitch = res.body.sta;
        }
      });
    },

    getCompetitionInfo() {
      API.competition_model.getCompetitionInfo({ cid: this.cid }).then((res) => {
        this.hasInstanceDone = true;
        if (res.state === 'OK') {
          this.formDataInfoName = res.body.typeName;
          this.breadcrumbs.push(
            { name: '比赛', path: '/competition' },
            {
              name: this.$route.query.name || this.formDataInfoName,
              path: `/competition/competition-detail?id=${this.cid}&name=${this.formDataInfoName}`,
            },
            { name: '报名' }
          );
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.competition-register-container {
  flex-direction: column;
  align-items: center;
  min-height: 600px;
  background-color: @jt-main-bg-color;
  .section {
    width: 1200px;
    padding: 20px;
    margin: 20px auto;
    background-color: @jt-color-white;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);

    .section-title {
      height: 24px;
      font-size: @jt-font-size-lg;
      color: @jt-title-color;
      font-weight: @jt-font-weight-medium;
      margin-bottom: 24px;
    }
  }
}

// 提示alert
.section-alert {
  :deep(.ant-alert) {
    height: 40px;
    background: #fef6e7;
    border-radius: 2px;
    border: 1px solid #ffd666;
    font-size: @jt-font-size-base;
    font-weight: @jt-font-weight;
    color: @jt-table-header-color;
  }
}
</style>
<style lang="less">
.join-dis-title .ant-tooltip-inner {
  width: 262px;
}
</style>
