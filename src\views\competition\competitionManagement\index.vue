<template>
  <div>
    <manage-header />
    <div class="competition-manage-content">
      <div class="content-container">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import manageHeader from './manageHeader.vue';
import { competitionApi } from '@/apis/index.js';
import { checkPublishCompetitionPermission } from '@/keycloak';
import store from '@/store/index';
export default {
  name: 'CompetitionManagement',
  components: {
    manageHeader,
  },
  async beforeRouteEnter(to, from, next) {
    // 无创建比赛权限
    if (!checkPublishCompetitionPermission()) {
      next('/competition');
      return;
    }
    const { competitionId } = to.params;
    // 进入创建比赛页面
    if (competitionId === undefined) {
      next();
    } else {
      const res = await competitionApi.getCompetitionDetail({ cid: competitionId });
      // -906 无该比赛的操作权限
      if (res.state === 'OK' && res.errorCode !== '-906') {
        store.commit('competition/SET_CURRENTMANAGECOMPETITION', res.body);
        next();
      } else {
        next('/competition');
      }
    }
  },
  beforeUnmount() {
    store.commit('competition/SET_CURRENTMANAGECOMPETITION', {});
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.competition-manage-content {
  padding-bottom: 28px;
  .content-container {
    margin: 20px auto 0px;
    width: 1200px;
    background-color: white;
    min-height: calc(100vh - @jt-header-height - @jt-footer-height - 140px);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    border-radius: 0px 0px 2px 2px;
  }
}
</style>
