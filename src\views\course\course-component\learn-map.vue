<template>
  <div class="learn-map">
    <div class="inner">
      <h1 class="portal-title">学习地图</h1>
      <!-- <h2 class="sub-title" style="margin-bottom: 32px">理论实战结合，助你快速成长</h2> -->
      <div class="course-list-btn" @click="onExtendClick" shape="round" type="primary">
        <span> 全部公开课 <jt-icon type="iconright" /> </span>
      </div>
      <jt-skeleton :loading="loading" :rows="8" :skeletonStyle="{ height: '402px', display: 'flex', 'flex-direction': 'column', 'justify-content': 'space-between' }">
        <course-tree v-if="treeData.contents.length > 0" :treeData="treeData" @extendClick="onExtendClick" @contentClick="onContentClick" @bottomClick="onBottomClick"></course-tree>
      </jt-skeleton>
    </div>
  </div>
</template>

<script>
import courseTree from '@/components/courseTree';
export default {
  name: 'learnMap',
  components: {
    courseTree,
  },
  data() {
    return {
      treeData: {
        title: 'AI学习地图',
        contents: [],
      },
      loading: false,
    };
  },
  mounted() {
    // 获取学习地图数据
    this.getCourseTree();
  },
  methods: {
    async getCourseTree() {
      this.loading = true;
      const cateList = await this.getCatList();
      const childCateList = await this.getChildCatList();
      this.loading = false;
      const contents = cateList.body.map((item) => {
        const newItem = {
          title: item.cateName,
        };
        newItem.contents = childCateList.body.filter((it) => it.cateId === item.id).map((it) => ({ name: it.name, value: it.id, disabled: it.disabled }));

        return newItem;
      });
      this.treeData = { ...this.treeData, contents };
    },
    async getCatList() {
      return this.$GET(`/course_model/web/course_student/course/courseCateList`);
    },
    async getChildCatList() {
      return this.$GET(`/course_model/web/course_student/course/courseChildCateList`);
    },

    onExtendClick() {
      this.$router.push('/course/course-list');
    },
    onContentClick(val) {
      this.gotoZone({ name: val.name, id: val.value });
    },
    onBottomClick() {
      this.$router.push('/course/my-course');
    },
    gotoZone(item) {
      this.$router.push({
        path: `/course/course-zone/${item.id}`,
        query: {
          name: item.name,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import './common.less';
.course-tree-container {
  min-height: 400px;
}
.course-list-btn {
  display: flex;
  color: #606972;
  margin: 24px auto 30px;
  &:hover {
    color: #0082ff;
    border-color: #0082ff;
  }
  width: 116px;
  height: 32px;
  padding: 0 9px;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
  border: 1px solid #bec2c5;
  cursor: pointer;
  font-size: 14px;
}
.learn-map {
  padding: 30px 0 40px;
  background: #fff;
}
</style>
