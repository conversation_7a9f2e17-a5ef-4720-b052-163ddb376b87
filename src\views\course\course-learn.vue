<template>
  <div class="main-container">
    <bread-crumb class="bread-crumb-container" :value="breadcrumb">
      <a-space>
        <a-tooltip :title="prevCatalog.catalogName">
          <a-button :disabled="!havePre" size="small" type="primary" ghost class="btn" @click="handleNavigate(-1)"> <jt-icon class="arrow-icon" type="iconleft" /> 上一节 </a-button>
        </a-tooltip>
        <a-tooltip :title="nextCatalog.catalogName">
          <a-button :disabled="!haveNext" size="small" type="primary" ghost class="btn" @click="handleNavigate(1)"> 下一节 <jt-icon type="iconright" class="arrow-icon" /> </a-button>
        </a-tooltip>
      </a-space>
    </bread-crumb>
    <div class="content">
      <a-spin :spinning="loading" :tip="loadingTip">
        <component :is="temp" v-if="loaded" :video-list="videoList" :doc-list="docList" :project-info="projectInfo"></component>
      </a-spin>
    </div>
  </div>
</template>

<script>
import { checkAuth } from '@/utils/utils';
import { checkLogin } from '@/keycloak';
import breadCrumb from '../../components/breadCrumb';
import { tempSwitcher, weightMap } from '../../components/course-learn/adapter';
import { loadingTip } from '@/common/text';

const breadcrumbData = [
  { name: '学习', path: '/course' },
  { name: '我学习的公开课', path: '/course/my-course' },
];
const learnStatusMap = {
  0: { value: 'unstarted', label: '未学习' },
  1: { value: 'finished', label: '已学习' },
  2: { value: 'finished', label: '已学习' },
  3: { value: 'finished', label: '已学习' },
};

export default {
  components: { breadCrumb },
  data() {
    return {
      temp: null,
      courseId: null,
      breadcrumb: [],
      loading: false,
      docList: [],
      videoList: [],
      projectInfo: {},
      loaded: false,
      catalogList: [],
      preview: false,
      restart: false,
      loadingTip,
    };
  },
  computed: {
    haveNext() {
      return this.currentIndex < this.catalogList.length - 1;
    },
    havePre() {
      return this.currentIndex > 0;
    },
    currentIndex() {
      return this.catalogList.findIndex((item) => +item.id === +this.catalogId);
    },
    currentCatalog() {
      return this.catalogList[this.currentIndex] || {};
    },
    nextCatalog() {
      return this.catalogList[this.currentIndex + 1] || {};
    },
    prevCatalog() {
      return this.catalogList[this.currentIndex - 1] || {};
    },
    resourseUrl() {
      let url = '/course_model/web/course_student/course/catalogResourseList';
      if (this.preview) {
        url = '/course_model/web/course_student/course/teachingCatalogResourseList';
        if (this.$route.query.coursePublish === '1') {
          url = '/course_model/web/course_student/course/teachingLookCatalogResourseList';
        }
      }
      return url;
    },
  },
  watch: {
    // 如果路由有变化，会再次执行该方法
    $route: 'init',
  },
  created() {
    if (!checkLogin(true)) {
      return;
    }
    this.preview = this.$route.query.preview === 'true';
    this.restart = this.$route.query.restart === 'true';
    this.init();
  },
  methods: {
    async init() {
      this.courseName = this.$route.query.courseName;
      this.courseId = this.$route.params.courseId;
      this.catalogId = this.$route.params.catalogId;
      this.catalogName = this.$route.query.catalogName;
      this.breadcrumb = [];
      if (!this.preview) {
        this.breadcrumb = [...breadcrumbData];
      }
      this.breadcrumb.push({
        name: '课程主页',
        path: this.preview ? `/course/teaching/course-preview/${this.courseId}` : `/course/course-overview/${this.courseId}`,
      });
      this.breadcrumb.push({
        name: `${this.courseName}：${this.catalogName}`,
      });
      this.loading = true;
      await this.getCatalogList();
      const res = await this.checkCatalog();
      if (res) {
        await this.getCatalogResource();
      }
      this.loading = false;
    },
    async checkCatalog() {
      if (this.preview) {
        return true;
      }
      if (learnStatusMap[this.currentCatalog.status || 0].value === 'finished') {
        return true;
      }
      const obj = {
        catalogId: this.catalogId,
        courseId: this.courseId,
      };
      const res = await this.$GET('/course_model/web/course_student/student/studentCatalogAdd', obj, { useError: false });
      if (!checkAuth(res.errorCode, '-802', '/course')) {
        return false;
      }
      return true;
    },
    async getCatalogResource() {
      this.docList = [];
      this.videoList = [];
      this.projectInfo = {};
      this.$GET(this.resourseUrl, { catalogId: this.catalogId, courseId: this.courseId }, { useError: false }).then((res) => {
        this.loaded = true;
        if (!checkAuth(res.errorCode, this.preview ? '-801' : '-802', this.preview ? '/course/teaching' : '/course')) {
          return;
        }
        const list = res.body;
        this.generateResourceList(list || []);
        this.getTemp();
      });
    },
    generateResourceList(list) {
      this.docList = list.filter((item) => item.resourseType === '3');
      this.videoList = list.filter((item) => item.resourseType === '2');
      this.projectInfo = list.filter((item) => item.resourseType === '1')[0] || {};
      this.projectInfo.instanceModel = this.projectInfo.instanceModel || 'Jupyter';
      this.projectInfo.instanceName = this.projectInfo.resourseUrl;
      this.videoList = this.videoList.map((item) => {
        item.resourseUrl = this.decodedUrl(item.resourseUrl);
        return item;
      });
    },
    decodedUrl(url) {
      if (url.indexOf('<iframe') > -1) {
        return url;
      }
      return decodeURIComponent(url);
    },
    getTemp() {
      let weight = 0;
      console.log('getTemp', this.videoList, this.docList, this.projectInfo);
      if (this.videoList.length > 0) {
        weight = weight | weightMap['video'];
      }
      if (this.docList.length > 0) {
        weight = weight | weightMap['pdf'];
      }
      if (this.projectInfo.projectId) {
        weight = weight | weightMap['jupyter'];
      }
      console.log('weight', weight);
      this.temp = tempSwitcher(weight);
    },
    // eslint-disable-next-line no-unused-vars
    handleNavigate(offset) {
      const catalog = this.catalogList[this.currentIndex + offset];
      this.$router.push({ path: `/${this.preview ? 'course/teaching' : 'course'}/course-learn/${this.courseId}/${catalog.id}`, query: { courseName: this.courseName, catalogName: this.preview ? catalog.catalogNamePre : catalog.catalogName, preview: this.preview, coursePublish: this.$route.query.coursePublish } });
    },
    async getCatalogList() {
      const url = this.preview ? '/course_model/web/course_student/course/teacherCourseCatalogList' : '/course_model/web/course_student/studentCatalog/studentCatalogList';
      await this.$GET(url, { courseId: this.courseId }).then((res) => {
        this.catalogList = res.body.filter((item) => learnStatusMap[item.status || 0].value === 'finished' || +item.videoFlag || +item.itemFlag || +item.documentFlag);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.main-container {
  flex-direction: column;
  background: #f4f8fa;
}
.content {
  justify-content: center;
  padding: 20px 0;
  min-height: calc(100vh - 200px);
}
.btn {
  height: 28px;
}
.bread-crumb-container {
  :deep(.bread-crumb) {
    margin: 0 20px;
    min-width: 1200px;
    flex: 1;
  }
}
.arrow-icon {
  font-size: 12px;
}
:deep(.ant-spin.ant-spin-spinning) {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
:deep(.ant-spin .ant-spin-text) {
  margin-top: 4px;
}
</style>
