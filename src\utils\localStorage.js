const localStorage = window.localStorage;

/**
 * localStorage 设置
 * @param {String} key 键
 * @param {String} val 值
 */
export function set(key, val) {
  try {
    if (!key) return;
    const value = JSON.stringify(val);
    localStorage.setItem(key, value);
  } catch (err) {
    console.log(err);
  }
}

/**
 * localStorage 获取
 * @param {String} key 键
 * @return {Boolean} key 对应 localStorage 的值
 */
export function get(key) {
  try {
    if (!key) return '';
    const value = localStorage.getItem(key);
    return value ? JSON.parse(value) : '';
  } catch (err) {
    console.log(err);
  }
}

/**
 * 清除 localStorage，若不填参数 key ，则清除所有 localStorage
 * @param {String} key 键
 * @return {Boolean} 是否清除成功
 */
export function remove(key) {
  try {
    if (typeof key === 'undefined') return localStorage.clear();
    return localStorage.removeItem(key);
  } catch (err) {
    console.log(err);
  }
}

// 默认导出全量方法
export default {
  get,
  set,
  remove,
};
