<template>
  <div class="footer-container">
    <div class="content">
      <p>活动须知：</p>
      <p>1、注册成功并登录后，您将赢取{{ receiveCount }}个算力豆，您的好友将赢取{{ assistCount }}个算力豆，算力豆有效期均为{{ period }}天</p>
      <template v-if="activityAvailable">
        <p>2、如勾选“同时注册移动云账号”，您将额外赢取200个算力豆</p>
        <p>3、本活动最终解释权归九天·毕昇所有</p>
        <p>
          点击了解
          <a-button @click="handleNavigateToHome" class="link-button" type="link">九天·毕昇平台 <jt-icon class="icon" type="iconright"></jt-icon></a-button>
        </p>
        <p>
          点击了解
          <a-button @click="handleNavigateToEcloud" class="link-button" type="link">移动云 <jt-icon class="icon" type="iconright"></jt-icon></a-button>
        </p>
      </template>
      <template v-else>
        <p>2、本活动最终解释权归九天·毕昇所有</p>
        <p>
          点击了解
          <a-button @click="handleNavigateToHome" class="link-button" type="link">九天·毕昇平台 <jt-icon class="icon" type="iconright"></jt-icon></a-button>
        </p>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'invite-register-footer',
  components: {},
  props: {
    receiveCount: {
      type: Number,
    },
    period: {
      type: Number,
    },
    assistCount: {
      type: Number,
    },
    activityAvailable: Boolean,
  },
  data() {
    return {};
  },
  methods: {
    handleNavigateToHome() {
      window.open('./web#/home');
    },
    handleNavigateToEcloud() {
      window.open('https://ecloud.10086.cn');
    },
  },
};
</script>

<style lang="less" scoped>
.footer-container {
  display: flex;
  justify-content: center;
  p {
    font-size: 14px;
    font-weight: 400;
    color: #606972;
    line-height: 24px;
  }
}
.link-button {
  padding: 0;
  display: inline-flex;
  align-items: center;
  .icon {
    display: inline-flex;
    align-items: center;
    margin: 0;
  }
}
</style>
