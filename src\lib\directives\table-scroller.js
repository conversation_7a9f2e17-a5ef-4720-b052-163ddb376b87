export default {
  install(Vue) {
    Vue.mixin({
      directives: {
        loadmore: {
          bind(el, binding) {
            let selectWrap = el.querySelector('.ant-table-body');
            if (!selectWrap) selectWrap = el.querySelector('.el-table__body-wrapper');
            var lastScrollTop = 0;
            selectWrap.addEventListener('scroll', scroll);
            selectWrap.unbindEventListener = () => {
              selectWrap.removeEventListener('scroll', scroll);
            }
            function scroll() {
              let sign = 10;
              if (lastScrollTop != this.scrollTop) {
                lastScrollTop = this.scrollTop;
                const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight;
                if (scrollDistance <= sign) {
                  binding.value();
                }
              }
            }
          },
          unbind(el) {
            let selectWrap = el.querySelector('.ant-table-body')
            if (!selectWrap) selectWrap = el.querySelector('.el-table__body-wrapper');
            if(selectWrap && selectWrap.unbindEventListener) {
              selectWrap.unbindEventListener();
            }
          }
        },
      },
    });
  },
};
