const mutations = {
  updateStudent(state, data) {
    state.studentTableData = data.list;
    state.studentTotal = data.totalCount;
  },
  updateLoading(state, loading) {
    state.tableLoading = loading;
  },
  updateParams(state, params) {
    state.initParams = params;
  },
  updateExamUrl(state, params) {
    state.examUrl = params;
  },

  SET_COURSEINTRODUCTION_DATA(state, data) {
    state.courseIntroductionData = data;
  },

  SET_COURSERESOURCE_DATA(state, data) {
    state.courseResourceData = data;
  },
  SET_CURRENTACTIVECOURSE_DATA(state, data) {
    state.currentActiveCourse = data;
  },
};

export default mutations;
