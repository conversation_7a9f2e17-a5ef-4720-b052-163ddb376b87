import Home from '../views/Home';

const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/home',
    name: '首页',
    component: Home,
    meta: {
      noSubHeader: true,
    },
  },
  {
    path: '/document/:id',
    name: '帮助中心',
    component: () => import('../views/document/index.vue'),
  },
  {
    path: '/document-search',
    name: '帮助中心搜索',
    component: () => import('../views/document/documentSearch.vue'),
  },
  {
    path: '/course',
    name: '课程',
    component: () => import('../views/course/course.vue'),
    meta: {
      noSubHeader: true,
    },
  },
  {
    path: '/course/course-list',
    name: '全部课程',
    component: () => import('../views/course/course-list.vue'),
    meta: { parentPageUrl: '/course', parentPageName: '课程' },
  },
  {
    path: '/course/course-detail',
    name: '课程详情',
    component: () => import('../views/course/course-detail.vue'),
  },
  {
    path: '/teaching',
    name: '教学',
    component: () => import('@/views/teaching/Teaching.vue'),
    meta: {
      noSubHeader: true,
    },
  },
  {
    path: '/course/teaching/mycourses',
    name: '我的课程',
    component: () => import('@/views/myCourses/MyCourses.vue'),
    meta: {
      title: '我开设的公开课',
      requiresLogin: true,
      role: ['teacher'],
    },
    children: [
      {
        path: '',
        name: '我开设的课程',
        component: () => import('@/views/myCourses/MyOfferCourses.vue'),
        meta: {
          title: '我开设的课程',
          requiresLogin: true,
          role: ['teacher'],
        },
      },
      {
        path: 'course-manage/info/:courseId/:tab?/:subTab?/:subPage?',
        name: '课程管理',
        component: () => import('@/views/myCourses/CourseManage.vue'),
        meta: {
          title: '课程管理',
          requiresLogin: true,
          role: ['teacher'],
        },
      },
      {
        path: 'course-manage/edit/:courseId/:tab?/:subTab?/:subPage?',
        name: '课程管理编辑',
        component: () => import('@/views/myCourses/CourseManage.vue'),
        meta: {
          title: '课程管理编辑',
          requiresLogin: true,
          role: ['teacher'],
          confirmLeave: true,
        },
      },
      {
        path: 'create-course/:courseId?',
        name: '创建课程',
        component: () => import('@/views/myCourses/createCourse/CreateCourse.vue'),
        meta: {
          title: '课程管理', // 这块设计要求还是展示课程管理
          requiresLogin: true,
          role: ['teacher'],
        },
      },
      {
        path: 'course-manage/edit/:courseId?/:tab?/:subTab?/:subPage?',
        name: '编辑课程',
        component: () => import('@/views/myCourses/createCourse/CreateCourse.vue'),
        meta: {
          title: '课程管理', // 这块设计要求还是展示课程管理
          requiresLogin: true,
          role: ['teacher'],
        },
      },
      {
        path: 'create-work/:courseId/:workId?',
        name: '新增作业',
        component: () => import('@/views/myCourses/teachingEvaluation/AddHomework.vue'),
        meta: {
          title: '课程管理',
          requiresLogin: true,
          role: ['teacher'],
        },
      },
    ],
  },
  {
    path: '/user-center',
    name: '个人中心',
    component: () => import('@/views/userCenter/UserCenter.vue'),
    meta: {
      title: '个人中心',
      requiresLogin: true,
    },
  },
  {
    path: '/user-center/order-mobilecloud-computing',
    name: '订购移动云赢算力',
    component: () => import('@/views/userCenter/OrderMobilecloudComputing.vue'),
    meta: {
      title: '订购移动云赢算力',
      requiresLogin: true,
    },
  },
  {
    path: '/course/my-course',
    name: '我学习的课程',
    component: () => import('../views/course/my-course.vue'),
    meta: {
      title: '我学习的课程',
      requiresLogin: true,
    },
  },
  {
    path: '/course/course-learn/:courseId/:catalogId',
    name: '课程学习',
    component: () => import('../views/course/course-learn.vue'),
    meta: {
      title: '课程学习',
      requiresLogin: true,
    },
  },
  {
    path: '/course/my-school-course',
    name: '我的校内课程',
    component: () => import('../views/course/my-school-course.vue'),
    meta: {
      title: '我的校内课程',
      requiresLogin: true,
    },
  },
  {
    path: '/course/homework-detail/:courseId/:assignmentId',
    name: '作业详情',
    component: () => import('../views/course/homework-detail.vue'),
    meta: {
      title: '作业详情',
      requiresLogin: true,
    },
  },
  {
    path: '/course/teaching/homework-detail-preview/:courseId/:assignmentId',
    name: '作业详情预览',
    component: () => import('../views/course/homework-detail-preview.vue'),
    meta: {
      title: '作业详情预览',
      requiresLogin: true,
    },
  },
  {
    path: '/course/teaching/homework-review/:courseId/:stuAssignmentId',
    name: '评阅作业',
    component: () => import('../views/course/homework-review.vue'),
    meta: {
      title: '评阅作业',
      requiresLogin: true,
    },
  },
  {
    path: '/course/teaching/course-learn/:courseId/:catalogId',
    name: '课程学习预览',
    component: () => import('../views/course/course-learn.vue'),
    meta: {
      title: '课程学习预览',
      requiresLogin: true,
    },
  },
  {
    path: '/course/course-overview/:courseId',
    name: '课程主页',
    component: () => import('../views/course/course-overview.vue'),
    meta: {
      title: '课程主页',
      requiresLogin: true,
    },
  },
  {
    path: '/course/teaching/course-preview/:courseId',
    name: '课程预览',
    component: () => import('../views/course/course-preview.vue'),
    meta: {
      title: '课程预览',
      requiresLogin: true,
    },
  },
  {
    path: '/course/course-zone/:zoneId',
    name: '课程专区',
    component: () => import('../views/course/course-zone.vue'),
  },
  // 数据集
  // {
  //   path: '/dataset',
  //   name: '数据集',
  //   component: () => import('../views/dateset/dataset.vue'),
  // },
  // 数据集详情页
  // {
  //   path: '/dataset/dataset-detail',
  //   name: '数据详情',
  //   // meta: { parentPageUrl: '/dataset', parentPageName: '数据集' },
  //   component: () => import('../views/dateset/dataset-detail.vue'),
  // },
  // 求职
  // {
  //   path: '/job',
  //   name: '求职',
  //   component: () => import('../views/job.vue'),
  // },
  // 在线考试
  {
    path: '/job/online-exam',
    name: '在线考试',
    component: () => import('../views/online-exam.vue'),
    meta: {
      title: '在线考试',
      requiresLogin: true,
    },
  },
  // 能力认证
  // {
  //   path: '/certification',
  //   name: '能力认证',
  //   component: () => import('../views/Certification.vue'),
  // },
  // 比赛
  {
    path: '/competition',
    name: '比赛',
    component: () => import('../views/competition/competition.vue'),
    children: [
      {
        path: '',
        name: '比赛列表',
        component: () => import('../views/competition/competition-list.vue'),
        meta: {
          noSubHeader: true,
        },
      },
      // 比赛详情
      {
        path: 'competition-detail',
        name: '比赛详情',
        meta: { parentPageUrl: '/competition', parentPageName: '比赛' },
        component: () => import('../views/competition/competition-detail.vue'),
      },
      {
        path: 'competition-register',
        name: '比赛报名',
        component: () => import('../views/competition/competition-register.vue'),
        meta: {
          title: '比赛报名',
          requiresLogin: true,
        },
      },
      {
        path: 'my-competition',
        name: '我的比赛',
        component: () => import('../views/competition/my-competition.vue'),
        meta: {
          title: '我的比赛',
          requiresLogin: true,
        },
      },
      {
        path: 'held-competition',
        name: '我举办的比赛',
        component: () => import('../views/competition/held-competition-list.vue'),
        meta: {
          title: '我举办的比赛',
          requiresLogin: true,
        },
      },
      {
        path: 'competition-management/create-competition',
        name: '创建比赛',
        component: () => import('../views/competition/competitionManagement/index.vue'),
        meta: {
          title: '创建比赛',
          requiresLogin: true,
        },
        children: [
          {
            path: '',
            component: () => import('../views/competition/competitionManagement/competitionMessage/competitionInfoEdit/index.vue'),
            meta: {
              title: '创建比赛主页',
              requiresLogin: true,
            },
          },
        ],
      },
      {
        path: 'competition-management/:competitionId',
        name: '比赛管理',
        component: () => import('../views/competition/competitionManagement/index.vue'),
        meta: {
          title: '比赛管理',
          requiresLogin: true,
        },
        children: [
          {
            path: '',
            name: '比赛管理主页',
            component: () => import('../views/competition/competitionManagement/manageTabs.vue'),
            meta: {
              title: '比赛管理',
              requiresLogin: true,
            },
          },
          {
            path: 'info-edit',
            name: '编辑比赛基本信息',
            component: () => import('../views/competition/competitionManagement/competitionMessage/competitionInfoEdit/index.vue'),
            meta: {
              title: '编辑比赛基本信息',
              requiresLogin: true,
            },
          },
          {
            path: 'system-edit',
            name: '编辑赛制介绍',
            component: () => import('../views/competition/competitionManagement/competitionMessage/competitionSystemIntroEdit/index.vue'),
            meta: {
              title: '编辑赛制介绍',
              requiresLogin: true,
              confirmLeave: true,
            },
          },
          {
            path: 'question-edit',
            name: '编辑赛题说明',
            component: () => import('../views/competition/competitionManagement/competitionMessage/competitionQuestionIntroEdit/index.vue'),
            meta: {
              title: '编辑赛题说明',
              requiresLogin: true,
              confirmLeave: true,
            },
          },
          {
            path: 'common-question-edit',
            name: '编辑常见问题',
            component: () => import('../views/competition/competitionManagement/competitionMessage/commonQuestionsEdit/index.vue'),
            meta: {
              title: '编辑常见问题',
              requiresLogin: true,
              confirmLeave: true,
            },
          },
          {
            path: 'teamsettings-edit',
            name: '编辑报名及团队设置',
            component: () => import('../views/competition/competitionManagement/competitionTeam/teamSettingsEdit/index.vue'),
            meta: {
              title: '编辑报名及团队设置',
              requiresLogin: true,
              confirmLeave: true,
            },
          },
          {
            path: 'result-edit',
            name: '编辑结果文件提交设置',
            component: () => import('../views/competition/competitionManagement/competitionRank/resultFile/resultEdit/index.vue'),
            meta: {
              title: '编辑结果文件提交设置',
              requiresLogin: true,
              confirmLeave: true,
            },
          },
          {
            path: 'review-edit',
            name: '编辑审查及答辩材料提交设置',
            component: () => import('../views/competition/competitionManagement/competitionRank/review/reviewEdit/index.vue'),
            meta: {
              title: '编辑审查及答辩材料提交设置',
              requiresLogin: true,
              confirmLeave: true,
            },
          },
          {
            path: 'datacase-edit',
            name: '编辑数据及实例设置',
            component: () => import('../views/competition/competitionManagement/competitionDataCase/dataCaseSettingEdit/index.vue'),
            meta: {
              title: '编辑数据及实例设置',
              requiresLogin: true,
              confirmLeave: true,
            },
          },
        ],
      },
    ],
  },
  {
    path: '/exam-feedback/giveup-feedback',
    meta: {
      title: '考试反馈',
      requiresLogin: false,
    },
    component: () => import('../views/exam-feedback/giveup-feedback.vue'),
  },

  {
    path: '/401',
    name: '401',
    component: () => import('../views/401.vue'),
  },

  {
    path: '/pdf-viewer',
    name: 'pdf预览',
    component: () => import('../views/myCourses/fileViewers/PdfViewer.vue'),
  },
  //h5页面
  {
    path: '/h5/test',
    name: 'h5',
    component: () => import('../h5/h5Test.vue'),
  },
  {
    path: '/h5/register', // 带有“福利大放送”模块的注册页，已废弃
    name: 'h5',
    component: () => import('../h5/Campus/register.vue'),
  },
  {
    path: '/h5/question',
    name: 'h5',
    component: () => import('../h5/Campus/question.vue'),
  },
  {
    path: '/h5/commonregister', // 注册页
    name: 'h5',
    component: () => import('../h5/Register/commonRegister/index.vue'),
  },
  {
    path: '/h5/inviteregister', // 邀请注册页
    name: 'h5',
    component: () => import('../h5/Register/inviteRegister/index.vue'),
  },
  {
    path: '/video-viewer',
    name: '视频预览',
    component: () => import('../views/myCourses/fileViewers/VideoViewer.vue'),
  },
  {
    path: '/invite-register',
    name: '邀请注册',
    component: () => import('../views/register/inviteRegister.vue'),
  },
  {
    path: '/register',
    name: '邀请注册',
    component: () => import('../views/register/register.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: '404',
    component: () => import('../views/404.vue'),
  },
];

export default routes;
