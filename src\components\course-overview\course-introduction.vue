<template>
  <div class="contents">
    <div class="header">课程介绍</div>
    <jt-common-content :empty="dataEmpty" :loading="loading">
      <div class="main-contents">
        <div class="course-introduce-row">
          <h6>课程描述</h6>
          <div class="w-e-text">
            <div v-html="introductionInfo.courseDesc || '无'"></div>
          </div>
        </div>
        <div class="course-introduce-row">
          <h6>前置知识</h6>
          <div class="w-e-text">
            <div v-html="introductionInfo.courseFrontKnowledge || '无'"></div>
          </div>
        </div>
        <div class="course-introduce-row">
          <h6>课程目标</h6>
          <div class="w-e-text">
            <div v-html="introductionInfo.courseGoal || '无'"></div>
          </div>
        </div>
      </div>
    </jt-common-content>
  </div>
</template>

<script>
import { getRichText } from '@/utils/utils';

export default {
  props: {
    courseDesc: String,
    courseFrontKnowledge: String,
    courseGoal: String,
  },
  data() {
    return {
      introductionInfo: {},
      loading: false,
    };
  },
  mounted() {
    this.getIntroductionInfo();
  },
  computed: {
    dataEmpty() {
      return !Object.values(this.introductionInfo).some((item) => !!item);
    },
  },
  watch: {
    courseDesc() {
      this.getIntroductionInfo();
    },
    courseFrontKnowledge() {
      this.getIntroductionInfo();
    },
    courseGoal() {
      this.getIntroductionInfo();
    },
  },
  methods: {
    async getIntroductionInfo() {
      this.loading = true;
      const courseDesc = await getRichText(this.courseDesc);
      const courseFrontKnowledge = await getRichText(this.courseFrontKnowledge);
      const courseGoal = await getRichText(this.courseGoal);
      this.introductionInfo = { courseDesc, courseFrontKnowledge, courseGoal };
      this.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.contents {
  background: #fff;
  flex: 1;
  flex-direction: column;
  .header {
    padding: 24px 0 16px 32px;
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    border-bottom: 1px solid #e0e1e1;
  }
  .main-contents {
    padding: 0px 32px;
    .item {
      padding: 24px 0;
    }
    h1 {
      margin: 0;
      padding-bottom: 16px;
      font-size: 16px;
      font-weight: @jt-font-weight-medium;
      color: #16161c;
    }
  }
}
.course-introduce-row {
  line-height: 20px;
  h6 {
    font-size: 16px;
    font-weight: @jt-font-weight-medium;
    margin-top: 48px;
    color: #16161c;
  }
}
</style>
