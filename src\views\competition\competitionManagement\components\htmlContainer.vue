<template>
  <div class="html-container">
    <h3 class="header-title">{{ title }}</h3>
    <htmlViewer :text-value="textValue" :text-url="textUrl" @updateRichLoading="updateRichLoading" />
  </div>
</template>

<script>
import htmlViewer from './htmlViewer.vue';
export default {
  name: 'HtmlContainer',
  components: {
    htmlViewer,
  },
  props: {
    title: {
      type: String,
    },
    textUrl: {
      type: String,
      default: null,
    },
    textValue: {
      type: String,
      default: null,
    },
  },
  emits: ['updateRichLoading'],
  methods: {
    updateRichLoading(val) {
      this.$emit('updateRichLoading', val);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.html-container {
  .header-title {
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: @jt-title-color;
    line-height: 25px;
    padding-bottom: 16px;
  }
}
</style>
