<template>
  <div class="submit-file">
    <div>
      <head-title :title="title" />
      <a-button :disabled="disabled" type="primary" @click="coverShow(title)">
        <UploadOutlined />
        上传
      </a-button>
      <div class="alarms" :class="{ 'alarms-nobottom': !moblieTipShow && !submitRequire }">
        <alarm v-if="message && message.indexOf('需创建自己的团队') == -1" :title="message" />
        <alarm v-else-if="message">
          <p class="alarm-tip">温馨提示：需<a @click="createTeamVisible = true">创建自己的团队</a>或 <a @click="joinTeamVisible = true">加入已有的团队</a>后，才可提交{{ title === '提交结果文件' ? '结果文件' : '答辩材料' }}</p>
        </alarm>
        <!-- 创建团队弹框 -->
        <modal v-model:open="createTeamVisible" title="创建团队" :spinning="createTeamModalSpinning" :confirm-loading="teamName == ''" @ok="createTeamOk" @cancel="switchToMyteamTab">
          <p>团队名称： <a-input v-model:value="teamName" placeholder="请输入" style="width: 320px" :max-length="20" /></p>
        </modal>
        <!-- 加入团队弹框 -->
        <modal v-model:open="joinTeamVisible" title="加入团队" ok-text="知道了" :show-cancel="false" @ok="handleOkJoinTeamModal"> 您可接受其他团队邀请，加入已有团队，请联系团队队长获取邀请链接 </modal>
      </div>
      <div v-if="moblieTipShow" class="mobile-tip">
        <p>您需在移动云深度学习平台，使用如下命令，将文件同步至团队共享存储空间</p>
        <div>
          <a-tooltip overlay-class-name="mobile-tip-tooltip">
            <template #title>{{ mobileCloudTip }}</template>
            <span>{{ mobileCloudTip }}</span>
          </a-tooltip>
          <span @click="() => clickCopy(mobileCloudTip)">一键复制</span>
        </div>
      </div>
      <p v-if="submitRequire" class="submit-require">提交要求：</p>
      <!-- eslint-disable vue/no-v-html -->
      <div v-if="submitRequire" class="cpt-explain markdown-body" v-html="submitRequire"></div>
      <img v-if="resultPicturePath" class="result-picture" :src="resultPicturePath" alt="" />
    </div>
    <upload-result-modal :file-submit-type="fileSubmitType" :is-mobile-cloud="isMobileCloud" :visible="coverPopUps.show" :title="coverPopUps.title" :filetype="filetype" @cancel="uploadModalCancel" @ok="uploadModalOk"></upload-result-modal>
  </div>
</template>
<script setup>
import { UploadOutlined } from '@ant-design/icons-vue';
</script>
<script>
import API from '@/constants/api/API.js';
import HeadTitle from '@/components/headTitle/index.vue';
import Modal from '@/components/modal/modalSpin.vue';
import Alarm from '@/components/alarm/index';
import UploadResultModal from './UploadResultModal.vue';

import { handleCopy } from '@/utils/utils';
export default {
  components: {
    HeadTitle,
    Modal,
    Alarm,
    UploadResultModal,
  },
  props: {
    title: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
    message: { type: String, default: '' },
    mobileCloudTip: { type: String, default: '' },
    submitRequire: { type: String, default: '' },
    resultPicturePath: { type: String, default: '' },
    filetype: { type: String, default: '' },
    fileSubmitType: { type: String, default: '' },
    isMobileCloud: { type: Boolean, default: false },
    isLeader: { type: Boolean, default: false },
    competitionId: { type: String, default: '' },
  },
  emits: ['switchToMyteamTab', 'uploadModalOk'],
  data() {
    return {
      coverPopUps: {
        show: false,
        title: '',
        plusShow: true, //隐藏上传按钮
        fileLis: [], //团队共享存储文件以及文件夹展示
        pathList: [], //实例路径
      },
      createTeamVisible: false,
      joinTeamVisible: false,
      createTeamModalSpinning: false,
      teamName: '',
    };
  },
  computed: {
    moblieTipShow() {
      return this.isMobileCloud && this.mobileCloudTip != '';
    },
  },
  methods: {
    coverShow(title) {
      if (!this.isLeader) return; //不是队长不允许提交
      this.coverPopUps.plusShow = true;
      this.coverPopUps.show = !this.coverPopUps.show;
      this.coverPopUps.title = title;
    },

    createTeamOk() {
      this.createTeamModalSpinning = true;
      API.competition_model.createTeam({ owner: this.competitionId, teamName: this.teamName }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.teamHave = true;
          this.createTeamVisible = false;
          this.switchToMyteamTab();
        } else {
          this.$message.error(res.errorMessage || '已有同名团队，请重新设置');
        }
        this.createTeamModalSpinning = false;
        this.teamName = '';
      });
    },
    switchToMyteamTab() {
      this.$emit('switchToMyteamTab');
    },
    handleOkJoinTeamModal() {
      this.joinTeamVisible = false;
      this.switchToMyteamTab();
    },
    uploadModalCancel() {
      this.coverPopUps.show = false;
    },
    uploadModalOk() {
      this.coverPopUps.show = false;
      this.$emit('uploadModalOk');
    },
    clickCopy(text) {
      handleCopy(text);
      this.$message.success('复制成功');
    },
  },
};
</script>

<style lang="less">
.mobile-tip-tooltip {
  .ant-tooltip-inner {
    position: relative;
    left: -391px;
    min-width: 1136px;
  }
}
</style>
<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.submit-file {
  position: relative;
  margin-top: 36px;
  a {
    color: #0082ff;
  }
  .alarm-tip {
    height: 18px;
    font-size: 14px;
    color: #333333;
    line-height: 18px;
  }
  .submit-require {
    height: 22px;
    font-size: 14px;
    color: #606972;
    line-height: 22px;
  }
  .ant-btn-primary {
    width: 84px;
    height: 32px;
    font-size: 14px;
    padding: 0;
    border-radius: 2px;
    position: absolute;
    top: 0px;
    right: 0;
  }
  .mobile-tip {
    color: #606972;
    margin: 24px 0;
    p {
      color: #606972;
      margin-bottom: 8px;
    }
    div {
      width: 1136px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      background: #f7f9fa;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      & > span:nth-of-type(1) {
        max-width: 1000px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      & > span:nth-of-type(2) {
        width: 70px;
        font-weight: 400;
        color: #0082ff;
        cursor: pointer;
      }
    }
  }
  .alarms {
    margin: 19px 0 24px 0;
  }
  .alarms-nobottom {
    margin-bottom: 0;
  }
  .result-picture {
    margin-top: 16px;
  }
}
</style>
