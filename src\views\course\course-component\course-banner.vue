<template>
  <div class="banner">
    <div class="inner">
      <div class="banner-text">
        <h1 v-if="isEntranceBySasac()">人工智能课程</h1>
        <h1 v-else>九天 · 毕昇课程</h1>
        <h2>优质课程资源 交互式学习 人人皆可AI</h2>
      </div>
      <div class="banner-content login" v-if="logined">
        <h2>
          Hi,<span>{{ userName }}</span>
        </h2>
        <!-- 公开课 -->
        <div class="content-text" v-if="activeTab === 'publicCourse'">
          <jt-common-content :loading="contentTextLoading" :empty="contentTextLoading" :emptyStyle="{ height: '0px' }">
            <p>
              您共学习
              <span>&nbsp;{{ learnedCourseTotal }}&nbsp;</span>
              个课程
            </p>
            <ol class="learned-course-container">
              <li class="ellipsis-text" v-for="item in learnedCourse.slice(0, visibleLength)" :key="item.id">
                <span @click="() => openPublicCourse(item.id)">{{ item.name }}</span>
              </li>
            </ol>
            <div v-if="learnedCourseTotal > visibleLength">······</div>
          </jt-common-content>
        </div>
        <!-- 校内课 -->
        <div class="content-text" v-if="activeTab === 'schoolCourse'">
          <jt-common-content :loading="contentTextLoading" :empty="contentTextLoading" :emptyStyle="{ height: '0px' }">
            <p>
              您共学习
              <span>&nbsp;{{ schoolCourseTotal }}&nbsp;</span>
              个课程
            </p>
            <ol class="learned-course-container">
              <li class="ellipsis-text" v-for="item in schoolCourseList.slice(0, visibleLength)" :key="item.id">
                <span @click="() => openSchoolCourse(item)">{{ item.name }}</span>
              </li>
            </ol>
            <div v-if="schoolCourseTotal > visibleLength">······</div>
          </jt-common-content>
        </div>
        <router-link class="button" :to="activeTab === 'publicCourse' ? '/course/my-course' : '/course/my-school-course'">{{ activeTab === 'publicCourse' ? '我学习的公开课' : '我学习的校内课' }}</router-link>
        <div v-if="showSchoolCourse" class="tab-box">
          <div class="tab-item" :class="{ active: activeTab === 'publicCourse' }" @click="activeTab = 'publicCourse'"></div>
          <div class="tab-item" :class="{ active: activeTab === 'schoolCourse' }" @click="activeTab = 'schoolCourse'"></div>
        </div>
      </div>
      <div class="banner-content" v-else>
        <h2>Hi,<span style="font-size: 24px">您好！</span></h2>
        <div class="content-text">
          <p>欢迎来到九天 · 毕昇</p>
          <p>您尚未登录，请登录查看课程</p>
        </div>
        <button class="button" @click="gotoLogin">立即登录</button>
      </div>
    </div>
  </div>
</template>

<script>
import { courseStudentApi } from '@/apis';
import { checkLogin, login, keycloak } from '@/keycloak';
import { GET } from '@/request';
import { getTeachingStatus } from '@/apis/teaching.js';
import { openInNewTab, isEntranceBySasac } from '@/utils/utils';

export default {
  name: 'course-banner',
  data() {
    return {
      isEntranceBySasac,
      learnedCourseTotal: 0,
      learnedCourse: [],
      contentTextLoading: true,
      schoolCourseTotal: 0,
      schoolCourseList: [],
      activeTab: 'publicCourse',
      visibleLength: 3,
    };
  },
  async mounted() {
    this.checkteachUpgradingStatus();
    if (keycloak.authenticated) {
      this.contentTextLoading = true;
      await this.getStudentCourse();
      this.contentTextLoading = false;
    }
  },
  computed: {
    logined() {
      return checkLogin();
    },
    userName() {
      return this.$keycloak ? this.$keycloak.idTokenParsed.preferred_username : '';
    },
    showSchoolCourse() {
      return this.schoolCourseTotal > 0;
    },
  },
  methods: {
    getStudentCourse() {
      return courseStudentApi.getCourseStudentFindname({ pageNum: 1, pageSize: 5 }).then((res) => {
        if (!res.errorCode) {
          this.learnedCourse = res.body.data;
          this.learnedCourseTotal = res.body.total;
        }
      });
    },
    checkteachUpgradingStatus() {
      if (keycloak.authenticated) {
        if (keycloak.idTokenParsed.DLP_USER_ALLOW_ENTER_DURING_UPGRADING != '1') {
          getTeachingStatus().then((res) => {
            if (res.state === 'OK' && !res.body.teachingSwitch) {
              this.getSchoolCourse();
            }
          });
        } else {
          this.getSchoolCourse();
        }
      }
    },
    getSchoolCourse() {
      GET('/course_model/web/cg_proxy/courses', {
        userType: 0, // 0:学生 1:教师
        page: 1,
        limitInPage: 5,
      }).then((res) => {
        if (!res.errorCode) {
          this.schoolCourseList = res.body.course;
          this.schoolCourseTotal = res.body.total;
        }
      });
    },
    gotoLogin() {
      login();
    },
    openPublicCourse(courseId) {
      const routeUrl = this.$router.resolve({ path: `/course/course-overview/${courseId}` });
      openInNewTab(routeUrl.href);
    },
    openSchoolCourse(item) {
      openInNewTab(item.ude.url);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import './common.less';
.banner {
  height: 460px;
  background-color: #d1efff;
  background-image: url('~@/assets/image/home/<USER>');
  background-repeat: no-repeat;
  background-size: 550px;
  background-position: center;
  .inner {
    position: relative;
  }
}
.banner-content {
  width: 280px;
  height: 298px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  position: absolute;
  right: 0;
  top: 116px;
  padding: 40px;
  padding: 16px 24px 24px;
  h2 {
    font-size: 32px;
    font-weight: @jt-font-weight-medium;
    color: #16161c;
    line-height: 48px;
    margin-top: 0;
    // TODO: 和教学那里的下边距差了5px，不知道为啥
    margin-bottom: 21px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    span {
      font-size: 24px;
    }
  }
  .content-text {
    font-size: 16px;
    font-weight: 400;
    color: #606972;
    p {
      margin-bottom: 8px;
      span {
        color: #0082ff;
      }
    }
    ul {
      margin-top: 8px;
      line-height: 26px;
      li {
        &:before {
          content: '';
          width: 4px;
          height: 4px;
          margin-right: 5px;
          display: inline-block;
          background-color: #a0a6ab;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    :deep(.ant-spin-dot) {
      margin-top: 35px;
    }
  }
  .button {
    position: absolute;
    left: 0;
    bottom: 30px;
    right: 0;
    margin: auto;
    width: 232px;
    height: 40px;
    background: #0082ff;
    border-radius: 2px;
  }
  &.login {
    > p {
      margin: 0;
    }
  }
}
.banner-text {
  padding-top: 167px;
  h1 {
    font-size: 48px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    line-height: 67px;
    margin-bottom: 16px;
  }
  h2 {
    font-size: 16px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    line-height: 26px;
  }
}
.learned-course-container {
  margin-bottom: 0;
  li {
    color: #606972;
    line-height: 26px;
    font-size: 14px;
    list-style: inside;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    span {
      cursor: pointer;
      &:hover {
        color: #0082ff;
      }
    }
  }
}
.tab-box {
  position: absolute;
  left: 0;
  bottom: 14px;
  width: 100%;
  display: flex;
  justify-content: center;
  .tab-item {
    width: 24px;
    height: 4px;
    margin-right: 12px;
    cursor: pointer;
    background: #e0e1e1;
    &.active {
      background: #0082ff;
    }
  }
}
</style>
