<script setup>
import { FilterFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div class="competition-team-manage">
    <div class="team-manage-operation">
      <a-space>
        <jt-search v-model="keywords" placeholder="用户名/姓名/手机号" @handSearch="handleSearch"></jt-search>
        <a-button :disabled="tableEmpty" type="primary" ghost @click="handleDownload"><jt-icon type="icondaochu2"></jt-icon>导出</a-button>
      </a-space>
    </div>
    <a-config-provider>
      <template #renderEmpty>
        <jt-common-content :loading="loading" :empty="true" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text"> </jt-common-content>
      </template>
      <a-table row-key="userName" :loading="tableData.length > 0 ? loading : undefined" :data-source="tableData" :columns="columns" :pagination="false" size="middle" @change="handleTableChange">
        <template #teamSta-slot="{ value }">
          <div class="team-status-item">
            <div
              :class="{
                'already-join': value == 1,
                joining: value == 0,
                unjoin: value == 2,
              }"
            >
              {{ signStatusMaps[value] }}
            </div>
          </div>
        </template>
        <template #teamStaFilterIcon>
          <FilterFilled :style="{ color: teamSta != 3 ? '#108ee9' : undefined, right: 'auto' }" />
        </template>
        <template #teamStaFilterDropdown="{ setSelectedKeys, selectedKeys, confirm }">
          <div class="filter-team-status-select">
            <div class="all" :class="{ select: teamSta == 3 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 3)">全部</div>
            <div class="already-join" :class="{ select: teamSta == 1 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 1)">已加入团队</div>
            <div class="joining" :class="{ select: teamSta == 0 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 0)">报名流程中</div>
            <div class="unjoin" :class="{ select: teamSta == 2 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 2)">待加入团队</div>
          </div>
        </template>
        <template #identityFilterIcon>
          <FilterFilled :style="{ color: identityFlag !== '0' ? '#108ee9' : undefined, right: 'auto' }" />
        </template>
        <template #identityFilterDropdown="{ setSelectedKeys, selectedKeys, confirm }">
          <div class="filter-identity-status-select">
            <div class="all" :class="{ select: identityFlag == '0' }" @click="handleIdentitySelectClick(setSelectedKeys, selectedKeys, confirm, '0')">全部</div>
            <div class="teacher" :class="{ select: identityFlag == '3' }" @click="handleIdentitySelectClick(setSelectedKeys, selectedKeys, confirm, '3')">教师</div>
            <div class="student" :class="{ select: identityFlag == '2' }" @click="handleIdentitySelectClick(setSelectedKeys, selectedKeys, confirm, '2')">学生</div>
            <div class="developer" :class="{ select: identityFlag == '1' }" @click="handleIdentitySelectClick(setSelectedKeys, selectedKeys, confirm, '1')">开发者</div>
          </div>
        </template>
      </a-table>
    </a-config-provider>
    <jt-pagination v-if="!tableEmpty" :page-size="pagination.pageSize" :page-num="pagination.pageNum" :total="pagination.total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
  </div>
</template>

<script>
import jtSearch from '@/components/search/index.vue';
import { signStatusMaps } from '../../competitionConfig/index';
import { competitionApi } from '@/apis/index';
import { downloadFileWithToken } from '@/utils/file';
import { filterTip } from '@/common/text';
export default {
  name: 'TeamManage',
  components: { jtSearch },
  data() {
    return {
      signStatusMaps,
      loading: false,
      columns: [
        {
          key: 'userName',
          title: '用户名',
          dataIndex: 'userName',
          ellipsis: true,
          width: '100px',
        },
        {
          key: 'fullName',
          title: '姓名',
          dataIndex: 'fullName',
          ellipsis: true,
          width: '90px',
        },
        {
          key: 'phoneNum',
          title: '手机号',
          dataIndex: 'phoneNum',
        },
        {
          key: 'email',
          title: '邮箱',
          dataIndex: 'email',
          ellipsis: true,
          width: '180px',
        },
        {
          key: 'identity', // 过滤
          title: '身份',
          dataIndex: 'identity',
          slots: {
            filterDropdown: 'identityFilterDropdown',
            filterIcon: 'identityFilterIcon',
          },
        },
        {
          key: 'school',
          title: '学校',
          dataIndex: 'school',
          ellipsis: true,
          width: '120px',
        },
        {
          key: 'company',
          title: '工作单位',
          dataIndex: 'company',
          customRender(text) {
            return text || '--';
          },
          ellipsis: true,
          width: '90px',
        },
        {
          key: 'joinTime', // 排序
          title: '报名时间',
          sorter: true,
          dataIndex: 'joinTime',
          width: '180px',
        },
        {
          key: 'teamSta', // 过滤
          title: '状态',
          dataIndex: 'teamSta',
          slots: {
            filterDropdown: 'teamStaFilterDropdown',
            filterIcon: 'teamStaFilterIcon',
            customRender: 'teamSta-slot',
          },
        },
      ],
      tableData: [],
      keywords: '',
      teamSta: 3, // 状态  1已加入，2待加入，0报名流程中 3不筛选
      joinTimeFlag: 'desc', // 时间升序降序
      identityFlag: '0', // 身份
      pagination: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    tableEmpty() {
      return this.tableData.length === 0;
    },
    emptyStatus() {
      if (this.keywords || this.identityFlag !== '0' || this.teamSta !== 3) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关报名用户',
          text: filterTip,
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '暂无报名用户',
          text: '',
        };
      }
    },
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    handleSelectClick(setSelectedKeys, selectedKeys, confirmCallBack, index) {
      confirmCallBack();
      this.teamSta = index;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    handleIdentitySelectClick(setSelectedKeys, selectedKeys, confirmCallBack, index) {
      confirmCallBack();
      this.identityFlag = index;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    handleSearch(val) {
      this.keywords = val;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    handleDownload() {
      downloadFileWithToken({ url: `/competiton/web/manage/edit/join/user/export?cid=${this.$route.params.competitionId}` });
    },
    async getTableData() {
      const params = {
        cid: this.$route.params.competitionId,
        keyWord: this.keywords,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        joinTimeFlag: this.joinTimeFlag, // 时间排序
        teamSta: this.teamSta, // 状态过滤
        identityFlag: this.identityFlag, // 身份过滤
      };
      this.loading = true;
      const res = await competitionApi.getUserManageList(params);
      if (res.state === 'OK') {
        this.pagination.total = res.body.total;
        this.tableData = res.body.list;
      } else {
        this.pagination.total = 0;
        this.tableData = [];
      }
      this.loading = false;
    },
    handleTableChange(page, filter, sort) {
      this.joinTimeFlag = sort.order == 'ascend' ? 'asc' : 'desc';
      if (!sort.order) {
        this.joinTimeFlag = 'desc';
      }
      this.getTableData();
    },
    pageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    pageNumChange(pageNum) {
      this.pagination.pageNum = pageNum;
      this.getTableData();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.competition-team-manage {
  .team-manage-operation {
    position: absolute;
    top: 0px;
    right: 32px;
  }
  .common-middile-table-style();
}
.team-status-icons(@top:18px, @left:10px, @pgl: 30px) {
  .already-join,
  .joining,
  .unjoin {
    position: relative;
    padding-left: @pgl;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 8px;
      position: absolute;
      left: @left;
      top: @top;
    }
  }
  .already-join::before {
    background-color: #1dca94;
  }
  .joining::before {
    background-color: #0082ff;
  }
  .unjoin::before {
    background-color: #606972;
  }
}
.team-status-item {
  .team-status-icons(6px, 0px, 14px);
}
.filter-team-status-select {
  div {
    cursor: pointer;
    width: 120px;
    height: 40px;
    font-size: 12px;
    color: #606972;
    line-height: 40px;
    &:hover {
      background-color: #f0f8ff;
    }
  }
  .all {
    padding-left: 10px;
  }
  .team-status-icons();
  .select {
    background-color: #f0f8ff;
  }
}
.filter-identity-status-select {
  div {
    cursor: pointer;
    width: 120px;
    height: 40px;
    font-size: 12px;
    color: #606972;
    line-height: 40px;
    padding-left: 20px;
    &:hover {
      background-color: #f0f8ff;
    }
  }
  .select {
    background-color: #f0f8ff;
  }
}
</style>
