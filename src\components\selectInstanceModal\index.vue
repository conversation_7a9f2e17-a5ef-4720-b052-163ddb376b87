<template>
  <jtModal :visible="value" title="选择实例" class="select-instance-modal" :confirm-loading="!(isValid && selectedRowKeys.length > 0)" width="630px" @cancel="onClose" @ok="submitInstanceSelect">
    <div class="instance-modal-header">
      <span>模型训练实例列表</span>
      <a-input style="width: 240px" placeholder="搜索实例" @change="onTableSearch">
        <template #prefix>
          <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
        </template>
      </a-input>
    </div>
    <a-table ref="tableRef" v-loadmore="loadMore" rowkey="instanceId" :loading="loading" size="middle" :columns="isntanceTableColumn" :scroll="{ y: 400 }" :custom-header-row="subCustomHeaderRow" :custom-row="customRow" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }" :data-source="instanceTableData" :pagination="false">
      <template #status-slot="{ status }">
        <a-space>
          <div class="status-icon" :class="`color-${status}`"></div>
          {{ statusMaps[status] }}
        </a-space>
      </template>

      <template #name-slot="{ name }">
        <a-tooltip placement="topLeft">
          <template #title>
            <span>{{ name }}</span>
          </template>
          <div class="instance-name">{{ name }}</div>
        </a-tooltip>
      </template>
    </a-table>
    <span v-if="!isValid && selectedRowKeys.length > 0" style="color: red; font-size: 12px">{{ errorMessage }}</span>
  </jtModal>
</template>

<script>
import _ from 'lodash';
import { getInstanceList, validInstanceSize } from '@/apis/teaching.js';
import jtModal from '@/components/modal/index.vue';
export default {
  name: 'SelectInstanceModal',
  components: {
    jtModal,
  },
  props: {
    value: {
      type: Boolean,
      required: true,
    },
    instanceId: {
      type: String,
      default: '',
    },
    instanceName: {
      type: String,
      default: '',
    },
  },
  emits: ['input', 'cancel', 'ok'],
  data() {
    return {
      loading: false,
      searchValue: undefined,
      pageSize: 10, // 每页的条数
      pageNum: 1, // 第几页
      total: 0, // 总条数
      isValid: true,
      selectedRowKeys: [],
      tempSelectInstance: { instanceId: this.instanceId, instanceName: this.instanceName },
      instanceTableData: [],
      errorMessage: '',
      statusMaps: {
        1: '启动中',
        2: '运行中',
        3: '停止中',
        4: '失败',
        5: '锁定',
        0: '停止',
      },
      isntanceTableColumn: [
        {
          title: '实例名称',
          dataIndex: 'instanceName',
          slots: { customRender: 'name-slot' },
          ellipsis: true,
          width: '35%',
        },
        // 实例状态，启动中1，进行中2，停止中3，已停止4，失败5
        {
          title: '状态',
          dataIndex: 'instanceStatus',
          slots: { customRender: 'status-slot' },
          width: '20%',
        },
        {
          title: '最近启动时间',
          dataIndex: 'startTime',
        },
      ],
    };
  },
  watch: {
    value: function (newval) {
      if (newval) {
        // 每次打开弹窗需要重置，以弹窗传入的为准
        this.tempSelectInstance = { instanceId: this.instanceId, instanceName: this.instanceName };
        this.getTableData({}, true);
      } else {
        window.setTimeout(this.resetParams, 100);
      }
    },
  },
  methods: {
    subCustomHeaderRow() {
      return {
        style: {
          'font-size': '12px',
          color: '#121F2C',
        },
      };
    },
    customRow() {
      return {
        style: {
          'font-size': '12px',
          color: '#606972',
        },
      };
    },
    // 滚动更多加载
    loadMore() {
      if (!this.loading) {
        if (this.instanceTableData.length >= this.total || this.instanceTableData.length === 0) return;
        this.pageNum++;
        this.loading = true;
        this.getTableData({}, false, true);
      } else {
        return;
      }
    },
    // 校验选中的selectedRowKeys是否在table中
    validSelectKeys() {
      let instanceInTable = this.instanceTableData.some((item, index) => {
        if (item.instanceId === this.tempSelectInstance.instanceId) {
          this.selectedRowKeys = [index];
          return true;
        }
        return false;
      });
      if (!instanceInTable) {
        this.selectedRowKeys = [];
      }
    },
    resetParams() {
      this.searchValue = undefined;
      this.pageSize = 10;
      this.pageNum = 1;
      this.total = 0;
      this.selectedRowKeys = [];
      this.instanceTableData = [];
    },
    onTableSearch: _.debounce(function (e) {
      this.searchValue = e.target.value;
      this.pageNum = 1;
      this.getTableData();
    }, 500),

    // 获取table数据，initSelectKey是否选中当前instanceId，
    async getTableData(data = {}, initSelectKeys = false, isLoadmore = false) {
      this.loading = { tip: '加载中' };
      data = Object.assign(data, {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        instanceName: this.searchValue,
      });
      const res = await getInstanceList(data);
      if (res.code === 200) {
        if (isLoadmore) {
          this.instanceTableData = this.instanceTableData.concat(res.data.list);
          this.$nextTick(() => {
            // 由于滚动条进行滚动条的重置
            if (this.$refs.tableRef.$el) {
              this.$refs.tableRef.$el.querySelector('.ant-table-body').scrollTop = ((this.pageNum - 1) * 10 - 1) * 40;
            }
          });
        } else {
          this.instanceTableData = res.data.list;
        }
        this.total = res.data.total;
        // 存在编辑的情况，第一次打开需要初始化选中的key
        if (initSelectKeys && this.instanceId) {
          res.data.list.forEach((item, index) => {
            if (this.instanceId === item.instanceId) {
              this.selectedRowKeys = [index];
              return;
            }
          });
        }
      } else {
        this.instanceTableData = [];
        this.total = 0;
      }
      this.validSelectKeys();
      this.loading = false;
    },
    onClose(e) {
      this.closeInstanceModal();
      this.emitClose(e);
    },
    closeInstanceModal() {
      this.$emit('input', false);
    },

    emitClose(e) {
      this.$emit('cancel', e);
    },
    // 选择实例切换
    async onSelectChange(selectedRowKeys, record) {
      const currentRecord = record[0];
      const instanceId = currentRecord.instanceId;
      this.loading = { tip: '校验中' };
      const res = await validInstanceSize({ instanceId });
      if (res.state === 'OK' && res.body === true) {
        // 可用
        this.isValid = true;
        this.tempSelectInstance = currentRecord; // 需要存放临时，当真正点击确定时再赋值给selectInstance
      } else {
        // 不可用
        this.isValid = false;
        this.tempSelectInstance = {};
        this.errorMessage = '您选择的实例文件大小超过1G/文件个数超过1000个，请重新选择';
        if (res.errorCode === '-913') {
          this.errorMessage = '您所选择的实例挂载个人数据集超过30G，请重新选择';
        }
      }
      this.loading = false;
      this.selectedRowKeys = selectedRowKeys;
    },

    // 确定提交
    submitInstanceSelect() {
      const { instanceId, instanceName } = this.tempSelectInstance;
      this.$emit('ok', { instanceId, instanceName });
      this.closeInstanceModal();
    },
  },
  mounted() {
    this.getTableData();
  },
};
</script>

<style lang="less">
.select-instance-modal {
  .instance-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    span {
      color: #121f2c;
    }
  }
  .ant-table-fixed-header .ant-table-scroll .ant-table-header {
    overflow: hidden !important;
    margin-bottom: 0px;
  }
  .status-icon {
    display: inline-block;
    width: 7px;
    height: 7px;
    border-radius: 8px;
  }

  .color-1 {
    background-color: #ff931d;
  }
  .color-2 {
    background-color: #337dff;
  }
  .color-3 {
    background-color: #ff931d;
  }
  .color-4 {
    background-color: #f04134;
  }
  .color-5 {
    background-color: #999;
  }
  .color-0 {
    background-color: #6a7580;
  }
  .instance-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
