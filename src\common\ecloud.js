import { getEnvConfig } from '@/config';

export const ECLOUD_URL = getEnvConfig('ECLOUD_URL');
export const CONSOLE_ECLOUD_URL = getEnvConfig('CONSOLE_ECLOUD_URL');

export const ECLOUD_URL_CONFIG = {
  CONSOLE_DEEP_LEARNING_URL: '/api/page/deepLearning/web#/home/<USER>',
  ECLOUD_SSO_CHECK_URL: '/op-openstore-agent/ecloudsso/checkTicket/common',
};

// 移动云手机号发生变化弹窗状态
export const ECLOUD_UPDATE_INFO_MODAL_STATE = {
  UPDATED: 1,
  CLOSED_TO_REGISTER: 2,
  CLOSED_TO_ECLOUD: 3,
};

export const ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE = {
  // 比赛当前未开放团队编辑操作，邀请链接已失效，无法加入团队
  [ECLOUD_UPDATE_INFO_MODAL_STATE.UPDATED]: {
    title: '移动云账号变动提示',
    okText: '知道了',
    cancelText: '',
    content: '我们检测到您的移动云账号信息已更新，您在毕昇平台的比赛报名信息将被同步更新',
  },
  // 您已报名XXX，不可同时报名本比赛，无法加入团队
  [ECLOUD_UPDATE_INFO_MODAL_STATE.CLOSED_TO_REGISTER]: {
    title: '移动云账号变动提示',
    okText: '前往报名',
    cancelText: '',
    content: '我们检测到您的移动云账号已被注销，您在毕昇平台的比赛报名信息将被重置，您可重新开始报名流程',
  },
  // 您已报名XXX，不可同时报名本比赛，无法加入团队
  [ECLOUD_UPDATE_INFO_MODAL_STATE.CLOSED_TO_ECLOUD]: {
    title: '移动云账号变动提示',
    okText: '前往移动云',
    cancelText: '稍后再来',
    content: `我们检测到您的移动云账号已被注销，请尽快前往移动云，使用手机号`,
    secondContent: '，完成注册和深度学习平台订购，继续参加比赛',
  },
};

// 移动云手机号发生变化返回状态码 -921移动云账号已注销 -922移动云账号发生变化
export const ecloudErrorCodeMaps = {
  mobileCloudJoin: '-921',
  mobileCloudKnow: '-922',
};

// 移动云比赛 报名第二步一些文案显示
export const ECLOUD_COMPETITION_AUDIT_STATE = {
  PASS: 0, // 已认证
  AUDITING: 1, // 审核中
  FAILED: 2, // 审核失败
  UNAUDITING: 3, // 未认证
  UNREGISTER: null, // 未注册
};
export const ECLOUD_COMPETITION_REGISTER_TEXT_STATE = {
  // 已注册已实名  0
  [ECLOUD_COMPETITION_AUDIT_STATE.PASS]: {
    tips: '本次比赛要求您在“移动云深度学习平台”进行模型训练，我们检测到您的手机号已注册移动云账号并已完成实名认证',
    upStep: '下一步',
    nextStep: '取消',
  },
  // 审核中  1
  [ECLOUD_COMPETITION_AUDIT_STATE.AUDITING]: {
    tips: '本次比赛要求您在“移动云深度学习平台”进行模型训练，我们检测到您的手机号已注册移动云账号，并已提交实名认证申请',
    secondTips: '请您耐心等待，在实名认证审核（1-3个工作日）通过后，可继续订购深度学习平台',
    upStep: '稍后再来',
  },
  // 继续报名 审核失败  2
  [ECLOUD_COMPETITION_AUDIT_STATE.FAILED]: {
    tips: '本次比赛要求您在“移动云深度学习平台”进行模型训练，我们检测到您的手机号已注册移动云账号，但尚未完成实名认证，请您前往移动云官网，完成 ',
    upStep: '去移动云实名认证',
    nextStep: '稍后认证',
  },
  // 继续报名 未认证  3
  [ECLOUD_COMPETITION_AUDIT_STATE.UNAUDITING]: {
    tips: '本次比赛要求您在“移动云深度学习平台”进行模型训练，我们检测到您的手机号已注册移动云账号，但尚未完成实名认证，请您前往移动云官网，完成 ',
    upStep: '去移动云实名认证',
    nextStep: '稍后认证',
  },
  // 未注册  null
  [ECLOUD_COMPETITION_AUDIT_STATE.UNREGISTER]: {
    tips: '本次比赛要求您在“移动云深度学习平台”进行模型训练，我们检测到您的手机号尚未注册移动云账号，在获得您的同意后，我们将自动为您注册',
    upStep: '下一步',
    nextStep: '上一步',
  },
};
export const ECLOUD_COMPETITION_AUDIT_STATE_CLASS = {
  0: 'status-complete',
  1: 'status-auditmiddle',
  2: 'status-notcre',
  3: 'status-notcre',
};

export const ECLOUD_COMPETITION_AUDIT_STATE_TEXT = {
  0: '已认证',
  1: '审核中',
  2: '审核失败',
  3: '未认证',
};

// 移动云比赛 报名第三步一些文案显示
export const COMPETITION_DEEPLEARN_SUBSCRIBE_STATE = {
  UNSUBSCRIBE: 0,
  SUBSCRIBED: 1,
};
export const COMPETITION_DEEPLEARN_SUBSCRIBE_TEXT = {
  [COMPETITION_DEEPLEARN_SUBSCRIBE_STATE.UNSUBSCRIBE]: {
    tips: '您尚未订购“移动云深度学习平台”，请您前往',
    secondTips: '进行产品订购',
    upStep: '去移动云订购',
    nextStep: '稍后订购',
  },
  [COMPETITION_DEEPLEARN_SUBSCRIBE_STATE.SUBSCRIBED]: {
    tips: '您已订购“移动云深度学习平台”，点击下一步完成报名',
    upStep: '下一步',
    nextStep: '上一步',
  },
};
export const COMPETITION_DEEPLEARN_SUBSCRIBE_STATE_CLASS = {
  0: 'status-notcre',
  1: 'status-complete',
};
export const COMPETITION_DEEPLEARN_SUBSCRIBE_STATE_TEXT = {
  0: '未订购',
  1: '已订购',
};

// 移动云比赛type id
export const ECLOUD_COMPETITION_TYPE_ID_LIST = [4, 5];

// 订购移动云赢算力卡片 报名状态
export const ORDER_ECLOUD_JOIN_STATE_LIST = [4, 5, 6];
export const ORDER_ECLOUD_JOIN_STATE = {
  ORDER_OPEN: 4,
  JOINED_IS_OPEN: 5,
  JOINED_CLOSED: 6,
};
// 订购移动云赢算力卡片 订购移动云赢算力卡片活动开关
export const ECLOUD_CAMPAIGN_SWITCH_STATE = {
  OPEN: 1,
  CLOSE: 0,
};
// 订购移动云赢算力卡片 是否有移动云账号（是否进入过活动流程）
export const ECLOUD_ACCOUNT = {
  HAVE: 1,
  NOT: 0,
};

// 订购移动云赢算力卡片 是否有手机号
export const ECLOUD_PHONE_NUMBER = {
  HAVE: 1,
  NOT: 0,
};

// 订购移动云深度学习平台赢算力 当前加入步骤集合
export const ORDER_ECLOUD_JOIN_LIST = [1, 2, 3];

// 订购移动云深度学习平台赢算力 接口返回当前的步骤
export const ORDER_ECLOUD_JOIN_TYPE = {
  ACCOUNT_OPEN: 1,
  DEEP_LEARN_ORDER: 2,
  FINISH: 3,
};

// 订购移动云深度学习平台赢算力 步骤条对应的下标
export const ORDER_ECLOUD_STEPS_TYPE = {
  ACCOUNT_OPEN: 0,
  DEEP_LEARN_ORDER: 1,
  FINISH: 2,
};

// 订购移动云深度学习平台赢算力 接口返回与步骤条对应起来
export const ORDER_ECLOUD_JOIN_STEP_TYPE = {
  [ORDER_ECLOUD_JOIN_TYPE.ACCOUNT_OPEN]: ORDER_ECLOUD_STEPS_TYPE.ACCOUNT_OPEN,
  [ORDER_ECLOUD_JOIN_TYPE.DEEP_LEARN_ORDER]: ORDER_ECLOUD_STEPS_TYPE.DEEP_LEARN_ORDER,
  [ORDER_ECLOUD_JOIN_TYPE.FINISH]: ORDER_ECLOUD_STEPS_TYPE.FINISH,
};

// 订购移动云深度学习平台赢算力 订购移动云审核状态
export const ORDER_ECLOUD_AUDIT = {
  PASS: 0, // 已审核
  AUDITING: 1, // 审核中
  FAILED: 2, // 审核失败
  UNAUDITED: 3, // 未认证
  UNREGISTER: null, // 未注册
};

// 订购移动云深度学习平台赢算力 订购移动云顶部文案显示
export const ORDER_ECLOUD_AUDIT_TEXT_STATE = {
  // 已注册已实名  0
  [ORDER_ECLOUD_AUDIT.PASS]: {
    tips: '我们检测到您的手机号已注册移动云账号并完成实名认证',
    upStep: '下一步',
    nextStep: '取消',
  },
  // 审核中  1
  [ORDER_ECLOUD_AUDIT.AUDITING]: {
    tips: '我们检测到您的手机号已注册移动云账号，并已提交实名认证申请',
    secondTips: '请您耐心等待，在实名认证审核（1-3个工作日）通过后，可继续订购深度学习平台',
    upStep: '稍后再来',
  },
  // 继续报名 审核失败  2
  [ORDER_ECLOUD_AUDIT.FAILED]: {
    tips: '我们检测到您的手机号已注册移动云账号，但尚未完成实名认证。请您前往 ',
    upStep: '去移动云实名认证',
    nextStep: '稍后认证',
  },
  // 继续报名 未认证  3
  [ORDER_ECLOUD_AUDIT.UNAUDITED]: {
    tips: '恭喜，您已成功注册移动云账号，请您前往移动云官网，完成 ',
    upStep: '去移动云实名认证',
    nextStep: '稍后认证',
  },
  // 未注册  null
  [ORDER_ECLOUD_AUDIT.UNREGISTER]: {
    tips: '我们检测到您的手机号尚未注册移动云账号，在获得您的同意后，我们将自动为您注册',
    upStep: '下一步',
    nextStep: '稍后注册',
  },
};

// 订购移动云深度学习平台赢算力 订购状态class
export const ORDER_ECLOUD_AUDIT_CLASS = {
  0: 'status-complete',
  1: 'status-auditing',
  2: 'status-unaudited',
  3: 'status-unaudited',
};

// 订购移动云深度学习平台赢算力 订购状态文案
export const ORDER_ECLOUD_AUDIT_TEXT = {
  0: '已认证',
  1: '审核中',
  2: '审核失败',
  3: '未认证',
};

// 订购移动云深度学习平台赢算力 深度学习订购状态
export const DEEP_LEARN_AUDIT = {
  UNSUBSCRIBE: 0, // 未认证
  SUBSCRIBED: 1, // 已认证
};

// 订购移动云深度学习平台赢算力 深度学习订购顶部文案显示
export const DEEP_LEARN_AUDIT_TEXT_STATE = {
  [DEEP_LEARN_AUDIT.UNSUBSCRIBE]: {
    tips: '您尚未订购“移动云深度学习平台”，请您前',
    secondTips: '进行产品订购',
    upStep: '去移动云订购',
    nextStep: '稍后订购',
  },
  [DEEP_LEARN_AUDIT.SUBSCRIBED]: {
    tips: '您已订购移动云深度学习平台，点击下一步赢取算力豆',
    upStep: '下一步',
    nextStep: '上一步',
  },
};

export const ORDER_MODAL_TEXT = {
  [ORDER_ECLOUD_STEPS_TYPE.ACCOUNT_OPEN]: {
    title: '移动云账号实名认证',
    firstText: '请确认您已提交移动云账号实名认证',
    secondText: '您需要完成实名认证及深度学习平台订购，才能赢取算力豆哟~~',
    okText: '已提交认证',
    cancelText: '稍后认证',
  },
  [ORDER_ECLOUD_STEPS_TYPE.DEEP_LEARN_ORDER]: {
    title: '深度学习平台订购',
    firstText: '请确认您已成功订购深度学习平台',
    secondText: '您需要完成深度学习平台订购，才能赢取算力豆哟~',
    okText: '已成功订购',
    cancelText: '稍后订购',
  },
};
