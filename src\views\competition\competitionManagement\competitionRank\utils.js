import { GET } from '@/request';

export const formatDate = (date) => {
  if (!date) {
    return '';
  }
  const time = new Date(date);
  return `${time.getFullYear()}.${+time.getMonth() + 1 < 10 ? '0' + (+time.getMonth() + 1) : time.getMonth() + 1}.${+time.getDate() < 10 ? '0' + +time.getDate() : time.getDate()}`;
};
export const getRichText = (url) => {
  return GET(url, {});
};
// 根据后端返回，生成对应列和数据
export const getTableRow = ({ scoreName, sorter = false }) => {
  return new Promise((resolve) => {
    const newRow = [];
    for (const i in scoreName) {
      newRow.push({
        dataIndex: scoreName[i],
        key: scoreName[i],
        titleName: i,
        ellipsis: true,
        isAjaxRow: true,
        sorter,
        // width: 150,
        slots: {
          title: i + 'Slot',
        },
      });
    }
    resolve(newRow);
  });
};
// 将后端返回的数据的列放到对应的数据中
export const setTableDataByRow = (tableData, scoreKey = 'score') => {
  for (const i in tableData) {
    const scoreItem = JSON.parse(tableData[i][scoreKey]);
    for (const j in scoreItem) {
      const item = scoreItem[j];
      tableData[i][j] = item;
    }
  }
};
// 根据后端返回列，动态更改列宽
export const setTableRowWidth = (columns = [], baseWidth = 50) => {
  let width = 0;
  if (columns.length === 0) {
    width = baseWidth;
  } else {
    width = baseWidth / Math.max(columns.filter((x) => x.isAjaxRow).length, 1);
  }
  columns.forEach((x) => {
    if (!x.isAjaxRow) {
      x.width += width;
    }
  });
};
