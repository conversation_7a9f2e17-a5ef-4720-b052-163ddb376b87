<template>
  <div class="wrap">
    <rank-title-vue @change="rankTitleChange">
      <div ref="subTitle"></div>
    </rank-title-vue>
    <resultFileVue v-if="subtabId === rankTabsList[0].value" @getSubtitle="getSubtitle" />
    <rankVue v-if="subtabId === rankTabsList[1].value" @getSubtitle="getSubtitle" />
    <reviewVue v-if="subtabId === rankTabsList[2].value" @getSubtitle="getSubtitle" />
  </div>
</template>

<script>
import rankTitleVue from './rankTitle.vue';
import resultFileVue from './resultFile/result';
import rankVue from './rank/index.vue';
import reviewVue from './review/review';
import { rankTabsList } from './constants.js';

export default {
  name: 'manageHeader',
  components: {
    rankTitleVue,
    resultFileVue,
    rankVue,
    reviewVue,
  },
  data() {
    return {
      rankTabsList,
      subtabId: '',
      subtitle: '',
    };
  },
  methods: {
    rankTitleChange(e) {
      this.subtabId = e.subtabId;
      if (this.$refs.subTitle?.innerHTML) {
        this.$refs.subTitle.innerHTML = null;
      }
    },
    getSubtitle(subtitle) {
      this.$nextTick(() => {
        if (!this.$refs.subTitle?.innerHTML) {
          this.$refs.subTitle.appendChild(subtitle);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  padding: 0 32px;
}
</style>
