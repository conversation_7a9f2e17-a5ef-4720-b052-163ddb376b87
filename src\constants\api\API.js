/**
 * 该文件下存放接口
 */

import course_model from './API_course_model';
import course_management from './API_course_management';
import competition_model from './API_competition_model';
import dasteset_model from './API_dateset_model';
import dp_platform from './API_dp_platform';
import my_courses from './API_mycourses';
import h5_campus from './API_h5_campus_form';
import feedback_model from './API_feedback_model';
import keycloak_model from './API_keycloak_model';

export default { course_model, course_management, competition_model, dasteset_model, dp_platform, my_courses, h5_campus, feedback_model, keycloak_model };
