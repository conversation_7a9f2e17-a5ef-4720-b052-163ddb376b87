<script setup>
import { InfoCircleFilled, LinkOutlined, PlusOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <confirm-modal v-if="!needAttachment" :visible="confirmModalVisible" :spinning="submitLoading" :confirmLoading="submitLoading" :cancelLoading="submitLoading" title="确定提交作业吗？" @ok="handleOk" @cancel="$emit('cancel')" :getContainer="container">
      <template #icon>
        <InfoCircleFilled style="font-size: 18px; color: #7abdff" />
      </template>
      <p style="margin-bottom: 32px; font-size: 13px">您将提交当前实例中的文件，提交后如有修改，可在截止时间之前，再次提交</p>
    </confirm-modal>
    <modal v-else :visible="modalVisible" title="提交作业" @ok="handleOk" :confirmLoading="!uploaded || submitLoading" :cancelLoading="submitLoading" @cancel="$emit('cancel')" :getContainer="container">
      <div class="add-file-container">
        <p style="font-size: 13px">您将提交当前实例中的文件，提交后如有修改，可在截止时间之前，再次提交</p>
        <p class="tips">请同时提交50MB以内的一个文件，格式支持zip/rar/pdf/docx/pptx</p>
        <input ref="upload" hidden type="file" @change="selectFile($event)" multiple="multiple" />
        <div class="upload-operations">
          <p v-if="selected" :title="fileInfo.fileName" class="file-name" :class="fileInfo.status === 'exception' && 'file-exception'"><LinkOutlined style="margin-right: 8px" />{{ fileInfo.fileName }}</p>
          <a-button style="padding: 0" type="link" v-else @click="handleSelectFile"><PlusOutlined /><span>选择文件</span></a-button>
          <a-space v-if="selected" :size="24" style="margin-right: 35px">
            <a @click="goonUpload" v-if="fileInfo.status === 'exception'">继续上传</a>
            <a @click="resetFile">重新选择</a>
            <a @click="handleDelete">删除</a>
          </a-space>
        </div>
        <a-progress v-if="selected && !uploaded" :percent="fileInfo.percent" :status="fileInfo.status" :strokeWidth="2" :show-info="false" />
      </div>
    </modal>
  </div>
</template>

<script>
import confirmModal from '@/components/confirmModal';
import modal from '@/components/modal';
import { GET, POST, axiosWithNoToken } from '@/request';
import { getFilePsw } from '@/utils/file';

// 可以上传的文件类型
const FILE_TYPES = ['zip', 'rar', 'pdf', 'docx', 'pptx'];

const UPLOAD_STATUS = {
  MERGE_FINISH: 0, // 0.文件合并完毕
  PART_UPLOAD: 1, // 1.分片部分上传，续传状态
  SECOND_UPLOAD: 2, // 2.分片需要秒传状态
  PART_FINISH: 3, // 3.分片已传完成，可直接合并
  MERGE_FAIL: -1, // -1：文件合并失败
};
const RETRIES = 3; // 上传分片最大重试次数
const CHUNK_SIZE = 10 * 1024 * 1024; // 10M一个分块
const multipartInitUrl = '/course_model/web/course_student/fileupload/multipartinit';
const multipartComposeUrl = '/course_model/web/course_student/fileupload/compose';
const getUploadInfoUrl = '/course_model/web/course_student/fileupload/info';

const MaxFileSize = 50 * 1024 * 1024; // 50M

// eslint-disable-next-line no-unused-vars
let statusTimeoutId = null;

export default {
  name: 'homeworkSubmitDialog',
  components: {
    confirmModal,
    modal,
  },
  props: {
    visible: Boolean,
    assignmentId: String,
    needAttachment: Boolean,
    courseId: String,
    fileName: String,
    instanceId: String,
  },
  data() {
    return {
      loading: false,
      confirmModalVisible: false,
      modalVisible: false,
      selected: false,
      uploaded: false,
      chunkSize: CHUNK_SIZE,
      fileId: undefined,
      fileInfo: {
        percent: 0,
        status: 'normal', // active, success, exception
        reties: 0,
      },
      submitLoading: false, // 取消按钮 disabled
    };
  },
  computed: {
    container() {
      return () => document.querySelector('#jupyter-viewer');
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      } else {
        this.reset();
      }
    },
  },
  methods: {
    reset() {
      this.selected = false;
      this.fileId = undefined;
      this.confirmModalVisible = false;
      this.modalVisible = false;
      this.fileInfo = {
        percent: 0,
        status: 'normal', // active, success, exception
        reties: 0,
      };
    },
    init() {
      this.initVisible();
      this.initFileInfo();
    },
    initVisible() {
      if (this.needAttachment) {
        this.modalVisible = true;
      } else {
        this.confirmModalVisible = true;
      }
    },
    initFileInfo() {
      if (this.fileName) {
        this.uploaded = true;
        this.selected = true;
        this.fileInfo.fileName = this.fileName;
      } else {
        this.uploaded = false;
      }
    },
    submit() {
      const obj = {
        courseId: this.courseId,
        assignmentId: this.assignmentId,
        fileName: this.fileInfo.fileName,
        instanceId: this.instanceId, // 这里传一个instanceId会很别扭，但前期的调用设计如此，后续可以进行优化
      };
      this.uploaded = false;
      this.submitLoading = true;
      POST('/course_model/web/course_student/student/submitAssignment', obj, { useLoading: true }).then((res) => {
        if (res.state === 'OK') {
          this.$message.success('提交成功');
          this.$emit('ok');
        } else {
          this.$message.error(res.errorMessage);
        }
        this.uploaded = true;
        this.submitLoading = false;
      });
    },
    handleOk() {
      if (!this.checkFile()) {
        this.$message.error('请先上传附件');
        return;
      }
      this.submit();
    },
    checkFile() {
      if (this.needAttachment) {
        return this.uploaded;
      }
      return true;
    },
    async selectFile(e) {
      if (e.target.files.length === 0) {
        return;
      }

      const file = e.target.files[0];
      if (!this.validFileType(file.name)) {
        this.$message.error('不支持该格式，请重新选择');
        this.$refs.upload.value = '';
        return;
      }
      if (!this.validFileSize(file.size)) {
        this.$message.error(`文件大小需在${MaxFileSize / (1024 * 1024)}M以内`);
        this.$refs.upload.value = '';
        return;
      }
      this.selected = true;
      this.uploaded = false;
      const fileMd5 = await getFilePsw(file);
      this.fileInfo = {
        file,
        fileSize: file.size,
        fileName: file.name,
        contentType: file.type,
        chunkCount: this.getChunkCount(file), //计算当前选择文件需要的分片数量
        fileMd5, //计算当前文件前10mb和后10mb的md5拼接起来，防止重复    修改时间 文件长度 文件内容md5（头尾）
        percent: 0, // 文件上传进度
        chunkUrls: [],
        uploadedChunkCount: 0,
        status: 'active',
      };
      this.initUpload();
    },
    getChunkCount(file) {
      return Math.ceil(file.size / this.chunkSize);
    },
    validFileType(name) {
      if (name.indexOf('.') === -1) {
        return false;
      }
      const suffix = name.substring(name.lastIndexOf('.') + 1);
      return FILE_TYPES.includes(suffix.toLocaleLowerCase());
    },
    validFileSize(size) {
      return size <= MaxFileSize;
    },
    // 初始化上传或续传
    initUpload() {
      this.fileInfo.status = 'active';
      const initParams = {
        chunkCount: this.fileInfo.chunkCount,
        contentType: this.fileInfo.contentType,
        fileMd5: this.fileInfo.fileMd5,
        fileName: this.fileInfo.fileName,
        fileSize: this.fileInfo.fileSize,
        assignmentId: this.assignmentId,
      };
      POST(multipartInitUrl, initParams)
        .then((res) => {
          if (!this.visible) return;
          if (res.state === 'OK') {
            const { fileId, uploadStatus, uploadUrls } = res.body;
            this.fileId = fileId;
            switch (uploadStatus) {
              case UPLOAD_STATUS.MERGE_FINISH:
              case UPLOAD_STATUS.SECOND_UPLOAD:
                this.fileInfo.percent = 100;
                this.queryMergeResult();
                break;
              case UPLOAD_STATUS.PART_UPLOAD:
                this.fileInfo.chunkUrls = uploadUrls;
                this.fileInfo.uploadedChunkCount = this.fileInfo.chunkCount - this.fileInfo.chunkUrls.length;
                this.fileInfo.percent = (this.fileInfo.uploadedChunkCount / this.fileInfo.chunkCount) * 100;
                this.uploadChunks();
                break;
              case UPLOAD_STATUS.PART_FINISH:
                this.fileInfo.percent = 100;
                this.mergeFile();
                break;
              case UPLOAD_STATUS.MERGE_FAIL:
                this.fileInfo.percent = 0;
                this.fileInfo.status = 'exception';
                this.$message.error('文件合并失败');
                break;
              default:
                console.warn('未知状态');
                this.fileInfo.percent = 0;
                this.fileInfo.status = 'exception';
            }
          } else {
            // 已经上传过了，直接获取下载地址
            this.fileInfo.percent = 0;
            this.fileInfo.status = 'exception';
            this.$message.error('文件上传失败');
          }
        })
        .catch(() => {
          this.fileInfo.percent = 0;
          this.fileInfo.status = 'exception';
          this.$message.error('文件上传失败');
        });
    },
    // 上传分片
    uploadChunks(index = 0) {
      const chunkUrl = this.fileInfo.chunkUrls[index];
      const start = (chunkUrl.pageNumber - 1) * this.chunkSize;
      const end = Math.min(this.fileInfo.fileSize, start + this.chunkSize);
      const chunkFile = this.fileInfo.file.slice(start, end);

      // 非标接口，所以使用未封装的put方法
      axiosWithNoToken
        .put(chunkUrl.uploadUrl, chunkFile)
        .then((res) => {
          if (!this.visible) return;
          if (res.status == 200) {
            this.fileInfo.reties = 0;
            this.fileInfo.uploadedChunkCount++;
            const { uploadedChunkCount, chunkCount } = this.fileInfo;
            this.fileInfo.percent = (uploadedChunkCount / chunkCount) * 100;
            if (uploadedChunkCount === chunkCount) {
              this.mergeFile();
            } else {
              this.uploadChunks(index + 1);
            }
          } else {
            this.fileInfo.status = 'exception';
            this.$message.error('文件上传失败');
          }
        })
        .catch(() => {
          if (this.fileInfo.reties < RETRIES) {
            this.fileInfo.reties++;
            this.uploadChunks(index);
          } else {
            this.fileInfo.status = 'exception';
            this.$message.error('文件上传失败');
          }
        });
    },
    // 文件合并
    async mergeFile() {
      const mergeParams = {
        fileId: this.fileId,
      };
      const res = await GET(multipartComposeUrl, mergeParams);
      if (!this.visible) return;
      if (res.state === 'OK') {
        this.queryMergeResult();
      } else {
        this.fileInfo.status = 'exception';
      }
    },
    // 轮询查询合并结果
    async queryMergeResult() {
      const queryParams = {
        fileId: this.fileId,
      };
      const res = await GET(getUploadInfoUrl, queryParams);
      if (!this.visible) return;
      if (res.state === 'OK') {
        const { uploadStatus } = res.body;
        if (uploadStatus === UPLOAD_STATUS.MERGE_FINISH) {
          this.$message.success(`文件上传成功`);
          this.uploaded = true;
        } else if (uploadStatus === UPLOAD_STATUS.MERGE_FAIL) {
          this.fileInfo.status = 'exception';
        } else {
          statusTimeoutId = setTimeout(() => {
            this.queryMergeResult();
          }, 1000);
        }
      }
    },
    resetState() {
      this.selected = false;
      this.uploaded = false;
      this.fileId = undefined;
      this.fileInfo = {
        percent: 0,
        status: 'normal', // active, success, exception
        reties: 0,
      };
    },
    // 点击打开文件选择
    handleSelectFile() {
      this.$refs.upload.click();
    },
    handleCancel() {
      this.$emit('cancel');
    },
    // 继续上传
    goonUpload() {
      this.initUpload();
    },
    // 重新选择
    resetFile() {
      this.$refs.upload.value = '';
      this.$refs.upload.click();
    },
    handleDelete() {
      this.resetState();
      this.$refs.upload.value = ''; // 重复选择一个文件不触发change事件
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.add-file-container {
  .upload-operations {
    font-size: @jt-font-size-sm;
    color: @jt-primary-color;
    display: flex;
    justify-content: space-between;
    p {
      max-width: 200px;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      span {
        margin-left: @jt-gap-base;
      }
    }
    p.file-exception {
      color: @jt-error-color;
    }
  }
}
.tips {
  margin-top: 8px;
  margin-bottom: 24px;
  font-size: 12px;
  font-weight: 400;
  color: #a0a6ab;
}
</style>
