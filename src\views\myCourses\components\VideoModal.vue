<template>
  <a-modal v-model:open="open" :destroy-on-close="true" :title="editing ? '编辑视频' : '新增视频'" :mask-closable="false" @cancel="() => hideModal()">
    <a-form ref="addVideoModel" :model="formData" :colon="false" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <a-form-item
        label="视频名称"
        name="videoName"
        :rules="{
          required: true,
          message: '20个字符以内',
          trigger: 'change',
          max: 20,
        }"
      >
        <a-input v-model:value="formData.videoName" placeholder="请输入视频名称" />
      </a-form-item>

      <a-form-item
        label="嵌入代码"
        name="embededCode"
        :rules="{
          required: true,
          message: '请输入嵌入代码',
          trigger: 'blur',
        }"
      >
        <a-textarea v-model:value="formData.embededCode" placeholder="仅支持嵌入代码格式" :rows="3" />
        <!-- 因为安全策略，预览要传入id，新增的时候和编辑的时候，这里的预览去掉 -->
        <!-- <div slot="extra" style="text-align: right; color: #0082ff; cursor: pointer; margin-top: 10px" @click="previewVideo">
          <PlayCircleOutlined />
          <span style="margin-left: 8px">预览</span>
        </div> -->
      </a-form-item>
    </a-form>
    <template #footer>
      <a-space style="margin: 6px 16px">
        <a-button class="w-64" @click="() => hideModal()"> 取消 </a-button>
        <a-button class="w-64" type="primary" :disabled="submitBtnDisable" @click="handleConfirm"> 确定 </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addVideoOrDoc, editVideoOrDoc } from '@/apis/teaching.js';
import { openInNewTab } from '@/utils/utils';
export default {
  name: 'VideoModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    courseId: {
      type: String,
      default: '',
    },
    catalogId: {
      type: Number,
      default: 0,
    },
    editing: {
      type: Boolean,
    },
    selectResource: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  emits: ['hideModal'],
  data() {
    return {
      open: false,
      formData: {
        videoName: '',
        embededCode: '',
      },
    };
  },
  computed: {
    submitBtnDisable() {
      return !(this.formData.videoName && this.formData.embededCode);
    },
    encodedUrl() {
      if (this.formData.embededCode.indexOf('<iframe') > -1) {
        return encodeURIComponent(this.formData.embededCode);
      }
      return this.formData.embededCode;
    },
    decodedUrl() {
      if (this.selectResource.resourseUrl.indexOf('<iframe') > -1) {
        return this.selectResource.resourseUrl;
      }
      return decodeURIComponent(this.selectResource.resourseUrl);
    },
  },
  watch: {
    visible(val) {
      this.open = val;
    },
  },
  created() {
    this.open = this.visible;
  },
  mounted() {
    if (this.editing) {
      this.formData.videoName = this.selectResource.resourseName;
      this.formData.embededCode = this.decodedUrl;
    }
  },
  methods: {
    hideModal(bol = false) {
      this.formData.videoName = '';
      this.formData.embededCode = '';
      this.$emit('hideModal', bol);
    },
    previewVideo() {
      if (this.formData.embededCode) {
        let routeUrl = this.$router.resolve({
          path: `/video-viewer`,
          query: { id: this.selectResource.id },
        });
        openInNewTab(routeUrl.href);
      }
    },
    handleConfirm() {
      this.$refs.addVideoModel.validate().then(() => {
        if (this.editing) {
          editVideoOrDoc({
            id: this.selectResource.id,
            resourseName: this.formData.videoName,
            resourseUrl: this.encodedUrl,
            // resourseUrl: this.formData.embededCode,
          }).then((res) => {
            if (res.state === 'OK') {
              this.$message.success('修改视频成功');
              this.hideModal(true);
            } else {
              this.$message.error('修改视频失败');
            }
          });
        } else {
          addVideoOrDoc({
            courseId: this.courseId,
            catalogId: this.catalogId,
            resourseType: '2',
            resourseName: this.formData.videoName,
            resourseUrl: this.encodedUrl,
            // resourseUrl: this.formData.embededCode,
          }).then((res) => {
            if (res.state === 'OK') {
              this.$message.success('新增视频成功');
              this.hideModal(true);
            } else {
              this.$message.error('新增视频失败');
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
