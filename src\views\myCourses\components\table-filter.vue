<script setup>
import { FilterFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div ref="tableFilter" class="jt-table-filter">
    <FilterFilled class="jt-icon" :class="{ active: filterMaskDisplay || filterListActive > 0 }" style="position: relative" @click="toggleFilter" />
    <div v-if="filterMaskDisplay" ref="filterBox" class="filter-box" :style="{ ...filterBoxStyle, ...filterBoxDefaultStyle } || filterBoxDefaultStyle">
      <div class="filter-list">
        <div v-for="(item, index) in dataTypesFilterList" :key="index" class="filter-item" :class="{ active: index === filterListActive && item.value !== '' }" :style="{ paddingLeft: index === 0 || havePadding ? '0' : '' }" @click="dataTypefilter(item, index)">
          <span :class="['status-common', getStatusMarkStyle(item.value)]">{{ item.text }}</span>
        </div>
      </div>
      <div class="filter-mask" @click="toggleFilter"></div>
    </div>
  </div>
</template>
<style lang="less" scoped>
// //@import '~@/assets/font/iconfont.css';
@import '~@/assets/styles/var.less';
.jt-table-filter {
  display: flex;
  .jt-icon {
    color: #606972;
    &.active {
      color: @jt-primary-color;
    }
  }
}
.status-common {
  position: relative;
  padding-left: 14px;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 8px;
    position: absolute;
    left: 0px;
    top: 6px;
  }
}
.status-complete::before {
  background-color: #10c038;
}
.status-fail::before {
  background-color: @jt-error-color;
}
.status-queue::before {
  background-color: @jt-warn-color;
}
.status-running::before {
  background-color: @jt-primary-color;
}
.status-stoping::before {
  background-color: #6a7580;
}
.status-lockup::before {
  background-color: #999;
}
.filter-box {
  position: absolute;
  // left: 16px;
  // top: 0;
  z-index: 9999;
}
.filter-mask {
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.filter-list {
  width: 86px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
  border-radius: 2px;
  position: relative;
  z-index: 1000;
}
.filter-item {
  display: block;
  height: 32px;
  line-height: 32px;
  padding-left: 10px;
  cursor: pointer;
  &:hover,
  &.active {
    background: rgba(245, 248, 255, 1);
  }
}
</style>
<script>
export default {
  props: ['filterList', 'statusClassnamemap', 'havePadding', 'filterBoxStyle'],
  emits: ['changeStatus'],
  data() {
    return {
      status: '',
      filterListActive: -1,
      dataTypesFilterList: [],
      filterMaskDisplay: false,
      filterBoxDefaultStyle: {},
    };
  },
  watch: {
    filterMaskDisplay(status) {
      const tableFilter = this.$refs.tableFilter;
      if (status) {
        const style = {
          left: tableFilter.getBoundingClientRect().x + 5 + 'px',
          top: tableFilter.getBoundingClientRect().y + 20 + 'px',
        };
        this.filterBoxDefaultStyle = style;
        this.$nextTick(() => {
          const body = document.querySelector('body');
          const filterBox = this.$refs.filterBox;
          if (body.append) {
            body.append(filterBox);
          } else {
            body.appendChild(filterBox);
          }
        });
      }
    },
  },
  mounted() {
    if (!this.filterList) {
      this.dataTypesFilterList = [
        { value: '', text: '全部类型' },
        { value: 1, text: '启动中' },
        { value: 2, text: '运行中' },
        { value: 3, text: '停止中' },
        { value: 4, text: '失败' },
        { value: 5, text: '锁定' },
        { value: 0, text: '停止' },
      ];
    } else {
      this.dataTypesFilterList = this.filterList;
    }
  },
  methods: {
    getStatusMarkStyle(status) {
      let statusClassnamemap;
      if (this.statusClassnamemap) {
        statusClassnamemap = this.statusClassnamemap;
      } else {
        statusClassnamemap = new Map([
          [1, 'status-queue'],
          [3, 'status-queue'],
          [0, 'status-stoping'],
          // [this.STATUS.COMPLETE, "status-complete"],
          [2, 'status-running'],
          [4, 'status-fail'],
          [5, 'status-lockup'],
        ]);
      }
      if (!statusClassnamemap.has(status)) {
        return status;
      }
      return statusClassnamemap.get(status);
    },
    // 过滤
    dataTypefilter(item, index, list) {
      console.log(item);
      this.toggleFilter(list);
      this.filterListActive = index;
      if (item.value == -1) {
        this.status = '';
      } else {
        this.status = item.value;
      }
      // console.log(this.status);
      this.$emit('changeStatus', this.status);
    },
    toggleFilter() {
      this.filterMaskDisplay = !this.filterMaskDisplay;
    },
  },
};
</script>
