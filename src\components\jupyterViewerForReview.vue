<template>
  <!-- 基本逻辑和jupyterViewer一样，更改了查询实例，启动实例，停止实例，更新实例状态接口的相关逻辑 -->
  <div id="jupyter-viewer" class="jupyter-viewer-container">
    <div class="toolbar">
      <div class="left">
        <a-button v-if="showStop" :disabled="!stopable" class="btn" type="primary" danger @click="handleStop">停止实例</a-button>
        <a-button v-if="showStart" :disabled="!startable" class="btn" type="primary" @click="handleStart">启动实例</a-button>
        <a-tag class="align-center" style="margin-right: 12px" :class="`${statusItem.class}-tag`"> {{ statusItem.label }}</a-tag>
        <a-tag class="type-tag align-center">{{ (specMap[projectInfo.spec] || {}).label }}</a-tag>
        <span style="vertical-align: middle">
          <jt-icon style="font-size: 20px" :style="{ color: instanceIconColor }" :type="instanceIcon" />
        </span>
      </div>
      <div>
        <slot name="extraToolbar"></slot>
        <span class="icon-container">
          <a-popover placement="bottomRight">
            <template #content>
              <slot name="popoverContent">
                <p class="popover-container">页面关闭后10分钟课节实例将自动停止；</p>
                <p class="popover-container">最多只能同时运行4个课节或作业实例；</p>
                <p class="popover-container">当启动多于4个实例时，则自动关闭启动时间较久的已运行实例。</p>
              </slot>
            </template>
            <template #title>
              <span>说明</span>
            </template>
            <jt-icon class="icon info" type="iconinfo-circle"></jt-icon>
          </a-popover>
        </span>
        <span class="icon-container fullscreen">
          <jt-icon v-if="!isFullscreen" class="icon pointer" type="iconfullscreen" @click="handleFullScreen"></jt-icon>
          <jt-icon v-else class="icon pointer" type="iconcompress" @click="handleFullScreen"></jt-icon>
        </span>
      </div>
    </div>
    <div class="frame-container">
      <a-spin class="frame-spin" :spinning="spinning">
        <template v-if="showVscode || showJupyter">
          <iframe v-if="showVscode" id="vscodeFrame" name="vscodeFrame" frameborder="0" title=""></iframe>
          <iframe v-else :src="instanceInfo.jupyterURL" frameborder="0" title=""></iframe>
        </template>
        <div v-else class="empty">
          <div class="content">
            <img src="@/assets/image/emptys2x.png" alt="" />
            <p v-if="!stopable" class="main-text">实例停止中</p>
            <p v-if="stopable" class="main-text">您尚未启动实例哦～</p>
            <p v-if="stopable">
              <span>请</span>
              <a-button style="padding: 0" type="link" @click="handleStart">启动实例</a-button>
              <span>查看详情</span>
            </p>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import { GET, POST } from '../request';
import { BrowserDetect } from '@/utils/browserdetect';
import { storageApi } from '@/apis';

const statusMap = {
  0: {
    label: '停止',
    value: 'STOP',
    class: 'stopped',
  },
  1: {
    label: '启动中',
    value: 'STARTING',
    class: 'starting',
  },
  2: {
    label: '运行中',
    value: 'RUNNING',
    class: 'running',
  },
  3: {
    label: '停止中',
    value: 'STOPPING',
    class: 'stoping',
  },
  4: {
    label: '失败',
    value: 'FAIL',
    class: 'fail',
  },
  5: {
    label: '锁定',
    value: 'LOCKUP',
    class: 'lockup',
  },
  6: {
    label: '完成',
    value: 'COMPLETE',
    class: 'complete',
  },
  7: {
    label: '排队中',
    value: 'QUEUE',
    class: 'queue',
  },
};
const specMap = {
  cpu: {
    key: '0',
    label: 'CPU',
  },
  vGpu: {
    key: '1',
    label: 'vGPU',
  },
};

const INSTANCEICON = {
  VSCode: 'iconVscode',
  Jupyter: 'iconJupyter',
};

const INSTANCEICONCOLOR = {
  VSCode: '#6bb8f9',
  Jupyter: '#f3a579',
};

export default {
  props: {
    projectInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    stuAssignmentId: {
      type: String,
      default: '',
    },
  },
  emits: ['instanceIdChange'],
  data() {
    return {
      instanceInfo: {
        gpuSpecName: '',
        state: 0,
        jupyterURL: '',
      },
      stopping: false,
      starting: false,
      rendering: false,
      pending: false,
      isFullscreen: false,
      readyToRender: false,
      specMap,
      updateTimmer: null,
      queryTimmer: null,
      remainingCapacity: {
        resourceId: '',
        total: '0', // 总量
        indexCode: '',
        threadshold: '',
        usage: '0', // 使用量
        available: '0', // 剩余量
        resourceCode: '',
      }, // 用户储存
    };
  },
  computed: {
    instanceIconColor() {
      return this.vsCodeMode ? INSTANCEICONCOLOR.VSCode : INSTANCEICONCOLOR.Jupyter;
    },
    instanceIcon() {
      return this.vsCodeMode ? INSTANCEICON.VSCode : INSTANCEICON.Jupyter;
    },
    vsCodeMode() {
      return this.projectInfo.instanceModel === 'VSCode';
    },
    showVscode() {
      return this.running && this.instanceInfo.vscodeURL && this.vsCodeMode && this.readyToRender;
    },
    showJupyter() {
      return this.running && this.readyToRender && this.instanceInfo.jupyterURL && !this.vsCodeMode;
    },
    statusItem() {
      return statusMap[this.instanceInfo.state] || {};
    },
    running() {
      return this.statusItem.value === 'RUNNING';
    },
    pendingStatus() {
      return statusMap[this.instanceInfo.state].value === 'STARTING' || statusMap[this.instanceInfo.state].value === 'STOPPING';
    },
    showStart() {
      return statusMap[this.instanceInfo.state].value === 'STOP' || statusMap[this.instanceInfo.state].value === 'FAIL' || statusMap[this.instanceInfo.state].value === 'LOCKUP' || statusMap[this.instanceInfo.state].value === 'STARTING';
    },
    showStop() {
      return statusMap[this.instanceInfo.state].value === 'RUNNING' || statusMap[this.instanceInfo.state].value === 'STOPPING';
    },
    stopable() {
      return statusMap[this.instanceInfo.state].value !== 'STOPPING';
    },
    startable() {
      return statusMap[this.instanceInfo.state].value !== 'STARTING';
    },
    spinning() {
      return this.starting || this.stopping || this.rendering || this.pending;
    },
  },
  watch: {
    showVscode(val) {
      if (val) {
        this.$nextTick().then(async () => {
          this.initVSCode(this.instanceInfo);
        });
      }
    },
  },
  async mounted() {
    await this.getInstance();
  },
  unmounted() {
    clearTimeout(this.updateTimmer);
    clearTimeout(this.queryTimmer);
  },
  methods: {
    initVSCode(record) {
      console.log('initVSCode');
      function getStringPosition(str, cha, num) {
        let x = str.indexOf(cha);
        for (let i = 0; i < num; i++) {
          x = str.indexOf(cha, x + 1);
        }
        return x;
      }
      const base = record.vscodeURL.substring(getStringPosition(record.vscodeURL, '/', 2) + 1).replace('/login', '');
      const currentBrowser = BrowserDetect.init().browser;
      if (currentBrowser === 'Safari') {
        console.log(`%c Safari!!!!!`, 'background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff');
        const currentBase = base.charAt(0) === '/' ? base.slice(1, base.length) : base;
        console.log(currentBase, '--currentBase');
        this.creatFormPost(record.vscodeURL, {
          currentBase,
          password: record.podPwd,
        });
      } else {
        this.creatFormPost(record.vscodeURL, {
          base,
          password: record.podPwd,
        });
      }
    },
    /**
     * 使用js创建form标签提交表单（防止跨域）
     * @param { string } URL 提交地址
     * @param { object } param 参数
     */
    creatFormPost(URL, param) {
      //创建form表单
      const tempForm = document.createElement('form');
      tempForm.action = URL;
      //如需打开新窗口，form的target属性要设置为'_blank'
      tempForm.target = document.getElementById('vscodeFrame').name;
      tempForm.method = 'post';
      tempForm.style.display = 'none';
      //添加参数
      for (const item in param) {
        const opt = document.createElement('input'); //添加一个input类型
        opt.name = item;
        opt.value = param[item];
        tempForm.appendChild(opt);
      }
      document.body.appendChild(tempForm);
      //提交数据
      tempForm.submit();
    },
    async updateInstance() {
      if (statusMap[this.instanceInfo.state].value === 'RUNNING') {
        const obj = { stuAssignmentId: this.stuAssignmentId };
        await this.updateInstanceState(obj);
        this.updateTimmer = setTimeout(() => {
          this.updateInstance();
        }, 60000);
      }
    },
    handleFullScreen() {
      const element = document.getElementById('jupyter-viewer');
      if (screenfull.isEnabled) {
        screenfull.toggle(element);
        this.isFullscreen = !this.isFullscreen;
      }
    },
    async getInstanceState({ stuAssignmentId }) {
      const res = await GET('/course_model/web/teaching/assignment/instance/state', {
        stuAssignmentId,
      });
      if (res.code === 200) {
        return res.data;
      } else {
        return {
          state: 0,
        };
      }
    },

    async startInstance({ stuAssignmentId }) {
      const obj = {
        stuAssignmentId,
      };
      const res = await POST('/course_model/web/teaching/assignment/instance/run', obj);
      if (res.code === 200) {
        return res.data;
      } else {
        throw new Error(res.errorMessage || res.msg);
      }
    },

    async stopInstance({ stuAssignmentId }) {
      const obj = {
        stuAssignmentId,
      };
      const res = await POST('/course_model/web/teaching/assignment/instance/stop', obj);
      if (res.code === 200) {
        return res.data;
      } else {
        throw new Error(res.errorMessage || res.msg);
      }
    },

    async updateInstanceState({ stuAssignmentId }) {
      const obj = { stuAssignmentId };
      const res = await POST(`/course_model/web/teaching/assignment/instance/update`, obj);
      return res;
    },
    async getInstance() {
      const res = await this.getInstanceState({ stuAssignmentId: this.stuAssignmentId });
      this.instanceInfo = res;
      this.$emit('instanceIdChange', this.instanceInfo.instanceId);
      if (this.pendingStatus) {
        this.pending = true;
        this.queryTimmer = setTimeout(() => {
          this.getInstance();
        }, 1000);
      } else {
        this.pending = false;
        if (statusMap[this.instanceInfo.state].value === 'RUNNING') {
          this.renderFrame();
        }
      }
    },
    async renderFrame() {
      const url = this.vsCodeMode ? this.instanceInfo.vscodeURL : this.instanceInfo.jupyterURL;
      this.readyToRender = await this.getFrameStatus(url);
      if (this.readyToRender) {
        this.updateInstance();
        this.rendering = false;
      } else {
        this.rendering = true;
        this.queryTimmer = setTimeout(() => {
          this.getInstance();
        }, 1000);
      }
    },
    async getFrameStatus(url) {
      return fetch(url)
        .then((res) => {
          if (res.status === 200) {
            return true;
          } else {
            return false;
          }
        })
        .catch(() => {
          return false;
        });
    },
    // 启动实例
    async handleStart() {
      if (this.starting) {
        return;
      }
      const obj = {
        stuAssignmentId: this.stuAssignmentId,
      };
      this.starting = true;
      try {
        await this.startInstance(obj);
        await this.getInstance();
        await this.searchRemainingCapacity();
      } catch (error) {
        this.$notification.error({
          message: '启动实例失败',
          description: error.message,
        });
      } finally {
        this.starting = false;
      }
    },
    // 停止 实例
    async handleStop() {
      if (this.stopping) {
        return;
      }
      const obj = {
        stuAssignmentId: this.stuAssignmentId,
      };
      this.stopping = true;
      try {
        await this.stopInstance(obj);
        this.getInstance();
      } catch (error) {
        this.$notification.error({
          message: '停止实例失败',
          description: error.message,
        });
      } finally {
        this.stopping = false;
      }
    },
    // 存储用满时仍可进入实例
    searchRemainingCapacity() {
      storageApi.searchRemainingCapacity().then((res) => {
        if (res.state === 'OK') {
          res.body.available = this.accMul(res.body.available, this.accMul(this.accMul(1024, 1024), 1024));
          res.body.total = this.accMul(res.body.total, this.accMul(this.accMul(1024, 1024), 1024));
          res.body.usage = this.accMul(res.body.usage, this.accMul(this.accMul(1024, 1024), 1024));
          this.remainingCapacity = res.body;
          if (this.remainingCapacity.available === 0) {
            this.$notification.error({
              message: '存储不足提示',
              description: '您的剩余可用存储为 0，请及时清理或申请扩容',
            });
          }
        } else {
          this.remainingCapacity = {
            resourceId: '',
            total: null,
            indexCode: '',
            threadshold: '',
            usage: null,
            available: null,
            resourceCode: '',
          }; // 用户储存
        }
      });
    },
    accMul(arg1, arg2) {
      var m = 0,
        s1 = arg1.toString(),
        s2 = arg2.toString();
      try {
        m += s1.split('.')[1].length;
      } catch (e) {
        console.warn(e);
      }
      try {
        m += s2.split('.')[1].length;
      } catch (e) {
        console.warn(e);
      }
      return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

div {
  display: flex;
}
.jupyter-viewer-container {
  flex-direction: column;
  width: 100%;
  padding: 0 20px;
}
.toolbar {
  justify-content: space-between;
  background: #fff;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e0e1e1;
  .btn {
    margin-right: 16px;
  }
  .left {
    display: block;
    padding: 0 !important;
  }
  .icon-container {
    display: flex;
    align-items: center;
    padding: 0 16px;
    &.fullscreen {
      border-left: 1px solid #ddd;
    }
    .icon {
      font-size: 24px;
      &:hover {
        color: #0082ff;
      }
    }
  }
}
.frame-container {
  flex: 1;
  .frame-spin {
    width: 100%;
  }
  :deep(.ant-spin-container) {
    width: 100%;
  }
  iframe {
    width: 100%;
    height: 100%;
  }
}
.pointer {
  cursor: pointer;
}
.popover-container {
  width: 304px;
}
.stopped-tag {
  background: #fff5f6;
  border-radius: 12px;
  border: 1px solid #ff454d;
  color: #ff454d;
}
.running-tag {
  background: #eafff8;
  border-radius: 12px;
  border: 1px solid #17bb85;
  color: #17bb85;
}
.starting-tag {
  background: #fff6ee;
  border-radius: 12px;
  border: 1px solid #ff8415;
  color: #ff8415;
}
.type-tag {
  background: #e1ecff;
  border-radius: 12px;
  color: #337dff;
  border: none;
}
.align-center {
  text-align: center;
}
.empty {
  width: 100%;
  height: 100%;
  background: #fff;
  justify-content: center;
  .content {
    width: 394px;
    flex-direction: column;
    align-items: center;
    img {
      width: 100%;
      margin-bottom: -84px;
    }
    .main-text {
      margin: 0;
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
    }
  }
}
</style>
