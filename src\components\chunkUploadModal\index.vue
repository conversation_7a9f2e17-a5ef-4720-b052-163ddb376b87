<script setup>
import { PlusOutlined } from '@ant-design/icons-vue';
</script>
<!--  富文本上传附件的弹框 -->
<template>
  <modal title="上传附件" class="select-file-modal" :visible="visible" :confirmLoading="!(fileInfo.status === 'success' && fileInfo.downloadUrl)" @cancel="handleCancel" @ok="handleOk">
    <div class="add-file-container">
      <input ref="upload" hidden type="file" @change="selectFile($event)" multiple="multiple" />
      <div class="upload-operations">
        <p v-if="selected" :title="fileInfo.fileName" class="file-name" :class="fileInfo.status === 'exception' && 'file-exception'"><jt-icon type="iconlianjie" style="margin-right: 8px; font-size: 16px" />{{ fileInfo.fileName }}</p>
        <p v-else @click="handleSelectFile"><PlusOutlined /><span>选择文件</span></p>
        <a-space v-if="selected" :size="24">
          <a @click="goonUpload" v-if="fileInfo.status === 'exception'">继续上传</a>
          <a @click="resetFile">重新选择</a>
          <a @click="handleDelete">删除</a>
        </a-space>
      </div>
      <a-progress style="padding-left: 20px" v-if="selected && fileInfo.status !== 'success'" :percent="fileInfo.percent" :status="fileInfo.status" :strokeWidth="2" :show-info="false" />
    </div>
  </modal>
</template>

<script>
import Modal from '@/components/modal/index.vue';
import { homeworkApi } from '@/apis';
import { axiosWithNoToken } from '@/request';
import { getFilePsw } from '@/utils/file.js';

// 可以上传的文件类型
const FILE_TYPES = ['csv', 'txt', 'npz', 'npy', 'rar', 'zip', 'tar', 'gz', '7z', 'json', 'h5', 'tfrecord', 'pb', 'pbtxt', 'pkl', 'pth', 'jar', 'bin', 'jpg', 'png', 'jpeg', 'bmp', 'gif', 'psd', 'html', 'rmvb', 'mp3', 'mp4', 'mp5', 'm4a', 'aac', 'flac', 'wav', 'wma', 'mov', 'doc', 'docx', 'pdf', 'xls', 'xlsx', 'py', 'md', 'ipynb', 'log', 'yaml', 'sh', 'md', 'ppt', 'pptx', 'mat'];

const UPLOAD_STATUS = {
  MERGE_FINISH: 0, // 0.文件合并完毕
  PART_UPLOAD: 1, // 1.分片部分上传，续传状态
  SECOND_UPLOAD: 2, // 2.分片需要秒传状态
  PART_FINISH: 3, // 3.分片已传完成，可直接合并
  MERGE_FAIL: -1, // -1：文件合并失败
};
const UPLOAD_MSG = {
  ERROR_MSG: '上传失败',
  SUCCESS_MSG: '上传成功',
  SIZE_LIMIT: '文件大小需在1GB以内',
  TYPE_LIMIT: '不支持该格式，请重新选择',
  ZERO_SIZE: '上传失败，文件为空',
};
const RETRIES = 3; // 上传分片最大重试次数
let statusTimeoutId = null;

export default {
  name: 'UploadModal',
  components: {
    Modal,
  },
  props: {
    visible: {
      // 弹框关闭的时候需要清理上传相关的逻辑
      type: Boolean,
      default: false,
    },
  },
  watch: {
    visible(val) {
      if (!val) {
        this.handleDelete();
        if (statusTimeoutId) {
          clearTimeout(statusTimeoutId);
          statusTimeoutId = null;
        }
      }
    },
  },
  data() {
    return {
      selected: false,
      chunkSize: 10 * 1024 * 1024, // 10M一个分块
      fileId: undefined,
      fileInfo: {
        percent: 0,
        status: 'normal', // active, success, exception
        reties: 0,
      },
    };
  },
  methods: {
    async selectFile(e) {
      if (e.target.files.length === 0) {
        return;
      }

      const file = e.target.files[0];

      if (file.size === 0) {
        this.$message.error(UPLOAD_MSG.ZERO_SIZE);
        this.$refs.upload.value = '';
        return;
      }
      if (!this.validFileType(file.name)) {
        this.$message.error(UPLOAD_MSG.TYPE_LIMIT);
        this.$refs.upload.value = '';
        return;
      }
      if (!this.validFileSize(file.size)) {
        this.$message.error(UPLOAD_MSG.SIZE_LIMIT);
        this.$refs.upload.value = '';
        return;
      }
      this.selected = true;
      this.fileInfo = {
        file,
        fileSize: file.size,
        fileName: file.name,
        contentType: file.type,
        chunkCount: Math.ceil(file.size / this.chunkSize), //计算当前选择文件需要的分片数量
        fileMd5: file ? await getFilePsw(file) : '', //计算当前文件前10mb和后10mb的md5拼接起来，防止重复    修改时间 文件长度 文件内容md5（头尾）
        percent: 0, // 文件上传进度
        chunkUrls: [],
        uploadedChunkCount: 0,
        status: 'active',
      };
      this.initUpload();
    },
    validFileType(name) {
      if (name.indexOf('.') === -1) {
        return false;
      }
      const suffix = name.substring(name.lastIndexOf('.') + 1);
      return FILE_TYPES.includes(suffix.toLocaleLowerCase()) || name.slice(-7) === '.tar.gz';
    },
    validFileSize(size) {
      if (size > 1024 * 1024 * 1024) {
        // 最多1G
        return false;
      } else {
        return true;
      }
    },
    // 初始化上传或续传
    initUpload() {
      this.fileInfo.status = 'active';
      const initParams = {
        chunkCount: this.fileInfo.chunkCount,
        contentType: this.fileInfo.contentType,
        fileMd5: this.fileInfo.fileMd5,
        fileName: this.fileInfo.fileName,
        fileSize: this.fileInfo.fileSize,
      };
      homeworkApi
        .multipartInit(initParams)
        .then((res) => {
          if (!this.visible || !this.selected) return;
          if (res.state === 'OK') {
            const { fileId, uploadStatus, uploadUrls } = res.body;
            this.fileId = fileId;
            switch (uploadStatus) {
              case UPLOAD_STATUS.MERGE_FINISH:
              case UPLOAD_STATUS.SECOND_UPLOAD:
                this.fileInfo.percent = 100;
                this.queryMergeResult();
                break;
              case UPLOAD_STATUS.PART_UPLOAD:
                this.fileInfo.chunkUrls = uploadUrls;
                this.fileInfo.uploadedChunkCount = this.fileInfo.chunkCount - this.fileInfo.chunkUrls.length;
                this.fileInfo.percent = (this.fileInfo.uploadedChunkCount / this.fileInfo.chunkCount) * 100;
                this.uploadChunks();
                break;
              case UPLOAD_STATUS.PART_FINISH:
                this.fileInfo.percent = 100;
                this.mergeFile();
                break;
              default:
                this.fileInfo.percent = 0;
                this.fileInfo.status = 'exception';
                this.$message.error(UPLOAD_MSG.ERROR_MSG);
            }
          } else {
            this.fileInfo.percent = 0;
            this.fileInfo.status = 'exception';
            this.$message.error(UPLOAD_MSG.ERROR_MSG);
          }
        })
        .catch(() => {
          this.fileInfo.percent = 0;
          this.fileInfo.status = 'exception';
          this.$message.error(UPLOAD_MSG.ERROR_MSG);
        });
    },
    // 上传分片
    uploadChunks(index = 0) {
      const chunkUrl = this.fileInfo.chunkUrls[index];
      const start = (chunkUrl.pageNumber - 1) * this.chunkSize;
      const end = Math.min(this.fileInfo.fileSize, start + this.chunkSize);
      const chunkFile = this.fileInfo.file.slice(start, end);

      axiosWithNoToken
        .put(chunkUrl.uploadUrl, chunkFile)
        .then((res) => {
          if (!this.visible || !this.selected) return;
          if (res.status == 200) {
            this.fileInfo.reties = 0;
            this.fileInfo.uploadedChunkCount++;
            const { uploadedChunkCount, chunkCount } = this.fileInfo;
            this.fileInfo.percent = (uploadedChunkCount / chunkCount) * 100;
            if (uploadedChunkCount === chunkCount) {
              this.mergeFile();
            } else {
              this.uploadChunks(index + 1);
            }
          } else {
            this.fileInfo.status = 'exception';
            this.$message.error(UPLOAD_MSG.ERROR_MSG);
          }
        })
        .catch(() => {
          if (this.fileInfo.reties < RETRIES) {
            this.fileInfo.reties++;
            this.uploadChunks(index);
          } else {
            this.fileInfo.status = 'exception';
            this.$message.error(UPLOAD_MSG.ERROR_MSG);
          }
        });
    },
    // 文件合并
    async mergeFile() {
      const mergeParams = {
        fileId: this.fileId,
      };
      const res = await homeworkApi.multipartCompose(mergeParams);
      if (!this.visible || !this.selected) return;
      if (res.state === 'OK') {
        this.queryMergeResult();
      } else {
        this.fileInfo.status = 'exception';
        this.$message.error(UPLOAD_MSG.ERROR_MSG);
      }
    },
    // 轮询查询合并结果
    async queryMergeResult() {
      const queryParams = {
        fileId: this.fileId,
      };
      const res = await homeworkApi.UploadState(queryParams);
      if (!this.visible || !this.selected) return;
      if (res.state === 'OK') {
        const { uploadStatus } = res.body;
        if (uploadStatus === UPLOAD_STATUS.MERGE_FINISH) {
          this.getDownLoadUrl();
        } else if (uploadStatus === UPLOAD_STATUS.MERGE_FAIL) {
          this.fileInfo.status = 'exception';
          this.$message.error(UPLOAD_MSG.ERROR_MSG);
        } else {
          statusTimeoutId = setTimeout(() => {
            this.queryMergeResult();
          }, 1000);
        }
      }
    },
    // 获取上传附件的下载地址
    async getDownLoadUrl() {
      const reqParam = {
        fileId: this.fileId,
      };
      const res = await homeworkApi.getDownloadUrl(reqParam);
      if (!this.visible || !this.selected) return;
      if (res.state === 'OK') {
        this.fileInfo.status = 'success';
        this.fileInfo.downloadUrl = res.body.fileUrl;
        this.$message.success(UPLOAD_MSG.SUCCESS_MSG);
      } else {
        this.fileInfo.status = 'exception';
        this.$message.error(UPLOAD_MSG.ERROR_MSG);
      }
    },
    resetState() {
      this.selected = false;
      this.fileId = undefined;
      this.fileInfo = {
        percent: 0,
        status: 'normal', // active, success, exception
        reties: 0,
      };
    },
    // 点击打开文件选择
    handleSelectFile() {
      this.$refs.upload.click();
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      this.$emit('ok', { downloadUrl: this.fileInfo.downloadUrl, fileName: this.fileInfo.fileName });
    },
    // 继续上传
    goonUpload() {
      this.initUpload();
    },
    // 重新选择
    resetFile() {
      this.$refs.upload.value = '';
      this.$refs.upload.click();
    },
    handleDelete() {
      this.resetState();
      this.$refs.upload.value = ''; // 重复选择一个文件不触发change事件
    },
  },
};
</script>

<style lang="less">
@import '~@/assets/styles/index.less';
.select-file-modal {
  .add-file-container {
    .upload-operations {
      font-size: @jt-font-size-base;
      color: @jt-primary-color;
      display: flex;
      justify-content: space-between;
      p {
        max-width: 200px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        span {
          margin-left: @jt-gap-base;
        }
        &.file-name {
          color: @jt-text-color;
        }
        &.file-exception.file-name {
          color: @jt-error-color;
        }
      }
    }
  }
}
</style>
