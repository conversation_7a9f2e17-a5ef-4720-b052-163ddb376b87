<template>
  <div class="receive-bean-button">
    <div class="receive-bean-icon" :class="{ 'cursor-hover': buttonText === '领取算力豆' && !rechargeBeanLoading }" @click="handleReceiveBean">{{ buttonText }}</div>
    <a-modal v-model:open="modalVisible" class="receive-bean-message-modal" :width="420" :footer="null" @cancel="() => (modalVisible = false)">
      <div class="message-modal-body">
        <h2>领取算力豆成功！</h2>
        <div class="content-paragraph">
          <p style="margin-bottom: 10px">恭喜完成《{{ courseName }}》学习</p>
          <p>
            获得<span class="bean-count">{{ giveBeansByCourse.beanCount }}</span
            >个算力豆
          </p>
        </div>
        <div style="text-align: right">
          <a-button style="width: 96px" type="primary" @click="() => (modalVisible = false)">知道了</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { courseModelApi } from '@/apis';
export default {
  name: 'ReceiveBeanButton',
  props: {
    courseName: {
      type: String,
      default: '',
    },
    courseId: {
      type: String,
      default: '',
    },
    learnComplete: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      modalVisible: false,
      received: true, // 是否领取过
      receiveStatusLoading: true,
      rechargeBeanLoading: false,
    };
  },
  computed: {
    ...mapState(['giveBeansByCourse']),
    buttonText() {
      if (this.received) {
        return '已领完算力';
      } else if (!this.learnComplete || this.receiveStatusLoading) {
        return '学完领算力';
      } else {
        return '领取算力豆';
      }
    },
  },
  created() {
    this.getReceiveStatusByCourseId();
  },
  methods: {
    async getReceiveStatusByCourseId() {
      const res = await courseModelApi.getReceiveStatusByCourseId({
        courseId: this.courseId,
      });
      if (res.state === 'OK') {
        this.received = res.body;
      }
      this.receiveStatusLoading = false;
    },
    rechargeBeanByCourse() {
      this.rechargeBeanLoading = true;
      courseModelApi
        .rechargeBeanByCourse({
          courseName: this.courseName,
          courseId: this.courseId,
        })
        .then((res) => {
          if (res.state === 'OK') {
            this.received = true;
            this.modalVisible = true;
          }
        })
        .finally(() => {
          this.rechargeBeanLoading = false;
        });
    },
    handleReceiveBean() {
      if (this.buttonText === '领取算力豆' && !this.rechargeBeanLoading) {
        this.rechargeBeanByCourse();
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.receive-bean-button {
  position: absolute;
  top: 0px;
  right: -6px;
  text-align: center;
  line-height: 63px;
  .receive-bean-icon {
    width: 126px;
    height: 63px;
    background-image: url('~@/assets/image/course/bean-icon.png');
    background-size: 100%;
    background-repeat: no-repeat;
    color: #c13900;
    font-size: 12px;
    padding-left: 36px;
    transition: 0.3s all;
    &:hover {
      background-image: url('~@/assets/image/course/bean-icon-hover.png');
    }
  }
  .cursor-hover {
    cursor: pointer;
  }
}
.receive-bean-message-modal {
  :deep(.ant-modal-body) {
    padding: 40px;
    background-image: url('~@/assets/image/course/bean-modal-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .message-modal-body {
      h2 {
        font-size: 20px;
        font-weight: 600;
        color: #f17506;
        line-height: 28px;
        position: relative;
        &::before {
          content: '';
          width: 32px;
          height: 4px;
          background: #f17506;
          position: absolute;
          top: 32px;
        }
      }
      .content-paragraph {
        color: #552b00;
        line-height: 22px;
        margin: 40px 0px 32px;

        p:nth-child(2) {
          font-size: 16px;
          font-weight: @jt-font-weight-medium;
          .bean-count {
            color: #f17506;
            font-weight: 600;
            font-size: 24px;
          }
        }
      }
    }
  }
}
</style>
