<template>
  <div class="course-tree-container">
    <div class="tree-container">
      <div class="tree-title">{{ title }}</div>
      <div class="tree-detail">
        <div class="top-content">
          <div v-for="item in topContents" :key="item.title" class="content-container">
            <div class="border-line"></div>
            <ul class="content-list">
              <li class="content-title">
                <span class="circle title">
                  <span></span>
                </span>
                <span class="text title">
                  {{ item.title }}
                </span>
              </li>
              <li v-for="it in item.contents" :key="it.name" class="content-item">
                <span class="circle item" :class="{ active: it.active }"></span>
                <span class="link text" :class="{ disabled: +it.disabled }" @click="handleNavigateTo(it)">
                  {{ it.name }}
                </span>
              </li>
            </ul>
          </div>
        </div>
        <div class="divider-line"></div>
        <div class="bottom-content">
          <div v-for="item in bottomContents" :key="item.title" class="content-container">
            <div class="border-line"></div>
            <ul class="content-list">
              <li class="content-title">
                <span class="circle title">
                  <span></span>
                </span>
                <span class="text title">
                  {{ item.title }}
                </span>
              </li>
              <li v-for="it in item.contents.concat().reverse()" :key="it.name" class="content-item">
                <span class="circle item"></span>
                <span class="link text" :class="{ disabled: +it.disabled }" @click="handleNavigateTo(it)">
                  {{ it.name }}
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    treeData: {
      title: String,
      contents: Array,
    },
  },
  emits: ['contentClick'],
  data() {
    return { topContents: [], bottomContents: [], title: '' };
  },
  watch: {
    treeData: 'generateData',
  },
  mounted() {
    this.generateData();
  },

  methods: {
    handleNavigateTo(value) {
      if (+value.disabled) {
        return;
      }
      this.$emit('contentClick', value);
    },
    generateData() {
      const treeData = this.treeData;
      const topContents = [];
      const bottomContents = [];
      const title = treeData.title;
      const contents = [...treeData.contents];
      let current = null;
      function pickContents() {
        if (contents.length === 0) {
          return;
        }
        if (current === topContents) {
          bottomContents.push(contents.shift());
          current = bottomContents;
        } else {
          topContents.push(contents.shift());
          current = topContents;
        }
        pickContents();
      }
      pickContents();
      this.topContents = topContents;
      this.bottomContents = bottomContents;
      this.title = title;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
div,
ul,
li {
  display: flex;
}
ul {
  margin: 0;
}
.course-tree-container {
  flex-direction: column;
  width: fit-content;
}
.tree-container {
  align-items: center;
}
.tree-title {
  width: 40px;
  height: 160px;
  padding: 0 12px;
  align-items: center;
  background: linear-gradient(180deg, #65c3ff 0%, #0082ff 100%);
  border-radius: 24px;
  color: #fff;
  font-weight: @jt-font-weight-medium;
}
.tree-detail {
  flex-direction: column;
  justify-content: center;
  .top-content {
    align-items: flex-end;
    margin-bottom: -1px;
    padding-left: 124px;
    .border-line {
      width: 24px;
      border: 1px solid #2c79fb;
      border-radius: 0 0 24px 0;
      border-top: none;
      border-left: none;
    }
    .content-list {
      top: -12px;
    }
  }
  .bottom-content {
    align-items: flex-start;
    margin-top: -1px;
    padding-left: 244px;
    .border-line {
      width: 24px;
      border: 1px solid #2c79fb;
      border-radius: 0 24px 0 0;
      border-bottom: none;
      border-left: none;
    }
    .content-list {
      bottom: -12px;
      flex-direction: column-reverse;
    }
  }
  .content-list {
    flex-direction: column;
    position: relative;

    li {
      align-items: center;
      span {
        display: flex;
        &.text {
          margin-left: 4px;
          &.title {
            margin-left: 8px;
            font-size: 18px;
            font-weight: @jt-font-weight-medium;
            color: #121f2c;
          }
        }
      }
      padding: 4px 0;
    }
  }

  .content-container {
    width: 252px;
  }
  .divider-line {
    height: 1px;
    background: #2c79fb;
    width: calc(100% - 252px);
  }
  .link {
    font-size: 14px;
    font-weight: 400;
    color: #0082ff;
    cursor: pointer;
    padding: 4px 8px;
    &:hover {
      background: #edf6ff;
      border-radius: 15px;
    }
    &.disabled {
      cursor: default;
      &:hover {
        background: none;
      }
    }
  }
}
.circle {
  height: 9px;
  width: 9px;
  border-radius: 50%;
  border: 1px solid #2c79fb;
  background-color: #fff;
  &.active {
    border: none;
    background-color: #2c79fb;
  }
  &.item {
    // margin-right: 6px;
    margin-left: -5px;
  }
  &.title {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 13px;
    width: 13px;
    box-shadow: none;
    border: 1px solid #2c79fb;
    margin-left: -7px;
    // margin-right: 6px;
    span {
      width: 9px;
      height: 9px;
      border-radius: 50%;
      background: #2c79fb;
    }
  }
}
.legend-bar {
  align-items: center;
  height: fit-content;
  span {
    display: block;
  }
  .circle {
    margin: 0 8px;
  }
}
.link-bar {
  margin-top: 16px;
  justify-content: center;
  .ant-btn-link {
    color: #121f2c;
  }
}
.extend {
  align-items: center;
  margin-left: -60px;
  .course-list-btn {
    color: #606972;
    &:hover {
      color: #0082ff;
      border-color: #0082ff;
    }
    width: 116px;
    height: 32px;
    padding: 0 9px;
    align-items: center;
    justify-content: center;
    border-radius: 18px;
    border: 1px solid #bec2c5;
    cursor: pointer;
    font-size: 14px;
  }
}
</style>
