<template>
  <div v-if="visible" class="notice-mask">
    <div class="notice-content">
      <div class="top">
        <h1>{{ popupText.title }}</h1>
      </div>
      <div class="middle">
        <!-- eslint-disable vue/no-v-html -->
        <div class="notice-text" v-html="popupText.content"></div>
      </div>
      <div class="bottom">
        <div class="operation-bar">
          <div></div>
          <div>
            <a-checkbox style="margin-right: 24px" @change="onCheckboxChange"> 不再提醒 </a-checkbox>
            <a-button type="primary" @click="close">知道了</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { systemNotice } from '@/apis';
import { getMessageObj, getSessionKey } from './systemNotice';
import { UPGRADING } from '@/keycloak.js';
let intervalId = null;

export default {
  data() {
    return {
      visible: false,
      popupText: {
        title: '九天·毕昇平台维护升级公告',
        content: '<p>尊敬的用户：</p><p>您好！九天毕昇平台将在8月25号至26号期间维护升级，升级期间平台无法正常访问，所有运行实例将被停止。</p><p>感谢您的理解和配合。</p>',
      },
      sessionKey: '',
      hidePopup: false,
    };
  },
  created() {
    this.getNoticeStatus().then(() => {
      this.getPopUpMessage();
    });
    this.loopSearchNoticeStatus();
  },
  beforeUnmount() {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
  },
  methods: {
    loopSearchNoticeStatus() {
      intervalId = setInterval(() => {
        this.getNoticeStatus().then(() => {
          this.getPopUpMessage(true);
        });
      }, 1000 * 60);
    },
    getNoticeStatus() {
      return new Promise((resolve) => {
        systemNotice.getNoticeStatus().then((res) => {
          if (res.state === 'OK') {
            if (!UPGRADING()) {
              location.replace(res.body.url);
            } else {
              resolve();
            }
          } else {
            resolve();
          }
        });
      });
    },
    async getPopUpMessage(forceUpdate) {
      try {
        const sessionKey = await getSessionKey(forceUpdate);
        this.sessionKey = sessionKey;
        if (!this.sessionKey) {
          return;
        }
        const hidePopup = this.checkSession();
        if (hidePopup) {
          return;
        }
        this.visible = true;
        const messageObj = await getMessageObj();
        this.popupText = messageObj;
      } catch (error) {
        console.error(error);
      }
    },
    checkSession() {
      if (localStorage[this.sessionKey] === 'true') {
        return true;
      }
      if (this.$route.query.hideSystemPopup === 'true') {
        localStorage[this.sessionKey] = 'true';
        return true;
      }
    },
    close() {
      this.visible = false;
      if (!this.hidePopup) {
        return;
      }
      const keys = Object.keys(localStorage);
      const oldKey = keys.find((item) => item.includes('hideSystemPopup'));
      if (oldKey) {
        localStorage.removeItem(oldKey);
      }
      localStorage[this.sessionKey] = 'true';
    },
    onCheckboxChange(e) {
      this.hidePopup = e.target.checked;
      console.log(this.hidePopup);
    },
  },
};
</script>

<style lang="less" scoped>
@import './system-notice.less';
</style>
