<template>
  <div class="team-settings">
    <div class="team-settings-editbtn">
      <a-button type="primary" @click="handleEdit">
        <EditOutlined />
        编辑
      </a-button>
    </div>
    <head-title title="报名设置" style="margin-bottom: 24px" />
    <a-row v-for="itemKey in Object.keys(teamSignSettingMaps)" :key="itemKey" :gutter="[24, 16]">
      <a-col :span="4" style="text-align: right">{{ teamSignSettingMaps[itemKey] }}：</a-col>
      <a-col v-if="itemKey === 'signupEndTime'" :span="19">{{ teamSettings.signupEndTime ? handleTime(teamSettings.signupEndTime) : '--' }}</a-col>
      <a-col v-else-if="itemKey === 'rejectNames'" :span="19">{{ teamSettings.rejectNames.length > 0 ? teamSettings.rejectNames.join('；') : '--' }}</a-col>
      <a-col v-else-if="itemKey === 'beanNum'" :span="19" class="bean1">{{ teamSettings.beanNum ? `${teamSettings.beanNum}个` : '--' }}</a-col>
      <a-col v-else-if="itemKey === 'daynum'" :span="19">{{ teamSettings.daynum ? `${teamSettings.daynum}天` : '--' }}</a-col>
      <a-col v-else-if="itemKey === 'protocolvalue'" :span="19">
        <htmlViewer v-if="teamSettings.protocolvalue" :text-url="teamSettings.protocolvalue" />
        <div v-else>--</div>
      </a-col>
      <a-col v-else :span="20">{{ teamSettings[itemKey] }}</a-col>
    </a-row>
    <div style="margin: 20px 0px 46px">
      <head-title title="团队设置" style="margin-bottom: 24px" />
      <a-row v-for="itemKey in Object.keys(teamSettingMaps)" :key="itemKey" :gutter="[24, 16]">
        <a-col :span="4" style="text-align: right">{{ teamSettingMaps[itemKey] }}：</a-col>
        <a-col v-if="itemKey === 'teamEditEndTime'" :span="19">{{ teamSettings[itemKey] ? handleTime(teamSettings[itemKey]) : '--' }}</a-col>
        <a-col v-else-if="itemKey === 'teamMax'" :span="19">{{ teamSettings[itemKey] ? `${teamSettings[itemKey]}人` : '--' }}</a-col>
        <a-col v-else :span="19">{{ teamSettings[itemKey] }}</a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>
<script>
import headTitle from '@/components/headTitle';
import htmlViewer from './../components/htmlViewer.vue';
import API from '@/constants/api/API.js';
import { dateConvert } from '@/utils/utils';
import { teamSignSettingMaps, teamSettingMaps } from './../../competitionConfig/index.js';
export default {
  name: 'TeamManage',
  components: {
    headTitle,
    htmlViewer,
  },
  data() {
    return {
      dateConvert,
      competitionId: this.$route.params.competitionId,
      teamSettings: {
        signupEndTime: '',
        rejectNames: [],
        beanNum: '',
        daynum: '',
        protocolvalue: '',
        teamEditEndTime: '',
        teamMax: '',
      },
      teamSignSettingMaps,
      teamSettingMaps,
    };
  },
  created() {
    this.getCptInfo();
  },
  methods: {
    getCptInfo() {
      API.competition_model.getCompetitionManageEditJoinTeam({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          for (const key in res.body) {
            if (res.body[key]) {
              this.teamSettings[key] = res.body[key];
            }
          }
          if (!this.teamSettings.signupEndTime || !this.teamSettings.teamEditEndTime) {
            API.competition_model.getCompetitionInfo({ cid: this.competitionId }).then((res) => {
              if (res.state === 'OK' && res.body) {
                if (!this.teamSettings.signupEndTime) this.teamSettings.signupEndTime = res.body.endTime;
                if (!this.teamSettings.teamEditEndTime) this.teamSettings.teamEditEndTime = res.body.endTime;
              }
            });
          }
        }
      });
    },
    handleTime(t) {
      return this.dateConvert(t).replace(/\//g, '.');
    },
    handleEdit() {
      const { competitionId } = this.$route.params;
      this.$router.push(`${competitionId}/teamsettings-edit`);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.team-settings {
  color: #121f2c;
  :deep(.ant-col) {
    padding: 12px !important;
  }
  .team-settings-editbtn {
    position: absolute;
    top: 0px;
    right: 32px;
  }
  .bean1 {
    height: 18px;
    font-size: 20px;
    font-weight: @jt-font-weight-medium;
    color: #f17506;
    line-height: 18px;
  }
}
</style>
