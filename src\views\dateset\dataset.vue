<template>
  <div class="home">
    <!-- tab title -->
    <div class="tabs">
      <div class="home-title">
        <h1>数据集</h1>
        <jt-input v-model:value="listform.keyword" placeholder="搜索数据集" allow-clear class="search-input" @input="doSearch((listform.pageNum = 1))">
          <template #prefix>
            <span class="iconfont search-icon search-fn-btn">&#xe622;</span>
          </template>
          <!-- <span slot="prefix" class="iconfont search-icon search-fn-btn" >&#xe607;</span > -->
        </jt-input>
      </div>
      <ul class="tab-tilte">
        <li v-for="(title, index) in tabTitle" :id="cur == index ? 'liWidth' : ''" :key="index" :ref="'liWidthRef' + index" :class="{ active: cur == index }" class="tab-item" @click="tabtitle(index, title.datasetClassId)">
          {{ title.datasetClassName }}
        </li>

        <div class="risk-active-line risk-active-line-l" :style="`width:${width}px;transform:translateX(${offsetW}px);`"></div>
      </ul>

      <ul class="tab-children">
        <li v-for="(title, index) in tabClass" :key="index" :class="{ actives: curs == index }" class="tab-child" @click="tabclass(index, title.dataTypeId)">
          {{ title.dataTypeName }}
        </li>
      </ul>
    </div>
    <!-- 内容 -->
    <div class="main">
      <!-- <div style="margin: 0 120px;background:#fff;"> -->
      <div style="width: 1200px; margin: auto; background: #fff">
        <jt-empty v-if="tableData.length === 0" description="暂无结果"></jt-empty>
        <div v-for="(item, index) in tableData" :key="index" class="content" @click="detiles(item)">
          <div class="content-left">
            <div class="text">
              <h6>{{ item.dataName }}</h6>
              <!-- <span class="span-tag">{{ item.dataClassificationId == '1' ? '公开' : '中国移动' }}</span> -->
              <jt-button type="primary" class="filter-item" ghost style="display: inline-block">{{ item.dataClassificationId == '1' ? '公开' : '中国移动' }}</jt-button>
              <p class="details">
                {{ item.dataDescribe }}
              </p>
              <div class="labels">
                <span class="label" style="color: #a0a6ab; padding-right: 10px">
                  <jt-icon type="user"></jt-icon>
                </span>
                <strong class="label" style="color: #606972; padding-right: 15px">{{ item.dataUserNumber }}人使用</strong>
                <span class="label" style="color: #a0a6ab; padding-right: 10px">
                  <span class="iconfont">&#xe607;</span>
                </span>
                <strong class="label" style="color: #606972; padding-right: 24px">{{ item.dataTime }}</strong>
              </div>
            </div>
          </div>
        </div>
        <!-- 分页 -->
        <jt-row v-if="totalCount != 0" class="pagination-box">
          <jt-col :span="6">
            共 {{ totalCount }} 条
            <span class="pageOption" style="margin-left: 15px">每页显示</span>
            <jt-select v-model="listform.pageSize" :default-value="listform.pageSize" style="min-width: 50px; margin: 0 5px" @change="pageSizeChange">
              <template #suffixIcon>
                <jt-icon type="caret-down" :style="{ color: '#606266', marginRight: '-7px' }" />
              </template>
              <jt-select-option :value="5">5</jt-select-option>
              <jt-select-option :value="10">10</jt-select-option>
              <jt-select-option :value="20">20</jt-select-option>
            </jt-select>
            <span>条</span>
          </jt-col>
          <jt-col :span="18">
            <jt-pagination v-model:page-size="listform.pageSize" v-model="listform.pageNum" show-quick-jumper :default-current="2" :total="totalCount" style="text-align: right" @change="doSearch" />
          </jt-col>
        </jt-row>
      </div>
    </div>
  </div>
</template>

<script>
import { Input as JtInput, Icon as JtIcon, Button as JtButton, Row as JtRow, Col as JtCol, Select as JtSelect, Pagination as JtPagination } from 'ant-design-vue';

import API from '@/constants/api/API.js';
import JtEmpty from '@/components/Empty.vue';
import { mapGetters, mapMutations } from 'vuex';
export default {
  components: {
    JtInput,
    JtIcon,
    JtButton,
    JtRow,
    JtCol,
    JtSelect,
    JtPagination,
    JtSelectOption: JtSelect.Option,
    JtEmpty,
  },
  data() {
    return {
      tabTitle: [],
      tabClass: [],
      tabMain: ['内容一', '内容二', '内容三', '内容四'],
      cur: 0, //默认选中第一个tabTitle
      curs: 0, //默认选中第一个tabClass
      width: '',
      offsetW: '0',
      typeACount: 0,
      typeACurrentPage: 1,
      typeAPageSize: 5,
      listform: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      totalCount: 0,
      datasetClassId: 0,
    };
  },
  watch: {
    tabTitle: function () {
      this.$nextTick(function () {
        /*现在数据已经渲染完毕*/
        try {
          this.width = this.$refs['liWidthRef' + (this.cur || 0)][0].offsetWidth;
          this.offsetW = this.$refs['liWidthRef' + (this.cur || 0)][0].offsetLeft;
        } catch (error) {
          // console.log();
        }
      });
    },
  },
  mounted() {
    this.doSearch();
  },
  created() {
    this.cur = this.$route.params.datasetClassId ? this.$route.params.datasetClassId : 0;
    // 数据集分类列表
    API.dasteset_model.getDatasetClassList(null).then((res) => {
      this.tabTitle = res.body;
    });
    // getDataTypeList 获取数据类型列表
    API.dasteset_model.getDataTypeList(null).then((res) => {
      this.tabClass = res.body;
    });
    // 数据集计量接口查询 getDataList
    API.dasteset_model.getDataCount({ dataClassificationId: 1, dataTypeId: 1 });
    Object.assign(this.listform, this.getDataSetData());
    this.datasetClassId = this.listform.dataClassificationId;
    this.cur = this.listform.dataClassificationId;
    this.curs = parseInt(this.listform.dataTypeId) ? parseInt(this.listform.dataTypeId) : 0;
  },
  methods: {
    ...mapMutations(['SET_DATASET_DATA']),
    ...mapGetters(['getDataSetData']),

    doSearch() {
      this.SET_DATASET_DATA(this.listform);
      // 数据集接口查询
      if (this.$route.params.datasetClassId) {
        this.listform = this.getDataSetData();
        this.curs = parseInt(this.listform.dataTypeId) ? parseInt(this.listform.dataTypeId) : 0;
        delete this.$route.params.datasetClassId;
      }
      API.dasteset_model.getDataList(this.listform).then(({ body }) => {
        this.tableData = body.list;
        this.totalCount = body.totalCount;
      });
    },

    // 跳转详情页
    detiles(item) {
      this.$router.push({
        path: '/dataset/dataset-detail',
        query: {
          dataId: item.dataId,
          dataName: item.dataName,
          dataUserNumber: item.dataUserNumber,
          dataDescribe: item.dataDescribe,
          dataIntroduce: item.dataIntroduce,
          dataTime: item.dataTime,
          dataClassificationId: item.dataClassificationId,
          projectId: item.projectId,
          datasetClassId: this.datasetClassId,
        },
      });
      // this.SET_DATASET_DATA(this.listform)
    },

    tabtitle(key, dataClassificationId) {
      this.datasetClassId = dataClassificationId;
      this.cur = key;
      this.curs = 0;
      this.$nextTick(() => {
        this.width = document.getElementById('liWidth').offsetWidth;
        this.offsetW = document.getElementById('liWidth').offsetLeft;
      });
      if (dataClassificationId == '0') {
        delete this.listform.dataClassificationId;
      } else {
        this.listform.dataClassificationId = dataClassificationId;
      }
      delete this.listform.dataTypeId;
      this.listform.pageNum = 1;
      this.doSearch();
    },
    tabclass(key, dataTypeId) {
      this.curs = key;
      if (dataTypeId == '0') {
        delete this.listform.dataTypeId;
        this.curs = 0;
      } else {
        this.listform.dataTypeId = dataTypeId;
      }
      this.listform.pageNum = 1;
      this.doSearch();
    },
    // 分页
    pageSizeChange() {
      var totalPage = Math.floor((this.totalCount + this.listform.pageSize - 1) / this.listform.pageSize);
      if (this.listform.pageNum > totalPage) {
        this.listform.pageNum = totalPage;
      }
      this.doSearch();
    },
    onTypeAShowSizeChange(showSizeSelected) {
      this.typeAPageSize = showSizeSelected;
    },
    // 跳至xx页
    onTypeAPageChange(pageIndex) {
      this.typeACurrentPage = pageIndex;
    },
    // 重置分页和搜索数据参数
    resetPage() {
      this.typeACount = 0;
      this.typeACurrentPage = 1;
      this.searchText = '';
      this.searchInputShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.tabs {
  display: flex;
  flex-direction: column;
  height: 236px;
  /* background: #ffffff; */
  /* box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05); */
  /* padding: 0 120px; */
  width: 1200px;
  margin: auto;

  .tab-tilte {
    position: relative;
    border-bottom: 1px solid #e0e1e1;
    .tab-item {
      width: 136px;
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      line-height: 25px;
      margin: 32px 56px 0px 0px;
      padding-bottom: 16px;
      transition: color 1s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
    .filter-item {
      margin-right: 12px;
      display: block;
      padding: 2px 11px;
      line-height: 20px;
      height: auto;
      font-size: 14px;
      &.ant-btn-default {
        border-color: transparent;
        box-shadow: none;
      }
    }
    // 下划线
    .risk-active-line {
      width: 136px;
      height: 2px;
      background: #0082ff;
      position: absolute;
      top: 73px;
    }
    .risk-active-line-l {
      transition-duration: 0.5s;
    }
  }

  .tab-children {
    flex: 1;

    .tab-child {
      // width: 48px;
      // height: 22px;
      border-radius: 2px;
      margin-right: 32px;
      margin-top: 32px;
      line-height: 22px;
      font-size: 16px;
      padding: 5px 12px;
    }
  }

  // 我要办赛
  .match {
    font-size: 16px;
    font-weight: 400;
    color: #0082ff;
    line-height: 22px;
    padding: 9px 32px;
    border-radius: 2px;
    border: 1px solid #0082ff;
    float: right;
    margin-top: 32px;
  }
}
.tabs .home-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
}
.tabs .home-title h1 {
  font-size: 32px;
  color: #121f2c;
  height: 45px;
  line-height: 45px;
}
.tabs .home-title .search-input {
  width: 240px;
  height: 32px;
  border-radius: 2px;
}
/* input搜索框样式 */
:deep(.ant-input) {
  text-indent: 8px;
}
/* input icon样式颜色修改 */
:deep(.ant-input-affix-wrapper .ant-input-prefix :not(.anticon)) {
  color: #bec2c5;
}
.tab-tilte > li,
.tab-children > li {
  float: left;
  text-align: center;
  cursor: pointer;
  background: #ffffff;
}

/* 点击对应的标题添加对应的背景颜色 */
.tab-tilte .active {
  color: #0082ff;
}
.tab-children .actives {
  background: #0082ff;
  color: #fff;
}

.main {
  background: #f4f8fa;
  padding-top: 20px;
  padding-bottom: 20px;
}

.content {
  /* margin: 0 120px; */
  height: 192px;
  background: #ffffff;
  border-bottom: 1px solid #e0e1e1;
  //   box-shadow: 0px 1px 0px 0px #e0e1e1;
  border-radius: 2px;
  padding: 32px;
  display: flex;
  justify-content: space-between;

  .content-left {
    display: flex;

    .text {
      padding-left: 24px;
      > h6 {
        height: 28px;
        font-size: 20px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
        line-height: 28px;
        margin-bottom: 14px;
        display: inline-block;
        margin-right: 9px;
      }
      .details {
        height: 22px;
        font-size: 14px;
        font-weight: 400;
        color: #606972;
        line-height: 22px;
        margin-bottom: 24px;
      }
      .label {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #a0a6ab;
        line-height: 20px;
      }
    }
  }
  .content-right {
    .reward {
      height: 33px;
      font-size: 24px;
      font-weight: @jt-font-weight-medium;
      color: #ff4945;
      line-height: 33px;
      margin-bottom: 8px;
    }
    .joins {
      height: 22px;
      font-size: 16px;
      color: #606972;
      line-height: 18px;
      text-align: right;
    }
  }
}
.text .span-tag {
  height: 17px;
  font-size: 12px;
  font-weight: 400;
  color: #0082ff;
  padding: 4px 12px;
  line-height: 17px;
  background: #ffffff;
  border: 1px solid #389bff;
  border-radius: 2px;
}
.content {
  cursor: pointer;
}
.content:hover {
  background: #f8f9fa;
}
.content:hover .text h6 {
  color: #0082ff;
}
// 分页
.page {
  height: 98px;
  border: 1px solid #ccc;
  margin: 0 120px;
  background: #fff;
}

// 正在进行
.conduct {
  height: 24px;
  background: #389bff;
  border-radius: 2px;
  text-align: center;
  font-size: 12px;
  line-height: 24px;
  color: #fff;
  padding: 3px 12px;
  margin-right: 8px;
}

// alla
.alla {
  height: 17px;
  font-size: 12px;
  font-weight: 400;
  color: #0082ff;
  padding: 4px 12px;
  line-height: 17px;
  background: #ffffff;
  border: 1px solid #389bff;
  border-radius: 2px;
}

.banner {
  background-color: rgba(0, 130, 255, 0.18);
  height: 460px;
}
ul {
  margin-bottom: 0;
}
.pagination-box {
  display: flex;
  align-items: center;
  height: 92px;
  background-color: #fff;
  margin: 0 10px;
  padding: 0 32px;
  box-sizing: border-box;
}
</style>
