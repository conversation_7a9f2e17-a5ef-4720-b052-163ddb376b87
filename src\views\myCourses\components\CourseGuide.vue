<script setup>
import { RightOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <div class="course-guide">
      <div class="guide-title">
        <div class="course-msg">设置课程信息</div>
        <div class="ellip-img">
          <img src="@/assets/image/teaching/guide-ellipsis.png" alt="" width="145px" />
        </div>

        <div class="teach-content">发布教学内容</div>
        <div class="ellip-img">
          <img src="@/assets/image/teaching/guide-ellipsis.png" width="145px" alt="" />
        </div>

        <div class="publish-work">发布教学评测</div>
      </div>
      <a-row style="margin-bottom: 30px">
        <a-col :span="8">
          <div v-for="item in courseInformationItems" :key="item.name" class="guide-item">
            <div :class="item.disable ? 'disable-guide' : ''" @click="handleItemClick(item)">{{ item.name }}<RightOutlined class="arror-icon" /></div>
            <p>{{ item.description }}</p>
          </div>
          <!-- 不是公开课的时候才显示 -->
          <div v-if="currentActiveCourse.courseFlag != '1'" key="导入课程学生" class="guide-item">
            <div :class="studentInformation.disable ? 'disable-guide' : ''" @click="handleItemClick(studentInformation)">{{ studentInformation.name }}<RightOutlined class="arror-icon" /></div>
            <p>{{ studentInformation.description }}</p>
          </div>
        </a-col>
        <a-col :span="8">
          <div v-for="item in courseContentItems" :key="item.name" class="guide-item">
            <div :class="item.disable ? 'disable-guide' : ''" @click="handleItemClick(item)">{{ item.name }}<RightOutlined class="arror-icon" /></div>
            <p>{{ item.description }}</p>
          </div>
          <div v-if="currentActiveCourse.courseFlag != '1'" :key="studyTrack.name" class="guide-item">
            <div :class="studyTrack.disable ? 'disable-guide' : ''" @click="handleItemClick(studyTrack)">{{ studyTrack.name }}<RightOutlined class="arror-icon" /></div>
            <p>{{ studyTrack.description }}</p>
          </div>
        </a-col>
        <a-col :span="8">
          <div v-for="item in homeworkItems" :key="item.name" class="guide-item">
            <div :class="item.disable || isPublicCourse ? 'disable-guide' : ''" @click="handleItemClick(item)">{{ item.name }}<RightOutlined class="arror-icon" /></div>
            <p>{{ item.description }}</p>
          </div>
        </a-col>
      </a-row>

      <a-modal v-model:open="publishAlertVisible" class="course-operation-modal" centered :closable="false" :title="null" :footer="null" width="372px" :mask-closable="false">
        <a-spin :tip="operationType === 'publish' ? '发布中...' : '下架中...'" :spinning="loading">
          <div>
            <div class="off-title">
              <jt-icon :style="`color: ${operationType === 'publish' ? 'rgba(250, 173, 20, 1)' : 'rgba(255, 69, 77, 1)'}`" type="iconwarning-circle-fill"></jt-icon>
              <span>{{ operationType === 'publish' ? '发布' : '下架' }}课程提示</span>
            </div>
            <p v-if="operationType === 'publish'" class="operation-alert-msg">发布前请务必确保课程介绍、教学内容、课程资源均已完成设置<br /><span style="color: #faad14">发布后不可更改、如需修改需要先下架</span></p>
            <p v-else class="operation-alert-msg">下架后课程不再出现在课程列表中，但不影响<br />已加入学生的学习，请谨慎操作</p>

            <a-space style="justify-content: flex-end; width: 100%">
              <a-button style="width: 64px" @click="publishAlertVisible = false">取消</a-button>
              <a-button style="width: 64px" :type="operationType === 'publish' ? 'primary' : 'danger'" @click="publishOrOffSubmit">{{ operationType === 'publish' ? '发布' : '下架' }}</a-button>
            </a-space>
          </div>
        </a-spin>
      </a-modal>
    </div>
    <!-- 封闭课不显示按钮 -->
    <div class="bottom-background">
      <div style="height: 33px"></div>
      <div v-if="currentActiveCourse.courseFlag == '1'" class="operation-btn">
        <span class="btn-container">
          <template v-if="currentActiveCourse.coursePublish == '0' || currentActiveCourse.coursePublish == null">
            <a-tooltip v-if="currentActiveCourse.itemFlag === '0'" title="仍有项目创建中，请稍后再试">
              <a-button disabled class="publish-btn" type="primary" @click="handleOperation('publish')">发布课程</a-button>
            </a-tooltip>
            <a-button v-else class="publish-btn" type="primary" @click="handleOperation('publish')">发布课程</a-button>
          </template>

          <a-button v-else class="off-btn" @click="handleOperation('off')">下架课程</a-button>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { publishCourse, deleteCourse, getCourseDetail } from '@/apis/teaching.js';
export default {
  name: 'CourseGuide',
  data() {
    return {
      loading: false,
      courseId: this.$route.params.courseId,
      publishAlertVisible: false,
      operationType: 'publish', // publish or off
      // tabKey 判断跳转到第几个tab , btnKey判断到第几个组件，disable可暂时关闭该项跳转
      studentInformation: {
        name: '导入课程学生',
        description: '导入课程学生列表',
        tabKey: '3',
        btnKey: '2',
      },
      studyTrack: {
        name: '学生学习跟踪',
        tabKey: '2',
        btnKey: '4',
        description: '跟踪学生学习进度',
      },
      courseInformationItems: [
        {
          name: '设置基本信息',
          tabKey: 'course-management',
          btnKey: 'basic-info',
          description: '课程名称、开课时间、封面、一句话简介',
        },
        {
          name: '组建教学团队',
          description: '管理教师和助教',
          disable: true,
        },
      ],
      courseContentItems: [
        {
          name: '发布课程介绍',
          tabKey: 'teach-area',
          btnKey: 'course-introduction',
          description: '发布课程描述、前置知识、课程目标',
        },
        {
          name: '发布教学内容',
          tabKey: 'teach-area',
          btnKey: 'course-content',
          description: '发布Notebook项目、教学视频及文档',
        },
        {
          name: '设置课程资源',
          tabKey: 'teach-area',
          btnKey: 'course-resource',
          description: '设置课程算力需求、代码量、数据量、参考资料',
        },
      ],
      // 现在没有作业功能了，所以这块暂时不改动
      homeworkItems: [
        {
          name: '发布/评阅作业',
          description: '发布课程作业，并评阅',
          tabKey: '4',
          btnKey: '1',
          disable: false,
          //disable: true,
          closeCourse: true, // 在封闭课的时候显示
        },
        {
          name: '发布/评阅考试',
          description: '发布考试，并评阅',
          disable: true,
        },
      ],
    };
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
    isPublicCourse() {
      return this.currentActiveCourse.courseFlag == '1';
    },
  },
  mounted() {
    this.updateCurrentCourseState();
  },
  methods: {
    async updateCurrentCourseState() {
      const res = await getCourseDetail({ courseId: this.courseId });
      if (res.state === 'OK') {
        this.$store.commit('course/SET_CURRENTACTIVECOURSE_DATA', res.body);
      }
    },
    publishOrOffSubmit() {
      // 发布
      this.loading = true;
      if (this.operationType === 'publish') {
        publishCourse({ courseId: this.courseId }, { useError: false })
          .then((res) => {
            this.loading = false;
            if (res.state === 'OK') {
              this.publishAlertVisible = false;
              this.$message.success('课程发布成功');
              // 不跳转，直接更新课程状态
              this.updateCurrentCourseState();
            } else {
              this.$message.error(res.errorMessage || '课程发布失败');
            }
          })
          .catch(() => {
            this.loading = false;
          });
        // 下架
      } else {
        deleteCourse({ courseId: this.courseId })
          .then((res) => {
            this.loading = false;
            if (res.state === 'OK') {
              this.publishAlertVisible = false;
              this.$message.success('课程下架成功');
              this.updateCurrentCourseState();
            } else {
              this.$message.error(res.errorMessage || '课程下架失败');
            }
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    handleOperation(type) {
      this.operationType = type;
      this.publishAlertVisible = true;
    },
    // 点击item控制路由跳转的逻辑
    handleItemClick(item) {
      // 对于在封闭课中显示，公共课中不显示的内容特殊处理
      if (item.closeCourse && this.isPublicCourse) {
        return;
      }
      if (!item.disable) {
        // item.tabKey && this.$emit('handleTabChange', { tabKey: item.tabKey, btnKey: item.btnKey });
        item.tabKey && this.$router.push({ params: { ...this.$route.params, tab: item.tabKey, subTab: item.btnKey } });
      }
    },
  },
};
</script>
<style lang="less">
@import '~@/assets/styles/index.less';

.course-operation-modal {
  .operation-alert-msg {
    line-height: 30px;
    margin-bottom: 20px;
    padding-left: 20px;
  }
}
</style>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.course-guide {
  margin-left: 88px;
  margin-top: 24px;
  .guide-title {
    margin-bottom: 20px;
    display: flex;
    .ellip-img {
      line-height: 64px;
      margin-right: 24px;
    }
    .course-msg,
    .teach-content,
    .publish-work {
      height: 64px;
      width: 200px;
      background-size: 100% 100%;
      line-height: 64px;
      padding-left: 60px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      font-size: 16px;
    }
    .course-msg {
      background-image: url('~@/assets/image/teaching/guid1.png');
    }
    .teach-content {
      background-image: url('~@/assets/image/teaching/guid2.png');
    }
    .publish-work {
      background-image: url('~@/assets/image/teaching/guid3.png');
    }
  }
  .guide-item {
    display: inline-block;
    height: 64px;
    padding: 10px;
    line-height: 32px;
    margin: 10px 0px;
    border-radius: 2px;
    min-width: 300px;
    &:hover {
      background-color: #f8f9fa;
    }
    div {
      height: 25px;
      line-height: 25px;
      color: #0082ff;
      font-size: 16px;
      cursor: pointer;
    }
    p {
      font-size: 12px;
      color: #606972;
    }
    .disable-guide {
      color: #a0a6ab;
      cursor: not-allowed;
    }
  }
}

.arror-icon {
  font-size: 14px;
  margin-left: 8px;
}

.off-title {
  margin: 10px 0px 20px;
  i {
    font-size: 18px;
    margin-right: 5px;
  }
  span {
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    font-size: 16px;
  }
}
.bottom-background {
  background-image: url('~@/assets/image/teaching/btns-bac.png');
  background-size: 100%;
  border-radius: 0px 0px 2px 2px;
}
.operation-btn {
  border-top: 1px solid #efefef;
  display: flex;
  justify-content: center;
  align-items: center;
  .btn-container {
    display: inline-block;
    padding: 24px 0px 40px;

    button {
      width: 160px;
    }

    .off-btn {
      &:hover {
        border-color: #ff454d;
        color: #ff454d;
      }
    }
  }
}
</style>
