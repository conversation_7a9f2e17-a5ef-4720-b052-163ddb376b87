const mutations = {
  UPDATE_RETRESHTOKEN(state, token) {
    state.refreshToken = token;
  },
  SET_DATASET_DATA(state, data) {
    state.dataSetData = data;
  },
  SET_USERINFO_DATA(state, data) {
    state.userInfo = data;
  },
  SET_USER_COUNT(state, data) {
    state.userCount = data;
  },
  SET_GLOBAL_LOADING(state, data) {
    state.globalLoading = data;
  },
  SET_SUANLISTATE_DATA(state, data) {
    state.suanLiState = data;
  },
  SET_GIVEBEANSBYCOURSE(state, data) {
    state.giveBeansByCourse = data;
  },
};
export default mutations;
