<template>
  <div>
    <modal :visible="visible" title="评阅作业" :confirm-loading="loading" :get-container="container" @ok="handleOk" @cancel="$emit('cancel')">
      <a-form ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-item ref="score" label="分数" name="score" help="0-100数字">
          <a-input-number v-model:value="form.score" style="width: 100%" placeholder="请输入分数" :precision="(form.score + '').includes('.') ? 1 : 0" :max="100" :min="0" />
        </a-form-item>
        <a-form-item
          ref="remark"
          class="intro-paragraph"
          label="评语"
          name="remark"
          :rules="{
            required: true,
            message: '500个字符以内',
            trigger: 'change',
            max: 500,
          }"
        >
          <a-textarea v-model:value="form.remark" :auto-size="{ minRows: 4, maxRows: 4 }" placeholder="请输入评语" />
          <span class="intro-size-total">{{ form.remark ? form.remark.length : 0 }}/500</span>
        </a-form-item>
      </a-form>
    </modal>
  </div>
</template>

<script>
import modal from '@/components/modal';
import { POST } from '@/request';
export default {
  name: 'HomeworkReviewDialog',
  components: {
    modal,
  },
  props: {
    studentAssignmentId: {
      type: [String, Number],
      default: '',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    studentRemark: {
      type: [String, Number],
      default: null,
    },
    studentScore: {
      type: [String, Number],
      default: null,
    },
  },
  emits: ['cancel', 'ok'],
  data() {
    return {
      loading: false,
      modalVisible: false,
      form: {
        score: '',
        remark: '',
      },
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 20,
      },
      rules: {
        score: [{ required: true, message: '0-100数字', trigger: 'blur' }],
      },
    };
  },
  computed: {
    container() {
      return () => document.querySelector('#jupyter-viewer');
    },
  },
  watch: {
    studentRemark(v) {
      this.form.remark = v;
    },
    studentScore(v) {
      this.form.score = v;
    },
  },
  methods: {
    handleOk() {
      this.loading = true;
      this.$refs.ruleForm
        .validate()
        .then(() => {
          this.submit();
        })
        .catch((err) => {
          this.loading = false;
          throw new Error(err);
        });
    },
    submit() {
      const obj = {
        correctNote: this.form.remark,
        score: this.form.score,
        studentAssignmentId: this.studentAssignmentId,
      };
      POST('/course_model/web/teaching/assignment/correct', obj).then((res) => {
        this.loading = false;
        if (res.state === 'OK') {
          this.$message.success('评阅成功');
          this.$emit('ok');
        } else {
          this.$message.error(res.errorMessage);
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.intro-paragraph {
  margin-top: 24px;
  //.intro-paragraph-content {
  position: relative;
  :deep(textarea) {
    padding-bottom: 20px;
    resize: none;
  }
  .intro-size-total {
    position: absolute;
    bottom: -15px;
    right: 8px;
  }
  //}
}
</style>
