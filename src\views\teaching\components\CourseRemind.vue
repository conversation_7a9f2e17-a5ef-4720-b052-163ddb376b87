<template>
  <!-- 登录 -->
  <div class="course-remind jt-box-shadow" v-if="$keycloak && $keycloak.authenticated">
    <h2 class="user">
      Hi,<span style="font-size: 24px"> {{ $keycloak.idTokenParsed.preferred_username }}</span>
    </h2>
    <jt-common-content :loading="loading" :empty="loading" :emptyStyle="{ height: '0' }">
      <p class="total mgb-8" v-if="total > 0">
        您好！您已开设<span style="color: #0082ff"> {{ total }} </span>门课程。如您需要了解更多内容，欢迎咨询客户经理。
      </p>
      <p v-else class="total mgb-8">您好！您尚未开设课程。如您需要了解，欢迎咨询客户经理。</p>
    </jt-common-content>
    <div class="course-route">
      <a-tooltip title="请发送邮件至：<EMAIL>">
        <a-button class="route-btn" type="primary">立即咨询</a-button>
      </a-tooltip>
    </div>
  </div>
  <!-- 未登录 -->
  <div class="course-remind jt-box-shadow" v-else>
    <h2 class="user">Hi, <span style="font-size: 24px">您好！</span></h2>
    <p class="total" style="margin-bottom: 8px">欢迎来到九天 · 毕昇</p>
    <p class="total">您尚未登录，请登录查看课程</p>

    <div class="course-route">
      <a-button class="route-btn" type="primary" @click="handleLogin">立即登录</a-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'CourseRemind',
  props: {
    purchased: {
      type: Boolean,
    },
    total: {
      type: Number,
    },
    loading: {
      type: Boolean,
    },
  },
  computed: {
    ...mapState(['userInfo']),
  },
  methods: {
    handleLogin() {
      const loginUrl = this.$keycloak.createLoginUrl();
      window.location.replace(loginUrl);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.course-remind {
  position: relative;
  padding: 16px 24px 24px 24px;
  width: 280px;
  height: 298px;
  background: #ffffff;
  border-radius: 8px;
  margin-top: 116px;
  :deep(.ant-spin-dot-spin) {
    margin-top: 35px !important;
  }

  .user {
    width: 100%;
    font-size: 32px;
    font-weight: @jt-font-weight-medium;
    color: #16161c;
    line-height: 48px;
    margin-bottom: 16px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .total {
    font-size: 16px;
    color: #606972;
  }
  .mgb-8 {
    margin-bottom: 8px;
  }
  button {
    height: 40px;
  }
}
.course-route {
  text-align: center;
  margin-top: 10px;
  position: absolute;
  bottom: 30px;
  .route-btn {
    width: 232px;
  }
}
</style>
