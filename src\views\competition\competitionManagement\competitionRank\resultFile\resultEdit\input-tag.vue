<script setup>
import { PlusOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <template v-for="tag in tags" :key="tag">
      <a-tooltip v-if="tag.length > 20" :key="tag" :title="tag">
        <a-tag class="tag" :class="{ disabled }" :closable="disabled ? false : true" @close="() => handleClose(tag)">
          {{ `${tag.slice(0, 20)}...` }}
        </a-tag>
      </a-tooltip>
      <a-tag v-else class="tag" :class="{ disabled }" :closable="disabled ? false : true" @close="() => handleClose(tag)">
        {{ tag }}
      </a-tag>
    </template>
    <a-input v-if="inputVisible" ref="input" v-model:value="inputValue" :max-length="30" type="text" size="small" :style="{ width: '78px' }" @change="handleInputChange" @blur="handleInputConfirm" @keyup.enter="handleInputConfirm" />
    <a-tag v-if="!inputVisible && tags.length < max && !disabled" class="tag add" :class="{ disabled }" @click="showInput"> <PlusOutlined /> 添加标签 </a-tag>
  </div>
</template>
<script>
export default {
  // props: ['value', 'disabled', 'max'],
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    max: {
      type: Number,
      default: 10,
    },
  },
  emits: ['change'],
  data() {
    const value = this.value || [];
    return {
      tags: value,
      inputVisible: false,
      inputValue: '',
    };
  },
  watch: {
    value(n) {
      this.tags = n;
    },
  },
  methods: {
    handleClose(removedTag) {
      const tags = this.tags.filter((tag) => tag !== removedTag);
      this.tags = tags;
      this.$emit('change', tags);
    },

    showInput() {
      if (this.disabled) {
        return;
      }
      this.inputVisible = true;
      this.$nextTick(function () {
        this.$refs.input.focus();
      });
    },

    handleInputChange(e) {
      this.inputValue = e.target.value;
    },

    handleInputConfirm() {
      const inputValue = this.inputValue;
      let tags = this.tags;
      if (inputValue) {
        if (tags.indexOf(inputValue) === -1) {
          tags = [...tags, inputValue];
        } else {
          this.$message.error('已有同名指标');
        }
      }
      Object.assign(this, {
        tags,
        inputVisible: false,
        inputValue: '',
      });
      this.$emit('change', tags);
    },
  },
};
</script>

<style lang="less" scoped>
.tag {
  height: 32px;
  line-height: 30px;
  &.add {
    padding-left: 18px;
    min-width: 100px;
    background: #fff;
  }
  &.disabled {
    cursor: no-drop;
    user-select: none;
  }
}
</style>
