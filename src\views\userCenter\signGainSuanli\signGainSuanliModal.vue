<template>
  <a-modal v-model:open="open" :title="null" :mask-closable="false" @cancel="cancelClose">
    <a-spin :spinning="signGaninSuanliSpinning">
      <img src="@/assets/image/sign-gain-suanli/sign-detail.png" class="sign-detail-img" alt="" />
      <div>
        <h1 class="sign-detail-title">
          <img src="@/assets/image/sign-gain-suanli/continuity-sign-text.png" alt="" />
        </h1>
        <p class="sign-detail-subtitle" style="margin-bottom: 4px">
          今日已签到，获得 <span class="num">{{ checkinDetail.beanCount }}</span> 个算力豆
        </p>
        <p class="sign-detail-subtitle">
          连续签到 <span class="num">{{ checkinDetail.checkinDays }}</span> 天，本周已获得 <span class="num">{{ checkinDetail.beanTotal }}</span>
          个算力豆
        </p>
      </div>
      <ul class="sign-gain-date-week">
        <li v-for="(item, l) in checkinDetail.dayDetailList" :key="item.weekDay" :class="wewekdayclass(l)">
          <div class="suanli-num">{{ item.checkinStatus ? '+' + item.beanCount : '-' }}</div>
          <div class="suanli-time">
            <jt-icon type="iconwancheng-tianchong" class="icon" />
            {{ item.checkinStatus && isWeekDay === item.weekDay ? '今日' : `${getWeekDay(item.weekDay)}` }}
          </div>
        </li>
      </ul>

      <ul class="sign-gain-activity-rules">
        <li>活动规则：</li>
        <li>1. 签到活动以自然周为一个周期，新一周周一从第一天开始计算</li>
        <li>2. 每天签到都可以获得算力豆，连续签到能获得翻倍算力豆</li>
        <li>3. 断签后，再次签到，将按照第一天来赠送算力豆</li>
        <li>4. 签到获得的算力豆有效期{{ checkinDetail.beanPeriod }}天</li>
        <li>5. 可以在算力豆明细中看到历史签到获得的算力豆</li>
        <li>6. 解释权归九天·毕昇所有</li>
      </ul>
    </a-spin>
    <template #footer>
      <a-button v-if="isCount" type="primary" style="width: 128px" @click="cancelClose"> 确定({{ countdown }}) </a-button>
      <a-button v-else type="primary" style="width: 128px" @click="cancelClose"> 确定 </a-button>
    </template>
  </a-modal>
</template>
<script>
import { GET } from '@/request';
import { getWeekDay } from '@/utils/utils';

export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    isCount: Boolean,
  },
  emits: ['input'],
  data() {
    return {
      open: false,
      checkinDetail: {},
      countdown: 3, // 倒计时
      timers: null,
      signGaninSuanliSpinning: false,
    };
  },
  computed: {
    // 是否显示今日
    isWeekDay() {
      let date = new Date();
      let day = date.getDay();
      if (day == 0) day = 7; // 星期日的时候daty是0
      return day;
      // return (this.dayDetailList[this.dayDetailList.length - 1].weekDay) + 1;
    },
  },
  watch: {
    value(val) {
      this.open = val;
      if (val && this.isCount) {
        this.countdown = 3;
        this.timers = setInterval(() => {
          this.countdown -= 1;
          if (this.countdown === 0) {
            this.$emit('input', false);
            clearInterval(this.timers);
          }
        }, 1000);
      }
    },
  },
  created() {
    this.open = this.value;
  },
  beforeUnmount() {
    clearInterval(this.timers);
  },
  methods: {
    getWeekDay,
    wewekdayclass(index) {
      let dayDetailList = this.checkinDetail.dayDetailList;
      return {
        signin: dayDetailList[index].checkinStatus && !dayDetailList[index].future,
        'no-signin': !dayDetailList[index].checkinStatus && !dayDetailList[index].future,
        'future-signin': !dayDetailList[index].checkinStatus && dayDetailList[index].future,
      };
    },
    cancelClose() {
      this.$emit('input', false);
    },
    // 获取一周签到详情
    getCheckinDetail() {
      this.signGaninSuanliSpinning = true;
      GET('/marketing/web/checkin/getCheckinDetail', {}).then((res) => {
        this.checkinDetail = res.body;
        let dayDetailList = res.body.dayDetailList;
        this.signGaninSuanliSpinning = false;
        dayDetailList.map((item) => (item.future = false));

        if (dayDetailList.length < 7) {
          let length = dayDetailList.length;
          for (let index = length + 1; index <= 7; index++) {
            let weekday = {
              beanCount: 0,
              checkinStatus: false,
              weekDay: index,
              future: true,
            };
            this.checkinDetail.dayDetailList.push(weekday);
          }
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/var.less';

:deep(.ant-modal-body) {
  padding: 40px 32px 24px;
  background: linear-gradient(180deg, #fdedcc 0%, #ffffff 43%);
  border-radius: 4px;
}

:deep(.ant-modal-content) {
  width: 560px;
  border-radius: 4px;
}

:deep(.ant-modal-footer) {
  display: flex;
  justify-content: center;
  margin: 0 32px;
  padding: 16px 0 24px;
}

.sign-gain-date-week {
  display: flex;
  flex-direction: row;
  margin-bottom: 24px;
  margin-top: 24px;

  li {
    width: 64px;
    height: 93px;
    border-radius: 4px;
    margin-right: 8px;

    .suanli-num {
      text-align: center;
      line-height: 24px;
      margin-top: -1px;
      width: 62px;
      border-radius: 4px 4px 0px 0px;
    }

    .suanli-time {
      display: flex;
      flex-direction: column;
      text-align: center;
      font-size: @jt-font-size-sm;
    }

    .icon {
      font-size: 25px;
      margin: 10px 0;
    }
  }

  .signin {
    background: #fff0c9;

    .suanli-num {
      height: 24px;
      background: #ffda61;
      color: #f17506;
    }

    .icon {
      color: #f17506;
    }
  }

  .no-signin {
    background: #f6f6f6;

    .suanli-num {
      height: 24px;
      background: #e5e5e5;
      color: @jt-text-color-secondary;
    }

    .icon {
      color: #d4d4d4;
    }
  }

  .future-signin {
    background: #ffffff;
    border: 1px solid #ffba00;

    .suanli-num {
      height: 24px;
      background: #ffda61;
      color: #f17506;
    }

    .icon {
      color: #ffe0bb;
    }
  }
}

.sign-detail-title {
  margin-bottom: 15px;
  img {
    width: 286px;
    height: 36px;
  }
}

.sign-detail-subtitle {
  font-size: @jt-font-size-lg;
  font-weight: @jt-font-weight-medium;
  color: #552b00;
  line-height: 22px;

  .num {
    font-size: 24px;
    font-weight: @jt-font-weight-medium;
    color: #f17506;
    line-height: 33px;
  }
}

.sign-gain-activity-rules {
  font-size: @jt-font-size-sm;
}

.sign-detail-img {
  width: 277px;
  height: 178px;
  position: absolute;
  top: -69px;
  right: -28px;
}
</style>
