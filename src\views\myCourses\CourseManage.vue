<template>
  <div class="course-manage jt-box-shadow">
    <div style="background-color: white; border-radius: 2px">
      <a-tabs v-show="!subPage" v-model:activeKey="activekey" class="course-manage-tabs" @tabClick="tabClick">
        <a-tab-pane key="course-guide" tab="引导">
          <course-guide />
        </a-tab-pane>
        <a-tab-pane key="teach-area" tab="教学专区">
          <teach-area />
        </a-tab-pane>
        <a-tab-pane key="course-management" tab="课程管理">
          <course-management />
        </a-tab-pane>
        <a-tab-pane key="teaching-evaluation" tab="教学评测" :disabled="currentActiveCourse.courseFlag === '1'">
          <teaching-evaluation />
        </a-tab-pane>
      </a-tabs>
      <router-view />
      <course-introduction-edit v-if="subPage === 'course-introduction-edit'" />
      <course-resource-edit v-if="subPage === 'course-resource-edit'" />
    </div>
  </div>
</template>

<script lang="jsx">
import { mapState } from 'vuex';

import CourseGuide from './components/CourseGuide.vue';
import TeachArea from './components/TeachArea.vue';
import CourseIntroductionEdit from './components/CourseIntroductionEdit.vue';
import CourseResourceEdit from './components/CourseResourceEdit.vue';
import CourseManagement from './components/courseManagement/CourseManagement.vue';
import TeachingEvaluation from './teachingEvaluation/TeachingEvaluation.vue';
import { getCourseDetail } from '@/apis/teaching.js';
let courseItem = {};
export default {
  name: 'CourseManage',
  components: {
    CourseGuide,
    TeachArea,
    CourseIntroductionEdit,
    CourseResourceEdit,
    CourseManagement,
    TeachingEvaluation,
  },
  // 若用户信息已完善且有权限则进入，负责进入教学主页
  beforeRouteEnter(to, from, next) {
    const courseId = to.params.courseId;
    getCourseDetail({ courseId })
      .then((res) => {
        if (res.state === 'OK' && res.body) {
          courseItem = res.body;
          next();
        } else {
          next('/course');
        }
      })
      .catch(() => {
        next('/course');
      });
  },

  data() {
    return {};
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
    activekey() {
      return this.$route.params.tab;
    },
    subPage() {
      return this.$route.params.subPage;
    },
  },

  created() {
    this.$store.commit('course/SET_CURRENTACTIVECOURSE_DATA', courseItem);
    const tab = this.$route.params.tab;
    if (!tab) {
      this.$router.replace({ params: { ...this.$route.params, tab: 'course-guide' } });
    }
  },
  methods: {
    // Click handler for tab switching
    tabClick(key) {
      this.$router.push({ params: { ...this.$route.params, tab: key, subTab: null } });
    },
  },
};
</script>

<style lang="less" scoped>
.course-manage {
  width: 1200px;
  margin: 20px auto 0px;
  :deep(.ant-tabs-tab) {
    font-size: 18px;
  }
  /* 调整头部padding的适配 */
  .course-manage-tabs {
    :deep(.ant-tabs-bar) {
      padding: 0px 32px;
      border-bottom: none;
      .ant-tabs-nav-container {
        border-bottom: 1px solid #e8e8e8;
      }
      .ant-tabs-tab:hover {
        color: #0082ff;
      }
      .ant-tabs-tab-disabled:hover {
        color: rgba(0, 0, 0, 0.25);
      }
    }
    :deep(.ant-tabs-tab-active) {
      color: #0082ff;
    }
    :deep(.ant-tabs-ink-bar) {
      background-color: #0082ff;
    }
  }
}
</style>
