<template>
  <div>
    <a-space v-if="isNotOpenAndPublishedCourse" class="operation-group">
      <a-button v-if="currentActiveCourse.courseFlag != '1'" class="publish-btn" :disabled="courseIntroductionData.status === null || courseIntroductionData.status == '1'" @click="handlePublish"><jt-icon type="iconfasong"></jt-icon>发布</a-button>
      <a-button type="primary" @click="handleEditClick">
        <EditOutlined />
        编辑
      </a-button>
    </a-space>
    <div v-if="hasIntroContent">
      <a-space class="course-intro-header">
        <a-tag :color="courseIntroductionData.status == 1 ? 'green' : ''">{{ courseIntroductionData.status == '1' ? '已发布' : '未发布' }}</a-tag>
        <span>上次发布时间： {{ courseIntroductionData.pubTime || '————' }}</span>
      </a-space>
      <div class="title">课程描述</div>
      <div>
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div class="html-area markdown-body" v-html="courseIntroductionData.courseDescData" />
      </div>

      <div class="title">前置知识</div>
      <div>
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div class="html-area markdown-body" v-html="courseIntroductionData.courseFrontKnowledgeData" />
      </div>

      <div class="title">课程目标</div>
      <div>
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div class="html-area markdown-body" v-html="courseIntroductionData.courseGoalData" />
      </div>
    </div>
    <div v-else class="empty-tooltip">
      <div style="position: relative">
        <img src="@/assets/image/emptys2x.png" alt="" />
        <div class="tootip-txt">
          <p class="intro">您暂未添加课程介绍</p>
          <p v-if="isNotOpenAndPublishedCourse" class="route">请立即 <span @click="handleEditClick">编辑课程介绍</span></p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>

<script>
import { mapState, mapMutations } from 'vuex';
import { getCourseIntroduction, publishIntroOrResource, textFind } from '@/apis/teaching.js';

export default {
  name: 'CourseIntroduction',
  data() {
    return {
      courseId: this.$route.params.courseId,
    };
  },
  computed: {
    ...mapState('course', ['courseIntroductionData', 'currentActiveCourse']),
    isNotOpenAndPublishedCourse() {
      return !(this.currentActiveCourse.courseFlag == '1' && this.currentActiveCourse.coursePublish == '1');
    },
    hasIntroContent() {
      return this.courseIntroductionData.courseFrontKnowledge && this.courseIntroductionData.courseDesc && this.courseIntroductionData.courseGoal;
    },
    subTab() {
      return this.$route.params.subTab;
    },
  },
  watch: {
    subTab(val) {
      if (val === 'course-introduction') {
        this.initData();
      }
    },
  },
  mounted() {
    if (this.subTab === 'course-introduction') {
      this.initData();
    }
  },
  methods: {
    ...mapMutations('course', ['SET_COURSEINTRODUCTION_DATA']),

    async initData() {
      const res = await getCourseIntroduction({ courseId: this.courseId });
      if (res.state === 'OK') {
        const { courseDesc, courseGoal, courseFrontKnowledge, status, pubTime } = res.body;
        const courseIntroduction = {
          courseId: this.courseId,
          courseDesc,
          courseFrontKnowledge,
          courseGoal,
          courseDescData: '',
          courseFrontKnowledgeData: '',
          courseGoalData: '',
          pubTime,
          status,
        };
        if (courseDesc && courseGoal && courseFrontKnowledge) {
          Promise.all([textFind({ id: courseDesc }), textFind({ id: courseFrontKnowledge }), textFind({ id: courseGoal })]).then((res) => {
            if (res[0].state === 'OK' && res[1].state === 'OK' && res[2].state === 'OK') {
              courseIntroduction.courseDescData = res[0].body.content;
              courseIntroduction.courseFrontKnowledgeData = res[1].body.content;
              courseIntroduction.courseGoalData = res[2].body.content;
              this.SET_COURSEINTRODUCTION_DATA(courseIntroduction);
            }
          });
        } else {
          this.SET_COURSEINTRODUCTION_DATA(courseIntroduction);
        }
      }
    },
    handleEditClick() {
      const params = { ...this.$route.params, subPage: 'course-introduction-edit' };
      this.$router.push({
        name: '课程管理编辑',
        params,
      });
    },
    async handlePublish() {
      const res = await publishIntroOrResource({
        courseId: this.courseId,
        type: 'introduce',
      });
      if (res.state === 'OK') {
        this.$message.success('课程介绍发布成功');
        this.initData();
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.operation-group {
  position: absolute;
  top: 0px;
  right: 32px;
}
.course-intro-header {
  height: 40px;
  width: 100%;
  background-color: #f8f9fa;
  padding-left: 16px;
  margin-bottom: 24px;
  font-size: 12px;
  color: #606972;
}

.title {
  font-weight: @jt-font-weight-medium;
  color: #121f2c;
  margin-bottom: 16px;
}
.html-area {
  color: #606972;
  min-height: 60px;
  white-space: pre-line;
  margin-bottom: 48px;
}
.empty-tooltip {
  display: flex;
  justify-content: center;
  .tootip-txt {
    position: absolute;
    bottom: 90px;
    width: 100%;
    text-align: center;
    .intro {
      color: #121f2c;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .route {
      font-size: 14px;
      color: #606972;
      span {
        color: #0082ff;
        cursor: pointer;
      }
    }
  }
}
</style>
