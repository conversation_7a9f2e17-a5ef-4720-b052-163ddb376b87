<script setup>
import { UploadOutlined } from '@ant-design/icons-vue';
</script>
/* 新增文档弹框 */
<template>
  <a-modal v-model:open="open" :destroy-on-close="true" :title="editing ? '编辑文档' : '新增文档'" :mask-closable="false" @cancel="() => hideModal()">
    <a-form ref="addDocModel" :model="formData" :colon="false" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <a-form-item
        label="文档名称"
        name="docName"
        :rules="{
          required: true,
          message: '20个字符以内',
          trigger: 'change',
          max: 20,
        }"
      >
        <a-input v-model:value="formData.docName" placeholder="请输入文档名称" />
      </a-form-item>

      <a-form-item
        label="上传文档"
        name="uploadDoc"
        extra="仅支持上传PDF文件，不超过50M"
        :rules="{
          required: true,
          message: '请选择文件',
          trigger: 'blur',
        }"
      >
        <a-upload
          accept=".pdf"
          :file-list="fileList"
          action="./object/web/storage/file/upload"
          :multiple="false"
          :headers="{
            Authorization: 'Bearer ' + refreshToken,
          }"
          :remove="handleRemove"
          @change="uploadPdf"
        >
          <a-button> <UploadOutlined /> 上传文档 </a-button>
        </a-upload>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-space style="margin: 6px 16px">
        <a-button class="w-64" @click="() => hideModal()"> 取消 </a-button>
        <a-button type="primary" :disabled="submitBtnDisable" class="w-64" @click="handleConfirm"> 确定 </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script>
import { addVideoOrDoc, editVideoOrDoc } from '@/apis/teaching.js';
import { mapState } from 'vuex';

export default {
  name: 'DocModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    courseId: {
      type: String,
      default: '',
    },
    catalogId: {
      type: Number,
      default: 0,
    },
    editing: {
      type: Boolean,
    },
    selectResource: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['hideModal'],
  data() {
    return {
      open: false,
      formData: {
        docName: '',
        uploadDoc: '',
      },
      fileList: [],
      docRealName: '',
      docLoading: false, // 正在上传中标识
    };
  },
  computed: {
    submitBtnDisable() {
      return !(this.formData.docName && this.formData.uploadDoc && !this.docLoading);
    },
    ...mapState(['refreshToken']),
  },
  watch: {
    visible(val) {
      this.open = val;
    },
  },
  created() {
    this.open = this.visible;
  },
  mounted() {
    //数据初始化赋值
    if (this.editing) {
      this.formData.docName = this.selectResource.resourseName;
      this.formData.uploadDoc = this.selectResource.resourseUrl;
      this.docRealName = this.selectResource.docRealName;
      if (!this.docRealName) {
        // 为了兼容之前没有docRealName的数据
        let arr = this.selectResource.resourseUrl.split('/');
        this.docRealName = arr[arr.length - 1];
      }
      this.fileList = [
        {
          uid: '1',
          name: this.docRealName, // 仅显示上传时的名称
          status: 'done',
          url: this.selectResource.resourseUrl,
        },
      ];
    }
  },
  methods: {
    hideModal(bol = false) {
      this.formData.docName = '';
      this.formData.uploadDoc = '';
      this.$emit('hideModal', bol);
    },
    uploadPdf(info) {
      this.docLoading = true;
      const file = info.file;
      const splitArr = file.name.split('.');
      // 格式校验
      if (splitArr.length > 0) {
        const type = splitArr[splitArr.length - 1];
        if (type !== 'pdf') {
          this.$message.error('上传文件格式有误');
          return false;
        }
      }
      if (file.size / 1024 > 1024 * 50) {
        this.$message.error('上传文件超过50M');
        return false;
      }
      let fileList = [...info.fileList];
      fileList = fileList.slice(-1);
      if (file.status === 'done') {
        this.docRealName = file.name;
        if (file.response && file.response.state === 'OK') {
          this.formData.uploadDoc = file.response.body.url;
          this.$message.success(`文件上传成功`);
        }
        this.docLoading = false;
      } else if (status === 'error') {
        this.docLoading = false;
        this.$message.error(`文件上传失败`);
      }
      this.fileList = fileList;
      console.log('上传完成');
    },
    handleRemove() {
      this.formData.uploadDoc = '';
    },
    handleConfirm() {
      this.$refs.addDocModel.validate().then(() => {
        if (this.editing) {
          editVideoOrDoc({
            id: this.selectResource.id,
            resourseName: this.formData.docName,
            resourseUrl: this.formData.uploadDoc,
            docRealName: this.docRealName,
          }).then((res) => {
            if (res.state === 'OK') {
              this.$message.success('修改文档成功');
              this.hideModal(true);
            } else {
              this.$message.error('修改文档失败');
            }
          });
        } else {
          // 提交新增文档数据
          addVideoOrDoc({
            courseId: this.courseId,
            catalogId: this.catalogId,
            resourseType: '3',
            resourseName: this.formData.docName,
            resourseUrl: this.formData.uploadDoc,
            docRealName: this.docRealName,
          }).then((res) => {
            if (res.state === 'OK') {
              this.$message.success('新增文档成功');
              this.hideModal(true);
            } else {
              this.$message.error('新增文档失败');
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
