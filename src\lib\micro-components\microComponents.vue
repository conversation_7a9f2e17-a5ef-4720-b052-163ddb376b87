<template>
  <micro-app :name="moduleName" :url="url" :data="microAppData"></micro-app>
</template>

<script>
import microApp from '@micro-zoe/micro-app';
// 本地开发模式地址不固定
const path = location.pathname.split('/').slice(0, -1).join('/');
const url = process.env.NODE_ENV === 'development' ? 'http://localhost:8089/common-components' : `${location.origin}${path}/common-components`;
// 使用此类型地址，可以直接调试研发域和测试域的远程组件
// const url = `http://localhost:8080/api/dev/common-components`;
export default {
  name: 'MicroComponents',
  props: {
    moduleName: String,
    data: {
      type: Object,
      default: null,
    },
  },
  emits: ['*'],
  data() {
    return {
      url,
    };
  },
  computed: {
    microAppData() {
      return {
        moduleName: this.moduleName,
        data: {
          ...this.data,
        },
      };
    },
  },
  mounted() {
    microApp.addDataListener(this.moduleName, (remoteData) => {
      if (remoteData.isEvent) {
        this.$emit(remoteData.detail.type, remoteData.detail.data);
      }
    });
  },
};
</script>
