<template>
  <div class="teaching-box">
    <div v-if="upgrading" class="upgrading-container">
      <div class="empty-box">
        <img :src="upgradingImage" alt="" />
        <div class="empty-title">
          <h3>{{ upgradingTitle }}</h3>
          <span>{{ upgradingContent }}</span>
        </div>
      </div>
    </div>
    <template v-else>
      <section class="banner-section">
        <div class="banner-container">
          <div class="banner-description purchase-intro">
            <template v-if="purchased && !loading">
              <h1>丰富的课程内容</h1>
              <h2 style="margin-top: 16px">覆盖人工智能、区块链、大数据、信息安全、集成电路等专业</h2>
              <h2>上百门精品课程，欢迎试用</h2>
            </template>
            <template v-if="!purchased && !loading">
              <h1>在线教研一体化智能平台</h1>
              <h2 style="margin-top: 16px">深耕信息类专业的教学实验系统</h2>
              <h2>中国移动·希冀 联合打造</h2>
            </template>
          </div>
          <course-remind :loading="loading" :purchased="purchased" :total="total" />
        </div>
      </section>
      <jt-common-content style="min-height: 418px" :empty-style="emptyStyle" :loading="loading" :empty="false">
        <template v-if="!loading">
          <!-- 2025-05-23 取消课程列表 -->
          <!-- <section v-if="total > 0">
            <school-course-list :courseData="courseList" :initCourseTotal="total" />
          </section> -->
          <section>
            <teach-manage-intro />
            <develop-enviroment-intro />
            <course-content-intro />
            <chrastic-intro />
          </section>
        </template>
      </jt-common-content>
    </template>
  </div>
</template>

<script>
import CourseRemind from './components/CourseRemind.vue';
import SchoolCourseList from './components/SchoolCourseList.vue';
import chrasticIntro from './introContent/chrasticIntro.vue';
import courseContentIntro from './introContent/courseContentIntro.vue';
import developEnviromentIntro from './introContent/developEnviromentIntro.vue';
import teachManageIntro from './introContent/teachManageIntro.vue';
import { getXJCourseList, getTeachingStatus } from '../../apis/teaching.js';
import { keycloak } from '@/keycloak';
let upgrading = false;
let upgradingTitle = '';
let upgradingContent = '';
export default {
  name: 'Teaching',
  components: {
    CourseRemind,
    chrasticIntro,
    courseContentIntro,
    developEnviromentIntro,
    teachManageIntro,
  },
  beforeRouteEnter(to, from, next) {
    // 登录了且无升级中访问权限
    if (keycloak.authenticated && keycloak.idTokenParsed.DLP_USER_ALLOW_ENTER_DURING_UPGRADING != '1') {
      getTeachingStatus()
        .then((res) => {
          if (res.state === 'OK' && res.body.teachingSwitch) {
            upgrading = true;
            upgradingTitle = res.body.title || '维护升级中';
            upgradingContent = res.body.content || '等会再来看看吧';
          }
          next();
        })
        .catch(() => {
          next();
        });
    } else {
      next();
    }
  },
  data() {
    return {
      upgradingImage: require('@/assets/image/teaching/system-maintenance.png'),
      upgrading, // 希冀升级状态
      upgradingTitle,
      upgradingContent,
      loading: true,
      purchased: false, // 已购买课程
      courseList: [], // 课程列表
      total: 0, //总数
      emptyStyle: {
        height: 'calc(100vh - 460px - 403px)', // 减去顶部和底部高度
        'min-height': '416px',
      },
    };
  },
  created() {
    if (!this.$keycloak.authenticated || this.upgrading) {
      this.loading = false;
    } else {
      this.initCourseData();
    }
  },
  methods: {
    async initCourseData() {
      const res = await getXJCourseList({
        page: 1,
        limitInPage: 8,
        userType: 1, // 1 教师
      });
      if (res.state === 'OK' && res.body && res.body.total > 0) {
        this.purchased = true;
        this.courseList = res.body.course;
        this.total = res.body.total;
      }
      this.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.teaching-box {
  .banner-section {
    height: 460px;
    background-color: #c3e5ff;
    .banner-container {
      width: 1200px;
      margin: 0px auto;
      height: 100%;
      display: flex;
      justify-content: space-between;
      .banner-description {
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 48px;
        margin-bottom: 56px;
        flex-grow: 1;
        h1 {
          color: #121f2c;
          font-size: 48px;
          font-weight: @jt-font-weight-medium;
          line-height: 67px;
        }
        h2 {
          font-weight: @jt-font-weight-medium;
          font-size: 16px;
          color: #121f2c;
          line-height: 26px;
        }
      }
      .purchase-intro {
        padding-top: 167px;
        background-image: url('~@/assets/image/teaching/teaching-1.png');
      }
    }
  }
  .upgrading-container {
    padding-top: 80px;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 640px;
    height: calc(100vh - @jt-footer-height + @jt-header-height);

    .empty-box {
      position: relative;
      img {
        width: 416px;
        height: 416px;
      }
      .empty-title {
        position: absolute;
        bottom: 90px;
        left: 0px;
        right: 0px;
        text-align: center;
        h3 {
          font-size: 18px;
          font-weight: @jt-font-weight-medium;
          color: #121f2c;
        }
        span {
          font-size: 12px;
          color: #606972;
        }
      }
    }
  }
}
</style>
