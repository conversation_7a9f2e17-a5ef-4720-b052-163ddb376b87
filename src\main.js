import { createApp } from 'vue';
import router from './router';
import 'ant-design-vue/dist/reset.css';
import { init, keycloak } from './keycloak.js';
import { setupAntd } from './useAntd';
import { setupVant } from './useVant';
import App from './App.vue';
import store from './store';
import jtIcon from './lib/jtIcon';
import jtComponents from './lib/jtComponents';
import scoller from './lib/directives/table-scroller';
import './lib/micro-components/useMicroComponents';
import { GET, POST } from './request/index';

const app = createApp(App);

app.config.productionTip = false;
// 挂载全局方法
app.config.globalProperties.$keycloak = keycloak;
app.config.globalProperties.$GET = GET;
app.config.globalProperties.$POST = POST;
app.config.silent = false;

const ifH5page = window.location.href.indexOf('/h5') !== -1;

if (ifH5page) {
  setupVant(app);
  app.mount('#app');
} else {
  init().finally(() => {
    app.use(router);
    app.use(store);
    app.use(jtIcon);
    app.use(jtComponents);
    app.use(scoller);
    setupAntd(app);
    app.mount('#app');
  });
}
