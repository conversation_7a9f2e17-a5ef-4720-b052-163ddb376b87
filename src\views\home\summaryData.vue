<template>
  <section class="content-box school-box">
    <div class="inner">
      <h1 class="portal-title" style="margin-top: 130px">全新功能陆续上线，敬请期待</h1>
      <h2 class="sub-title">
        这里已经有
        {{ userCount }}
        位开发者一起创建模型、学习课程和参加比赛，赢取奖金和offer
      </h2>
      <div class="data-list">
        <div class="data-item">
          {{ courseNumber }}
          <span>已有课程</span>
        </div>
        <div class="data-item">
          {{ dataCount }}
          <span>已有数据集</span>
        </div>
        <div class="data-item">
          {{ userCount }}
          <span>已有开发者</span>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { GET, POST } from '@/request';
export default {
  name: 'home-summary-data',
  data() {
    return {
      courseNumber: 0,
      dataCount: 0,
    };
  },
  computed: {
    userCount() {
      return this.$store.state.userCount;
    },
  },
  mounted() {
    this.getCourseNum();
    this.getDataCount();
    this.$store.dispatch('getUserCount');
  },
  methods: {
    getCourseNum() {
      GET('/course_model/web/course/existed/num', { requestId: this.$store.state.requestId }).then((res) => {
        if (res.state === 'OK') {
          this.courseNumber = res.body;
        }
      });
    },
    getDataCount() {
      POST('dataset/web/dataCount').then((res) => {
        if (res.state === 'OK') {
          this.dataCount = res.body.dataCount;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import url('./common.less');

.school-box {
  padding-top: 0;
  padding-bottom: 163px;
  h2 {
    margin-top: 10px;
  }
}

.data-list {
  display: flex;
  justify-content: space-around;
  margin-top: 64px;
}
.data-item {
  font-size: 48px;
  font-weight: bold;
  color: @jt-primary-color;
  line-height: 58px;
  text-align: center;
  span {
    margin-top: 6px;
    display: block;
    font-size: 16px;
    color: #121f2c;
    line-height: 22px;
  }
}
</style>
