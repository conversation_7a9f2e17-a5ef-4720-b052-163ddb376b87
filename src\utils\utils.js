import { smsApi } from '@/apis/';
import { textFind } from '@/apis/teaching.js';
import router from '@/router';
import moment from 'moment';
import { BrowserDetect } from '@/utils/browserdetect.js';

/**
 * 数字转成汉字
 * @params num === 要转换的数字
 * @return 汉字
 * */
export function toChinesNum(num) {
  var arr1 = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  var arr2 = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿']; //可继续追加更高位转换值
  if (!num || isNaN(num)) {
    return '零';
  }
  var english = num.toString().split('');
  var result = '';
  for (var i = 0; i < english.length; i++) {
    var des_i = english.length - 1 - i; //倒序排列设值
    result = arr2[i] + result;
    var arr1_index = english[des_i];
    result = arr1[arr1_index] + result;
  }
  result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
  result = result.replace(/零+/g, '零');
  result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
  result = result.replace(/亿万/g, '亿');
  result = result.replace(/零+$/, '');
  result = result.replace(/^一十/g, '十');
  return result;
}

export function pasteContent(str) {
  const el = document.createElement('textarea');
  el.value = str;
  el.setAttribute('readonly', '');
  el.style.position = 'absolute';
  el.style.left = '-9999px';
  document.body.appendChild(el);
  el.select();
  document.execCommand('copy');
  document.body.removeChild(el);
}

export function phoneHider(str = '') {
  return (str || '').replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

const sendCode = (function () {
  let sending = false;
  return (phonenum) => {
    if (sending) {
      return;
    }
    sending = true;
    return smsApi.sendSmsCode({ phonenum }, true).then((res) => {
      sending = false;
      return res;
    });
  };
})();

export const userSendCode = (function () {
  let sending = false;
  return () => {
    if (sending) {
      return;
    }
    sending = true;
    return smsApi.userSendCode({}, true).then((res) => {
      sending = false;
      return res;
    });
  };
})();

const sendSmsCodeMolileCloud = (function () {
  let sending = false;
  return (phone) => {
    if (sending) {
      return;
    }
    sending = true;
    return smsApi.sendSmsCodeMolileCloud({ phone }, true).then((res) => {
      sending = false;
      return res;
    });
  };
})();

export { sendCode, sendSmsCodeMolileCloud };

// 通过url拉取富文本
export function getRichText(str) {
  if (!str) {
    return new Promise((resolve) => {
      resolve();
    });
  }
  return textFind({ id: str }).then((res) => {
    return (res.body && res.body.content) || '';
  });
}

export function checkFileType(types, fileName) {
  return types.split(',').includes('.' + fileName.split('.')[1]);
}

export function checkAuth(errorCode, targetCode = '-801', redirectUrl = '/') {
  if (`${errorCode}` === `${targetCode}`) {
    router.replace(redirectUrl);
    return false;
  }
  return true;
}

export function checkImgUrl(url, types = '.jpg,.jpeg,.gif,.png,.bmp,.JPG,.JPEG,.GIF,.PNG,.BMP') {
  const typeCollections = types.split(',').map((item) => item.slice(1));
  const suffix = (url || '').split('.').slice(-1)[0];
  if (typeCollections.includes(suffix)) {
    return true;
  }
  return false;
}

//防抖节流

// 节流函数，每隔一段时间，只执行一次该行为；可以自行判断是否禁止掉收尾的某一个行为，只有三种用法：
/*******
container.onmousemove = throttle(getUserAction, 1000);
container.onmousemove = throttle(getUserAction, 1000, {
  leading: false
});
container.onmousemove = throttle(getUserAction, 1000, {
  trailing: false
});
*******/

export function throttle(fn, wait, options = {}) {
  let timeout;
  let context;
  let args;
  let previous = 0;

  const later = function () {
    previous = options.leading === false ? 0 : new Date().getTime();
    timeout = null;
    fn.apply(context, args);
    if (!timeout) context = args = null;
  };

  const throttled = function () {
    const now = new Date().getTime();
    if (!previous && options.leading === false) previous = now;
    let remaining = wait - (now - previous);
    context = this;
    args = arguments;
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      fn.apply(context, args);
      if (!timeout) context = args = null;
    } else if (!timeout && options.trailing !== false) {
      timeout = setTimeout(later, remaining);
    }
  };
  return throttled;
}

// 防抖函数;第三个参数代表是否立即执行

export function debounce(fn, wait = 500, immediate = false) {
  let time = null;
  let result = null;
  return function () {
    let context = this;
    let args = arguments;
    if (time) {
      clearTimeout(time);
    }
    if (immediate) {
      const callNow = !time;
      time = setTimeout(function () {
        time = null;
      }, wait);
      if (callNow) {
        result = fn.apply(context, args);
      }
    } else {
      time = setTimeout(() => fn.apply(context, args), wait);
    }
    return result;
  };
}

// 分钟转显示的字符串
export function formatDuration(minute) {
  if (minute < 1) {
    return Math.round(minute * 60) + '秒';
  }
  return minute + '分钟';
}

export const sleep = (time) => {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
};

// 日期转换
export function dateConvert(date) {
  date = new Date(date);
  let y = date.getFullYear();
  let m = date.getMonth() + 1;
  let d = date.getDate();
  m = m < 10 ? '0' + m : m;
  d = d < 10 ? '0' + d : d;
  return y + '/' + m + '/' + d + ' ';
}

export function dateConvertYearMonthDay(date) {
  date = new Date(date);
  let y = date.getFullYear();
  let m = date.getMonth() + 1;
  let d = date.getDate();
  m = m < 10 ? '0' + m : m;
  d = d < 10 ? '0' + d : d;
  return y + '年' + m + '月' + d + '日';
}

// 将传入日期的时分秒转换为00:00:00
export function dateStartTime(date) {
  return moment(new Date(moment(new Date(date)).startOf('day').valueOf())).format('YYYY-MM-DD HH:mm:ss');
}
// 将传入日期的时分秒转换为23:59:59
export function dateEndTime(date) {
  return moment(new Date(moment(new Date(date)).endOf('day').valueOf())).format('YYYY-MM-DD HH:mm:ss');
}
// 跳转consle平台
export function gotoConslePlatform(url) {
  return window.location.replace(url);
}

export function getWeekDay(time) {
  const week = {
    1: '星期一',
    2: '星期二',
    3: '星期三',
    4: '星期四',
    5: '星期五',
    6: '星期六',
    7: '星期天',
  };
  return week[time] ? week[time] : '日期错误';
}

// 比赛奖池处理
export function handlePrize(num) {
  if (isNaN(num * 1)) return num;
  const isNegative = !(num >= 0);
  if (isNegative) num = Math.abs(num);
  let intNum = Math.trunc(num),
    flotNum = '';
  if (num.toString().split('.').length > 1) {
    flotNum = num.toString().split('.')[1];
  }
  let c = intNum && intNum !== 0 ? intNum.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,') : '0';
  if (isNegative) {
    c = '-' + c + (flotNum ? `.${flotNum}` : '');
  } else if (flotNum) {
    c = c + '.' + flotNum;
  }
  return c;
}

// 从右往左，隔三位加一个
export function toThousandFilter(num) {
  if (isNaN(num)) {
    return num;
  } else {
    return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
  }
}

/**
 * 浏览器复制内容到粘贴板
 * @param {String} text - 复制的内容
 */
export function handleCopy(text) {
  var oInput = document.createElement('input');
  oInput.style.border = '0 none';
  oInput.style.color = 'transparent';
  oInput.value = text;
  document.body.appendChild(oInput);
  oInput.select();
  document.execCommand('Copy');
  oInput.parentNode.removeChild(oInput);
}

/**
 * 判断元素在页面中是否可见
 */
export function isElementVisibility(el) {
  if (!window || !el) return;
  const rect = el.getBoundingClientRect();
  const visibility = rect.top <= (window.innerHeight || document.documentElement.clientHeight) && rect.left <= (window.innerWidth || document.documentElement.clientWidth) && rect.bottom >= 0 && rect.right >= 0;
  return visibility;
}

// 判断是否为移动端设备
export function isH5device() {
  const currentOs = BrowserDetect.init().OS;
  return currentOs === 'iPhone' || currentOs === 'Android';
}

export function openInNewTab(url, target) {
  const newwin = window.open(url, target);
  newwin.opener = null;
}

// 根据名称获取浏览器query参数
export function getQueryByName(name) {
  const queryList = window.location.href.split('?')[1]?.split('&') || [];
  const curQuery = queryList.find((item) => item.includes(name));
  return curQuery?.split('=')[1] || '';
}

// 对象的形式添加query参数
export function addUrlParams(url, urlParams) {
  let baseURL = new URL(url);
  let params = new URLSearchParams(urlParams);
  baseURL.search = params.toString();
  return baseURL.href;
}

// 检查没有数据显示--
export function checkTextEmpty(value, noTextData = '--') {
  if (value === null || value === undefined || value === '') {
    return noTextData;
  } else {
    return value;
  }
}

/**
 *
 * @param {String} queryString 拼接的字符串
 * @returns {Object} 返回参数对象
 */
export function queryStringToObject(queryString) {
  if (!queryString) return {};
  const pairs = queryString.split('&');
  const result = {};

  for (let i = 0; i < pairs.length; i++) {
    const pair = pairs[i];
    const [key, value] = pair.split('=');
    result[decodeURIComponent(key)] = decodeURIComponent(value || '');
  }

  return result;
}

/**
 *
 * @param {string} url
 * @returns {Object} 返回参数对象
 */
export function getQueryObjBeforeHash(url = window.location.href) {
  const [beforeHash] = url.split('#', 2);
  const [domainAndPath, queryString] = beforeHash.split('?', 2);
  return queryStringToObject(queryString);
}

// 入口资源是否为国资
export const isEntranceBySasac = () => {
  const entrance = getQueryObjBeforeHash()?.entrance || ''; // 系统来源（默认是九天），sasac-国资
  return entrance === 'sasac';
};
