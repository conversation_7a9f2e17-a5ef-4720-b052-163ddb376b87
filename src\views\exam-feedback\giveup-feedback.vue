<template>
  <div class="main-container">
    <div class="logo">
      <a href="./">
        <img src="@/assets/image/home/<USER>" alt="" />
      </a>
    </div>
    <div class="main-content">
      <template v-if="!submitted">
        <h3>考试反馈</h3>
        <a-form ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-item ref="reason" label="放弃参加原因" name="reason">
            <a-select v-model="form.reason" placeholder="请选择" :options="reasonOptions" />
          </a-form-item>
          <a-form-item ref="remark" label="备注" name="remark">
            <a-input v-model:value="form.remark" placeholder="请输入" type="textarea" />
          </a-form-item>
          <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
            <a-button type="primary" @click="onSubmit"> 提交 </a-button>
          </a-form-item>
        </a-form>
      </template>
      <a-result v-else status="success" title="反馈成功"> </a-result>
    </div>
  </div>
</template>

<script>
import { POST } from '@/request';
export default {
  name: 'GiveupFeedback',
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      form: {
        reason: undefined,
        remark: '',
      },
      rules: {
        reason: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        remark: [{ min: 1, max: 50, message: '最多50字', trigger: ['blur', 'change'] }],
      },
      reasonOptions: [
        {
          label: '时间不合适',
          value: '时间不合适',
        },
        {
          label: '已拿到offer',
          value: '已拿到offer',
        },
        {
          label: '没有网络环境',
          value: '没有网络环境',
        },
        {
          label: '其他，在备注中说明',
          value: '其他，在备注中说明',
        },
      ],
      submitted: false,
    };
  },
  computed: {
    serid() {
      return this.$route.query.serid;
    },
  },
  methods: {
    onSubmit() {
      this.$refs.ruleForm
        .validate()
        .then(() => {
          const obj = {
            serid: this.serid,
            causemsg: this.form.reason,
            comment: this.form.remark,
          };
          POST('/examination/saveAbandonMsg', obj).then((res) => {
            if (res.state === 'OK') {
              this.submitted = true;
            } else {
              this.$message.error(res.errorMessage || '提交失败');
            }
          });
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.main-container {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 20px;
  height: 100vh;
  .logo {
    position: absolute;
    left: 20px;
    top: 20px;
    img {
      width: 240px;
    }
  }
  .main-content {
    background: #fff;
    width: 800px;
    height: min-content;
    // margin-top: 20px;
    padding: 20px;
    h3 {
      margin-bottom: 20px;
      text-align: center;
    }
    p {
      margin-bottom: 20px;
    }
  }
}
</style>
