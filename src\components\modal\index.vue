<template>
  <a-modal v-model:open="sVisible" :get-container="getContainer" dialog-class="jt-normal-modal" :width="width" :destroy-on-close="true" :title="title" :closable="closable" :mask-closable="false" @cancel="handleCancel">
    <slot></slot>
    <template #footer>
      <a-space :size="8">
        <a-button v-if="showCancel" :cancel-loading="cancelLoading" @click="handleCancel">{{ cancelText }}</a-button>
        <a-button type="primary" :disabled="confirmLoading" @click="handleOk">{{ okText }}</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<!--
  弹框组件（带title和关闭按钮）
  <modal 
      v-model="visible" // 或者visible：Boolean
      :okText=“确定”
      :cancelText="取消"
      :confirmLoading="false" // 确认按钮disable
      :showCancel="true" // 是否显示取消按钮，默认显示
      @cancel="handlecancel"
      @ok="handleOk"
    >
    
  </modal>
 -->
<script>
export default {
  name: 'Modal',
  model: {
    prop: 'visible',
    event: 'change',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      required: true,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    cancelLoading: {
      type: Boolean,
      default: false,
    },
    okText: {
      type: String,
      default: '确定',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    width: {
      type: [String, Number],
      default: 520,
    },
    getContainer: {
      type: [Function],
      default: () => document.body,
    },
  },
  emits: ['change', 'cancel', 'ok'],
  data() {
    return {
      sVisible: !!this.visible,
    };
  },
  watch: {
    visible(val) {
      this.sVisible = !!val;
    },
  },
  methods: {
    handleCancel(e) {
      this.$emit('change', false);
      this.$emit('cancel', e);
    },
    handleOk(e) {
      this.$emit('ok', e);
    },
  },
};
</script>

<style lang="less">
.jt-normal-modal {
  .ant-modal-close-x {
    font-size: 12px;
    color: #a0a6ab;
    height: 49px;
    line-height: 49px;
  }
  .ant-modal-header {
    padding: 13px 24px;
    .ant-modal-title {
      color: #121f2c;
      font-weight: 600;
      border-color: #e9ebef;
    }
  }
  .ant-modal-footer {
    padding: 16px 32px;
    border-color: #e6ebf5;
    .ant-btn {
      font-size: 14px;
      height: 32px;
    }
  }
}
</style>
