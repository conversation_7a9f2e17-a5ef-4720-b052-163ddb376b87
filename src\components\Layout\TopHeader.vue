<template>
  <a-layout-header class="header-container">
    <div class="left" v-if="isEntranceBySasac()">
      <img @click="goToSasac" src="@/assets/image/logo_sasac.png" alt="" />
    </div>
    <div class="left" v-else>
      <img @click="goToHomePage" src="@/assets/image/chinamobile-jiutian.png" alt="" />
      <span class="title" @click="goToHomePage">九天人工智能</span>
    </div>
    <div class="right">
      <LinkItems />
    </div>
  </a-layout-header>
</template>

<script>
import LinkItems from '@/components/LinkItems.vue';
import { isEntranceBySasac } from '@/utils/utils.js';

export default {
  name: 'Header',
  props: {
    collapsed: {
      default: false,
      type: Boolean,
    },
  },
  data() {
    return {
      isEntranceBySasac,
    };
  },
  components: {
    LinkItems,
  },
  methods: {
    goToHomePage() {
      location.assign('/');
    },
    goToSasac() {
      window.location.href = '/sasac-aiopen/#/';
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.header-container {
  position: absolute;
  top: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // background: rgba(255, 255, 255, 0.15);
  background: #fff;
  border-bottom: 1px solid rgba(18, 31, 44, 0.08);
  backdrop-filter: blur(7px);
  height: 60px;
  padding-left: 24px;
  padding-right: 0px;
  z-index: 2014; // 会与其他z-index有冲突，故使用2014
  transition: 0.3s all ease;
  // &:hover {
  //   background: #ffffff;
  // }
  .left {
    display: flex;
    align-items: center;
    img {
      height: 32px;
      cursor: pointer;
    }
    .title {
      height: 60px;
      margin-left: 8px;
      margin-right: 40px;
      font-size: 16px;
      font-weight: 600;
      color: #121f2c;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    }
  }

  .right {
    a {
      color: @jt-color-nav-text;
      font-size: @jt-font-size-sm;
      &:hover {
        color: @jt-color-hover;
      }
    }
  }
}
</style>
