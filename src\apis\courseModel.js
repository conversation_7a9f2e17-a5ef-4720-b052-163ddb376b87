import { GET, POST } from '@/request';

// 查询课程所有分类 course-list
export const getCourseCategoryList = (data) => GET('/course_model/web/courseCategory/list', data);
// 查询课程所有级别 course-list
export const getCourseLevelList = (data) => GET('/course_model/web/courseLevel/list', data);
// 查询精品课程列表 course
export const getBoutiqueList = (data) => GET('/course_model/web/course/boutique', data);
// 查询课程详情 course-detail
export const getCourseDetail = (data) => POST('/course_model/web/course/detail', data);
// 查询课程详情目录 course-detail
export const getCourseDetailCatalogs = (data) => POST('/course_model/web/course/detail/catalogs', data);
// 查询相关课程 course-detail
export const getCourseRelate = (data) => POST('/course_model/web/course/detail/relate', data);
// 查询课程详情教师团队 course-detail
export const getCourseDetailteachers = (data) => POST('/course_model/web/course/detail/teachers', data);
// 获取已有课程数量 course-list
export const getCourseExistedNum = (data) => GET('/course_model/web/course/existed/num', data);
// 分页分类查询课程 course-list
export const getCourseList = (data) => POST('/course_model/web/course/list', data);
// AI路线图课程列表 course
export const getCourseSearchModelAI = (data) => POST('/course_model/web/course/searchModelAI', data);
// 获取明星老师 course
export const getExcellentTeacherList = (data) => GET('/course_model/web/excellent/teacher/list', data);
// 获取合作学校 course
export const getSchoolList = (data) => GET('/course_model/web/school/list', data);
// 获取课程图片
export const getImage = (data) => GET('/course_model/web/image', data);
// 添加登录人学习课程
export const getInstanceNum = (data) => GET('/course_model/web/instance/num/create', data);
// 登出 清除JSESSIONID
export const logoutClearCookies = (data) => GET('/course_model/web/logout', data);
// 首页 -- 学习课程
export const getHomeBoutique = (data) => GET('/course_model/web/home/<USER>/boutique', data);
// 首页 -- 精品内容
export const getHomeCourseList = (data) => GET('/recommend/home', data);
// 获取学习课程赠送算力豆信息
export const getGiveBeansByCourse = () => GET('/marketing/web/course/getMessage');

// 根据课程id获取算力豆是否领取过状态
export const getReceiveStatusByCourseId = (data) => GET('/marketing/web/course/getUserStatus', data);
// 领取算力豆
export const rechargeBeanByCourse = (data) => POST('/marketing/web/course/rechargeBean', data);
