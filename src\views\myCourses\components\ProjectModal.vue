/* 新增项目弹框 */
<template>
  <div>
    <a-modal v-model:open="open" class="select-project-modal" :title="editing ? '编辑项目' : '新增项目'" :mask-closable="false" :closable="!projectLoading" @cancel="() => hideModal()">
      <a-spin :spinning="projectLoading" tip="提交中">
        <a-form ref="addProjectForm" :model="formData" :colon="false" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item
            label="项目名称"
            name="projectName"
            :rules="{
              required: true,
              message: '20个字符以内',
              trigger: 'change',
              max: 20,
            }"
          >
            <a-input v-model:value="formData.projectName" placeholder="请输入项目名称" />
          </a-form-item>

          <a-form-item
            label="选择实例"
            name="selectInstance"
            extra="仅可选择模型训练的实例"
            class="instance-select-item"
            :rules="{
              required: true,
              message: '请选择实例',
              trigger: 'blur',
            }"
          >
            <a-input v-model:value="formData.selectInstance" placeholder="请选择实例" :disabled="false">
              <template #suffix>
                <jt-icon type="iconshujushangdian"></jt-icon>
              </template>
            </a-input>
            <!-- 这里设计要求input不能输入，但需要触发点击事件，所以加一个mask -->
            <div class="instance-input-mask" @click="openInstanceModal"></div>
          </a-form-item>

          <a-form-item label="算力需求" name="computeResource" required>
            <a-radio-group v-model="formData.computeResource">
              <a-radio value="cpu"> CPU </a-radio>
              <a-radio value="vGpu"> vGPU </a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="实例模式" name="instanceModel" required>
            <a-radio-group v-model="formData.instanceModel">
              <a-radio value="Jupyter"> <span class="instance-model-icon jt-jupyter-icon">J</span>Jupyter </a-radio>
              <a-radio value="VSCode"> <span class="instance-model-icon jt-vscode-icon">V</span>VSCode </a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item :wrapper-col="{ span: 24 }" style="margin-bottom: 0px">
            <div style="font-size: 10px; line-height: 20px; color: rgba(160, 166, 171, 1)">
              <ol>
                <li style="list-style: inside; margin-bottom: 8px">实例中挂载的data文件夹（含公开和个人数据）将自动挂载至学生实例，个人数据总大小不超过30GB；除data文件夹以外的文件将复制至学生实例，需确保文件总数小于1000，总大小不超过1GB</li>
                <li style="list-style: inside">当实例中挂载的data文件夹，或实例其他存储内容发生变化时，需再次编辑项目并发布，方可将最新实例内容同步至学生</li>
              </ol>
            </div>
            <!-- <p style="font-size: 10px; line-height: 20px">.实例中挂载的data文件夹（含公开和个人数据）将自动挂载至学生实例，个人数据总大小不超过30GB；除data文件夹以外的文件将复制至学生实例，需确保文件总数小于1000，总大小不超过1GB<br />当实例中挂载的data文件夹，或实例其他存储内容发生变化时，需再次编辑项目并发布，方可将最新实例内容同步至学生</p> -->
          </a-form-item>
        </a-form>
      </a-spin>
      <template #footer>
        <a-space style="margin: 6px 16px">
          <a-button class="w-64" :disabled="projectLoading" @click="() => hideModal()"> 取消 </a-button>
          <a-button type="primary" class="w-64" :disabled="submitBtnDisable || projectLoading" @click="handleConfirm"> 确定 </a-button>
        </a-space>
      </template>
    </a-modal>

    <!-- 选择实例弹框 -->
    <select-instance-modal v-model="instanceModelVisible" :instance-id="selectInstance.instanceId" :instance-name="selectInstance.instanceName" @ok="handleOk" />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { createProjectForCatalog, updateCatalogProject } from '@/apis/teaching.js';
import SelectInstanceModal from '@/components/selectInstanceModal/index.vue';
export default {
  name: 'ProjectModal',
  components: {
    SelectInstanceModal,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    catalogId: {
      type: Number,
      default: 0,
    },
    courseId: {
      type: String,
      default: '',
    },
    editing: {
      type: Boolean,
    },
    selectResource: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['hideModal', 'createInterval'],
  data() {
    return {
      open: false,
      instanceModelVisible: false,
      selectInstance: {}, // 选中的实例
      projectLoading: false,
      isntanceTableColumn: [
        {
          title: '实例名称',
          dataIndex: 'instanceName',
          slots: { customRender: 'name-slot' },
          ellipsis: true,
          width: '35%',
        },
        // 实例状态，启动中1，进行中2，停止中3，已停止4，失败5
        {
          title: '状态',
          dataIndex: 'instanceStatus',
          slots: { customRender: 'status-slot' },
          width: '20%',
        },
        {
          title: '最近启动时间',
          dataIndex: 'startTime',
        },
      ],
      formData: {
        projectName: '',
        selectInstance: '',
        computeResource: 'cpu', // 默认选择cpu
        instanceModel: 'Jupyter',
      },
      statusMaps: {
        1: '启动中',
        2: '运行中',
        3: '停止中',
        4: '失败',
        5: '锁定',
        0: '停止',
      },
      errorMessage: '',
    };
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
    submitBtnDisable() {
      return !(this.formData.projectName && this.formData.selectInstance && this.formData.computeResource);
    },
  },
  watch: {
    visible(val) {
      this.open = val;
    },
  },
  created() {
    this.open = this.visible;
  },
  mounted() {
    if (this.editing) {
      this.formData.projectName = this.selectResource.resourseName;
      this.formData.computeResource = this.selectResource.spec;
      this.formData.instanceModel = this.selectResource.instanceModel || 'Jupyter';
      this.selectInstance.instanceId = this.selectResource.instanceId;
      this.selectInstance.instanceName = this.selectResource.instanceName || this.selectResource.instanceId;
      this.formData.selectInstance = this.selectResource.instanceName || this.selectResource.instanceId;
    }
  },
  methods: {
    handleOk(selectInstance) {
      this.formData.selectInstance = selectInstance.instanceName;
      this.selectInstance.instanceName = selectInstance.instanceName;
      this.selectInstance.instanceId = selectInstance.instanceId;
    },
    hideModal(bol = false) {
      this.$emit('hideModal', bol);
      this.formData.projectName = '';
      this.formData.selectInstance = '';
      this.formData.computeResource = '';
      this.selectInstance = {};
    },
    createInterval(item, time) {
      this.$emit('createInterval', item, true, time);
    },
    openInstanceModal() {
      this.instanceModelVisible = true;
    },

    // 创建项目
    handleConfirm() {
      this.$refs.addProjectForm
        .validate()
        .then(() => {
          let self = this;
          this.projectLoading = true;
          const isPublicCourse = this.currentActiveCourse.courseFlag == '1';
          // 编辑
          if (this.editing) {
            const item = {
              catalogId: self.selectResource.catalogId,
              courseId: self.selectResource.courseId,
              id: self.selectResource.id,
              projectName: self.formData.projectName,
              instanceId: self.selectInstance.instanceId,
              instanceName: self.selectInstance.instanceName,
              cpuFlag: self.formData.computeResource,
              instanceModel: self.formData.instanceModel,
            };
            updateCatalogProject(item)
              .then((res) => {
                if (res.state == 'OK') {
                  if (isPublicCourse) {
                    const { resourceId } = res.body;
                    self.createInterval({ id: resourceId, resourseName: item.projectName });
                  } else {
                    this.$message.success('修改项目成功');
                  }
                  self.hideModal(true);
                } else {
                  this.$message.error(res.errorMessage || '修改项目失败');
                }
                this.projectLoading = false;
              })
              .catch(() => {
                this.projectLoading = false;
              });
          } else {
            createProjectForCatalog({
              catalogId: self.catalogId,
              courseId: self.courseId,
              projectName: self.formData.projectName,
              instanceId: self.selectInstance.instanceId,
              instanceName: self.selectInstance.instanceName,
              cpuFlag: self.formData.computeResource,
              instanceModel: self.formData.instanceModel,
            })
              .then((res) => {
                if (res.state === 'OK') {
                  if (isPublicCourse) {
                    const { resourceId } = res.body;
                    self.createInterval({ id: resourceId, resourseName: self.formData.projectName });
                  } else {
                    self.$message.success('新建项目成功');
                  }
                  self.hideModal(true);
                } else {
                  if (res.errorCode === '-913' || res.errorCode === '-912' || res.errorCode === '-911') {
                    self.$message.error(res.errorMessage);
                  } else {
                    self.$message.error('新建项目失败');
                  }
                }
                this.projectLoading = false;
              })
              .catch(() => {
                this.projectLoading = false;
              });
          }
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
  },
};
</script>

<style lang="less">
@import '~@/assets/styles/index.less';
.select-project-modal {
  .instance-select-item {
    .ant-input-affix-wrapper .ant-input-suffix {
      z-index: 1;
    }
    .instance-input-mask {
      position: absolute;
      top: -10px;
      left: 0px;
      width: 100%;
      z-index: 1;
      height: 36px;
      cursor: pointer;
    }
  }

  .instance-model-icon {
    margin-right: 5px;
  }
  .jt-vscode-icon {
    .jt-vscode-icon();
  }
  .jt-jupyter-icon {
    .jt-jupyter-icon();
  }
}
</style>
