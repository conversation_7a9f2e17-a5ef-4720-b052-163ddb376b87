<script setup>
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="compute-information">
    <div class="tab-container" style="padding: 9px 0 16px 0">
      <div class="suanli-bean-gain suanli-bean-gain-btn">
        <div class="suanli-title">
          <span class="label"></span>
          <h6>算力豆获取</h6>
        </div>
        <div v-if="suanliCardsFlag" class="suanli-buttons">
          <a-button class="border-right pre"><LeftOutlined /></a-button>
          <a-button class="next"><RightOutlined /></a-button>
        </div>
      </div>
      <suanli-card ref="suanliCardRef" @getDetail="getDetail" @changeActiveTab="changeActiveTab"></suanli-card>
    </div>
    <div class="tab-container">
      <div class="suanli-bean-gain">
        <span class="label"></span>
        <h6>算力豆信息</h6>
      </div>
      <div class="overview flex">
        <div class="overview-box flex">
          <img src="@/assets/image/computeMgr.png" alt="" />
          <span class="title">
            <span>算力豆情况</span>
            <a-tooltip>
              <template #title>
                <p v-if="FEATURE_MODEL_TASK">单机训练及比赛打榜模块， CPU 实例每 6 分钟消耗 0.1 算力豆，vGPU 实例每 6 分钟消耗 0.5 算力豆，单卡/双卡/四卡/八卡实例每 6 分钟分别消耗 2/5/15/50 算力豆，任务建模模块，运行任务每卡每6分钟消耗2算力豆</p>
                <p v-else>单机训练及比赛打榜模块，CPU实例每6分钟消耗0.1算力豆，vGPU实例每6分钟消耗0.5算力豆，单卡/双卡/四卡/八卡实例每6分钟分别消耗2/5/15/50算力豆</p>
              </template>
              <jt-icon type="iconbangzhu" class="icon"></jt-icon>
            </a-tooltip>
          </span>
        </div>
        <div class="statistic flex">
          <div v-for="item in computeStatistic" :key="item.type" class="statistic-item">
            <div>
              <span class="count">{{ Number(summary[`${item.type}Count`]).toFixed(1) }}</span>
            </div>
            <span class="desc">{{ item.description }}</span>
          </div>
        </div>
      </div>
      <a-radio-group v-model:value="type">
        <a-radio-button class="tab-btn" value="all"> 全部 </a-radio-button>
        <a-radio-button class="tab-btn" value="consumed"> 总消耗 </a-radio-button>
        <a-radio-button class="tab-btn" value="received"> 总获取 </a-radio-button>
      </a-radio-group>
    </div>
    <div class="table-container">
      <a-table row-key="id" :loading="loading" :pagination="false" :data-source="data" :columns="columns">
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column && column.dataIndex === 'count'">
            <span>
              <span :class="text > 0 ? 'increase' : 'decrease'">{{ text > 0 ? `+${text}` : text }}</span>
            </span>
          </template>
          <template v-else>
            <span v-if="column && column.dataIndex">{{ text }}</span>
          </template>
        </template>
      </a-table>
      <jt-pagination v-if="total !== 0" class="pagination-box" :page-size="pageSize" :page-num="pageNum" :total="total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"> </jt-pagination>
    </div>
  </div>
</template>

<script>
import { GET, POST } from '@/request';
import suanliCard from './computeInformationComponents/suanliCard/index.vue';
import { getEnvConfig } from '@/config';
export default {
  name: 'ComputeInformation',
  components: {
    suanliCard,
  },
  props: {
    activeTab: [String, Number],
  },
  emits: ['changeActiveTab'],
  data() {
    return {
      computeStatistic: [
        {
          type: 'available',
          description: '当前可用算力豆',
        },
        {
          type: 'received',
          description: '总获取算力豆',
        },
        {
          type: 'consumed',
          description: '总消耗算力豆',
        },
      ],
      columns: [
        { title: '编号', dataIndex: 'id', key: 'id' },
        { title: '时间', dataIndex: 'orderTime', key: 'orderTime' },
        { title: '事项', dataIndex: 'item', key: 'item' },
        { title: '详情', dataIndex: 'details', key: 'details' },
        { title: '数量', dataIndex: 'count', key: 'count', width: 120 },
      ],
      summary: {},
      data: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      type: 'all',
      loading: false,
      maxBeanTotal: 0,
      FEATURE_MODEL_TASK: getEnvConfig('FEATURE_MODEL_TASK'),
      suanliCardsFlag: false,
    };
  },
  watch: {
    type() {
      this.pageNum = 1;
      this.getDetail();
    },
    pageNum: 'getDetail',
    pageSize() {
      this.pageNum = 1;
      this.getDetail();
    },
  },
  mounted() {
    this.getSummary();
    this.getDetail();
    this.$nextTick(() => {
      const suanliCards = this.$refs.suanliCardRef.getSuanliCards();
      const list = [];
      Object.keys(suanliCards).forEach((key) => {
        if (suanliCards[key]) list.push(key);
      });
      this.suanliCardsFlag = list.length > 3;
      console.log('suanliCards', list, this.suanliCardsFlag);
    });
  },
  methods: {
    getSummary() {
      GET('/accounting/web/computemgr/summary').then((res) => {
        this.summary = res.body;
      });
    },
    getDetail() {
      const obj = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      this.loading = true;
      this.data = [];
      POST(`/accounting/web/computemgr/detail-${this.type}`, obj).then((res) => {
        this.loading = false;
        this.data = res.body.data;
        this.total = res.body.total;
      });
    },
    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
    },
    pageNumChange(pageNum) {
      this.pageNum = pageNum;
    },
    changeActiveTab() {
      this.$emit('changeActiveTab', '1', 'ecloud');
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.flex {
  display: flex;
}
.suanli-title {
  display: flex;
  align-items: center;
}
.overview {
  width: 1136px;
  height: 96px;
  background: #fff7dd;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;
}
.overview-box {
  align-items: center;
  padding-right: 48px;
  .title {
    margin-left: 16px;

    span {
      font-weight: 600;
      color: #78420c;
      font-size: 18px;
      margin-right: 8px;
    }
    .icon {
      font-size: 16px;
    }
  }

  img {
    width: 64px;
    height: 64px;
  }
}
.statistic {
  justify-content: space-around;
  align-items: center;
  flex: 1;
  text-align: center;
  .count {
    font-size: 32px;
    font-weight: @jt-font-weight-medium;
    color: #78420c;
  }
  .desc {
    font-size: 12px;
    color: #b2671c;
  }
}
.tab-container {
  padding: 24px 0;
  justify-content: space-between;
}
.tab-btn {
  width: 160px;
  text-align: center;
}
.decrease {
  color: #17bb85;
}
.increase {
  color: #f17506;
}
.suanli-bean-gain-btn {
  display: flex;
  justify-content: space-between;
}
.suanli-bean-gain {
  display: flex;
  align-items: center;
  .label {
    width: 5px;
    height: 18px;
    background: @jt-primary-color;
    display: inline-block;
    vertical-align: text-top;
  }
  h6 {
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: @jt-text-color-primary;
    line-height: 25px;
    display: inline-block;
    margin-left: 8px;
  }
  margin-bottom: 12px;
  .suanli-buttons {
    .ant-btn > span {
      display: block;
    }
  }
}
.pagination-box {
  margin: 0;
  height: 92px;
  display: flex;
  align-items: center;
}
.pre,
.next {
  width: 48px;
  height: 24px;
  border: 1px solid #cbcfd2;
  border-radius: 2px 0 0 2px;
  &:hover {
    border: 1px solid #0082ff;
    .anticon {
      color: #0082ff;
    }
  }
}

.pre {
  // border-right: 0;
  border-radius: 2px 0px 0px 2px;
  &:hover {
    border-right: 1px solid #0082ff;
  }
}
.next {
  border-left: 0;
  border-radius: 0px 2px 2px 0px;
  &:hover {
    border-left: 1px solid #0082ff;
  }
}

.button-disabled {
  background: #f7f9fa;
  cursor: not-allowed;
  border: 1px solid #cbcfd2;
  &:hover {
    border: 1px solid #cbcfd2;
    .anticon {
      color: #dadfe8;
    }
  }
  &:hover + .next {
    border-left: 0;
  }
  .anticon {
    color: #dadfe8;
  }
}
.anticon {
  font-size: 12px;
  color: #7f828f;
}
</style>
