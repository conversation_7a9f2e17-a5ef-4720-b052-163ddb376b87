<template>
  <div class="pagination">
    <div class="left">
      <span>共{{ total }}条记录</span>
      <div class="select-wrap" v-if="showSizeOptions">
        每页显示
        <a-select class="select" @change="changePageSize" :value="pageSize" :options="pageSizeOptions.map((item) => ({ label: item, value: item }))"></a-select>
        条
      </div>
    </div>
    <div class="right">
      <a-pagination @change="changePageNum" :pageSize="pageSize" show-quick-jumper :current="pageNum" :total="total" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    total: {
      type: Number,
      default: 0,
    },
    pageNum: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageSizeOptions: {
      type: Array,
      default: () => [5, 10, 20],
    },
    showQuickJumper: {
      type: Boolean,
      default: true,
    },
    showSizeOptions: {
      type: Boolean,
      default: true,
    },
    'onUpdate:pageNum': {
      type: Function,
    },
    'onUpdate:pageSize': {
      type: Function,
    },
  },
  emits: ['change', 'update:pageNum', 'update:pageSize', 'changePageSize', 'changePageNum'],
  methods: {
    changePageSize(size) {
      this.$emit('update:pageSize', size);
      this.$emit('changePageSize', size);
    },
    changePageNum(pageNum) {
      this.$emit('update:pageNum', pageNum);
      this.$emit('changePageNum', pageNum);
    },
  },
};
</script>

<style lang="less" scoped>
.pagination {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  .left {
    display: flex;
    align-items: center;
  }
}
:deep(.ant-select-selection) {
  display: inline-flex;
  height: 28px;
  align-items: center;
  width: 100%;
}
:deep(.ant-pagination-item) {
  display: inline-flex;
  height: 28px;
  align-items: center;
  justify-content: center;
  min-width: 28px;
}
:deep(.ant-pagination-prev),
:deep(.ant-pagination-next) {
  display: inline-flex;
  height: 28px;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
}
:deep(.ant-pagination-options-quick-jumper input) {
  height: 28px;
}
.select-wrap {
  display: flex;
  align-items: center;
  margin-left: 16px;
  .select {
    width: 60px;
    margin: 0 8px;
  }
}
</style>
