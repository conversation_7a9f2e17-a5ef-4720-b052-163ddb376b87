<template>
  <div class="competition-team">
    <a-radio-group v-model:value="subtabId" class="competition-team-radios" @change="radioChange">
      <a-radio-button value="1">报名及团队设置</a-radio-button>
      <a-radio-button value="2">报名用户管理</a-radio-button>
      <a-radio-button value="3">团队管理</a-radio-button>
    </a-radio-group>

    <teamSettings v-if="subtabId === '1'" />
    <userManage v-if="subtabId === '2'" />
    <teamManage v-if="subtabId === '3'" />
  </div>
</template>

<script>
import teamSettings from './teamSettings.vue';
import userManage from './userManage.vue';
import teamManage from './teamManage.vue';

export default {
  name: 'CompetitionTeam',
  components: {
    teamSettings,
    userManage,
    teamManage,
  },
  data() {
    return {
      subtabId: this.$route.query.subtabId || '1',
    };
  },
  methods: {
    radioChange(event) {
      const subtabId = event.target.value;
      this.$router.replace({ query: { tabId: '3', subtabId } });
    },
  },
};
</script>

<style lang="less" scoped>
.competition-team {
  padding: 0px 32px 64px;
  position: relative;
  .competition-team-radios {
    margin-bottom: 32px;
  }
}
</style>
