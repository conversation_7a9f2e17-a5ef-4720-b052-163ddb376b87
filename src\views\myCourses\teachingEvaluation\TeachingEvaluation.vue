<template>
  <div class="teaching-evaluation">
    <div class="evaluation-header">
      <a-radio-group v-model="activeKey">
        <a-radio-button value="1"> 作业 </a-radio-button>
        <a-radio-button value="2" disabled> 考试 </a-radio-button>
      </a-radio-group>
      <a-button type="primary" @click="routeToAddWork">
        <PlusOutlined />
        新增作业
      </a-button>
    </div>
    <div class="evaluation-content">
      <Homework v-if="activeKey === '1'" />
    </div>
  </div>
</template>

<script lang="jsx" setup>
import { PlusOutlined } from '@ant-design/icons-vue';
</script>

<script lang="jsx">
import Homework from './Homework.vue';

export default {
  name: 'TeachingEvaluation',
  components: {
    Homework,
  },
  activeKey() {
    return this.$route.params.subTab;
  },
  methods: {
    routeToAddWork() {
      const courseId = this.$route.params.courseId;
      this.$router.push(`/course/teaching/mycourses/create-work/${courseId}`);
    },
  },
};
</script>

<style lang="less" scoped>
.teaching-evaluation {
  margin: 0px 32px;
  .evaluation-header {
    display: flex;
    justify-content: space-between;
    margin: 8px 0px 16px;
    .ant-radio-button-wrapper {
      width: 160px;
      text-align: center;
    }
    .ant-radio-button-wrapper-checked {
      background-color: #f0f8ff;
    }
  }
  .evaluation-content {
    min-height: 460px;
  }
}
</style>
