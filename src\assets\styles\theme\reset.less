/* 对antd及其它组件样式class的复写 */
@import './../var.less';

// vue-router路由激活样式覆盖
.router-link-active {
  color: @jt-primary-color;
}

.ant-divider {
  background-color: @jt-text-color-secondary;
}


/* antd button样式统一修改 */
.ant-btn-primary:focus {
  background-color: @jt-primary-color;
}
.ant-btn-primary:hover {
    background-color: #0277e7;
}
.ant-btn-background-ghost.ant-btn-primary:hover {
  background: #F0F7FF !important;
}

.ant-breadcrumb > span:last-child a {
  color: #121f2c;
}

/* form 表单extra文字字体统一设置 */
.ant-form-extra {
  font-size: @jt-font-size-sm;
  color: @jt-text-color-secondary;
}

// 弹框样式覆盖
.ant-modal-title {
  font-weight: @jt-font-weight-medium;
  color: @jt-title-color;
}
.ant-modal-close-x {
  color: @jt-text-color-secondary;
  font-size: @jt-font-size-sm;
}
// wangeditor编辑器标签颜色覆盖
.w-e-text-container {
  a {
    color: @jt-primary-color;
  }
}

// 头部font-weight统一用400
.ant-table-thead > tr > th {
  font-weight: @jt-font-weight;
}

.ant-radio-button-wrapper {
  width: 160px;
  text-align: center;
  font-size: 14px;
  color: #606972;
  &.ant-radio-button-wrapper-checked {
    background: #f0f8ff;
    border: 1px solid #0082ff;
    color: #0082ff;
  }
}
// check radio选中时边框宽度重叠
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled), .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover{
  box-shadow: none;
}

// table空态border-bottom重置
.ant-table-placeholder {
  border-bottom: none;
}

// tab切换
.ant-tabs-top-bar {
  margin: 0;
}
.ant-tabs-nav .ant-tabs-tab-active {
  // font-size: 18px;
  font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
  font-weight: 600;
  color: #0082ff;
}
.ant-tabs-tab {
  font-size: 18px;
  font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
  font-weight: 400;
  color: #121f2c;
}

.jt-breadcrumb a {
  line-height: 22px;
}

// Modal（弹窗）组件样式调整
.ant-modal-content {
  .ant-modal-body {
    padding: 12px 8px 4px 8px;
    .ant-modal-confirm-body{
      .anticon{
        margin-right: 16px;
      }
      .ant-modal-confirm-title{
        font-size: 14px;
        font-weight: 500;
      }
      .ant-modal-confirm-content{
        font-size: 12px;
        margin-inline-start: 38px;
      }
    }
  }
}

// notification（右上角弹窗）组件的样式调整
.ant-notification-notice {
  &:not(.success-notification) {
    .ant-notification-notice-content {
      .ant-notification-notice-with-icon {
        .ant-notification-notice-message {
          padding-top: 3px;
          line-height: 18px;
        }
      }
    }
  }
  .ant-notification-notice-content {
    .ant-notification-notice-with-icon {
      .ant-notification-notice-message {
        margin-bottom: 0;
        font-size: 14px;
      }
    }
  }
}

// 进度条组件（progress）样式调整
:deep(.ant-progress) {
  margin: 0;
  font-size: 12px;
}
