<template>
  <li class="list">
    <div class="list-item">
      <div class="content-left">
        <div class="img-container">
          <img :src="img" v-if="!!img" alt class="content-left-img" />
          <div v-if="releaseSta !== null" :class="releaseSta === publishStatusKeys.PUBLISHED ? 'published' : 'unpublish'" class="publish-status">{{ publishStatusMaps[releaseSta] }}</div>
        </div>
      </div>
      <slot name="center"></slot>
      <slot name="right"></slot>
    </div>
  </li>
</template>

<!-- 
    1.提供了两个slot /center/right
    2.左侧的图片需要传属性 img

    <list-item :img="xxx">
         <template v-slot:center>
           <div><h6>2020世界人工智能创新大赛 暨 中国移动创客马拉松大赛AI巡回赛-工业质量检测专题赛</h6></div>
         </template>
         <template v-slot:right>
             <div class="content-right">
                  <a-button type="link">查看课程<jt-icon type="iconright" /></a-button>
             </div>
         </template>
    </list-item>
  
 -->

<script>
import { publishStatusKeys, publishStatusMaps } from '../../views/competition/competitionConfig';
export default {
  name: 'ListItem',
  props: {
    img: {
      type: String,
      default: '',
    },
    releaseSta: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      publishStatusKeys,
      publishStatusMaps,
    };
  },
};
</script>

<style lang="less" scoped>
.list {
  list-style: none;
}
.list-item {
  display: flex;
  .content-left {
    .content-left-img {
      width: 128px;
      height: 128px;
      margin-right: 24px;
    }
    .img-container {
      position: relative;
      .publish-status {
        position: absolute;
        left: 0px;
        top: 0px;
        width: 73px;
        height: 24px;
        border-radius: 0px 0px 12px 0px;

        color: #ffffff;
        font-size: 14px;
        text-align: center;
        line-height: 24px;
      }
      .published {
        background: #ff7b00;
      }
      .unpublish {
        background: rgba(0, 0, 0, 0.6);
      }
    }
  }
}
</style>
