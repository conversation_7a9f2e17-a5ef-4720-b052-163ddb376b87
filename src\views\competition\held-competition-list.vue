<!-- 我举办的比赛列表 -->
<template>
  <div class="my-held-competitions">
    <bread-crumb :value="breadCrumbs"></bread-crumb>
    <div class="tabs-container flex">
      <a-tabs v-model:activeKey="competitionType" class="tabs inner" default-active-key="1" @change="changeCompetitionType">
        <a-tab-pane key="1" :tab="`正式赛 ${formalCount}`"></a-tab-pane>
        <!-- <a-tab-pane key="2" :tab="`练习赛 ${practiceCount}`"></a-tab-pane> -->
      </a-tabs>
      <div class="jt-box-shadow">
        <div class="tabs-button-filter flex inner">
          <jt-button-filter v-model="competitionStatus" class="filter-row" :options="filterList" @change="changeCompetitionStatus"></jt-button-filter>
          <a-space>
            <a-select allow-clear placeholder="发布状态" style="width: 144px" @change="handlPublishStateChange">
              <a-select-option :value="publishStatusKeys.PUBLISHED"> 已发布 </a-select-option>
              <a-select-option :value="publishStatusKeys.UNPUBLISH"> 未发布 </a-select-option>
            </a-select>
            <jt-search v-model="keywords" placeholder="搜索比赛" @handSearch="handleSearch"></jt-search>
            <a-button type="primary" style="width: 112px; height: 32px" @click="hanldeCreateCompetition">
              <PlusOutlined />
              创建比赛
            </a-button>
          </a-space>
        </div>
      </div>
    </div>
    <div class="main-content flex">
      <div class="content-container">
        <ul class="content-list flex common-content-container">
          <jt-common-content :loading="loading" :empty="tableData.length === 0" :empty-style="emptyStyle" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text">
            <template #empty-operation>
              <p v-if="!isFilter" class="empty-operation">请立即 <span @click="hanldeCreateCompetition">创建比赛</span></p>
            </template>
            <jt-list-item v-for="item in tableData" :key="item.cid" :img="item.imageUrl" :release-sta="item.releaseSta">
              <template #center>
                <div class="content-info">
                  <p class="content-info-header">
                    <span class="competition-name enable-click-cursor" @click="toCompetitionManage(item)">{{ item.typeName }}</span>
                    <jt-tag class="competition-tag mg-8" :type="getCompetitionState(item.flag)">{{ runningStatusMaps[item.flag] }}</jt-tag>
                    <jt-tag class="competition-tag">{{ competitionTypeMaps[item.typeId] }}</jt-tag>
                  </p>

                  <p class="competition-detail" :title="item.summary">{{ item.summary }}</p>

                  <div class="content-info-intro">
                    <span>参赛人数：</span>
                    <span class="mg-18">{{ item.number }}</span>
                    <span>时间：</span>
                    <span class="mg-18">{{ dateConvert(item.startTime) }} - {{ dateConvert(item.endTime) }}</span>
                    <span>举办方：</span>
                    <img v-for="(items, indexs) in item.leader" v-cloak :key="indexs" class="leader-img" :src="items.imageUrl" alt="" />
                  </div>
                </div>
              </template>
              <template #right>
                <div class="content-right">
                  <a-button type="link" @click="toCompetitionManage(item)">
                    比赛管理
                    <jt-icon type="iconright" />
                  </a-button>
                </div>
              </template>
            </jt-list-item>
          </jt-common-content>
          <jt-pagination v-if="tableData.length !== 0" class="pagination-box" :page-size="pagination.pageSize" :page-num="pagination.pageNum" :total="pagination.total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
        </ul>
      </div>
    </div>
  </div>
</template>
<script setup>
import { PlusOutlined } from '@ant-design/icons-vue';
</script>
<script>
import breadCrumb from '../../components/breadCrumb';
import jtSearch from '../../components/search/index.vue';
import jtListItem from '../../components/listItem/index.vue';
import JtTag from '@/components/tag';
import { dateConvert } from '@/utils/utils.js';
import { competitionApi } from '@/apis/index.js';
import { competitionTypeMaps, runningStatusMaps, runningStatusKeys, publishStatusKeys } from './competitionConfig/index';
import { checkPublishCompetitionPermission } from '@/keycloak';
import { filterTip, searchTip } from '@/common/text';
export default {
  name: 'HeldCompetitionList',
  components: { breadCrumb, jtSearch, jtListItem, JtTag },
  beforeRouteEnter(to, from, next) {
    if (checkPublishCompetitionPermission()) {
      next();
    } else {
      next('/competition');
    }
  },
  data() {
    return {
      competitionTypeMaps,
      runningStatusMaps,
      runningStatusKeys,
      publishStatusKeys,
      breadCrumbs: [{ name: '比赛', path: '/competition' }, { name: '我举办的比赛' }],
      keywords: '',
      competitionType: '1',
      competitionStatus: runningStatusKeys.ALL,
      releaseSta: publishStatusKeys.ALL, // 全部
      formalCount: 0,
      practiceCount: 0,
      loading: false,
      pagination: {
        total: 0,
        pageSize: 5,
        pageNum: 1,
      },
      filterList: [
        {
          value: '0',
          label: '全部',
        },
        {
          value: '2',
          label: '进行中',
        },
        {
          value: '1',
          label: '即将开始',
        },
        {
          value: '3',
          label: '已结束',
        },
      ],
      tableData: [],
      emptyStyle: {
        'min-height': '416px',
        height: 'calc(100vh - 753px)', // 根据头部及底部高度动态计算空态高度撑满
      },
    };
  },
  computed: {
    isFilter() {
      return this.keywords || this.competitionStatus !== runningStatusKeys.ALL || this.releaseSta !== publishStatusKeys.ALL;
    },
    emptyStatus() {
      if (this.isFilter) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关比赛',
          text: this.keywords ? searchTip : filterTip,
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '您暂未举办任何比赛',
          text: '',
        };
      }
    },
  },
  mounted() {
    this.getTableData();
    this.getCompetitionCount();
  },
  methods: {
    hanldeCreateCompetition() {
      this.$router.push({ path: '/competition/competition-management/create-competition' });
    },
    getCompetitionState(flag) {
      if (flag === runningStatusKeys.STARTING) {
        return 'tobegin';
      } else if (flag === runningStatusKeys.RUNNING) {
        return 'running';
      } else {
        return 'end';
      }
    },
    handlPublishStateChange(state) {
      this.releaseSta = state === undefined ? publishStatusKeys.ALL : state;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    changeCompetitionType(key) {
      this.competitionType = key;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    changeCompetitionStatus(val) {
      this.competitionStatus = val;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    async getTableData() {
      this.loading = true;
      const params = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        competitionName: this.keywords,
        typeId: this.competitionType,
        releaseSta: this.releaseSta,
        flag: this.competitionStatus,
      };
      const res = await competitionApi.getHeldCompetitions(params);
      if (res.state === 'OK') {
        res.body.list.forEach((item, index) => {
          res.body.list[index].leader = JSON.parse(item.leader);
        });
        this.tableData = res.body.list;
        this.pagination.total = res.body.total;
      } else {
        this.tableData = [];
        this.pagination.total = 0;
      }
      this.loading = false;
    },
    async getCompetitionCount() {
      const res = await competitionApi.getManageCompetitionCount();
      if (res.state === 'OK') {
        this.formalCount = res.body.total;
        this.practiceCount = res.body.practiceTotal;
      }
    },
    handleSearch(val) {
      this.keywords = val;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    toCompetitionManage(item) {
      this.$router.push({
        path: `/competition/competition-management/${item.cid}`,
      });
    },
    pageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    pageNumChange(pageNum) {
      this.pagination.pageNum = pageNum;
      this.getTableData();
    },
    dateConvert,
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.my-held-competitions {
  .flex {
    display: flex;
  }
  .tabs-container {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-top: 16px;
    background: @jt-color-white;

    :deep(.ant-tabs-nav-container) {
      font-size: 18px;
    }
    .tabs-button-filter {
      height: @jt-header-height;
      align-items: center;
      justify-content: space-between;
    }
    .tabs {
      width: 1200px;
    }
  }
  .filter-row {
    display: flex;
    align-items: center;
    &:last-of-type {
      margin-bottom: 0;
    }
    > p {
      width: 76px;
      line-height: 20px;
      margin-right: 13px;
    }
    :deep(.ant-radio-button-wrapper) {
      margin: 24px 0;
    }
  }
  .main-content {
    justify-content: center;
    margin-top: 20px;
    padding-bottom: 48px;
    background: @jt-main-bg-color;
    .content-container {
      width: 1200px;
    }
    .content-list {
      flex-direction: column;
      background: @jt-color-white;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
      .empty-operation {
        font-size: 14px;
        color: #606972;
        span {
          color: @jt-primary-color;
          cursor: pointer;
        }
      }
      li {
        align-items: center;
        width: 100%;
        height: 192px;
        padding: 32px;
        margin-bottom: 1px;
        background: @jt-color-white;
        box-shadow: 0px 1px 0px 0px @jt-line-color;
        position: relative;

        &:hover {
          background: #f8f9fa;
        }
        .content-right {
          width: 177px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
      }

      .content-info {
        flex: 1;
        .content-info-header {
          margin: 0px 8px 10px 0px;
          display: flex;
          .competition-name {
            font-size: 20px;
            font-weight: @jt-font-weight-medium;
            color: @jt-title-color;
            line-height: 28px;
            word-break: break-all;
          }
          .competition-tag {
            margin-top: 2px;
          }
          .mg-8 {
            margin-left: 8px;
            margin-right: 8px;
          }
        }
        .enable-click-cursor {
          cursor: pointer;
          transition: 0.3s all;
          &:hover {
            color: #0082ff;
          }
        }
        .competition-detail {
          height: 22px;
          font-size: @jt-font-size-base;
          font-weight: @jt-font-weight;
          color: @jt-text-color;
          line-height: 22px;
          margin-bottom: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 786px;
        }
        .content-info-intro {
          display: flex;
          flex-wrap: wrap;
          span {
            color: @jt-text-color-secondary;
          }
          .mg-18 {
            margin-right: 18px;
          }
          .leader-img {
            height: 23px;
            margin-right: 16px;
            object-fit: contain;
          }
        }
      }
    }
  }
  .pagination-box {
    display: flex;
    align-items: center;
    height: 92px;
    padding: 0 32px;
    box-sizing: border-box;
    z-index: 1;
  }
}
</style>
