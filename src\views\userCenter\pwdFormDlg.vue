<template>
  <a-modal v-model:open="open" dialog-class="pwd-form-dlg-container" :mask-closable="false" title="修改密码" @ok="handleOk" @cancel="$emit('cancel')">
    <div class="tab-container">
      <a-radio-group v-model="type">
        <a-radio-button value="password"> 旧密码验证 </a-radio-button>
        <a-radio-button value="phone"> 手机号验证 </a-radio-button>
      </a-radio-group>
    </div>
    <a-form v-if="type === 'password'" ref="passwordRuleForm" :colon="false" class="form-content" :model="passwordForm" :rules="passwordRules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="旧密码" name="oldPassword">
        <a-input-password v-model:value="passwordForm.oldPassword" placeholder="请输入旧密码" autocomplete="new-password" />
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="新密码" name="newPassword">
        <a-input-password v-model:value="passwordForm.newPassword" placeholder="请输入新密码" autocomplete="new-password" />
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="确认密码" name="confirmPassword">
        <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请再次输入新密码" autocomplete="new-password" />
      </a-form-item>
    </a-form>
    <a-form v-else ref="phoneRuleForm" :colon="false" class="form-content" :model="phoneForm" :rules="phoneRules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item label="手机号" name="phoneNum">
        <p>{{ hidePhone || '--' }}</p>
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="验证码" name="code">
        <a-input v-model:value="phoneForm.code" class="code-input" placeholder="请输入验证码" autocomplete="new-password" />
        <a-button class="code-button" :class="timmer > 0 ? 'code-button-timmer' : 'code-button-normal'" :disabled="timmer > 0" type="primary" ghost @click="handleSendCode">{{ timmer > 0 ? `重新获取 (${timmer})` : `${sended ? '重新获取' : '获取验证码'}` }}</a-button>
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="新密码" name="newPassword" help="8-20个字符，必须包含大、小写字母和数字">
        <a-input-password v-model:value="phoneForm.newPassword" name="pwd" placeholder="请输入新密码" autocomplete="new-password" />
      </a-form-item>
      <a-form-item label="确认密码" name="confirmPassword">
        <a-input-password v-model:value="phoneForm.confirmPassword" placeholder="请再次输入新密码" autocomplete="new-password" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
// import { defineComponent } from '@vue/composition-api';
import { phoneHider, userSendCode } from '@/utils/utils';
import { passwordRegex } from '@/utils/regex';
// const zxcvbn = require('zxcvbn');
export default {
  props: { phoneNum: { type: String, default: '' }, visible: { type: Boolean, default: false } },
  emits: ['cancel', 'ok'],
  data() {
    return {
      open: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      type: 'phone',
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
      },
      phoneForm: {
        // phoneNum: this.phoneNum,
        newPassword: '',
        confirmPassword: '',
        code: '',
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入', trigger: ['change', 'blur'] }],
        newPassword: [
          { required: true, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          { pattern: passwordRegex, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          {
            validator: (rule, value, callback) => {
              if (value) {
                // if (zxcvbn(value).score >= 2) {
                //   callback();
                // } else {
                callback('密码过于简单或存在安全风险，请修改');
                // }
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        confirmPassword: [{ required: true, validator: this.passwordPwdCheck, message: '两次密码输入不一致', trigger: ['blur', 'change'] }],
      },
      phoneRules: {
        code: [{ required: true, message: '请输入验证码' }],
        newPassword: [
          { required: true, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          { pattern: passwordRegex, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          {
            validator: (rule, value, callback) => {
              if (value) {
                // if (zxcvbn(value).score >= 2) {
                //   callback();
                // } else {
                callback('密码过于简单或存在安全风险，请修改');
                // }
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        confirmPassword: [{ required: true, validator: this.phonePwdCheck, message: '两次密码输入不一致', trigger: ['blur', 'change'] }],
      },
      timmer: 0,
      sending: false,
      sended: false,
    };
  },
  computed: {
    hidePhone() {
      return phoneHider(this.phoneNum);
    },
  },
  watch: {
    visible(val) {
      this.open = val;
      if (val) {
        this.$refs[`passwordRuleForm`] && this.$refs[`passwordRuleForm`].resetFields();
        this.$refs[`phoneRuleForm`] && this.$refs[`phoneRuleForm`].resetFields();
        this.timmer = 0;
        this.sending = false;
        this.sended = false;
      }
    },
    type() {
      this.$refs[`passwordRuleForm`] && this.$refs[`passwordRuleForm`].resetFields();
      this.$refs[`phoneRuleForm`] && this.$refs[`phoneRuleForm`].resetFields();
    },
    'passwordForm.newPassword'() {
      this.$refs[`passwordRuleForm`].validateFields(['confirmPassword']);
    },
    'phoneForm.newPassword'() {
      this.$refs[`phoneRuleForm`].validateFields(['confirmPassword']);
    },
  },
  created() {
    this.open = this.visible;
  },
  methods: {
    // zxcvbn,
    passwordPwdCheck(rule, value, callback) {
      if (value === this.passwordForm.newPassword) {
        callback();
      } else {
        callback(new Error('新密码和旧密码不一致'));
      }
    },
    phonePwdCheck(rule, value, callback) {
      if (value === this.phoneForm.newPassword) {
        callback();
      } else {
        callback(new Error('新密码和旧密码不一致'));
      }
    },
    handleOk() {
      this.$refs[`${this.type}RuleForm`]
        .validate()
        .then(() => {
          const form = this[`${this.type}Form`];
          this.$emit('ok', { ...form, passwordType: this.type });
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
    handleSendCode() {
      if (!this.phoneNum) {
        this.$notification.error({
          message: '提示',
          description: '请绑定手机号后再试',
        });
        return;
      }
      userSendCode().then((res) => {
        if (!res.errorCode) {
          this.sended = true;
          this.timmer = 60;
          this.timmerDecrease();
          this.$message.success('发送成功');
        } else {
          this.$message.error(res.errorMessage || '发送失败');
        }
      });
    },
    timmerDecrease() {
      if (this.timmer > 0) {
        this.timmer--;
        setTimeout(() => {
          this.timmerDecrease();
        }, 1000);
      }
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.pwd-form-dlg-container) {
  width: 536px !important;
}
:deep(.ant-modal-header) {
  padding: 0 0 0 20px;
  .ant-modal-title {
    height: 49px;
    line-height: 49px;
  }
}
:deep(.ant-modal-body) {
  padding: 20px 0;
}
:deep(.ant-form-explain) {
  font-size: 12px;
}
:deep(.ant-modal-footer) {
  height: 64px;
  padding: 16px 31px;
}
.form-content {
  padding: 0 66px;
}
.tab-container {
  display: flex;
  margin-bottom: 24px;
  justify-content: center;
  :deep(.ant-radio-button-wrapper) {
    width: 120px;
  }
}
.code-input {
  width: 208px;
  margin-right: 7px;
}
.code-button-timmer {
  background: #cbcfd2 !important;
  color: #ffffff !important;
  &:hover {
    background: #cbcfd2 !important;
  }
}

.code-button-normal {
  background: #e5f3ff !important;
  color: #0082ff;
  &:hover {
    background: #dbeeff !important;
  }
}
.code-button {
  font-size: 12px;
  width: 104px;
  height: 32px;
  border: 0;
}
</style>
