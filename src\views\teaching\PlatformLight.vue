<template>
  <section class="highlight-section jt-box-shadow">
    <div class="platform-highlight">
      <div class="highlight-intro">
        <div v-for="item in highLights" :key="item.header" class="hightlight-item">
          <img class="intro-img" :src="item.src" alt="" />
          <div>
            <h3>{{ item.header }}</h3>
            <p>{{ item.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
const processManageImg = require('@/assets/image/teaching/process-manage.png');
const interactiveTrainImg = require('@/assets/image/teaching/interactive-train.png');
const computeResourceImg = require('@/assets/image/teaching/compute-resource.png');
export default {
  name: 'PlatformLight',
  data() {
    return {
      highLights: [
        {
          header: '全流程教学管理',
          description: '全面满足高校AI开发需求',
          src: processManageImg,
        },
        {
          header: '交互式实训环境',
          description: '显著提升教学质量和效率',
          src: interactiveTrainImg,
        },
        {
          header: '充沛算力资源',
          description: '解决GPU算力后顾之忧',
          src: computeResourceImg,
        },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.highlight-section {
  height: 168px;
  background: #ffffff;
}
.platform-highlight {
  height: 100%;
  width: 1200px;
  margin: 0px auto;
  .highlight-intro {
    display: flex;
    justify-content: space-between;
    height: 100%;
    .hightlight-item {
      display: flex;
      align-items: center;
      h3 {
        font-size: 20px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
        line-height: 28px;
        margin-bottom: 8px;
      }
      p {
        font-size: 14px;
        color: #606972;
      }
    }
    .intro-img {
      height: 72px;
      width: 72px;
      margin-right: 16px;
    }
  }
}
</style>
