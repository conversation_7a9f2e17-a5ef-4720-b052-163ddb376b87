<script setup>
import { PlusOutlined } from '@ant-design/icons-vue';
</script>
<!-- 批量上传的弹框 -->
<template>
  <modal :title="title" class="competition-file-modal" :confirm-loading="!submitBtnEnable" :visible="visible" @cancel="handleCancel" @ok="handleOk">
    <a-radio-group v-model:value="radioType" style="width: 100%" @change="onChange">
      <a-radio v-if="fileSubmitType == '1' || fileSubmitType == '2'" :style="radioStyle" :value="'1'"> 本地上传 </a-radio>
      <div v-if="fileSubmitType == '1' || fileSubmitType == '2'" class="local-file-container" :class="{ 'hidden-local-container': radioType === UPLOAD_TYPES.STORAGE_PATH }">
        <input ref="upload" hidden type="file" multiple="multiple" @change="selectFile($event)" />
        <div class="upload-operations">
          <p v-if="selected" :title="fileInfo.fileName" class="file-name" :class="fileInfo.status === 'exception' && 'file-exception'"><jt-icon type="iconlianjie" style="margin-right: 8px; font-size: 16px" />{{ fileInfo.fileName }}</p>
          <p v-if="!selected && radioType === UPLOAD_TYPES.LOCAL_FILE" @click="handleSelectFile"><PlusOutlined /><span>选择文件</span></p>
          <!-- 写一个空元素，防止切换时radio跳动 -->
          <p v-else style="height: 21px"></p>
          <a-space v-if="selected" :size="24">
            <a v-if="fileInfo.status === 'exception'" @click="goonUpload">继续上传</a>
            <a @click="resetFile">重新选择</a>
            <a @click="handleDelete">删除</a>
          </a-space>
        </div>
        <a-progress v-if="selected && fileInfo.status !== 'success'" style="padding-left: 20px" :class="{ 'progress-hidden': !selected || fileInfo.status === 'success' }" :percent="fileInfo.percent" :status="fileInfo.status" :stroke-width="2" :show-info="false" />
      </div>
      <a-radio v-if="fileSubmitType == '1' || fileSubmitType == '3'" :style="radioStyle" :value="'2'"> {{ isMobileCloud ? '团队共享存储路径' : '比赛实例团队共享存储路径' }} </a-radio>
      <div v-if="fileSubmitType == '1' || fileSubmitType == '3'" :class="{ 'instance-container-hidden': radioType !== UPLOAD_TYPES.STORAGE_PATH }" class="instance-path-container">
        <a-tree-select v-model:value="treeSelectValue" tree-data-simple-mode style="width: 100%" :dropdown-style="{ maxHeight: '400px', overflow: 'auto', width: '446px' }" :tree-data="treeData" placeholder="请选择" :load-data="onLoadData" @select="treeItemSelect" />
      </div>
    </a-radio-group>
  </modal>
</template>

<script>
import Modal from '@/components/modal/index.vue';
import API from '@/constants/api/API.js';
import { axiosWithNoToken } from '@/request';
import { getFilePsw } from '@/utils/file.js';

// 可以上传的文件类型
const FILE_TYPES = ['csv', 'txt', 'npz', 'npy', 'rar', 'zip', 'tar', 'gz', '7z', 'json', 'h5', 'tfrecord', 'pb', 'pbtxt', 'pkl', 'pth', 'jar', 'bin', 'jpg', 'png', 'jpeg', 'bmp', 'gif', 'psd', 'html', 'rmvb', 'mp3', 'mp4', 'mp5', 'm4a', 'aac', 'flac', 'wav', 'wma', 'mov', 'doc', 'docx', 'pdf', 'xls', 'xlsx', 'py', 'md', 'ipynb', 'log', 'yaml', 'sh', 'md', 'ppt', 'pptx', 'mat'];

const UPLOAD_STATUS = {
  MERGE_FINISH: 0, // 0.文件合并完毕
  PART_UPLOAD: 1, // 1.分片部分上传，续传状态
  SECOND_UPLOAD: 2, // 2.分片需要秒传状态
  PART_FINISH: 3, // 3.分片已传完成，可直接合并
  MERGE_FAIL: -1, // -1：文件合并失败
};
const RETRIES = 3; // 上传分片最大重试次数
let statusTimeoutId = null;

export default {
  name: 'UploadModal',
  components: {
    Modal,
  },
  props: {
    visible: {
      // 弹框关闭的时候需要清理上传相关的逻辑
      type: Boolean,
      default: false,
    },
    filetype: {
      // 1 结果 0 答辩
      type: String,
      default: '1',
    },
    title: {
      type: String,
    },
    fileSubmitType: {
      // 1是本地跟团队共享，2是本地，3是团队共享
      type: String,
      default: '1',
    },
    isMobileCloud: {
      //是否为移动云的比赛
      type: Boolean,
      default: false,
    },
  },
  emits: ['cancel', 'ok'],
  data() {
    return {
      cid: this.$route.query.id,
      selected: false,
      chunkSize: 10 * 1024 * 1024, // 10M一个分块
      fileId: undefined,
      treeSelectValue: undefined,
      treeData: [],
      selectTreeNode: {}, // 选中的treenode节点
      radioType: '1', // 1.本地上传 2.比赛实例存储路径
      UPLOAD_TYPES: {
        LOCAL_FILE: '1',
        STORAGE_PATH: '2',
      },
      FILE_TYPES: {
        1: '结果文件',
        0: '答辩文件',
      },
      radioStyle: {
        display: 'block',
        height: '30px',
        lineHeight: '30px',
      },
      fileInfo: {
        percent: 0,
        status: 'normal', // active, success, exception
        reties: 0,
      },
      submitting: false,
      autoScoringSwitch: false,
    };
  },
  computed: {
    submitBtnEnable() {
      return (this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.fileInfo.status === 'success' && !this.submitting) || (this.radioType === this.UPLOAD_TYPES.STORAGE_PATH && Object.keys(this.selectTreeNode).length > 0 && !this.submitting);
    },
  },
  watch: {
    visible(val) {
      if (!val) {
        this.handleDelete();
        if (statusTimeoutId) {
          clearTimeout(statusTimeoutId);
          statusTimeoutId = null;
        }
        this.selectTreeNode = {};
        this.treeData = [];
        this.treeSelectValue = undefined;
        this.radioType = this.fileSubmitType == '3' ? '2' : '1';
      } else {
        if (this.treeData.length === 0) {
          this.initTreeData();
        }
      }
    },
    fileSubmitType() {
      this.radioType = this.fileSubmitType == '3' ? '2' : '1';
    },
  },
  mounted() {
    this.getAutoScoringSwitch();
  },
  methods: {
    onChange(e) {
      this.radioType = e.target.value;
    },
    onLoadData(treeNode) {
      return new Promise((resolve) => {
        const { id } = treeNode.dataRef;
        this.initTreeData(id, resolve);
      });
    },
    initTreeData(path = 'teams', resolve) {
      const cid = this.$route.query.id;
      const firstlist = path === 'teams' ? '1' : '0';
      API.competition_model
        .getCompetitionFileList({
          cid,
          firstlist,
          path,
        })
        .then((res) => {
          if (res.state === 'OK') {
            this.treeData = this.treeData.concat(this.formatData(path, res.body));
          }
          resolve && resolve();
        });
    },
    treeItemSelect(value, node) {
      this.selectTreeNode = node.dataRef;
    },
    formatData(pId, dataList) {
      return dataList.map((item) => {
        return {
          id: `${item.parent}/${item.path}`,
          pId,
          parent: item.parent,
          path: item.path,
          title: item.type === 'dir' ? item.path : item.name,
          value: item.path,
          isLeaf: item.type !== 'dir',
          type: item.type,
        };
      });
    },
    async selectFile(e) {
      if (e.target.files.length === 0) {
        return;
      }
      const file = e.target.files[0];
      if (file.size === 0) {
        this.$message.error('上传失败，文件为空');
        this.$refs.upload.value = '';
        return;
      }
      if (!this.validFileType(file.name)) {
        this.$message.error('不支持该格式，请重新选择');
        this.$refs.upload.value = '';
        return;
      }
      if (!this.validFileSize(file.size)) {
        this.$message.error('文件大小需在1GB以内');
        this.$refs.upload.value = '';
        return;
      }
      this.selected = true;
      this.fileInfo = {
        file,
        fileSize: file.size,
        fileName: file.name,
        contentType: file.type,
        chunkCount: Math.ceil(file.size / this.chunkSize), //计算当前选择文件需要的分片数量
        fileMd5: file ? await getFilePsw(file) : '', //计算当前文件前10mb和后10mb的md5拼接起来，防止重复    修改时间 文件长度 文件内容md5（头尾）
        percent: 0, // 文件上传进度
        chunkUrls: [],
        uploadedChunkCount: 0,
        status: 'active',
      };
      this.initUpload();
    },
    validFileType(name) {
      if (name.indexOf('.') === -1) {
        return false;
      }
      const suffix = name.substring(name.lastIndexOf('.') + 1);
      return FILE_TYPES.includes(suffix.toLocaleLowerCase()) || name.slice(-7) === '.tar.gz';
    },
    validFileSize(size) {
      if (size > 1024 * 1024 * 1024) {
        // 最多1G
        return false;
      } else {
        return true;
      }
    },
    // 初始化上传或续传
    initUpload() {
      this.fileInfo.status = 'active';
      const initParams = {
        chunkCount: this.fileInfo.chunkCount,
        contentType: this.fileInfo.contentType,
        fileMd5: this.fileInfo.fileMd5,
        fileName: this.fileInfo.fileName,
        fileSize: this.fileInfo.fileSize,
      };
      API.competition_model
        .getCompetitionMultipartinit(this.cid, initParams)
        .then((res) => {
          if (!this.visible || !this.selected) return;
          if (res.state === 'OK') {
            const { fileId, uploadStatus, uploadUrls } = res.body;
            this.fileId = fileId;
            switch (uploadStatus) {
              case UPLOAD_STATUS.MERGE_FINISH:
              case UPLOAD_STATUS.SECOND_UPLOAD:
                this.fileInfo.percent = 100;
                this.queryMergeResult();
                break;
              case UPLOAD_STATUS.PART_UPLOAD:
                this.fileInfo.chunkUrls = uploadUrls;
                this.fileInfo.uploadedChunkCount = this.fileInfo.chunkCount - this.fileInfo.chunkUrls.length;
                this.fileInfo.percent = (this.fileInfo.uploadedChunkCount / this.fileInfo.chunkCount) * 100;
                this.uploadChunks();
                break;
              case UPLOAD_STATUS.PART_FINISH:
                this.fileInfo.percent = 100;
                this.mergeFile();
                break;
              case UPLOAD_STATUS.MERGE_FAIL:
                this.fileInfo.percent = 0;
                this.fileInfo.status = 'exception';
                this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
                break;
              default:
                console.warn('未知状态');
                this.fileInfo.percent = 0;
                this.fileInfo.status = 'exception';
                this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
            }
          } else {
            this.fileInfo.percent = 0;
            this.fileInfo.status = 'exception';
            this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
          }
        })
        .catch(() => {
          this.fileInfo.percent = 0;
          this.fileInfo.status = 'exception';
          this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
        });
    },
    // 上传分片
    uploadChunks(index = 0) {
      const chunkUrl = this.fileInfo.chunkUrls[index];
      const start = (chunkUrl.pageNumber - 1) * this.chunkSize;
      const end = Math.min(this.fileInfo.fileSize, start + this.chunkSize);
      const chunkFile = this.fileInfo.file.slice(start, end);

      axiosWithNoToken
        .put(chunkUrl.uploadUrl, chunkFile)
        .then((res) => {
          if (!this.visible || !this.selected) return;
          if (res.status == 200) {
            this.fileInfo.reties = 0;
            this.fileInfo.uploadedChunkCount++;
            const { uploadedChunkCount, chunkCount } = this.fileInfo;
            this.fileInfo.percent = (uploadedChunkCount / chunkCount) * 100;
            if (uploadedChunkCount === chunkCount) {
              this.mergeFile();
            } else {
              this.uploadChunks(index + 1);
            }
          } else {
            this.fileInfo.status = 'exception';
            this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
          }
        })
        .catch(() => {
          if (this.fileInfo.reties < RETRIES) {
            this.fileInfo.reties++;
            this.uploadChunks(index);
          } else {
            this.fileInfo.status = 'exception';
            this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
          }
        });
    },
    // 文件合并
    async mergeFile() {
      const mergeParams = {
        fileId: this.fileId,
      };
      const res = await API.competition_model.getCompetitionComposeFile(this.cid, mergeParams);
      if (!this.visible || !this.selected) return;
      if (res.state === 'OK') {
        this.queryMergeResult();
      } else {
        this.fileInfo.status = 'exception';
        this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
      }
    },
    // 轮询查询合并结果
    async queryMergeResult() {
      const queryParams = {
        fileId: this.fileId,
      };
      const res = await API.competition_model.getCompetitionFileInfo(this.cid, queryParams);
      if (!this.visible || !this.selected) return;
      if (res.state === 'OK') {
        const { uploadStatus } = res.body;
        if (uploadStatus === UPLOAD_STATUS.MERGE_FINISH) {
          this.fileInfo.status = 'success';
          this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.success('上传成功');
        } else if (uploadStatus === UPLOAD_STATUS.MERGE_FAIL) {
          this.fileInfo.status = 'exception';
          this.radioType === this.UPLOAD_TYPES.LOCAL_FILE && this.$message.error('上传失败');
        } else {
          statusTimeoutId = setTimeout(() => {
            this.queryMergeResult();
          }, 1000);
        }
      }
    },
    // 进行附件上传提交
    async handleResultSubmit() {
      this.submitting = true;
      let params = {
        cid: this.cid,
        filetype: this.filetype,
        submittype: this.radioType,
      };
      if (this.radioType === this.UPLOAD_TYPES.LOCAL_FILE) {
        params.fileMd5 = this.fileInfo.fileMd5;
        params.fileName = this.fileInfo.fileName;
      } else {
        params.sourcepath = `${this.selectTreeNode.parent}/${this.selectTreeNode.path}`;
        if (this.selectTreeNode.type !== 'dir') {
          params.fileName = this.selectTreeNode.title;
        }
      }

      const res = await API.competition_model.getCompetitionFileSubmit(params);
      if (res.state === 'OK') {
        if (this.filetype == '1') {
          this.$message.success(`结果文件${this.autoScoringSwitch ? '评分' : '提交'}中`);
        } else {
          this.$message.success('审查及答辩材料提交中');
        }
        this.$emit('ok');
      } else {
        this.$message.error(res.errorMessage || '提交失败');
      }
      this.submitting = false;
    },
    resetState() {
      this.selected = false;
      this.fileId = undefined;
      this.fileInfo = {
        percent: 0,
        status: 'normal', // active, success, exception
        reties: 0,
      };
    },
    // 点击打开文件选择
    handleSelectFile() {
      this.$refs?.upload.click();
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      this.handleResultSubmit();
    },
    // 继续上传
    goonUpload() {
      this.initUpload();
    },
    // 重新选择
    resetFile() {
      if (this.$refs.upload) {
        this.$refs.upload.value = '';
        this.$refs.upload.click();
      }
    },
    handleDelete() {
      this.resetState();
      if (this.$refs.upload) {
        this.$refs.upload.value = ''; // 重复选择一个文件不触发change事件
      }
    },
    getAutoScoringSwitch() {
      API.competition_model.getAutoScoringSwitch({ cid: this.cid }).then((res) => {
        if (res.state === 'OK') {
          this.autoScoringSwitch = res.body;
        }
      });
    },
  },
};
</script>

<style lang="less">
@import '~@/assets/styles/index.less';
.competition-file-modal {
  .local-file-container {
    padding-left: 26px;
    // margin-top: 12px;
    // margin-bottom: 24px;
    height: 54px;
    padding-top: 8px;
    &.hidden-local-container {
      visibility: hidden;
      height: 16px;
    }
    &.progress-hidden {
      visibility: hidden;
    }
    .upload-operations {
      font-size: @jt-font-size-base;
      color: @jt-primary-color;
      display: flex;
      justify-content: space-between;

      p {
        max-width: 200px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        span {
          margin-left: @jt-gap-base;
        }
        &.file-name {
          color: @jt-text-color;
        }
        &.file-exception.file-name {
          color: @jt-error-color;
        }
      }
    }
  }

  .instance-path-container {
    padding-left: 26px;
    // margin-top: 12px;
    // margin-bottom: 60px;
    padding-top: 8px;
    height: 84px;
    &.instance-container-hidden {
      visibility: hidden;
      height: 46px;
    }
  }
}
</style>
