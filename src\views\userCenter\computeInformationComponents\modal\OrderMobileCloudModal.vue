<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <confirm-modal :visible="orderEcloudComputingVisible" title="绑定手机号" ok-text="前往绑定" cancel-text="再想想" @ok="orderEcloudHandleOk('ok')">
      <template #icon>
        <ExclamationCircleFilled class="invited-icon" style="color: #0082ff" />
      </template>
      <div style="font-size: 14px">您尚未绑定手机号，</div>
      <div style="font-size: 14px; margin-bottom: 18px">请先在个人中心绑定手机号后再参加活动</div>
    </confirm-modal>
    <!-- 活动开放、毕昇绑定手机号此前已订购深度学习平台 -->
    <!-- 已参加过活动，无论活动是否开放 -->
    <div ref="mymodal" class="unable-join-dlg-container">
      <a-modal v-model:open="unableJoinOpen" :footer="null" :title="null" width="530px" :get-container="() => $refs.mymodal" @cancel="closeUnableJoinVisible('unableJoin')">
        <div class="unable-join-content">
          <p v-if="suanLiState.joinSta === ORDER_ECLOUD_JOIN_STATE.ORDER_OPEN" class="title">抱歉，您已订购<span class="title-deep">移动云深度学习平台</span>，无法参加本活动</p>
          <p v-else class="title">
            您已参加过本活动，<span class="num">{{ suanLiState.beannum }}</span
            >个算力豆已发放至您的账户
          </p>
          <div class="unable-join-text">
            <p>活动须知：</p>
            <p>1、针对未订购移动云深度学习平台的用户，按照引导完成移动云深度学习平台订购，即可赢取{{ suanLiState.beannum }}个算力豆，算力豆有效期为{{ suanLiState.daynum }}天</p>
            <p>2、每位用户仅可参加一次本活动，不可重复参加，不可重复赢取算力豆</p>
            <p>3、本活动最终解释权归九天·毕昇所有</p>
          </div>
          <a-button class="btn" type="primary" ghost @click="ToDeepLearning">前往移动云深度学习平台</a-button>
          <img class="unable-join-bg" src="@/assets/image/compute/unable-join.png" alt="" />
        </div>
      </a-modal>
    </div>
    <div ref="mymodal" class="not-open-dlg-container">
      <a-modal v-model:open="notOpenOpen" :footer="null" :title="null" width="530px" :get-container="() => $refs.mymodal" @cancel="closeUnableJoinVisible('notOpen')">
        <div class="unable-join-content">
          <p class="title" style="text-align: center">抱歉，订购移动云深度学习平台赢算力活动暂未开放</p>
          <div class="not-open-bg">
            <img src="@/assets/image/compute/not-open.png" alt="" />
          </div>
          <a-button class="btn" type="primary" ghost @click="ToDeepLearning">前往移动云深度学习平台</a-button>
        </div>
      </a-modal>
    </div>
  </div>
</template>
<script>
import API from '@/constants/api/API.js';
import { mapState } from 'vuex';
import confirmModal from '@/components/confirmModal/index.vue';
import { openInNewTab, addUrlParams } from '@/utils/utils';
import { ECLOUD_URL_CONFIG, ECLOUD_URL, ORDER_ECLOUD_JOIN_STATE } from '@/common/ecloud';

export default {
  components: { confirmModal },
  props: {
    orderEcloudComputingVisible: {
      type: Boolean,
      default: false,
    },
    unableJoinVisible: {
      type: Boolean,
      default: false,
    },
    notOpenVisible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['closeOrderVisible', 'closeUnableJoinVisible'],
  data() {
    return {
      ORDER_ECLOUD_JOIN_STATE,
      unableJoinOpen: false,
      notOpenOpen: false,
    };
  },
  computed: {
    ...mapState(['suanLiState']),
  },
  watch: {
    unableJoinVisible(val) {
      this.unableJoinOpen = val;
    },
    notOpenVisible(val) {
      this.notOpenOpen = val;
    },
  },
  created() {
    this.unableJoinOpen = this.unableJoinVisible;
    this.notOpenOpen = this.notOpenVisible;
  },
  methods: {
    ToDeepLearning() {
      let destUrl = '';
      if (this.suanLiState.joinSta === ORDER_ECLOUD_JOIN_STATE.ORDER_OPEN || this.suanLiState.joinSta === ORDER_ECLOUD_JOIN_STATE.JOINED_IS_OPEN) {
        destUrl = window.escape(`${ECLOUD_URL}${ECLOUD_URL_CONFIG.CONSOLE_DEEP_LEARNING_URL}`);
      } else {
        destUrl = window.escape(`${ECLOUD_URL}/home/<USER>/jiutiandl`);
      }
      API.competition_model.ecloudSSOCheck().then((res) => {
        const url = `${ECLOUD_URL}${ECLOUD_URL_CONFIG.ECLOUD_SSO_CHECK_URL}`;
        const urlParams = {
          token: res.body,
          destUrl: destUrl,
          systemSource: 'BiSheng',
        };
        const deepLearnUrl = addUrlParams(url, urlParams);
        openInNewTab(deepLearnUrl);
      });
    },
    orderEcloudHandleOk(text) {
      this.$emit('closeOrderVisible', text);
    },
    closeUnableJoinVisible(text) {
      this.$emit('closeUnableJoinVisible', text);
    },
  },
};
</script>
<style lang="less">
@import '~@/assets/styles/var.less';

.unable-join-dlg-container {
  :deep(.ant-modal-body) {
    padding: 48px 40px 0px 40px;
  }
  .btn {
    margin-top: 8px;
    margin-left: auto;
    margin-right: auto;
  }
}
.unable-join-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  .title {
    font-size: @jt-font-size-lg;
    font-weight: @jt-font-weight-medium;
    color: @jt-text-color-primary;
    line-height: 22px;
    margin: 0px 0 16px;
  }
  .title-deep {
    color: @jt-primary-color;
  }
  .unable-join-text {
    font-size: @jt-font-size-sm;
    font-weight: @jt-font-weight;
    color: @jt-text-color;
    line-height: 18px;
    p {
      margin-bottom: 12px;
    }
  }
  .unable-join-bg {
    height: 130px;
    width: 530px;
    margin-left: -40px;
    margin-top: -68px;
  }
}
.not-open-dlg-container {
  :deep(.ant-modal-body) {
    height: 326px;
    padding: 48px 40px 40px 40px;
  }

  .btn {
    margin-top: 8px;
    margin-left: auto;
    margin-right: auto;
  }
  .not-open-bg {
    width: 320px;
    height: 168px;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
