<template>
  <div class="competition-info-edit">
    <div class="edit-header">{{ isCreateCompetition ? '创建比赛' : '编辑比赛基本信息' }}</div>
    <div class="edit-content">
      <a-steps :current="current" class="step-graph">
        <a-step title="基本信息" />
        <a-step title="比赛封面及宣传图" />
        <a-step title="预览" />
        <a-step title="完成" />
      </a-steps>
      <infoEditForm v-show="current === 0" ref="infoEditFormRef" :is-create-competition="isCreateCompetition" @changeCurrent="changeCurrent" />
      <coverUpload v-show="current === 1" ref="coverUploadRef" :is-create-competition="isCreateCompetition" @changeCurrent="changeCurrent" />
      <infoPreview v-show="current === 2" :preview-data="previewData" :is-create-competition="isCreateCompetition" @changeCurrent="changeCurrent" />
      <editFinish v-show="current === 3" :is-create-competition="isCreateCompetition" @changeCurrent="changeCurrent" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRoute } from 'vue-router';
import infoEditForm from './infoEditForm.vue';
import coverUpload from './coverUpload.vue';
import infoPreview from './infoPreview.vue';
import editFinish from './editFinish.vue';

const route = useRoute();

const current = ref(0);
const previewData = ref({});
const infoEditFormRef = ref();
const coverUploadRef = ref();

const isCreateCompetition = ref(route.params.competitionId === undefined);

function changeCurrent(val) {
  const next = current.value + val;
  // 进入预览页时需要对预览的数据进行刷新
  if (next === 2) {
    const coverFormData = coverUploadRef.value?.getFormData();
    const banner = coverFormData.coverImageUrl[0] ? coverFormData.coverImageUrl[0].url : '';
    const imageUrl = coverFormData.advertiseImageUrl[0] ? coverFormData.advertiseImageUrl[0].url : '';
    const leader = coverFormData.logoImages.map((item) => item.url);
    previewData.value = { ...infoEditFormRef.value.getFormData(), banner, imageUrl, leader };
    console.log(previewData.value);
  }
  current.value = next;
}
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.competition-info-edit {
  .edit-header {
    .competition-edit-header();
  }
  .edit-content {
    padding: 40px 104px 64px;
    .step-graph {
      padding-bottom: 48px;
      :deep(.ant-steps-item-wait .ant-steps-item-icon) {
        background-color: #d7dfe6;
        border-color: #d7dfe6;
        .ant-steps-icon {
          color: white;
        }
      }
    }
  }
}
</style>
