<template>
  <div class="temp-container">
    <VueDragResize @resizing="resize" :w="initialWidth" :minw="minWidth" :parentW="maxWidth" :parentLimitation="true" :isActive="true" :isDraggable="false" :sticks="['mr']" class="drageble-item" :preventActiveBehavior="true">
      <div class="left">
        <pdf-viewer :style="{ 'min-height': adaptiveHeight + 'px' }" v-bind="$attrs" class="middle-size"></pdf-viewer>
      </div>
    </VueDragResize>
    <div class="right">
      <jupyter-viewer v-bind="$attrs"></jupyter-viewer>
    </div>
  </div>
</template>

<script>
import pdfViewer from '../pdfViewer';
import jupyterViewer from '../jupyterViewer';
import dragResizeMixin from './drag-resize-mixin';
import VueDragResize from 'vue-drag-resize';

export default {
  mixins: [dragResizeMixin],
  components: {
    pdfViewer,
    jupyterViewer,
    VueDragResize,
  },
};
</script>

<style lang="less" scoped>
@import './common.less';
@import './drag-resize.less';
</style>
