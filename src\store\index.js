import { createStore } from 'vuex';
import state from './public/state';
import mutations from './public/mutations';
import actions from './public/actions';
import getters from './public/getters';
import course from './modules/course';
import h5 from './modules/h5';
import systemNotice from './modules/system-notice';
import suanLiBean from './modules/suanLiBean';
import competition from './modules/competition';

// Vue.use(Vuex);

export default createStore({
  state,
  mutations,
  getters,
  actions,
  modules: { course, h5, competition, systemNotice, suanLiBean },
});
