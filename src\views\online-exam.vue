<template>
  <div>
    <iframe id="frame" allow="microphone;camera;midi;encrypted-media;fullscreen;display-capture;" :style="{ width: '100%', height: '99vh' }" :src="$store.state.course.examUrl" frameborder="0" title=""></iframe>
  </div>
</template>

<script>
import { POST } from '@/request';
// import { defineComponent } from '@vue/composition-api';

export default {
  mounted() {
    POST('/examination/queryAddressInfo', { isLogin: '1' }, { useError: false });
  },
  data() {
    return {};
  },
};
</script>
