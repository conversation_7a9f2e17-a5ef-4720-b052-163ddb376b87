apiVersion: apps/v1
kind: Deployment
metadata:
  name: jt-education-frontend-portal
  namespace: ${JT_MGM_NAMESPACE}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jt-education-frontend-portal
  template:
    metadata:
      labels:
        app: jt-education-frontend-portal
    spec:
    spec:
      # affinity:
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #       - matchExpressions:
      #         - key: node
      #           operator: In
      #           values:
      #           - cpu
      terminationGracePeriodSeconds: 10
      containers:
        - name: jt-education-frontend-portal
          image: ${HARBOR_BASE_URL}/${JT_SVC_IMAGE_PREFIX}/jt-education-frontend-portal:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              protocol: TCP
          securityContext:
            runAsUser: ${CONTAINER_USER_ID}
          env:
          - name: NACOS_ADDRESS
            value: nacos-headless:8848
          - name: SERVER_PORT
            value: "8080"
          - name: KEY<PERSON><PERSON><PERSON>_URL
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: KEYCLOAK_URL
          - name: ECLOUD_URL
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: ECLOUD_URL
          - name: CONSOLE_ECLOUD_URL
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: CONSOLE_ECLOUD_URL
          - name: SHOW_PREVIEW_LOGO
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: SHOW_PREVIEW_LOGO
          # 是否显示站内信
          # 1 显示
          # 0 不显示
          - name: FEATURE_MESSAGECENTER
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: FEATURE_MESSAGECENTER
          # 消息中心的url后缀
          - name: MESSAGE_URL_PATH
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: MESSAGE_URL_PATH
                # 是否显示任务建模
                    # 消息中心的url后缀
          - name: HELPCENTER_URL_PATH
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: HELPCENTER_URL_PATH
          - name: FEATURE_MODEL_TASK
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: FEATURE_MODEL_TASK
          # 问题反馈是否可见
          # 1可见
          # 0不可见
          - name: SHOW_FEATURE_TICKET
            valueFrom:
              configMapKeyRef:
                name: env-configure
                key: SHOW_FEATURE_TICKET