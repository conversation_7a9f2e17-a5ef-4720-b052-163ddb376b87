<template>
  <micro-components moduleName="common-feedback" :data="{ requester, config }" />
</template>

<script>
import { GET, POST } from '@/request';
export default {
  name: 'common-feedback-remote',
  props: {
    size: {
      // 默认用小size, 传large用大的size
      type: String,
    },
  },
  data() {
    return {
      requester: { GET, POST },
      config: {
        hostPlatform: '毕昇',
        size: this.size,
      },
    };
  },
};
</script>
