import { GET, POST } from '@/request';

export default {
  getCompetitionAuthority: (data) => GET('/competiton/web/manage/checkUserViewCompetition', data), // 是否有权限查看比赛

  // 比赛列表
  getCompetitionList: (data) => GET('competiton/web/list', data), // 比赛列表
  getCompetitionType: (data) => GET('competiton/type', data), // 比赛类型

  //比赛详情
  getCompetitionDescInfoUrl: (data) => GET('/competiton/web/desc/info', data), // 获取赛制介绍以及赛题说明富文本链接
  getCompetitionDescInfo: (url) => GET(url), // 获取赛制介绍以及赛题说明富文本
  getCompetitionInfo: (data) => GET('competiton/web/info', data), // 获取比赛信息
  getCompetitionListImg: (data) => GET('/competiton/image', data), // 获取base64编码的图片
  getCompetitionRejectInfo: (data) => GET('/competiton/join/web/rejectinfo', data), // 报名页排斥比赛信息列表
  getCompetitionjoinSta: (data) => GET('/competiton/web/join/sta', data), // 比赛状态信息(报名状态)

  getCompetitionListNum: (data) => GET('/competiton/getInstanceNumList', data), // 比赛实例数
  getSpecId: (data) => GET('/competiton/web/getSpecId', data), // 获取比赛支持的最小specId

  getCompetitionResultMsg: (data) => GET('competiton/web/result/getmsg', data), // 提交文件-结果文件温馨提示
  getCompetitionReplyMsg: (data) => GET('competiton/web/reply/getmsg', data), // 提交文件-答辩材料温馨提示
  getCompetitionSubmitType: (data) => GET('/competiton/submitType', data), // 获取比赛结果文件/答辩材料 上传方式
  getCompetitionResultRequire: (data) => GET('/competiton/web/result/require', data), // 结果/答辩文件提交要求叙述以及图片以及下载附件地址
  getCompetitionShowButton: (data) => GET('/competiton/web/result/showbutton', data), // 审查以及答辩材料按钮是否展示
  getCompetitionFileList: (data) => GET('/competiton/web/file/list', data), // 团队共享存储文件以及文件夹展示

  getCompetitionMultipartinit: (cid, data) => POST(`/competiton/web/result/fileupload/multipartinit?cid=${cid}`, data, { useError: false }), // 分片初始化/续传
  getCompetitionComposeFile: (cid, data) => POST(`/competiton/web/result/fileupload/compose?cid=${cid}`, data), // 分片合并
  getCompetitionFileInfo: (cid, data) => POST(`/competiton/web/result/fileupload/info?cid=${cid}`, data), // 获取文件续传状态信息
  getCompetitionFileSubmit: (data) => POST('/competiton/web/result/fileupload/submit', data), // 文件提交
  getCompetitionFileDelete: (data) => POST('/competiton/web/result/fileupload/delete', {}, {}, { params: data }), // 删除文件或目录
  getCompetitionGetScoreName: (data) => GET('/competiton/web/getScoreName', data), // 获取比赛方设置的指标名

  getCompetitionResultList: (data) => GET('/competiton/web/result/list', data), // 结果文件记录list
  getReplyDownLoadType: (data) => GET('/competiton/web/getDownLoadType', data), // 答辩材料记录是否允许下载
  getCompetitionReplyList: (data) => GET('/competiton/web/reply/list', data), // 答辩材料记录list
  getCompetitionReplyGetUrl: (data) => GET('/competiton/web/reply/getUrl', data), // 获取文件下载地址接口
  getManageReplyUrl: (data) => GET('/competiton/web/manage/reply/getUrl', data), // 获取文件下载地址接口
  getCompetitionReplygetDownLoadType: (data) => GET('/competiton/web/getDownLoadType', data), // 答辩材料是否允许下载

  getCompetitionRankingList: (data) => GET('/competiton/web/ranking/list', data), // 比赛排行榜
  getCompetitionQuestion: (data) => GET('/competiton/web/question', data), // 常见问题数据

  getEcloudInfo: (data) => GET('/competiton/join/web/ecloud/get', data), // 获取移动云基本信息
  addEcloudInfo: (data) => POST('/competiton/join/web/ecloud/add', data), // 提交移动云基本信息
  ecloudSSOCheck: () => GET('/ecloud/proxy/web/getToken'), // 访问移动云获取token

  // 获取一键复制命令
  getCompetitionMobileCloudOrder: (data) => GET('/competiton/web/get/script', data),

  // 我的团队
  createTeam: (data) => POST('/competiton/web/team/create', data, {}), // 创建团队
  getByTeam: (data) => GET('/competiton/web/team/get', data), // 获取我的团队信息
  getByMyTeam: (data) => GET('/competiton/web/team/get/myteam', data), //获取我的团队信息
  updateTeamName: (data) => POST('/competiton/web/team/edit', data), // 更新团队名称
  changeLeader: (data) => POST('/competiton/web/team/leader/change', data), // 修改队长
  dismissTeam: (data) => POST('/competiton/web/team/delete', data), // 解散团队
  checkDismissTeamSubmit: (data) => POST('/competiton/web/team/delete/check', data), // 解散团队，查看是否提交过文件
  deleteMember: (data) => POST('/competiton/web/team/member/delete', data), // 删除队员
  invitationCode: (data) => POST('/competiton/web/team/invitation/create', data), // 邀请其他人加入团队code
  joinTeam: (data) => POST('/competiton/web/team/join', data), // 加入团队
  getTeamConfig: (data) => GET('/competiton/web/team/get/config', data), // 获取团队的配置
  updateEcloudInfo: (data) => POST('/competiton/join/web/ecloud/update', data), // 更新移动云信息
  checkJoinTeamStatus: (data) => POST('/competiton/web/team/join/check', data), // 检查加入团队的状态
  checkTeamLeader: (data) => GET('/competiton/web/team/leader/check', data), // 检查团队的队长

  // 比赛管理
  getCompetitionManageInfo: (data) => GET('/competiton/web/manage/info', data), // 比赛信息
  getCompetitionManageEditJoinTeam: (data) => GET('/competiton/web/manage/edit/join/team/get', data), // 报名以及团队设置回显
  getCompetitionNames: (data) => GET('/competiton/web/manage/competition/names', data), // 当前用户举办的所有比赛(比赛名称以及id)
  getCompetitionTeamMemberMaxNum: (data) => GET('/competiton/web/manage/edit/team/teamMemberMaxNum', data), // 比赛下团队最多人数
  getCompetitionTeamSaveAndUpdate: (data) => POST('/competiton/web/manage/edit/join/team/temp/update', data), // 报名以及团队设置保存发布
  getCompetitionEditBasicInfo: (data) => POST('/competiton/web/manage/edit/basic/info/update', data), // 编辑比赛信息页基本信息保存发布

  // 我举办的比赛 => 比赛管理 => 提交及排行
  // 结果文件提交
  fileSubmitGet: (data) => GET('/competiton/web/manage/edit/file/submit/get', data), // 结果文件提交设置回显
  fileResultList: (data) => GET('/competiton/web/manage/edit/file/result/list', data), // 结果文件提交记录
  resultListExport: (data) => GET('/competiton/web/manage/edit/resultList/export', data), // 导出结果文件提交记录
  getAutoScoringSwitch: (data) => GET('/competiton/web/manage/getMarkType', data), // 自动评分开关

  resultSubmitUpdate: (data) => POST('/competiton/web/manage/edit/file/result/submit/update', data), // 结果文件提交设置保存
  // 排行榜
  getRankingList: (data) => GET('/competiton/web/manage/edit/file/ranking/list', data), // 排行榜列表
  rankingExport: (data) => GET('/competiton/web/manage/edit/ranking/export', data), // 排行榜导出
  getRankingStatus: (data) => GET('/competiton/web/manage/edit/ranking/status/get', data), // 是否公开排行榜,true为公开
  rankingStatusUpdate: (data) => POST('/competiton/web/manage/edit/ranking/status/update', data), // 编辑排行榜公开状态
  // 审查及答辩材料提交
  getFileReply: (data) => GET('/competiton/web/manage/edit/file/reply/get', data), // 审查答辩材料提交设置回显
  getFileReplyList: (data) => GET('/competiton/web/manage/edit/file/reply/list', data), // 审查以及答辩材料提交记录list接口
  getFileReplyTeamsList: (data) => GET('/competiton/web/manage/edit/file/reply/teams', data), // 有提交答辩材料权限的团队
  getFileReplyScope: (data) => GET('/competiton/web/manage/edit/file/reply/scope', data), // 答辩材料开放范围
  fileReplySubmitUpdate: (data) => POST('/competiton/web/manage/edit/file/reply/submit/update', data), // 提交审查以及答辩材料设置保存
  editReplyListExport: (data) => POST('/competiton/web/manage/edit/replyList/export', data), // 导出答辩材料提交记录
  getFileReplyTeamIds: (data) => GET('/competiton/web/manage/edit/file/reply/teamIds', data), // 已有答辩材料权限团队id
  getFileReplyTeamAllWithNoPage: (data) => GET('/competiton/web/manage/edit/file/reply/get/teamAll', data), // 该比赛所有团队信息不分页

  // 我参加的比赛
  getMyCompetitionList: (data) => GET('/competiton/web/myself/get', data), // 获取我参加的比赛列表
  getMyCompetitionCount: (data) => GET('/competiton/web/myself/count', data), // 获取我参加比赛的数量

  //比赛报名
  getUserInfo: (data) => GET('/competiton/join/web/showUserInfo', data), // 获取比赛个人信息
  getProtocol: (data) => GET('/competiton/join/web/getProtocol', data), // 获取比赛协议
  getCompleteInfo: (data) => POST('/competiton/join/web/complete', data), // 获取比赛报名后完成信息
};
