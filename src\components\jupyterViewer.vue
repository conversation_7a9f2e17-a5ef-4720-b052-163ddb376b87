<template>
  <div id="jupyter-viewer" class="jupyter-viewer-container flex">
    <div class="toolbar flex">
      <div class="left flex">
        <a-button v-if="showStopBtn" :disabled="currentStep === 'STOPPING'" class="btn" type="primary" danger @click="handleStop">停止实例</a-button>
        <a-button v-if="showStartBtn" :loading="postStarting" :disabled="currentStep === 'STARTING'" class="btn" type="primary" @click="handleStart">启动实例</a-button>
        <a-tag class="align-center" style="margin-right: 12px" :class="`${stepClass}-tag`"> {{ stepLabel }}</a-tag>
        <a-tag class="type-tag align-center">{{ (specMap[projectInfo.spec] || {}).label }}</a-tag>
        <span style="vertical-align: middle">
          <span style="vertical-align: super">
            <!-- 修改icon样式  项目库的图标放上不生效 -->
            <span :class="instanceIcon">{{ instanceIcon === 'jt-jupyter-icon' ? 'J' : 'V' }}</span>
          </span>
        </span>
      </div>
      <div class="flex">
        <slot name="extraToolbar"></slot>
        <span class="icon-container">
          <a-popover placement="bottomRight">
            <template #content>
              <slot name="popoverContent">
                <p class="popover-container">页面关闭后10分钟课节实例将自动停止；</p>
                <p class="popover-container">最多只能同时运行4个课节或作业实例；</p>
              </slot>
            </template>
            <template #title>
              <span>说明</span>
            </template>
            <jt-icon class="icon info" type="iconinfo-circle"></jt-icon>
          </a-popover>
        </span>
        <span class="icon-container fullscreen">
          <jt-icon v-if="!isFullscreen" class="icon pointer" type="iconfullscreen" @click="handleFullScreen"></jt-icon>
          <jt-icon v-else class="icon pointer" type="iconcompress" @click="handleFullScreen"></jt-icon>
        </span>
      </div>
    </div>
    <div class="frame-container flex">
      <a-spin class="frame-spin" :spinning="spinningState">
        <template v-if="currentStep === 'RUNNING' && readyToRender">
          <iframe v-if="vsCodeMode" id="vscodeFrame" name="vscodeFrame" frameborder="0" title=""></iframe>
          <iframe v-else :src="jupyterUrl" frameborder="0" title=""></iframe>
        </template>
        <div v-show="currentStep === 'READY'" class="empty flex">
          <div class="content flex">
            <img src="@/assets/image/emptys2x.png" alt="" />
            <p class="main-text">{{ '您尚未启动实例哦～' }}</p>
            <p>
              <span>请</span>
              <a-button style="padding: 0" type="link" @click="handleStart">启动实例</a-button>
              <span>查看详情</span>
            </p>
          </div>
        </div>
        <div v-show="currentStep === 'STOPPING'" class="empty flex">
          <div class="content flex">
            <img src="@/assets/image/loading-dynamic.gif" alt="" />
            <p class="main-text">{{ '实例停止中...' }}</p>
          </div>
        </div>
        <div v-show="currentStep === 'COPYING'" class="empty flex">
          <div v-show="copyStatus !== 'exception'" class="content flex">
            <img src="@/assets/image/loading-dynamic.gif" alt="" />
            <a-progress :format="progressFormat" :percent="currentWidth" :status="copyStatus"></a-progress>
            <p class="main-text">{{ '复制相关文件中' }}</p>
            <p>
              <span>请稍作等待，勿离开当前页面</span>
            </p>
          </div>
          <div v-show="copyStatus === 'exception'" class="content flex">
            <img src="@/assets/image/fail.png" alt="" />
            <a-progress :format="progressFormat" :percent="currentWidth" :status="copyStatus"></a-progress>
            <p class="main-text">{{ '啊哦，复制的时候出现一些状况' }}</p>
            <p>
              <span>请</span>
              <a @click="handleStart">点击重试</a>
            </p>
          </div>
        </div>
        <div v-show="currentStep === 'STARTING'" class="empty flex">
          <div class="content flex">
            <img src="@/assets/image/loading-dynamic.gif" alt="" />
            <p class="main-text">{{ '实例启动中...' }}</p>
            <p>
              <span>请稍作等待，勿离开当前页面</span>
            </p>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import API from '@/constants/api/API.js';
import { POST, GET } from '../request';
import { BrowserDetect } from '@/utils/browserdetect';
import { storageApi } from '@/apis';

const specMap = {
  cpu: {
    key: '0',
    label: 'CPU',
  },
  vGpu: {
    key: '220',
    label: 'vGPU',
  },
};

const INSTANCEICON = {
  VSCode: 'jt-vscode-icon',
  Jupyter: 'jt-jupyter-icon',
};

const INSTANCEICONCOLOR = {
  VSCode: '#6bb8f9',
  Jupyter: '#f3a579',
};

const STEP = {
  0: 'COPYING',
  1: 'STARTING',
  2: 'RUNNING',
  3: 'STOPPING',
  4: 'READY',
};

const STEP_LABEL = {
  COPYING: '启动中',
  STARTING: '启动中',
  RUNNING: '运行中',
  STOPPING: '停止中',
  READY: '停止',
};

const STEP_CLASS = {
  COPYING: 'starting',
  STARTING: 'starting',
  RUNNING: 'running',
  STOPPING: 'stopping',
  READY: 'stopped',
};

const COPY_STATUS = {
  0: 'active',
  1: 'success',
  2: 'exception',
};

const progressInterval = 2000;
const fakeProgressInterval = 200;

export default {
  props: {
    projectInfo: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      postStopping: false,
      postStarting: false,
      rendering: false,
      isFullscreen: false,
      readyToRender: false,
      specMap,
      updateTimmer: null,
      queryTimmer: null,
      destroyed: false,
      progress: 0,
      fakeProgress: 0,
      copyStatus: 'active',
      totalFileCount: 0,
      currentStep: '',
      instanceStatus: 0,
      instanceId: '',
      jupyterUrl: '',
      vsCodeUrl: '',
      podPwd: '',
      progress_growth: 0,
    };
  },
  computed: {
    projectId() {
      return this.projectInfo.projectId;
    },
    projectSpec() {
      return specMap[this.projectInfo.spec].key;
    },
    instanceIconColor() {
      return this.vsCodeMode ? INSTANCEICONCOLOR.VSCode : INSTANCEICONCOLOR.Jupyter;
    },
    instanceIcon() {
      return this.vsCodeMode ? INSTANCEICON.VSCode : INSTANCEICON.Jupyter;
    },
    vsCodeMode() {
      return this.projectInfo.instanceModel === 'VSCode';
    },
    stepClass() {
      return STEP_CLASS[this.currentStep] || '';
    },
    stepLabel() {
      return STEP_LABEL[this.currentStep] || '';
    },
    runningState() {
      return ['RUNNING'].includes(this.currentStep);
    },
    spinningState() {
      return this.postStarting || this.postStopping || this.rendering;
    },
    showStartBtn() {
      return ['READY'].includes(this.currentStep);
    },
    showStopBtn() {
      return ['RUNNING', 'STOPPING'].includes(this.currentStep);
    },
    currentWidth() {
      return Math.floor((this.fakeProgress / this.totalFileCount) * 100);
    },
  },
  watch: {
    projectInfo() {
      this.getInstanceStatus();
    },
  },
  async mounted() {
    // await this.getInstance()
    await this.getInstanceStatus();
  },
  beforeUnmount() {
    this.destroyed = true;
    clearTimeout(this.updateTimmer);
    clearTimeout(this.queryTimmer);
    clearInterval(this.progressTimmer);
    clearInterval(this.fakeProgressTimmer);
  },
  methods: {
    progressFormat() {
      return `${Math.floor(this.fakeProgress)}/${this.totalFileCount}`;
    },
    updateProgress(res) {
      this.progress = res.data.fileCopyProgressDto.completedFile;
      this.totalFileCount = res.data.fileCopyProgressDto.fileCount;
      this.copyStatus = COPY_STATUS[res.data.status];
      if (this.fakeProgress < this.progress) {
        this.fakeProgress = this.progress;
      }
    },
    updateCurrentWidth() {
      // 计算逻辑：假设刚好一个查询周期内能够走完进度，那每次平均增长的值就是100/增长次数
      this.progress_growth = (this.totalFileCount - this.progress) / (progressInterval / fakeProgressInterval);
      if (this.fakeProgress < this.totalFileCount - this.progress_growth) {
        this.fakeProgress += this.progress_growth;
      }
    },
    // vsCode的预登陆，jupyter不需要此操作是因为jupyter可以使用query的方式进行直登，但是vscode不行
    initVSCode() {
      if (this.destroyed) return;
      function getStringPosition(str, cha, num) {
        let x = str.indexOf(cha);
        for (let i = 0; i < num; i++) {
          x = str.indexOf(cha, x + 1);
        }
        return x;
      }
      const base = this.vsCodeUrl.substring(getStringPosition(this.vsCodeUrl, '/', 2) + 1).replace('/login', '');
      const currentBrowser = BrowserDetect.init().browser;
      if (currentBrowser === 'Safari') {
        console.log(`%c Safari!!!!!`, 'background:#41b883 ; padding: 1px; border-radius: 0 3px 3px 0;  color: #fff');
        const currentBase = base.charAt(0) === '/' ? base.slice(1, base.length) : base;
        console.log(currentBase, '--currentBase');
        this.creatFormPost(this.vsCodeUrl, {
          currentBase,
          password: this.podPwd,
        });
      } else {
        this.creatFormPost(this.vsCodeUrl, {
          base,
          password: this.podPwd,
        });
      }
    },
    /**
     * 使用js创建form标签提交表单（防止跨域）
     * @param { string } URL 提交地址
     * @param { object } param 参数
     */
    creatFormPost(URL, param) {
      if (this.destroyed) return;
      //创建form表单
      const tempForm = document.createElement('form');
      tempForm.action = URL;
      //如需打开新窗口，form的target属性要设置为'_blank'
      tempForm.target = document.getElementById('vscodeFrame').name;
      tempForm.method = 'post';
      tempForm.style.display = 'none';
      //添加参数
      for (const item in param) {
        const opt = document.createElement('input');
        opt.name = item;
        opt.value = param[item];
        tempForm.appendChild(opt);
      }
      document.body.appendChild(tempForm);
      //提交数据
      tempForm.submit();
    },

    // 定时给后台发送心跳请求
    async updateInstance() {
      if (this.runningState) {
        const obj = { projectId: this.projectId };
        await this.updateInstanceState(obj);
        this.updateTimmer = setTimeout(() => {
          this.updateInstance();
        }, 60000);
      }
    },
    handleFullScreen() {
      const element = document.getElementById('jupyter-viewer');
      if (screenfull.isEnabled) {
        screenfull.toggle(element);
        this.isFullscreen = !this.isFullscreen;
      }
    },
    async startInstance(obj) {
      const res = await POST('/dp_platform/resource/course/async_run_instance', obj);
      if (res.code === 200) {
        return res.data;
      } else {
        let msg = res.errorMessage || res.msg;
        if (res.code === -703) {
          const type = this.type === 'homework' ? '作业' : '课节';
          msg = `教师已删除${type}，无法启动实例`;
        }
        if (res.code === 512) {
          msg = '目前平台剩余可用资源不足，请稍后再试';
        }
        throw new Error(msg);
      }
    },

    async stopInstance({ instanceId }) {
      const obj = {
        instanceId: instanceId,
      };
      const res = await API.dp_platform.stopInstance(obj);
      if (res.code === 200) {
        return res.data;
      } else {
        throw new Error(res.errorMessage || res.msg);
      }
    },

    async updateInstanceState(obj) {
      const res = await POST(`/dp_platform/resource/course/update_instance`, obj);
      return res;
    },
    handleCopyStep(res) {
      this.updateProgress(res);
      if (this.copyStatus === 'active') {
        clearTimeout(this.queryTimmer);
        this.queryTimmer = setTimeout(() => {
          this.getInstanceStatus();
        }, progressInterval);
        clearInterval(this.fakeProgressTimmer);
        this.fakeProgressTimmer = setInterval(() => {
          this.updateCurrentWidth();
        }, fakeProgressInterval);
      } else {
        clearTimeout(this.queryTimmer);
        clearInterval(this.fakeProgressTimmer);
        this.getInstanceStatus();
      }
    },
    updateCurrentStep(res) {
      this.currentStep = STEP[res.data.step];
      console.log(this.currentStep);
      if (this.currentStep === 'COPYING') {
        this.updateProgress(res);
      }
    },
    async getInstanceStatus() {
      const res = await GET('/dp_platform/resource/course/instance_status', {
        projectId: this.projectId,
      });
      if (res.code === 200) {
        this.instanceId = res.data.instanceId;
        this.updateCurrentStep(res);
        if (this.currentStep === 'COPYING') {
          this.handleCopyStep(res);
        } else if (this.currentStep === 'STARTING' || this.currentStep === 'STOPPING') {
          this.queryTimmer = setTimeout(() => {
            this.getInstanceStatus();
          }, 1000);
        } else if (this.currentStep === 'RUNNING') {
          clearInterval(this.progressTimmer);
          clearInterval(this.fakeProgressTimmer);
          this.jupyterUrl = res.data.instanceAccessUrlDto.jupyterUrl;
          this.vsCodeUrl = res.data.instanceAccessUrlDto.vsCodeUrl;
          this.podPwd = res.data.instanceAccessUrlDto.podPwd;
          this.renderFrame();
        } else if (this.currentStep === 'READY') {
          clearTimeout(this.queryTimmer);
          clearInterval(this.progressTimmer);
          clearInterval(this.fakeProgressTimmer);
        }
      }
    },
    async renderFrame() {
      const url = this.vsCodeMode ? this.vsCodeUrl : this.jupyterUrl;
      this.rendering = true;
      this.readyToRender = await this.getFrameStatus(url);
      if (this.readyToRender) {
        if (this.vsCodeMode) {
          this.$nextTick().then(async () => {
            this.initVSCode();
          });
        }
        this.updateInstance();
        this.rendering = false;
      } else {
        this.queryTimmer = setTimeout(() => {
          this.getInstanceStatus();
        }, 1000);
      }
    },
    async getFrameStatus(url) {
      if (!url) {
        return false;
      }
      return fetch(url)
        .then((res) => {
          if (res.status === 200) {
            return true;
          } else {
            return false;
          }
        })
        .catch(() => {
          return false;
        });
    },

    async handleStart() {
      if (this.postStarting) {
        return;
      }
      const obj = {
        projectId: this.projectId,
        specId: this.projectSpec,
      };
      this.postStarting = true;
      try {
        await this.startInstance(obj);
        this.postStarting = false;
        await this.getInstanceStatus();
        await this.checkStorage();
      } catch (error) {
        console.log(error);
        this.$notification.error({
          message: '启动实例失败',
          description: error.message,
        });
        this.postStarting = false;
      }
    },

    async handleStop() {
      if (this.postStopping) {
        return;
      }
      const obj = {
        instanceId: this.instanceId,
      };
      this.postStopping = true;
      try {
        await this.stopInstance(obj);
        this.getInstanceStatus();
      } catch (error) {
        this.$notification.error({
          message: '停止实例失败',
          description: error.message,
        });
      } finally {
        this.postStopping = false;
      }
    },

    checkStorage() {
      storageApi.searchRemainingCapacity().then((res) => {
        if (res.code === 200) {
          const availableStorage = this.accMul(res.body.available, this.accMul(this.accMul(1024, 1024), 1024));
          if (availableStorage === 0) {
            this.$notification.error({
              message: '存储不足提示',
              description: '您的剩余可用存储为 0，请及时清理或申请扩容',
            });
          }
        }
      });
    },
    accMul(arg1, arg2) {
      var m = 0,
        s1 = arg1.toString(),
        s2 = arg2.toString();
      try {
        m += s1.split('.')[1].length;
      } catch (e) {
        console.warn(e);
      }
      try {
        m += s2.split('.')[1].length;
      } catch (e) {
        console.warn(e);
      }
      return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

div.flex {
  display: flex;
}
.jupyter-viewer-container {
  flex-direction: column;
  width: 100%;
  padding: 0 20px;
}
.toolbar {
  justify-content: space-between;
  background: #fff;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e0e1e1;
  .btn {
    margin-right: 16px;
  }
  .left {
    display: block;
    padding: 0 !important;
  }
  .icon-container {
    display: flex;
    align-items: center;
    padding: 0 16px;
    &.fullscreen {
      border-left: 1px solid #ddd;
    }
    .icon {
      font-size: 24px;
      &:hover {
        color: #0082ff;
      }
    }
  }
}
.frame-container {
  flex: 1;
  .frame-spin {
    width: 100%;
  }
  :deep(.ant-spin-container) {
    width: 100%;
    height: 100%;
  }
  iframe {
    width: 100%;
    height: 100%;
  }
}
.pointer {
  cursor: pointer;
}
.popover-container {
  width: 304px;
}
.stopped-tag {
  background: #fff5f6;
  border-radius: 12px;
  border: 1px solid #ff454d;
  color: #ff454d;
}
.running-tag {
  background: #eafff8;
  border-radius: 12px;
  border: 1px solid #17bb85;
  color: #17bb85;
}
.starting-tag {
  background: #fff6ee;
  border-radius: 12px;
  border: 1px solid #ff8415;
  color: #ff8415;
}
.type-tag {
  background: #e1ecff;
  border-radius: 12px;
  color: #337dff;
  border: none;
}
.align-center {
  text-align: center;
}
.empty {
  width: 100%;
  height: 100%;
  background: #fff;
  justify-content: center;
  .content {
    width: 394px;
    flex-direction: column;
    align-items: center;
    img {
      width: 100%;
      margin-bottom: -84px;
    }
    .main-text {
      margin: 0;
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
    }
  }
}

.jt-vscode-icon {
  .jt-vscode-icon();
}
.jt-jupyter-icon {
  .jt-jupyter-icon();
}
</style>
