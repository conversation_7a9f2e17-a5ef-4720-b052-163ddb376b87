import API from '@/constants/api/API.js';
import { message } from 'ant-design-vue';
import { POST } from '../../../request';

const actions = {
  getStudentTable({ commit }, params = {}) {
    commit('updateLoading', true);
    commit('updateParams', params);
    API.course_management.getCourseStudent(params).then((res) => {
      if (res.code === 200) {
        commit('updateStudent', res.body);
        setTimeout(() => {
          commit('updateLoading', false);
        }, 300);
      } else {
        setTimeout(() => {
          commit('updateLoading', false);
        }, 300);
      }
    });
  },
  deleteStudent({ dispatch }, params) {
    API.course_management.deleteStudent(params).then((res) => {
      if (res.code === 200) {
        message.success('删除成功');
        dispatch('getStudentTable', { page: 1, pageSize: 10 });
      } else {
        message.error('删除失败');
      }
    });
  },
  getExamUrl({ commit }) {
    POST('/examination/queryAddressInfo', {}, { useError: false }).then((res) => {
      if (!res.errorCode) {
        commit('updateExamUrl', res.body);
      }
    });
  },
};

export default actions;
