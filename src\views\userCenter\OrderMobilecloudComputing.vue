<template>
  <div class="competition-register-container">
    <bread-crumb :value="breadcrumbs"></bread-crumb>
    <div class="section">
      <div class="section-title">订购移动云深度学习平台赢算力</div>
      <span class="section-alert">
        <alarm class="jt-section-alert">
          <p>
            仅需 <span class="num">2</span> 步，完成移动云深度学习平台订购，即可赢取<span class="num"> {{ suanLiState.beannum }} </span>个算力豆
          </p>
        </alarm>
      </span>
      <register-form></register-form>
      <div class="section-footer">
        <div class="section-footer-text">
          <p>活动须知：</p>
          <p>1、针对未订购移动云深度学习平台的用户，按照引导完成移动云深度学习平台订购，即可赢取{{ suanLiState.beannum }}个算力豆，算力豆有效期为{{ suanLiState.daynum }}天</p>
          <p>2、每位用户仅可参加一次本活动，不可重复参加，不可重复赢取算力豆</p>
          <p>3、本活动最终解释权归九天·毕昇所有</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import breadCrumb from '../../components/breadCrumb';
import alarm from '@/components/alarm/index.vue';
import RegisterForm from './RegisterForm.vue';
import { mapState } from 'vuex';

export default {
  components: { breadCrumb, alarm, RegisterForm },
  data() {
    return {
      breadcrumbs: [
        { name: '个人中心', path: '/user-center' },
        {
          name: '算力信息',
          path: `/user-center?activeTab=2`,
        },
        { name: '订购移动云深度学习平台赢算力' },
      ],
    };
  },
  computed: {
    ...mapState(['suanLiState']),
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.competition-register-container {
  flex-direction: column;
  align-items: center;
  min-height: 600px;
  background-color: @jt-main-bg-color;
  .section {
    width: 1200px;
    margin: 20px auto 48px;
    padding: 20px;
    background-color: @jt-color-white;
    display: flex;
    flex-direction: column;
    box-shadow: @jt-box-shadow;
    .section-title {
      height: 24px;
      font-size: @jt-font-size-lg;
      color: @jt-title-color;
      font-weight: @jt-font-weight-medium;
      margin-bottom: 24px;
    }
  }
}

// 提示alert
.section-alert {
  .jt-alarm-banner {
    height: 40px;
    background: #edf7ff;
    border-radius: 2px;
    border: 1px solid #b0d5ff;
    font-size: @jt-font-size-base;
    font-weight: @jt-font-weight;
    color: @jt-table-header-color;
  }

  :deep(.jt-alarm-banner .icon) {
    color: #6bb4fb;
  }
  .num {
    color: #f17506;
  }
}
.section-footer {
  border-top: 1px solid #e6ebf5;
  margin: 0 40px;
  margin-top: 14px;
  padding: 24px 0 28px;
  display: flex;
  justify-content: center;

  .section-footer-text {
    font-size: @jt-font-size-base;
    font-weight: @jt-font-weight;
    color: @jt-text-color;
    line-height: 24px;
  }
}
</style>
