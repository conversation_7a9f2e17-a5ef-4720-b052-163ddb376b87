module.exports = {
  //h5营销活动（ni<PERSON><PERSON>an）
  '/api/marketing': {
    target: 'http://172.31.192.238/edu',
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api/': '/',
    },
    headers: {
      Connection: 'keep-alive',
    },
    rewrite: (path) => path.replace(/^.*\/api\//, '/'),
  },
  '/api/test': {
    target: 'http://172.31.192.238/edu',
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api/test/': '/',
    },
    headers: {
      Connection: 'keep-alive',
    },
    rewrite: (path) => path.replace(/^.*\/api\/test\//, '/'),
  },
  '/api/dev/file_upload': {
    target: 'http://172.31.197.98/edu/',
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api/dev/file_upload/': '/file_upload/',
    },
    headers: {
      Connection: 'keep-alive',
    },
    rewrite: (path) => path.replace(/^.*\/api\/dev\/file_upload\//, '/file_upload/'),
  },
  '/api/dev': {
    //target: 'http://172.31.197.98/edu/',
    target: 'http://172.31.192.237',
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api/dev/': '/',
    },
    headers: {
      Connection: 'keep-alive',
    },
    rewrite: (path) => path.replace(/^.*\/api\/dev\//, '/'),
  },
  // 文件上传和下载相关接口
  './objects-download': {
    target: 'http://172.31.192.238/edu',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^/.*/objects-download/': '/objects-download/',
    },
    headers: {
      Connection: 'keep-alive',
    },
  },
  // 文件上传和下载相关接口
  './object': {
    target: 'http://172.31.192.238/edu',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^/.*/object/': '/object/',
    },
    headers: {
      Connection: 'keep-alive',
    },
  },
};
