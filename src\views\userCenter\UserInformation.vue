<template>
  <div class="user-information-container">
    <div v-if="!editing" class="user-information flex">
      <a-descriptions class="descriptions-container" :column="1" :colon="false">
        <a-descriptions-item label="用户名"> {{ userInfo.userName || '-' }} </a-descriptions-item>
        <a-descriptions-item label="姓名"> {{ userInfo.fullName || '-' }} </a-descriptions-item>
        <a-descriptions-item label="身份"> {{ userInfo.identity }} </a-descriptions-item>
        <a-descriptions-item label="身份信息"> {{ identityInfo || '-' }} </a-descriptions-item>
        <a-descriptions-item label="账户信息">
          <p>
            <span class="label">手机号</span>
            <span>{{ hidePhone || '-' }}</span>
          </p>
          <p>
            <span class="label">邮箱</span>
            <span>{{ userInfo.email || '-' }}</span>
          </p>
        </a-descriptions-item>
        <a-descriptions-item label="简介"> {{ userInfo.introduction || '-' }} </a-descriptions-item>
        <a-descriptions-item label="">
          <a-button class="first-btn" type="primary" @click="handleEdit">编辑</a-button>
          <a-button @click="handleModifyPwd">修改密码</a-button>
        </a-descriptions-item>
      </a-descriptions>
      <div class="avatar-container">
        <img :src="userInfo.image || defaultUrl" alt="" />
      </div>
    </div>
    <user-information-form v-else :value="formValue" @ok="onFormOk" @cancel="editing = false" @changeActiveTab="changeActiveTab"></user-information-form>
    <pwd-form-dlg :phone-num="userInfo.phoneNum" :visible="dlgVisible" @ok="onDlgOk" @cancel="dlgVisible = false"></pwd-form-dlg>
  </div>
</template>

<script>
import userInformationForm from './userInformationForm.vue';
import pwdFormDlg from './pwdFormDlg.vue';
import { checkImgUrl, phoneHider } from '@/utils/utils';
export default {
  name: 'UserInformation',
  components: { userInformationForm, pwdFormDlg },
  emits: ['changeActiveTab'],
  data() {
    return {
      userInfo: {
        userName: '',
        fullName: '',
        identity: '',
        email: '',
        introduction: '',
        image: '',
        phoneNum: '',
      },
      editing: this.$route.query.edit === 'true',
      dlgVisible: false,
      defaultUrl: require('@/assets/image/avatar_big.png'),
    };
  },
  computed: {
    identityInfo() {
      const userInfo = this.userInfo;
      const res = [userInfo.school, userInfo.faculty, userInfo.major, userInfo.identity].filter((item) => !!item);
      if (userInfo.identity === '学生') {
        res.push(`(学号${userInfo.stuNum || '-'})`);
      }
      if (userInfo.identity === '开发者') {
        res.push(userInfo.companyArea || '');
        res.push(userInfo.company || '');
      }
      return res.join(' ');
    },
    formValue() {
      return { ...this.userInfo };
    },
    hidePhone() {
      return phoneHider(this.userInfo.phoneNum);
    },
  },
  watch: {
    editing(val) {
      if (this.$route.query.ecloud == 'true' && this.editing != true) {
        this.$router.replace({ query: { ...this.$route.query, edit: val, ecloud: 'false' } });
      } else {
        this.$router.replace({ query: { ...this.$route.query, edit: val } });
      }
      if (!val) {
        this.getUserInfo();
      }
    },
  },
  mounted() {
    this.getUserInfo();
  },
  methods: {
    handleEdit() {
      this.editing = true;
    },
    handleModifyPwd() {
      this.dlgVisible = true;
    },
    async getUserInfo() {
      const res = await this.$GET('/keycloak/web/user/getuserinfo', {}, { useError: false });
      if (!checkImgUrl(res.body.image)) {
        res.body.image = '';
      }
      this.userInfo = res.body;
      this.$store.commit('SET_USERINFO_DATA', res.body);
    },
    async onFormOk(form) {
      if (form.area == '其他') {
        form.school = form.schoolInput;
      }
      const res = await this.$POST('/keycloak/web/user/updateuser', form, { useError: false });
      if (res.body === 'OK') {
        await this.getUserInfo();
        //  ecloud true 就是从注册赢算力跳转过来的
        if (this.$route.query.ecloud == 'true') {
          const ecloudGetRes = await this.$GET('/marketing/web/ecloud/get').then((res) => {
            if (res.state === 'OK') {
              res.body = res.body || {};
              this.$store.commit('SET_SUANLISTATE_DATA', res.body);
              return res;
            } else {
              return {};
            }
          });
          const joinStaModalArr = [4, 5, 6]; // joinSta 456 返回到算力页面 弹窗
          const joinStaStepsArr = [1, 2, 3]; // joinSta 123 跳转到具体某个步骤
          if (joinStaModalArr.includes(ecloudGetRes.body.joinSta) && this.$route.query.ecloud == 'true') {
            this.$emit('changeActiveTab', '2', 'userInform');
          } else if (joinStaStepsArr.includes(ecloudGetRes.body.joinSta) && this.$route.query.ecloud == 'true') {
            this.$router
              .push({
                path: '/user-center/order-mobilecloud-computing',
              })
              .catch(() => {
                this.$router.replace('/user-center/order-mobilecloud-computing');
              });
          }
        }
        const redirectUrl = this.$route.query.redirectUrl;
        this.$notification['success']({
          message: '保存成功',
          description: '用户信息保存成功',
        });

        if (redirectUrl) {
          this.$router.push(redirectUrl);
        } else {
          this.editing = false;
        }
      } else {
        this.$notification['error']({
          message: '保存失败',
          description: res.errorMessage,
        });
      }
    },
    onDlgOk(form) {
      this.$POST('/keycloak/web/user/resetpassword', form, { useError: false }).then((res) => {
        if (res.body === 'OK') {
          this.$notification['success']({
            message: '修改密码成功',
            description: '修改密码成功',
          });
          this.dlgVisible = false;
        } else {
          this.$notification['error']({
            message: '修改密码失败',
            description: res.errorMessage,
          });
        }
      });
    },
    changeActiveTab() {
      this.$emit('changeActiveTab', '2', 'userInform');
    },
  },
};
</script>

<style lang="less" scoped>
.user-information-container {
  padding: 28px 0;
}
.flex {
  display: flex;
}
.avatar-container {
  margin-right: 200px;
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
  }
}
.descriptions-container {
  flex: 1;
}
:deep(.ant-descriptions-item-label) {
  margin-right: 24px;
  text-align: right;
  vertical-align: top;
  width: 100px;
}
:deep(.ant-descriptions-row > th),
:deep(.ant-descriptions-row > td) {
  padding-bottom: 32px;
}
.first-btn {
  margin-right: 16px;
}
.label {
  margin-right: 8px;
}

:deep(.ant-descriptions-item-content) {
  max-width: 500px;
}
</style>
