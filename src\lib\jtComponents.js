import jtContentWithEmpty from './components/contentWithEmpty.vue';
import commonContent from './components/commonContent.vue';
import buttonFilter from './components/buttonFilter.vue';
import pagination from './components/pagination.vue';
import skeleton from './components/skeleton.vue';
import MicroComponents from './micro-components/microComponents.vue';

export default {
  // eslint-disable-next-line no-unused-vars
  install: (app, options) => {
    app.component('jtContentWithEmpty', jtContentWithEmpty);
    app.component('jtCommonContent', commonContent);
    app.component('jtButtonFilter', buttonFilter);
    app.component('jtPagination', pagination);
    app.component('jtSkeleton', skeleton);
    app.component('micro-components', MicroComponents);
  },
};
