<template>
  <div class="rank-title">
    <a-radio-group v-model:value="defaultValue" @change="tabsChange">
      <a-radio-button v-for="x in rankTabsList" :key="x.value" :value="x.value">{{ x.label }}</a-radio-button>
    </a-radio-group>
    <div class="title-right">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { rankTabsList } from './constants';
import API from '@/constants/api/API';
export default {
  emits: ['change'],
  data() {
    return {
      rankTabsList,
      defaultValue: this.$route.query.subtabId || '1',
    };
  },
  watch: {
    //  自动评分关闭的比赛，无“提交及排行榜-排行榜”tab
    '$store.state.competition.resultContentData'(state) {
      if (!state.markType) {
        this.rankTabsList = this.rankTabsList.filter((x) => x.value != 2);
      }
    },
  },
  created() {
    if (!this.$route.query.subtabId) {
      this.$router.replace({ query: { tabId: 5, subtabId: this.rankTabsList[0].value } });
      this.$emit('change', { subtabId: this.rankTabsList[0].value });
    } else {
      this.$emit('change', { subtabId: this.$route.query.subtabId });
    }
    API.competition_model.fileSubmitGet({ cid: this.$route.params.competitionId }).then((res) => {
      if (res.state === 'OK') {
        this.$store.state.competition.resultContentData = res.body;
      }
    });
  },
  methods: {
    tabsChange(e) {
      this.$router.replace({ query: { tabId: 5, subtabId: e.target.value } });
      this.$emit('change', { subtabId: e.target.value });
    },
  },
};
</script>

<style lang="less" scoped>
.rank-title {
  display: flex;
  justify-content: space-between;
}
:deep(.ant-radio-button-wrapper) {
  width: 160px;
  text-align: center;
}
</style>
