<template>
  <div class="competition-breadcrumb jt-box-shadow">
    <jt-breadcrumb class="competition-breadcrumb-item">
      <jt-breadcrumb-item>
        <router-link :to="$route.meta.parentPageUrl">{{ $route.meta.parentPageName }}</router-link>
      </jt-breadcrumb-item>
      <jt-breadcrumb-item v-if="$route.query.all">
        <router-link :to="{ name: '我的比赛' }"> 我参加的比赛 </router-link>
      </jt-breadcrumb-item>
      <jt-breadcrumb-item>{{ competitionName }}</jt-breadcrumb-item>
    </jt-breadcrumb>
  </div>
</template>
<script>
import { Breadcrumb as JtBreadcrumb } from 'ant-design-vue';
export default {
  components: {
    JtBreadcrumb,
    JtBreadcrumbItem: JtBreadcrumb.Item,
  },
  props: {
    competitionName: String,
  },
};
</script>
<style lang="less" scoped>
.competition-breadcrumb {
  background: #fff;
  .competition-breadcrumb-item {
    line-height: 68px;
    width: 1200px;
    margin: 0 auto;
    a {
      display: unset;
    }
  }
}
</style>
