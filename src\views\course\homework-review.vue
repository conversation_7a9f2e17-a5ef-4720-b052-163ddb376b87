<template>
  <div class="main-container">
    <bread-crumb class="bread-crumb-container" :value="breadcrumb"></bread-crumb>
    <div class="content">
      <a-spin :spinning="loading">
        <div class="item-container">
          <jupyter-viewer-for-review v-if="projectInfo.projectId" :project-info="projectInfo" class="large-size" :stu-assignment-id="stuAssignmentId" type="homework">
            <template #extraToolbar>
              <a-space>
                <a-space>
                  <a-button v-if="hasFile && detail.fileName">
                    <a :href="detail.filePath" :download="detail.fileName">下载附件</a>
                  </a-button>
                  <a-button :disabled="outdated" type="primary" @click="handleReview">评阅</a-button>
                </a-space>
              </a-space>
            </template>
            <template #popoverContent>
              <p class="popover-container">页面关闭后10分钟作业实例将自动停止；</p>
              <p class="popover-container">最多只能同时运行4个课节或作业实例；</p>
              <p class="popover-container">当启动多于4个实例时，则自动关闭启动时间较久的已运行实例。</p>
            </template>
          </jupyter-viewer-for-review>
        </div>
      </a-spin>
    </div>
    <review-dialog :student-assignment-id="detail.stuAssignmentId" :student-remark="detail.remark" :student-score="detail.score" :visible="reviewDialogVisible" @cancel="reviewDialogVisible = false" @ok="onOk"></review-dialog>
  </div>
</template>

<script>
import { checkLogin } from '@/keycloak';
import breadCrumb from '../../components/breadCrumb';
import jupyterViewerForReview from '../../components/jupyterViewerForReview';
import reviewDialog from '@/components/homework/reviewDialog.vue';
import { GET } from '@/request';
import { READ_STATUS, OUTDATED_STATUS, SUBMIT_STATUS } from '@/components/course-overview/homework-maps';
import { downloadFile } from '@/utils/file';
import { checkAuth } from '@/utils/utils';

const breadcrumbData = [];

export default {
  components: {
    breadCrumb,
    jupyterViewerForReview,
    reviewDialog,
  },
  data() {
    return {
      courseId: null,
      breadcrumb: [],
      loading: false,
      detail: {},
      loaded: false,
      homeworkList: [],
      assignmentId: null,
      assignmentName: '',
      stuAssignmentId: null,
      reviewDialogVisible: false,
    };
  },
  computed: {
    haveNext() {
      return this.currentIndex < this.homeworkList.length - 1;
    },
    havePre() {
      return this.currentIndex > 0;
    },
    currentIndex() {
      return this.homeworkList.findIndex((item) => +item.id === +this.assignmentId);
    },
    needAttachment() {
      return false;
    },
    projectInfo() {
      return {
        projectId: this.detail.projectId,
        spec: this.detail.spec,
        instanceModel: this.detail.instanceModel,
        instanceName: this.detail.projectName,
      };
    },
    reviewed() {
      return READ_STATUS[this.detail.readStatus] === '已评阅';
    },
    outdated() {
      return OUTDATED_STATUS[this.detail.abort] === '已截止';
    },
    getEndTimeClass() {
      return OUTDATED_STATUS[this.detail.abort] === '已截止' ? 'end-time-outdated' : '';
    },
    getStartedStatusClass() {
      return SUBMIT_STATUS[this.detail.submitStatus] === '已提交' ? 'learn-status-reviewed' : '';
    },
    getStartedStatusText() {
      return SUBMIT_STATUS[this.detail.submitStatus];
    },
    hasFile() {
      // submit_status  提交状态（0未提交，1已提交）
      return this.detail.submitStatus === '1';
    },
  },
  watch: {
    // 如果路由有变化，会再次执行该方法
    $route: 'init',
  },
  created() {
    if (!checkLogin()) {
      return;
    }
    this.init();
  },
  methods: {
    async init() {
      this.courseId = this.$route.params.courseId;
      this.stuAssignmentId = this.$route.params.stuAssignmentId;
      this.assignmentName = this.$route.query.assignmentName;

      this.breadcrumb = [...breadcrumbData];
      this.loading = true;
      await this.getHomeworkDetail();
      this.assignmentId = this.detail.assignmentId;
      this.breadcrumb.push({
        name: `作业评阅：${this.detail.assignmentName}`,
        path: `/course/teaching/mycourses/course-manage/${this.courseId}/${this.assignmentId}?assignmentName=${this.detail.assignmentName}`,
      });

      this.breadcrumb.push({
        name: `学生：${this.detail.studentName}`,
      });
      this.loading = false;
    },

    getLocation() {
      return window.location.origin;
    },

    async getHomeworkDetail() {
      const res = await GET(
        '/course_model/web/teaching/assignment/correct/detail',
        {
          // courseId: this.courseId,
          stuAssignmentId: this.stuAssignmentId,
        },
        { useError: false }
      );
      if (res.state === 'OK') {
        this.detail = res.body;
        this.detail.filePath = `${this.getLocation()}${this.detail.filePath}`;
      } else {
        if (!checkAuth(res.errorCode, '-802', '/course')) {
          return;
        }
      }
    },
    handleDownloadAttachment() {
      downloadFile(this.detail.filePath, this.detail.fileName);
    },
    handleReview() {
      this.reviewDialogVisible = true;
    },
    onOk() {
      this.reviewDialogVisible = false;
      this.init();
    },
  },
};
</script>

<style lang="less" scoped>
@import './homework-detail.less';
</style>
