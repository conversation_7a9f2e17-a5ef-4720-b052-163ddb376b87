<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>
/* 用户信息简介 */
<template>
  <div class="user-intro jt-box-shadow">
    <div class="info-content">
      <div class="content-left">
        <img v-if="userInfo.image" :src="userInfo.image" class="user-avatar" alt="" />
        <img v-else src="@/assets/image/avatar_big.png" class="user-avatar" alt="" />

        <div class="user-text-des">
          <a-space style="margin-bottom: 16px">
            <span class="user-name">{{ userInfo.fullName }}</span>
            <EditOutlined style="cursor: pointer" @click="handleEditUserInfo" />
          </a-space>
          <div>
            <span class="user-job">{{ userInfo.identity }}</span>
          </div>
          <p class="user-des">简介：{{ userInfo.introduction ? userInfo.introduction : '暂无简介' }}</p>
        </div>
      </div>
      <div class="content-right">
        <div class="total-course">
          <span>{{ courseNum }}</span>
          <div>开设课程</div>
        </div>
        <div class="total-student">
          <span>{{ studentNum }}</span>
          <div>学习人数</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCourseNumAndStudentNum } from '@/apis/teaching.js';
import { mapState } from 'vuex';
import { openInNewTab } from '@/utils/utils';

export default {
  name: 'UserIntroduction',
  data() {
    return {
      courseNum: 0,
      studentNum: 0,
    };
  },
  mounted() {
    this.getTotal();
  },
  computed: {
    ...mapState(['userInfo']),
  },
  methods: {
    getTotal() {
      getCourseNumAndStudentNum().then((res) => {
        if (res.state === 'OK') {
          this.courseNum = res.body[0];
          this.studentNum = res.body[1];
        }
      });
    },
    handleEditUserInfo() {
      openInNewTab('/portal/#/admin');
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.user-intro {
  display: flex;
  background-color: white;
  .info-content {
    width: 1200px;
    margin: 0px auto;
    height: 144px;
    display: flex;
    padding: 0px 20px;
    justify-content: space-between;
    align-items: center;
    .content-left {
      display: flex;
      align-items: center;
      .user-text-des {
        margin-bottom: 10px;
        .user-name {
          font-size: 24px;
          font-weight: @jt-font-weight-medium;
          color: #121f2c;
        }
        i {
          color: #606972;
          &:hover {
            color: #0082ff;
          }
        }
        .user-job {
          font-size: 14px;
          color: #606972;
        }
        .user-des {
          font-size: 14px;
          color: #a0a6ab;
          margin-top: 8px;
        }
      }
      .user-avatar {
        height: 96px;
        width: 96px;
        margin-right: 24px;
        border-radius: 50%;
      }
    }
    .content-right {
      display: flex;
      align-items: center;
      .total-course,
      .total-student {
        font-size: 12px;
        color: #606972;
        text-align: center;
        span {
          font-size: 30px;
          color: #121f2c;
        }
      }
      .total-student {
        margin-left: 58px;
      }
    }
  }
}
</style>
