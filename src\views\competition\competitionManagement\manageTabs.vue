<template>
  <div>
    <a-tabs v-model:activeKey="tabId" class="competition-manage-tabs" @tabClick="tabClick">
      <a-tab-pane key="1" tab="引导">
        <competitionGuide @handleTabChange="handleTabChange"></competitionGuide>
      </a-tab-pane>
      <a-tab-pane key="2" tab="比赛信息">
        <competitionMessage v-if="tabId === '2'" />
      </a-tab-pane>
      <a-tab-pane v-if="!isDisplayCompetition" key="3" tab="报名及团队">
        <competitionTeam v-if="tabId === '3'" />
      </a-tab-pane>
      <a-tab-pane v-if="!isDisplayCompetition" key="4" tab="数据及实例">
        <competitionDataCase></competitionDataCase>
      </a-tab-pane>
      <a-tab-pane v-if="!isDisplayCompetition" key="5" tab="提交及排行">
        <competitionRank />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import competitionTeam from './competitionTeam/index.vue';
import competitionMessage from './competitionMessage/index.vue';
import competitionRank from './competitionRank/index.vue';
import competitionGuide from './competitionGuide/index.vue';
import competitionDataCase from './competitionDataCase/index.vue';
import { onlyInfoDiplayCompetitionTypeIds } from './../competitionConfig/index';
import { mapState } from 'vuex';
export default {
  name: 'ManageTabs',
  components: {
    competitionTeam,
    competitionMessage,
    competitionRank,
    competitionGuide,
    competitionDataCase,
  },

  computed: {
    ...mapState('competition', ['currentManageCompetition']),
    isDisplayCompetition() {
      return onlyInfoDiplayCompetitionTypeIds.includes(this.currentManageCompetition.typeId);
    },
  },
  data() {
    return {
      tabId: this.$route.query.tabId || '1',
    };
  },
  methods: {
    tabClick(key) {
      this.tabId = key;
      this.$router.replace({ query: { tabId: key, subtabId: '1' } });
    },
    handleTabChange(item) {
      this.tabId = item.tabKey;
      this.$router.replace({ query: { tabId: item.tabKey, subtabId: item.btnKey } });
    },
  },
};
</script>

<style lang="less" scoped>
.competition-manage-tabs {
  padding: 12px 12px 0px 12px;
  :deep(.ant-tabs-bar) {
    margin-bottom: 24px;
    padding: 0px 32px;
    border-bottom: none;
    .ant-tabs-nav-container {
      border-bottom: 1px solid #e8e8e8;
    }
    .ant-tabs-tab {
      font-size: 18px;
    }
    .ant-tabs-tab:hover {
      color: #0082ff;
    }
    .ant-tabs-tab-disabled:hover {
      color: rgba(0, 0, 0, 0.25);
    }
  }
  :deep(.ant-tabs-tab-active) {
    color: #0082ff;
  }
  :deep(.ant-tabs-ink-bar) {
    background-color: #0082ff;
  }
}
</style>
