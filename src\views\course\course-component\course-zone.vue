<template>
  <div class="course-zone">
    <div class="inner">
      <h1 class="portal-title">专区公开课</h1>
      <h2 class="sub-title">李宏毅专区，带你系统入门</h2>
      <div class="recommended-content">
        <div class="recommended-row">
          <div class="recommended-left">
            <div>
              <p>{{ `${lihongyiZone.name}专区` }}</p>
              <p class="sub">{{ lihongyiZone.introduction }}</p>
              <div class="button" @click="gotoZone({ id: lihongyiZone.id, name: lihongyiZone.name })">更多课程</div>
            </div>
            <img class="zone-img" src="~@/assets/image/course/t4.png" alt="" />
          </div>
          <jt-skeleton :loading="loading" :rows="2" :rowStyle="{ height: '100%', width: '284px' }" :skeletonStyle="{ height: '100%', width: '570px', display: 'flex', 'justify-content': 'space-between' }">
            <div class="recommended-right">
              <div class="recommended-item" v-for="item in lihongyiZone.list.slice(0, 2)" :key="item.id" @click="gotoDetail(item)">
                <div class="img">
                  <img :src="item.imageUrl" alt />
                </div>
                <div class="recommended-item-text">
                  <div class="recommended-item-text-top">
                    <h6>{{ item.name }}</h6>
                    <p>{{ item.courseIntroduce }}</p>
                  </div>
                  <div class="recommended-item-text-bottom">
                    <p>{{ item.instituteName }}</p>
                    <p>{{ item.studyNum || 0 }}人学习</p>
                  </div>
                </div>
              </div>
            </div>
          </jt-skeleton>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'course-zone',
  data() {
    return {
      lihongyiZone: {
        name: '李宏毅',
        introduction: '名师领学促提升',
        id: '27',
        list: [],
      },
      loading: false,
    };
  },
  mounted() {
    this.getCourseZone();
  },
  methods: {
    gotoDetail(item) {
      this.$router.push({
        path: '/course/course-detail',
        query: {
          courseId: item.id,
          num: item.courseStudyNum,
        },
      });
    },
    gotoZone(item) {
      this.$router.push({
        path: `/course/course-zone/${item.id}`,
        query: {
          name: item.name,
        },
      });
    },
    getCourseZone() {
      this.loading = true;
      this.$POST('/course_model/web/course_student/course/coursePrefectureList', { courseId: this.lihongyiZone.id }).then((res) => {
        this.loading = false;
        if (res.state === 'OK') {
          this.lihongyiZone.list = res.body.data;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import './common.less';
// 精品推荐
.course-zone {
  background: #f4f8fa;
  padding-top: 60px;
  padding-bottom: 40px;
  .sub-title {
    margin-bottom: 48px;
  }
}
.recommended-content {
  margin-bottom: 20px;
}
.recommended-row {
  &:not(:first-child) {
    margin-top: 40px;
  }
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
// 精品推荐 第二个左侧 背景图
.recommended-left {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 590px;
  height: 322px;
  background: linear-gradient(270deg, #2e97f9 0%, #0082ff 100%);
  border-radius: 2px;
  padding: 0 32px;
  flex-shrink: 0;
  p {
    font-size: 24px;
    color: #ffffff;
    line-height: 45px;
    &.sub {
      font-size: 18px;
    }
  }
  .button {
    width: 128px;
    height: 40px;
    line-height: 40px;
    outline: none;
    background: #ffffff;
    border-radius: 2px;
    font-size: 18px;
    color: #0070db;
    margin-top: 18px;
    &:hover {
      background: #1752c7;
      border-radius: 2px;
      color: #fff;
    }
  }
}
.recommended-right {
  display: flex;
  flex-wrap: wrap;
}
.recommended-item {
  width: 285px;
  height: 322px;
  position: relative;
  * {
    transition: all 0.5s;
  }
  .img {
    width: 285px;
    height: 216px;
    overflow: hidden;
    img {
      display: block;
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;
    }
  }
  &:hover {
    box-shadow: 0px 9px 28px 8px rgba(5, 11, 23, 0.05), 0px 6px 16px 0px rgba(5, 11, 23, 0.08), 0px 3px 6px -4px rgba(5, 11, 23, 0.12);
    .recommended-item-text {
      height: 100%;
      border-color: #0082ff;
      cursor: pointer;
    }
    .recommended-item-text-top {
      p {
        height: calc(100% - 33px);
        opacity: 1;
      }
    }
  }
}
.recommended-item-text {
  width: 100%;
  height: 106px;
  background-color: #fff;
  border-radius: 2px;
  padding: 20px 24px 24px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  border-top: 4px solid transparent;
  overflow: hidden;
}
.recommended-item:hover {
  .recommended-item-text {
    h6 {
      color: @jt-primary-color;
    }
  }
}
.recommended-item-text-top {
  h6 {
    font-size: 18px;
    line-height: 25px;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  p {
    color: #606972;
    line-height: 20px;
    height: 0;
    opacity: 0;
    transition: all 0.35s;
  }
}
.recommended-item-text-bottom {
  width: calc(100% - 48px);
  display: flex;
  justify-content: space-between;
  color: #606972;
  line-height: 20px;
  position: absolute;
  left: 24px;
  bottom: 24px;
  margin: auto;
  p {
    &:nth-of-type(2) {
      color: #a0a6ab;
      line-height: 20px;
    }
  }
}
.zone-img {
  width: 239px;
  height: 215px;
}
</style>
