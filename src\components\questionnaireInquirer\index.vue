<template>
  <div v-if="isShowInquir" class="inquirer">
    <div v-if="ISSHOWIMG" class="inquirer-img" :style="{ right: rightMove }" @click="showInquirer">
      <img :src="imgSrc" alt="" @load="ISSHOWCLOSE = true" />
      <span v-show="ISSHOWCLOSE" class="close" @click.stop="closeInquirer"></span>
    </div>
  </div>
</template>

<script>
import throttle from 'lodash/throttle';
import { mapState } from 'vuex';
import { getEnvConfig } from '../../config';
import { checkLogin } from '@/keycloak';
const noSurvey = ['/online-exam', '/job/online-exam', '/register', '/exam-feedback/giveup-feedback'];
/* 该功能于2024.4.13日已下线 */
export default {
  data() {
    return {
      imgSrc: require('@/assets/image/home/<USER>'),
      rightMove: '8px',
      scrollTop: 0,
      ISSHOWIMG: true, // 是否显示问卷调查
      ISSHOWCLOSE: false, // 加载的时候x先比图片加载出来，先设置为false

      ifH5Show: window.location.href.indexOf('/h5') === -1,
      questionnaire_Entry_Url: null,
    };
  },
  computed: {
    ...mapState(['userInfo']),
    /**
     * 问卷调查
     * 1.环境变量关不显示
     * 2.开始公告不显示
     * 3.h5相关页面不显示
     * 4.比赛和注册、登录页、未登录不显示

     */
    ENVIRONMENT_SURVEY_SWITCH() {
      return getEnvConfig('SURVEY_SWITCH') === '1';
    },
    isShowInquir() {
      if (checkLogin() && this.ifH5Show && this.ENVIRONMENT_SURVEY_SWITCH && !noSurvey.includes(this.$route.path) && this.questionnaire_Entry_Url) {
        return true;
      }
      return false;
    },
  },
  // 滚动监听
  mounted() {
    window.addEventListener('scroll', this.handleScroll); // 监听页面滚动

    /**
     * 1.查看环境变量是否开始 1开启 0关闭
     *   1.1 0关闭
     *   1.2 1开启
     *     1.1.1 调questionnaireCheck接口，查看问卷是否有配置，
     *          a. res.body 为null 未配置，不显示问卷
     *          b. res.body.formkey有具体值，显示问卷
     */
    if (this.ENVIRONMENT_SURVEY_SWITCH && checkLogin()) {
      this.showInquirer('questionnaireSwitch');
    }
  },
  // 滚动重置
  beforeUnmount() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 是否有配置问卷formkey
    showInquirer(tip = null) {
      this.rightMove = '8px';
      this.$GET('/marketing/web/questionnaireCheck').then((res) => {
        if (res.state === 'OK') {
          // 做一个判断，避免一进页面走window.open，打开一个新页面
          if (tip === 'questionnaireSwitch' && res.body) {
            this.questionnaire_Entry_Url = true;
          } else if (tip === 'questionnaireSwitch' && res.body == null) {
            this.questionnaire_Entry_Url = false;
          } else {
            const query = window.btoa(`userPhone=${this.userInfo.phoneNum}&userId=${this.userInfo.id}&countBean=${res.body.tduckBeanCount}&title=1`);
            window.open(`questionnaire/#/s/${res.body.formKey}?${query}`);
          }
        }
      });
    },
    closeInquirer() {
      this.ISSHOWIMG = false;
    },
    // 获取页面滚动距离
    handleScroll: throttle(function () {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      const scroll = scrollTop - this.scrollTop;
      this.scrollTop = scrollTop;
      if (!this.ISSHOWIMG) return; // 关掉问卷调查不走以下代码
      if (scroll < 0) {
        this.rightMove = '8px';
      } else {
        this.rightMove = '-47px';
      }
    }, 500),
  },
};
</script>

<style lang="less" scoped>
.inquirer-img {
  position: fixed;
  right: 0;
  bottom: 413px;
  z-index: 1;
  transition: all 0.6s;
  cursor: pointer;
  img {
    width: 103px;
    height: 66px;
  }
  .close {
    position: absolute;
    color: #ccc;
    right: 3px;
    top: 3px;
    width: 10px;
    height: 10px;
    z-index: 10;
  }
}
</style>
