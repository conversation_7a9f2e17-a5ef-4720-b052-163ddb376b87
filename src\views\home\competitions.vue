<template>
  <section class="content-box competition-box">
    <div class="hotcomptition">
      <div class="hotcomptition-img">
        <img src="@/assets/image/home/<USER>" alt style="width: 100%" />
      </div>
    </div>
    <div class="inner" style="margin-top: -226px">
      <h1 class="portal-title protal">热门比赛</h1>
      <h2 class="sub-title protal">国内外顶尖AI大赛，等你来战</h2>

      <div class="inner">
        <jt-skeleton :loading="competitionListLoading" :skeletonStyle="{ display: 'flex', 'justify-content': 'space-between', 'margin-top': '56px' }" :rowStyle="{ width: '387px', height: '269px', 'border-radius': '4px' }" :rows="3">
          <div class="competition-list">
            <div
              class="competition-item"
              :class="{
                begin: x.flag == 1,
                running: x.flag == 2,
                stop: x.flag != 1 && x.flag != 2,
              }"
              v-for="x in competitionList.slice(0, 3)"
              :key="x.id"
              @click="goToCompetionDetail(x)"
            >
              <h6>{{ x.typeName }}</h6>
              <p>{{ dateConvert(x.startTime) }} - {{ dateConvert(x.endTime) }}</p>
              <p>{{ x.summary }}</p>
              <p v-if="x.amount !== '特别礼包'">
                奖池
                <span>¥{{ toThousandFilter(x.amount) }}</span>
              </p>
              <p v-else>
                奖池
                <span style="font-size: 24px">特别礼包</span>
              </p>
            </div>
          </div>
        </jt-skeleton>
      </div>
      <div class="link-button">
        <router-link to="/competition" class="link-arrow-right">
          更多比赛
          <em class="iconfont iconjiantouyou"></em>
        </router-link>
      </div>
    </div>
  </section>
</template>

<script>
import { GET } from '@/request';
import { dateConvert, toThousandFilter } from '@/utils/utils.js';

export default {
  name: 'home-competitions',
  data() {
    return {
      competitionList: [],
      competitionListLoading: false,
    };
  },
  mounted() {
    this.getCompetitionList();
  },
  methods: {
    dateConvert,
    toThousandFilter,
    getCompetitionList() {
      this.competitionListLoading = true;
      GET('competiton/web/list', { hotFlag: 1 }).then((res) => {
        this.competitionListLoading = false;
        if (res.state === 'OK') {
          this.competitionList = res.body.data;
        }
      });
    },
    goToCompetionDetail(item) {
      this.$router.push({
        path: '/competition/competition-detail',
        query: {
          id: item.cid,
          flag: item.flag,
          name: item.typeName,
          all: 0,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import url('./common.less');

.competition-box {
  background-color: #f4f8fa;
  padding-top: 0;
  .link-button {
    margin-top: 32px;
    text-align: center;
    font-size: 16px;
  }
}
.competition-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 56px;
}
.competition-item {
  width: 387px;
  height: 269px;
  background: #ffffff;
  box-shadow: 0px 7px 24px -4px rgba(7, 45, 82, 0.12);
  border-radius: 2px;
  padding: 40px 32px 32px;
  position: relative;
  transition: 0.5s;

  &:hover {
    cursor: pointer;
    box-shadow: 0px 9px 28px 8px rgba(5, 11, 23, 0.05), 0px 6px 16px 0px rgba(5, 11, 23, 0.08), 0px 3px 6px -4px rgba(5, 11, 23, 0.12);
    // transition: .5s;
    > h6 {
      color: #0082ff;
      transition: 0.5s;
    }
  }

  &:nth-of-type(3n + 2) {
    margin-left: 20px;
    margin-right: 19px;
  }
  &:nth-of-type(3n) ~ .competition-item {
    margin-top: 20px;
  }
  &:before {
    position: absolute;
    right: 0;
    top: 0;
    width: 62px;
    height: 24px;
    line-height: 24px;
    border-radius: 0px 2px 0px 10px;
    font-size: 12px;
    color: #ffffff;
    text-align: center;
  }
  &.running {
    &:before {
      content: '进行中';
      background: #389bff;
    }
  }
  &.stop {
    &:before {
      content: '已结束';
      background: linear-gradient(270deg, #cbcfd2 0%, #d3d8db 100%);
    }
  }
  &.begin {
    &:before {
      content: '即将开始';
      background: #48c8e4;
    }
  }
  h6 {
    height: 56px;
    font-size: 20px;
    font-weight: bold;
    line-height: 28px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  p {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    &:nth-of-type(1) {
      margin-top: 16px;
      color: #606972;
      line-height: 16px;
    }
    &:nth-of-type(2) {
      height: 44px;
      font-size: 14px;
      margin-top: 10px;
      color: #5e6871;
      line-height: 22px;
    }
    &:nth-of-type(3) {
      margin-top: 16px;
      color: #a0a6ab;
      line-height: 20px;
      display: flex;
      align-items: center;
      span {
        margin-left: 8px;
        font-size: 32px;
        font-weight: bold;
        color: #ff454d;
        line-height: 39px;
      }
    }
  }
}
// 热门比赛背景图
.hotcomptition {
  height: 290px;
  background: #178cf9;
  display: flex;
  justify-content: center;

  .hotcomptition-img {
    width: 1440px;
  }
}
</style>
