<template>
  <div class="form-container">
    <a-form ref="ruleForm" :colon="false" class="form-content" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item label="用户名" name="userName">
        <a-input v-model:value="form.userName" disabled />
      </a-form-item>
      <a-form-item label="姓名" name="fullName">
        <a-input v-model:value="form.fullName" placeholder="请输入" />
      </a-form-item>
      <a-form-item class="section-title" label="身份信息"> </a-form-item>
      <a-form-item label="身份" name="identity">
        <a-radio-group v-model="form.identity" @change="changeIdentity">
          <a-radio value="教师"> 教师 </a-radio>
          <a-radio value="学生"> 学生 </a-radio>
          <a-radio value="开发者"> 开发者 </a-radio>
        </a-radio-group>
      </a-form-item>

      <div class="extra-info">
        <a-form-item required class="sub-item school-item" :wrapper-col="subWrapperCol" label="学校">
          <a-space>
            <a-form-item style="width: 100px">
              <a-select v-model="form.area" show-search :options="areaOptions" placeholder="地区" @change="areaChange" />
            </a-form-item>
            <a-form-item v-if="form.area === '其他'" style="width: 172px" name="schoolInput"> <a-input v-model:value="form.schoolInput" :placeholder="schoolePlaceholder" /> </a-form-item>
            <a-form-item v-else style="width: 172px" name="school">
              <a-select v-model="form.school" :placeholder="schoolePlaceholder" show-search :options="schoolOptions"></a-select>
            </a-form-item>
          </a-space>
        </a-form-item>
        <a-form-item v-if="form.identity === '开发者'" class="sub-item school-item" :wrapper-col="subWrapperCol" label="工作单位">
          <a-space>
            <a-form-item style="width: 100px">
              <a-select v-model="form.companyArea" show-search :options="areaOptions" placeholder="地区" @change="companyAreaChange" />
            </a-form-item>
            <a-form-item style="width: 172px" name="company">
              <a-input v-model:value="form.company" :disabled="form.companyArea === undefined" placeholder="工作单位" />
            </a-form-item>
          </a-space>
        </a-form-item>
        <a-form-item v-if="form.identity === '教师' || form.identity === '学生'" class="sub-item" :wrapper-col="subWrapperCol" label="院系" name="faculty">
          <a-input v-model:value="form.faculty" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="form.identity === '学生'" class="sub-item" :wrapper-col="subWrapperCol" label="专业" name="major">
          <a-input v-model:value="form.major" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="form.identity === '学生'" class="sub-item" :wrapper-col="subWrapperCol" label="学号" name="stuNum">
          <a-input v-model:value="form.stuNum" placeholder="请输入" />
        </a-form-item>
      </div>
      <a-form-item class="section-title" label="账户信息"> </a-form-item>
      <a-form-item label="手机号">
        <p>{{ originPhoneNum || '--' }}</p>
      </a-form-item>
      <a-row type="flex">
        <a-col flex="416px" style="margin-right: 16px">
          <a-form-item ref="phoneNumRef" :label-col="{ span: 11 }" :wrapper-col="{ span: 13 }" class="phoneNum-item" label="修改手机号" name="phoneNum" layout="inline">
            <a-input v-model:value="form.phoneNum" style="width: 228px" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col flex="240px">
          <a-form-item ref="codeRef" name="code" label="">
            <a-space>
              <a-input
                v-model:value="form.code"
                :disabled="!checkPhoneNum"
                class="code-input"
                placeholder="请输入验证码"
                @blur="
                  () => {
                    $refs.codeRef.onFieldBlur();
                  }
                "
                @change="
                  () => {
                    $refs.codeRef.onFieldChange();
                  }
                "
              />
              <a-button :disabled="!checkPhoneNum || timmer > 0" type="primary" ghost @click="handleSendCode">{{ timmer > 0 ? `重新获取 ${timmer}` : `${sended ? '重新获取' : '获取验证码'}` }}</a-button>
            </a-space>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="邮箱" name="email">
        <a-input v-model:value="form.email" placeholder="请输入" />
      </a-form-item>
      <a-form-item class="section-title" label="其他信息"> </a-form-item>
      <a-form-item class="introduction-item" label="简介" name="introduction">
        <a-input v-model:value="form.introduction" placeholder="请填写学校、职称、擅长领域等；简介信息将显示在课程主页的教师团队信息中" type="textarea" />
        <span class="count-area">{{ `${(form.introduction || '').length}/${maxCount}` }}</span>
      </a-form-item>

      <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-button type="primary" @click="onSubmit"> 确定 </a-button>
        <a-button style="margin-left: 10px" @click="$emit('cancel')"> 取消 </a-button>
      </a-form-item>
      <a-form-item class="avatar-container" name="image">
        <img-uploader v-model="form.image" :required="teachAuth" class="uploader"></img-uploader>
      </a-form-item>
    </a-form>
  </div>
</template>
<script>
import imgUploader from '../../components/imgUploader.vue';
import { sendCode } from '@/utils/utils';
import { chineseOrLetterOrBlankRegex, numberOrLetterOrLineRegex, telephoneNumberRegex, emailRegex, schoolInputRegex } from '@/utils/regex';
import { GET } from '@/request';
import { mapState } from 'vuex';

export default {
  components: { imgUploader },
  props: { value: Object },
  emits: ['ok', 'cancel'],
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 10 },
      subWrapperCol: { span: 14 },
      other: '',
      form: {
        userName: '',
        fullName: '',
        identity: '',
        phoneNum: '',
        code: '',
        email: '',
        emailChecked: false,
        school: undefined,
        schoolInput: '',
        faculty: '',
        major: '',
        stuNum: '',
        introduction: '',
        image: '',
        area: undefined,
        companyArea: undefined,
        company: '',
      },
      timmer: 0,
      maxCount: 30,
      sended: false,
      originPhoneNum: '',
      areaOptions: [],
      schoolOptions: [],
    };
  },
  watch: {
    value() {
      this.init();
    },
  },
  created() {
    this.init();
  },
  computed: {
    schoolePlaceholder() {
      const placeholderMap = new Map([
        ['教师', '就职学校'],
        ['学生', '就读学校'],
        ['开发者', '毕业学校'],
      ]);
      return placeholderMap.get(this.form.identity);
    },
    teachAuth() {
      // 开课权限
      return this.$keycloak.idTokenParsed.DLP_USER_ALLOW_PUBLISH_OPEN_COURSE === '1';
    },
    checkPhoneNum() {
      return this.form.phoneNum && this.$refs.phoneNumRef && this.$refs.phoneNumRef.validateState !== 'error';
    },
    rules() {
      return {
        userName: [{ required: true, message: '', trigger: ['blur', 'change'] }],
        identity: [{ required: true, message: '请选择身份', trigger: ['blur', 'change'] }],
        fullName: [
          { required: this.teachAuth, message: '请输入', trigger: ['blur', 'change'] },
          { max: 30, min: 0, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
        ],
        school: [{ required: true, message: '请选择/输入学校', trigger: ['blur', 'change'] }],
        schoolInput: [
          { required: true, message: '请选择/输入学校', trigger: ['blur', 'change'] },
          { min: 0, max: 50, message: '50个字符以内的中英文，可包含空格、小括号', trigger: ['blur', 'change'] },
          { pattern: schoolInputRegex, message: '50个字符以内的中英文，可包含空格、小括号', trigger: ['blur', 'change'] },
        ],
        faculty: [
          { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
        ],
        major: [
          { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
        ],
        stuNum: [
          { min: 0, max: 20, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
          { pattern: numberOrLetterOrLineRegex, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
        ],
        phoneNum: [
          // { required: true, message: '请输入', trigger: ['blur', 'change'] },
          // { pattern: telephoneNumberRegex, message: '请输入正确格式手机号', trigger: ['blur', 'change'] },
          { validator: this.phoneNumValidator, trigger: ['blur', 'change'] },
        ],
        code: [
          // { required: true, message: '请输入验证码', trigger: ['blur', 'change'] },
          { validator: this.codeValidator, trigger: ['blur', 'change'] },
        ],
        email: [
          { required: false, message: '请输入', trigger: ['blur', 'change'] },
          { pattern: emailRegex, message: '请输入正确格式邮箱', trigger: ['blur', 'change'] },
        ],
        introduction: [
          { required: this.teachAuth, message: '请输入', trigger: ['blur', 'change'] },
          { min: 0, max: 30, message: '不超过30个字符', trigger: ['blur', 'change'] },
        ],
        image: [{ required: this.teachAuth, message: '请上传头像', trigger: 'change' }],
        company: [
          { required: false, message: '请输入工作单位', trigger: ['blur', 'change'] },
          { min: 0, max: 50, message: '50字以内的中英文、空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '50字以内的中英文、空格', trigger: ['blur', 'change'] },
        ],
      };
    },
    ...mapState(['suanLiState']),
  },
  methods: {
    phoneNumValidator(rule, value, callback) {
      if (!value) {
        if (this.form.code) {
          callback(new Error('请输入手机号'));
        } else {
          callback();
        }
      } else if (!telephoneNumberRegex.test(value)) {
        callback(new Error('请输入正确格式手机号'));
      } else {
        callback();
      }
    },
    codeValidator(rule, value, callback) {
      if (!value) {
        if (this.form.phoneNum) {
          callback(new Error('请输入验证码'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    changeIdentity(e) {
      switch (e.target.value) {
        case '教师':
          this.form.faculty = '';
          break;
        case '学生':
          this.form.faculty = '';
          this.form.major = '';
          this.form.stuNum = '';
          break;
        default:
          break;
      }
    },
    init() {
      this.form = { ...this.value };
      this.originPhoneNum = this.form.phoneNum;
      this.form.phoneNum = '';
      if (this.value.area == undefined) {
        this.form.schoolInput = this.value.school;
      }
      this.getAreaOptions();
      this.getArea();
      this.formatDefaultValue();
    },
    formatDefaultValue() {
      this.form.school = this.form.school || undefined;
      this.form.area = this.form.area || undefined;
      this.form.companyArea = this.form.companyArea || undefined;
    },
    getArea() {
      if (!this.form.school) {
        return;
      }
      GET('/keycloak/web/user/getProvinceBySchool', { school: this.form.school }).then((res) => {
        this.form = { ...this.form, area: res.body && res.body.length > 0 ? res.body : '其他' };
        if (this.form.area !== '其他') {
          this.getSchoolOptions();
        }
      });
    },
    handleSendCode() {
      sendCode(this.form.phoneNum).then((res) => {
        if (!res.errorCode) {
          this.sended = true;
          this.timmer = 60;
          this.$message.success('发送成功');
          this.timmerDecrease();
        } else {
          this.$message.error(res.errorMessage || '发送失败');
        }
      });
    },
    timmerDecrease() {
      if (this.timmer > 0) {
        this.timmer--;
        setTimeout(() => {
          this.timmerDecrease();
        }, 1000);
      }
    },
    onSubmit() {
      this.$refs.ruleForm
        .validate()
        .then(() => {
          const form = { ...this.form };
          if (form.identity === '开发者') {
            // form.school = '';
            form.faculty = '';
            form.major = '';
            form.stuNum = '';
          } else if (form.identity === '教师') {
            form.major = '';
            form.stuNum = '';
          }
          this.$emit('ok', form);
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    areaChange(val) {
      this.form.area = val;
      this.form.school = undefined;
      if (val !== '其他') {
        this.getSchoolOptions();
      }
    },
    companyAreaChange(val) {
      this.form.companyArea = val;
    },
    getAreaOptions() {
      GET('/keycloak/web/user/getProvince', {}).then((res) => {
        this.areaOptions = res.body.map((item) => {
          return {
            value: item,
            label: item,
          };
        });
        this.areaOptions.push({
          value: '其他',
          label: '其他',
        });
      });
    },
    getSchoolOptions() {
      GET('/keycloak/web/user/getSchool', { province: this.form.area }).then((res) => {
        this.schoolOptions = res.body.map((item) => {
          return {
            value: item,
            label: item,
          };
        });
      });
      this.form.schoolInput = '';
    },
  },
};
</script>

<style lang="less" scoped>
.form-container {
  .form-content {
    position: relative;
  }
}
.uploader {
  width: 240px;
}
.section-title {
  margin-bottom: 0;
  margin-top: 28px;
  :deep(label) {
    padding-left: 8px;
    border-left: 4px solid #0082ff;
  }
}
.avatar-container {
  position: absolute;
  top: 0;
  right: 128px;
  :deep(.ant-form-item-control-wrapper) {
    width: 100%;
    text-align: center;
  }
}
.code-input {
  width: 120px;
}
.extra-info {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  max-height: 136px;
  padding-left: 116px;
  .sub-item {
    width: 480px;
  }
}
.introduction-item {
  position: relative;
  .count-area {
    position: absolute;
    top: -10px;
    right: 10px;
  }
}
.school-item {
  margin-bottom: 0;
  :deep(.ant-form-item-children) {
    display: flex;
  }
  :deep(.ant-form-item-label) {
    line-height: 32px;
  }
}
</style>
