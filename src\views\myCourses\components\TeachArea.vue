<template>
  <div class="teach-area">
    <div class="tab-btns">
      <div style="display: flex" class="btns-container">
        <div v-for="btn in btns" :key="btn.key" :class="activeKey == btn.key ? ['tab-btn', 'active-btn'] : ['tab-btn']" @click="handleBtnClick(btn.key)">{{ btn.name }}</div>
        <!-- 现在这里没有封闭课了，所以这个入口应该是没用的，不等于1是封闭课的意思 -->
        <div v-if="currentActiveCourse.courseFlag != '1'" :key="'study-track'" :class="activeKey == 'study-track' ? ['tab-btn', 'course-track-btn', 'active-btn'] : ['tab-btn', 'course-track-btn']" @click="handleBtnClick('study-track')">学习跟踪</div>
      </div>
      <a-button v-if="activeKey === 'course-content' && isNotOpenAndPublishedCourse" type="primary" style="width: 112px" @click="handleAddCourse"><span class="add-icon">+</span>新增课节</a-button>
    </div>
    <course-introduction v-show="activeKey === 'course-introduction'"></course-introduction>
    <course-content v-show="activeKey === 'course-content'" ref="courseContent"></course-content>
    <course-resource v-show="activeKey === 'course-resource'"></course-resource>
    <study-track v-show="activeKey === 'study-track'" @changeTab="changeTab" @changeParentTb="changeParentTb"></study-track>
    <router-view />
  </div>
</template>

<script lang="jsx">
import { mapState } from 'vuex';
import CourseIntroduction from './CourseIntroduction.vue';
import CourseContent from './CourseContent.vue';
import CourseResource from './CourseResource.vue';
import StudyTrack from './StudyTrack.vue';

export default {
  name: 'TeachArea',
  components: {
    CourseContent,
    CourseIntroduction,
    CourseResource,
    StudyTrack,
  },
  emits: ['teachAreaKeyChange', 'handleTabChange'],
  data() {
    return {
      btns: [
        { name: '课程介绍', key: 'course-introduction' },
        { name: '教学内容', key: 'course-content' },
        { name: '课程资源', key: 'course-resource' },
      ],
    };
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
    isNotOpenAndPublishedCourse() {
      return !(this.currentActiveCourse.courseFlag == '1' && this.currentActiveCourse.coursePublish == '1');
    },
    activeKey() {
      return this.$route.params.subTab;
    },
  },
  watch: {
    '$route.params.tab'() {
      this.initSubTab();
    },
  },
  created() {
    this.initSubTab();
  },
  methods: {
    handleBtnClick(activeKey) {
      this.$router.push({ params: { ...this.$route.params, subTab: activeKey } });
    },
    changeTab(key) {
      this.$emit('teachAreaKeyChange', key);
    },
    changeParentTb() {
      this.$emit('handleTabChange', { tabKey: '3', btnKey: '2' });
    },
    handleAddCourse() {
      this.$refs.courseContent.showCourseModal('add');
    },
    initSubTab() {
      if (this.$route.params.tab === 'teach-area') {
        const subTab = this.$route.params.subTab;
        if (!subTab) {
          this.$router.replace({ params: { ...this.$route.params, subTab: 'course-introduction' } });
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.teach-area {
  position: relative;
  padding: 0px 32px 32px;
  .tab-btns {
    display: flex;
    justify-content: space-between;
    margin: 24px 0;
    cursor: pointer;
    // .publish-btn {
    //   border-color: #0082ff;
    //   color: #0082ff;
    // }
    .add-icon {
      padding-right: 8px;
      font-size: 16px;
    }
    .btns-container .tab-btn:nth-of-type(1) {
      border-radius: 2px 0px 0px 2px;
    }

    .btns-container .tab-btn:last-child {
      border-radius: 0px 2px 2px 0px;
      border-right-width: 1px;
    }

    .tab-btn {
      border: 1px solid #cbcfd2;
      text-align: center;
      width: 160px;
      height: 32px;
      line-height: 30px;
      color: #606972;
      border-right-width: 0;
      position: relative;
    }
    .active-btn {
      color: #0082ff;
      background: #f0f8ff;
      border-color: #0082ff;
      &:not(.course-track-btn):before {
        position: absolute;
        top: -1px;
        right: -1px;
        bottom: -1px;
        display: block;
        box-sizing: content-box;
        width: 1px;
        height: 30px;
        padding: 1px 0px;
        background-color: #0082ff;
        content: '';
        z-index: 1;
      }
    }
  }
}
</style>
