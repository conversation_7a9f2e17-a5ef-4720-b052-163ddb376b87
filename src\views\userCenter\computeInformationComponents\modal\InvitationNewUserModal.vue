<template>
  <div>
    <a-modal v-model:open="open" dialog-class="pwd-form-dlg-container" :footer="null" :title="null" @cancel="closeModal">
      <a-spin :spinning="inviteModalLoading">
        <div class="invite-content">
          <div class="invite-title">
            <img src="@/assets/image/compute/title.png" alt="" />
          </div>
          <div class="invite-banner">
            <p class="banner-title">每邀请一位用户注册</p>
            <p class="banner-title" style="margin-top: 12px">
              即可赢取
              <span class="invite-count">{{ inviteInfo.beanCount || 0 }}</span>
              个
              <img src="@/assets/image/compute/bean.png" alt="" />
              算力豆
            </p>
            <div class="button-container">
              <a-button type="primary" @click="handleCopyLink">复制注册邀请链接</a-button>
            </div>
          </div>
          <div class="invitees-container">
            <p v-if="inviteInfo.inviteeTotal > 0" class="text">
              您已邀请
              <span class="invitees-count">{{ inviteInfo.inviteeTotal || 0 }}</span>
              人，赢取了
              <span class="invitees-count">{{ inviteInfo.beanTotal || 0 }}</span>
              个算力豆<template v-if="!inviteBeanLimit">，<a-button type="link" class="link-btn" @click="handleCopyLink">继续邀请</a-button></template>
            </p>
            <p v-else class="text">
              您尚未邀请用户，
              <a-button type="link" class="link-btn" @click="handleCopyLink">立即邀请</a-button>
            </p>
            <div class="invitees-list-container">
              <ul v-if="invitees.length > 0" class="invitees-list">
                <li v-for="(item, index) in invitees" :key="index">
                  <div class="left">
                    <div class="index-num">
                      <span>{{ indexTransfer(index) }}</span>
                    </div>
                    <div>
                      <span class="avatar">
                        <img v-if="item.name" :src="item.avatar || require('@/assets/image/avatar_big.png')" alt="" />
                        <span v-else class="avatar-empty">?</span>
                      </span>
                    </div>
                    <div>
                      <p class="name">{{ nameTransfor(item.name) }}</p>
                      <p class="submit-time">{{ item.submitTime }}</p>
                    </div>
                  </div>
                  <div class="right">
                    <span class="count">+{{ item.inviterBeanCount }}</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div style="width: 100%; height: 1px; background-color: #cbcfd2; margin: 16px 0"></div>
          <div class="invite-prompt">
            <p>活动须知：</p>
            <p>1、每成功邀请一位新用户注册，您将赢取{{ inviteInfo.beanCount || 0 }}个算力豆，对方将赢取{{ inviteInfo.inviteeBeanCount || 0 }}个算力豆，算力豆有效期均为{{ inviteInfo.period || 0 }}天；通过邀请新用户注册累计获得的算力豆上限为{{ inviteInfo.inviterBeanLimit || 0 }}个，达到上限后，不可继续参加本活动</p>
            <p>2、本活动最终解释权归九天·毕昇所有</p>
          </div>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import { handleCopy } from '@/utils/utils.js';

export default {
  props: {
    inviteDlgVisible: {
      type: Boolean,
      default: false,
    },
    inviteModalLoading: {
      type: Boolean,
      default: false,
    },
    invitees: {
      type: Array,
      required: true,
    },
    inviteInfo: {
      type: Object,
      required: true,
    },
  },
  emits: ['closeInvitationNewUserModal'],
  data() {
    return {
      open: false,
    };
  },
  computed: {
    inviteBeanLimit() {
      if (this.inviteInfo.beanTotal === undefined && this.inviteInfo.inviterBeanLimit === undefined) {
        return true;
      }
      return this.inviteInfo.beanTotal >= this.inviteInfo.inviterBeanLimit;
    },
  },
  watch: {
    inviteDlgVisible(val) {
      this.open = val;
    },
  },
  created() {
    this.open = this.inviteDlgVisible;
  },
  methods: {
    indexTransfer(index) {
      let innnerIndex = index + 1;
      return innnerIndex < 10 ? '0' + innnerIndex : innnerIndex;
    },
    nameTransfor(name) {
      return name;
    },
    handleCopyLink() {
      if (this.inviteBeanLimit) {
        this.$message.warning('您累计获得的算力豆已达上限，无法继续邀请注册');
        return;
      }
      const link = `分享给你一个宝藏 AI 学习和实战平台“九天·毕昇”，注册即可免费赢取 ${this.inviteInfo.inviteeBeanCount} 个算力豆（${Number(this.inviteInfo.inviteeBeanCount) / 20} 小时 V100 使用时长），还可助我赢取 ${this.inviteInfo.beanCount} 个算力豆哦~ ${location.origin}${location.pathname}#/invite-register?token=${this.inviteInfo.token}`;
      try {
        handleCopy(link);
        this.$message.success('复制成功');
      } catch (error) {
        this.$message.error('复制失败');
      }
    },
    closeModal() {
      this.$emit('closeInvitationNewUserModal');
    },
  },
};
</script>
<style lang="less">
@import '~@/assets/styles/index.less';
.invite-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 432px;
  margin: 0 auto;
  .invite-title {
    margin: 20px 0;
    img {
      width: 364px;
      height: 24px;
    }
  }
  .invite-banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 432px;
    height: 160px;
    padding: 24px;
    background: #e4f2ff;
    border-radius: 8px;
    .banner-title {
      font-size: 16px;
      font-weight: 400;
      color: #606972;
      line-height: 22px;
      img {
        width: 36px;
        height: 36px;
      }
      .invite-count {
        font-size: 24px;
        font-weight: @jt-font-weight-medium;
        color: #f17506;
      }
    }
    .button-container {
      margin-top: 12px;
    }
  }
  .invitees-container {
    width: 100%;
    .text {
      padding-top: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #606972;
      line-height: 20px;
      .invitees-count {
        color: #f17506;
      }
    }
    .invitees-list {
      max-height: 140px;
      margin-top: 16px;
      overflow: auto;
      // display: flex;
      li {
        display: flex;
        justify-content: space-between;
        &:not(:last-child) {
          margin-bottom: 12px;
        }
        .left {
          display: flex;
        }

        .avatar {
          img {
            width: 40px;
            height: 40px;
            margin: 0 12px;
          }
        }
        .index-num {
          display: flex;
          align-items: center;
          font-size: 14px;
          font-weight: normal;
          color: #a0a6ab;
        }
        .submit-time {
          font-size: 12px;
          font-weight: 400;
          color: #a0a6ab;
        }

        .right {
          display: flex;
          align-items: center;
          .count {
            font-size: 12px;
            font-weight: 400;
            color: #f17506;
            margin-right: 8px;
          }
        }
      }
    }
  }
  .invite-prompt {
    p {
      margin-bottom: 8px;
      font-size: 12px;
      font-weight: 400;
      color: #606972;
      line-height: 18px;
    }
  }
}
.link-btn {
  padding: 0;
}
</style>
