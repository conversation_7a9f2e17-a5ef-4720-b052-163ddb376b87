<template>
  <div class="main-container">
    <bread-crumb class="bread-crumb-container" :value="breadcrumb">
      <div>
        <a-button :disabled="!havePre" size="small" type="primary" ghost class="btn mr16" @click="handleNavigate(-1)"> <jt-icon type="iconleft" /> 上一次 </a-button>
        <a-button :disabled="!haveNext" size="small" type="primary" ghost class="ml16" @click="handleNavigate(1)"> 下一次 <jt-icon type="iconright" /> </a-button>
      </div>
    </bread-crumb>
    <div class="content">
      <a-spin :spinning="loading">
        <div class="item-container">
          <jupyter-viewer v-if="projectInfo.projectId" :project-info="projectInfo" class="large-size" type="homework" @instanceIdChange="instanceIdChange">
            <template #extraToolbar>
              <a-space class="handle-homework">
                <a-space class="end-time" :class="getEndTimeClass">
                  <jt-icon type="icontime-circle" style="font-size: 16px" />
                  <span v-if="outdated" style="margin-right: -8px">提交已经截止：</span>
                  <span v-else style="margin-right: -8px"> 提交截止时间：</span>
                  <span>{{ detail.endTime }}</span>
                </a-space>
                <p>
                  <a-tag v-if="getSubmitedStatusText !== '未提交'" class="learn-status-tag" :class="getSubmitedStatusClass">{{ getSubmitedStatusText }}</a-tag>
                  <span>{{ detail.submitTime }}</span>
                </p>
                <a-space>
                  <a-button v-if="needAttachment && hasFile && outdated && getSubmitedStatus">
                    <a :href="detail.filePath" :download="detail.fileName">下载附件</a>
                  </a-button>
                  <template v-if="getDeletedStatus">
                    <a-tooltip v-if="!outdated" title="教师已删除作业">
                      <a-button disabled type="primary" @click="handleSubmit">提交作业</a-button>
                    </a-tooltip>
                    <a-button v-if="outdated && !getSubmitedStatus" disabled type="primary" @click="handleSubmit">提交作业</a-button>
                    <a-tooltip v-if="outdated && getSubmitedStatus" title="教师已删除作业">
                      <a-button type="primary" disabled @click="handleCheckScore">查看成绩</a-button>
                    </a-tooltip>
                  </template>
                  <template v-else>
                    <a-tooltip v-if="outdated && getSubmitedStatus" :title="reviewed ? null : '教师未发布成绩'">
                      <a-button :disabled="!reviewed" type="primary" @click="handleCheckScore">查看成绩</a-button>
                    </a-tooltip>
                    <a-button v-else-if="outdated && !getSubmitedStatus" disabled type="primary" @click="handleSubmit">提交作业</a-button>
                    <a-tooltip v-else :title="!instanceIdAvailable ? '请启动实例，作答后提交' : null">
                      <a-button :disabled="!instanceIdAvailable" type="primary" @click="handleSubmit">提交作业</a-button>
                    </a-tooltip>
                  </template>
                </a-space>
              </a-space>
            </template>
            <template #popoverContent>
              <p class="popover-container">页面关闭后10分钟作业实例将自动停止；</p>
              <p class="popover-container">最多只能同时运行4个课节或作业实例；</p>
              <p class="popover-container">当启动多于4个实例时，则自动关闭启动时间较久的已运行实例。</p>
            </template>
          </jupyter-viewer>
        </div>
      </a-spin>
    </div>
    <submit-dialog :visible="submitDialogVisible" :file-name="detail.fileName" :assignment-id="assignmentId" :course-id="courseId" :need-attachment="needAttachment" :instance-id="instanceId" @ok="submitOk" @cancel="submitDialogVisible = false"></submit-dialog>
    <score-detail :level="level" :score="score" :remark="remark" :visible="scoreDetailVisible" @cancel="scoreDetailVisible = false"></score-detail>
  </div>
</template>

<script>
import { checkLogin } from '@/keycloak';
import breadCrumb from '../../components/breadCrumb';
import jupyterViewer from '../../components/jupyterViewer';
import submitDialog from '@/components/homework/submitDialog.vue';
import scoreDetail from '@/components/course-overview/score-detail.vue';
import { GET, POST } from '@/request';
import { OUTDATED_STATUS, SUBMIT_STATUS, ACCESSORY, DELETED_STATUS } from '@/components/course-overview/homework-maps';
import { downloadFile } from '@/utils/file';
import { checkAuth } from '@/utils/utils';

const breadcrumbData = [
  { name: '学习', path: '/course' },
  { name: '我学习的公开课', path: '/course/my-course' },
];

export default {
  components: {
    breadCrumb,
    jupyterViewer,
    submitDialog,
    scoreDetail,
  },
  data() {
    return {
      courseId: null,
      breadcrumb: [],
      loading: false,
      detail: {},
      loaded: false,
      homeworkList: [],
      assignmentId: null,
      submitDialogVisible: false,
      scoreDetailVisible: false,
      score: 0,
      level: '',
      remark: '',
      instanceId: '',
    };
  },
  computed: {
    haveNext() {
      return this.currentIndex < this.homeworkList.length - 1;
    },
    havePre() {
      return this.currentIndex > 0;
    },
    currentIndex() {
      return this.homeworkList.findIndex((item) => +item.id === +this.assignmentId);
    },
    needAttachment() {
      return ACCESSORY[this.detail.accessory] === '有附件';
    },
    hasFile() {
      return this.detail.fileName;
    },
    projectInfo() {
      return {
        projectId: this.detail.projectId,
        spec: this.detail.spec,
        instanceModel: this.detail.instanceModel,
        instanceName: this.detail.projectName,
      };
    },
    reviewed() {
      return this.detail.score !== null && this.detail.score !== undefined;
    },
    outdated() {
      return OUTDATED_STATUS[this.detail.abort] === '已截止';
    },
    getEndTimeClass() {
      return OUTDATED_STATUS[this.detail.abort] === '已截止' ? 'end-time-outdated' : '';
    },
    getSubmitedStatus() {
      return SUBMIT_STATUS[this.detail.submitStatus] === '已提交';
    },
    getDeletedStatus() {
      // 教师是否删除flag
      return DELETED_STATUS[this.detail.teacherDeleteFlag] === '已删除';
    },
    getSubmitedStatusClass() {
      return SUBMIT_STATUS[this.detail.submitStatus] === '已提交' ? 'finished' : '';
    },
    getSubmitedStatusText() {
      return SUBMIT_STATUS[this.detail.submitStatus] || '未提交';
    },
    instanceIdAvailable() {
      return !!this.instanceId;
    },
  },
  watch: {
    // 如果路由有变化，会再次执行该方法
    $route: 'init',
  },
  created() {
    if (!checkLogin()) {
      return;
    }
    this.init();
  },
  methods: {
    async init() {
      this.courseName = this.$route.query.courseName;
      this.courseId = this.$route.params.courseId;
      this.assignmentId = this.$route.params.assignmentId;
      this.breadcrumb = [...breadcrumbData];
      this.breadcrumb.push({
        name: '课程主页',
        path: `/course/course-overview/${this.courseId}`,
      });
      this.loading = true;
      await this.startAssignment();
      await this.getHomeworkDetail();
      await this.getFilePath();
      await this.getHomeworkList();
      this.breadcrumb.push({
        name: `${this.courseName}：${this.detail.assignmentName}`,
      });
      this.loading = false;
    },

    async startAssignment() {
      const obj = {
        courseId: +this.courseId,
        assignmentId: this.assignmentId,
      };

      await POST('/course_model/web/course_student/student/studentAssignmentAdd', obj);
    },

    async getHomeworkDetail() {
      //this.detail = {};
      const res = await GET(
        '/course_model/web/course_student/student/studentAssignmentCase',
        {
          courseId: this.courseId,
          assignmentId: this.assignmentId,
        },
        { useError: false }
      );
      if (res.state === 'OK') {
        this.detail = res.body;
      } else {
        if (!checkAuth(res.errorCode, '-802', '/course')) {
          return;
        }
      }
    },
    handleNavigate(offset) {
      const target = this.homeworkList[this.currentIndex + offset];
      this.$router.push(`/course/homework-detail/${this.courseId}/${target.id}?courseName=${this.courseName}`);
      this.detail = {};
    },
    async getHomeworkList() {
      const res = await GET('/course_model/web/course_student/course/studentCourseAssignment', { courseId: this.courseId }, { useError: false });
      if (res.state === 'OK') {
        this.homeworkList = res.body;
      } else {
        if (!checkAuth(res.errorCode, '-802', '/course')) {
          return;
        }
      }
    },
    handleDownloadAttachment() {
      const obj = {
        assignmentId: this.assignmentId,
        fileName: this.detail.fileName,
      };
      GET('/course_model/web/course_student/fileupload/downurl', obj, { useLoading: true }).then((res) => {
        downloadFile(res.body.downloadUrl, this.detail.fileName);
      });
    },
    getFilePath() {
      if (!(this.needAttachment && this.hasFile)) {
        return;
      }
      const obj = {
        assignmentId: this.assignmentId,
        fileName: this.detail.fileName,
      };
      GET('/course_model/web/course_student/fileupload/downurl', obj, { useLoading: true }).then((res) => {
        this.detail.filePath = res.body.downloadUrl;
      });
    },
    handleCheckScore() {
      this.scoreDetailVisible = true;
      this.remark = this.detail.remark;
      this.score = this.detail.score;
      this.level = this.getLevel(this.score);
    },
    getLevel(score) {
      if (!score) {
        return '';
      }
      if (score >= 90) {
        return 'good';
      } else if (score >= 60) {
        return 'normal';
      } else {
        return 'bad';
      }
    },
    handleSubmit() {
      this.submitDialogVisible = true;
    },
    submitOk() {
      this.submitDialogVisible = false;
      this.getHomeworkDetail();
    },
    instanceIdChange(val) {
      this.instanceId = val;
    },
  },
};
</script>

<style lang="less" scoped>
@import './homework-detail.less';
</style>
