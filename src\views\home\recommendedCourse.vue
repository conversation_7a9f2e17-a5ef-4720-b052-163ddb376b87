<template>
  <section class="content-box recommended-content">
    <div class="inner">
      <h1 class="portal-title">学习课程</h1>
      <h2 class="sub-title">联手名师名校倾力打造，Notebook交互教学，一站式学习体验</h2>
      <div ref="recommendCourseBox" class="recommended-box">
        <div class="recommended-title">
          课程推荐
          <router-link :to="'/course/course-list'" class="link-arrow-right">
            更多课程
            <em class="iconfont iconjiantouyou"></em>
          </router-link>
        </div>
        <jt-skeleton :loading="list.length === 0" :rows="4" :skeletonStyle="{ display: 'flex', 'justify-content': 'space-between' }" :rowStyle="{ height: '236px', width: '285px' }">
          <div class="recommended-list">
            <div class="recommended-item recommended-img" v-for="x in list" :key="x.id" @click="gotoDetail(x)">
              <img :src="x.courseImage" alt />
              <p>{{ x.courseName }}</p>
              <p>
                {{ x.instituteName }}
                <span>{{ x.courseStudyNum || 0 }}人学习</span>
              </p>
            </div>
          </div>
        </jt-skeleton>
      </div>
      <div ref="excellentTeacher" class="recommended-box teacher-box">
        <div class="recommended-title">明星讲师</div>
        <jt-skeleton :loading="excellentTeacherList.length === 0" :rows="4" :skeletonStyle="{ display: 'flex', 'justify-content': 'space-between' }" :rowStyle="{ height: '305px', width: '285px' }">
          <div class="recommended-list swiper-container">
            <div class="swiper-wrapper">
              <div class="recommended-item swiper-slide" v-for="x in excellentTeacherList" :key="x.id">
                <img :src="x.teacherImage" alt />
                <p>{{ x.teacherName }}</p>
                <p>{{ x.teacherDesc }}</p>
              </div>
            </div>
          </div>
          <div class="teacher-buttons">
            <div class="button prev iconfont iconjiantouzuo"></div>
            <div class="button next iconfont iconjiantouyou"></div>
          </div>
        </jt-skeleton>
      </div>
    </div>
  </section>
</template>

<script>
import Swiper from 'swiper';
import 'swiper/css/swiper.css';
import { GET } from '@/request';
import JtSkeleton from '@/components/skeleton';
const SHOW_COUNT = 4;

export default {
  name: 'home-recommended-course',
  components: {
    JtSkeleton,
  },
  data() {
    return {
      list: [],
      excellentTeacherList: [],
    };
  },
  mounted() {
    this.getList();
    this.getExcellentTeacherList().then(() => {
      this.initSwiper();
    });
  },
  methods: {
    initSwiper() {
      this.swiper = new Swiper('.swiper-container', {
        slidesPerView: 4,
        observer: true,
        observeParents: true,
        // 如果需要前进后退按钮
        navigation: {
          el: '.teacher-buttons',
          nextEl: '.next',
          prevEl: '.prev',
        },
      });
    },
    getList() {
      // 参数待优化，requestId参数现在弃用了
      GET('/course_model/web/home/<USER>/boutique', { requestId: this.$store.state.requestId }).then((res) => {
        if (res.state === 'OK') {
          this.list = res.body.slice(0, SHOW_COUNT);
        }
      });
    },
    gotoDetail(item) {
      this.$router.push({
        path: '/course/course-detail',
        query: {
          courseId: item.courseId,
          num: item.courseStudyNum,
        },
      });
    },
    getExcellentTeacherList() {
      return GET('/course_model/web/excellent/teacher/list', { requestId: this.$store.state.requestId }).then((res) => {
        if (res.state === 'OK') {
          this.excellentTeacherList = res.body;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import url('./common.less');
.recommended-content {
  background: #f4f8fa;
}
.recommended-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  border-left: 6px solid #0082ff;
  line-height: 20px;
  margin-bottom: 20px;
  padding-left: 8px;
}
.recommended-list {
  display: flex;
  &:nth-of-type(1) {
    margin-bottom: 48px;
  }
}
.recommended-item {
  cursor: pointer;
  width: 285px !important;
  height: 326px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  margin-right: 20px;
  &:last-of-type {
    margin-right: 0;
  }
  img {
    width: 285px;
    height: 213px;
  }
  p {
    padding: 0 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &:nth-of-type(1) {
      font-size: 18px;
      margin-top: 24px;
    }
    &:nth-of-type(2) {
      margin-top: 12px;
      color: #606972;
      display: flex;
      justify-content: space-between;
      span {
        color: #a0a6ab;
      }
    }
  }
}
.recommended-img img {
  width: 285px;
  height: 217px;
}
.recommended-img:hover {
  box-shadow: 0px 9px 28px 8px rgba(5, 11, 23, 0.05), 0px 6px 16px 0px rgba(5, 11, 23, 0.08), 0px 3px 6px -4px rgba(5, 11, 23, 0.12);

  > p:nth-of-type(1) {
    color: #0082ff;
    transition: 0.5s;
  }
}
.teacher-box {
  margin-top: 48px;
  .recommended-item {
    cursor: default;
    height: 305px;
    padding: 48px 0;
    img {
      width: 120px;
      height: 120px;
      margin: auto;
      display: block;
    }
    p {
      margin-top: 32px;
      text-align: center;
      &:nth-of-type(2) {
        margin-top: 12px;
        display: block;
      }
    }
  }
}
.teacher-buttons {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  .button {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
    width: 28px;
    height: 28px;
    background: #cbcfd2;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0.4;
    &:hover {
      opacity: 1;
      transition: all 0.8s;
    }

    &:nth-of-type(1) {
      margin-right: 20px;
    }
  }
}
</style>
