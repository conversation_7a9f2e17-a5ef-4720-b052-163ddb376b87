import { GET, POST } from '@/request';
// 控制台接口
export default {
  getCourseInstance_repeat: (data) => GET('/dp_platform/resource/course/instance_repeat_byprojectid', data),
  getCompetitionInstance_repeat: (data) => GET('/dp_platform/resource/competition/instance_state_by_project', data),
  getDatasetInstance_repeat: (data) => GET('/dp_platform/resource/dataset/instance_repeat_byprojectid', data),
  getJobInstance_repeat: (data) => GET('/dp_platform/resource/job/instance_state_by_project', data),
  // 首页 开发者数量
  getUserCount: (data) => POST('/storage/web/storage_user_count', data),
  // 各项目创建过实例的人数
  getUserCountByproject: (data, paramsSerializerValue) => GET('/dp_platform/web/resource/all/instance_user_count_byproject', data, paramsSerializerValue),
  getInstanceList: (data) => POST(`/dp_platform/resource/course/instance_list`, data),
  stopInstance: (data) => POST(`/dp_platform/resource/course/stop_instance`, data),
  startInstance: (data) => POST(`/dp_platform/resource/course/run_instance`, data),
  // 根据项目id验证实例是否存在
  validInstanceByProject: (data) => GET('/dp_platform/resource/course/instance_state_by_project', data),
};
