<template>
  <div class="jt-validate-input">
    <a-input v-model:value="inputValue" v-bind="$attrs" :class="{ hasError: showValidatorTxt }" @change="inputChange"></a-input>
    <span v-if="showValidatorTxt" class="validate-txt">请输入{{ maxNumber }}个以内的字符</span>
  </div>
</template>

<script>
export default {
  name: 'ValidateInput',
  props: {
    value: {
      type: String,
      default: '',
    },
    maxNumber: {
      type: Number,
      default: 20,
    },
  },
  emits: ['input'],
  data() {
    return {
      inputValue: this.value,
      showValidatorTxt: false,
    };
  },
  methods: {
    inputChange(e) {
      this.$emit('input', e.target.value);
      this.validator();
    },
    validator() {
      if (this.inputValue.length > this.maxNumber || !this.inputValue.trim()) {
        this.showValidatorTxt = true;
        return false;
      } else {
        this.showValidatorTxt = false;
        return true;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.jt-validate-input {
  position: relative;
  display: inline-block;
  .validate-txt {
    position: absolute;
    left: 0px;
    bottom: -24px;
    color: #f5222d;
  }
  .hasError {
    border-color: #f5222d;
    &:focus {
      box-shadow: 0 0 0 2px rgba(245, 34, 45, 20%);
    }
  }
}
</style>
