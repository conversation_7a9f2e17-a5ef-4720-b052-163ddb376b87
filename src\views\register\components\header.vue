<template>
  <div>
    <a-layout-header class="header">
      <div @click="handleNavigateToHome('/')" class="left">
        <img :src="logo" alt="" />
      </div>
      <div class="right" v-if="showBackToHome">
        <ul class="header-menu">
          <li>
            <a-button size="large" type="primary" ghost @click="handleNavigateToHome">返回首页</a-button>
          </li>
        </ul>
      </div>
    </a-layout-header>
  </div>
</template>

<script>
export default {
  name: 'invite-register-header',
  props: {
    showBackToHome: Boolean,
  },
  data() {
    return {
      logo: require('@/assets/image/home/<USER>'),
    };
  },
  methods: {
    handleNavigateToHome() {
      this.$router.push('./');
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.header {
  position: sticky;
  top: 0;
  width: 100%;
  display: flex;
  background: #fff;
  height: 80px;
  padding: 0 32px;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
  box-shadow: 0px -1px 8px 1px rgba(0, 0, 0, 0.2);
  z-index: 100;
  .trigger {
    font-size: 18px;
  }
  .right {
    display: flex;
  }
}
.left {
  cursor: pointer;
  img {
    height: 32px;
  }
}
.user-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    border-radius: 50%;
  }
  p {
    font-size: 14px;
    line-height: 20px;
  }
}
.header-menu {
  display: flex;
  align-items: center;
  li:not(:last-child) {
    margin-right: 32px;
  }
}
.navigations {
  margin-right: 12px;
  li {
    margin-right: 32px;
  }
}
</style>
