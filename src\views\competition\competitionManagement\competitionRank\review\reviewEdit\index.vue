<template>
  <div class="wrap">
    <h1>{{ preview ? '预览' : '编辑' }}审查及答辩材料提交设置</h1>
    <a-form v-show="!preview" ref="formRef" v-bind="formItemLayout" :model="formData" class="form-wrap" :colon="false">
      <a-row class="row" :gutter="rowGutter">
        <a-col :span="12">
          <a-form-item label="提交开放时间" help="需在比赛起止时间段内" name="rangePicker" :rules="[{ type: 'array', required: true, message: '需在比赛起止时间段内', trigger: ['blur', 'change'] }]">
            <a-range-picker v-model:value="formData.rangePicker" :disabled-date="disabledDate">
              <!-- <template #suffixIcon>
                <CalendarOutlined />
              </template> -->
            </a-range-picker>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="提交方式" style="display: flex" class="checkbox-group" name="replySubmitType" :rules="[{ required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-checkbox-group v-model:value="formData.replySubmitType" style="width: 100%">
              <a-row>
                <a-col :span="8">
                  <a-checkbox value="2"> 本地上传 </a-checkbox>
                </a-col>
                <a-col :span="16">
                  <a-checkbox value="3"> 从团队共享存储空间中选择 </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="row" :gutter="[24, 8]">
        <a-col :span="24">
          <a-form-item label="提交要求" :label-col="{ span: 3 }" name="richtextEditor" :rules="[{ required: true, validator: richtextEditorValidator, trigger: ['blur', 'change'] }]">
            <richtextEditor v-model:value="formData.richtextEditor" bind-id="competition-result-editor" :url="richTextUrl" @change="getRichtextEditor" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="rowGutter" type="flex" align="middle">
        <a-col :span="12">
          <a-form-item label="提交开放范围" style="margin-bottom: 0" name="replyCreateSta" :rules="[{ required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-radio-group v-model:value="formData.replyCreateSta">
              <a-radio :value="false"> 指定团队 </a-radio>
              <a-radio :value="true"> 所有团队 </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-row type="flex" align="middle" justify="end" style="padding-right: 32px">
            <p style="margin-right: 40px; color: #121f2c">已选团队： {{ editTableData.length }} 个</p>
            <search v-model="searchValue" placeholder="团队名称/队长用户名/队长姓名" class="search-input" @handSearch="(val) => (searchValue = val)" />
          </a-row>
        </a-col>
      </a-row>
      <reviewTableVue class="reviewEditTable" edit-mode="1" :search-value="searchValue" :reply-create-sta="formData.replyCreateSta" @loading="(e) => (tableLoading = e)" @getTableSelectedRowKeys="getTableSelectedRowKeys" @getTableData="getTableData" />
      <a-row>
        <a-col :span="12">
          <div class="button-group">
            <a-button type="primary" class="button" :disabled="tableLoading" @click="gotoPreview"> 预览 </a-button>
            <a-button type="default" class="button" @click="gobackBtn()"> 取消 </a-button>
          </div>
        </a-col>
      </a-row>
    </a-form>
    <div v-show="preview" class="result-content">
      <reviewContentVue v-if="preview" :preview-details="previewDetails" @getSearchValue="(val) => (searchValue = val)" />
      <reviewTableVue v-if="preview" edit-mode="2" :reply-create-sta="formData.replyCreateSta" :preview-table-list="editTableData" :search-value="searchValue" @loading="(e) => (tableLoading = e)" />
      <div class="descriptions-item">
        <div class="title"></div>
        <div class="content">
          <div class="button-group">
            <a-button type="primary" class="button" :loading="loading" :disabled="tableLoading" @click="submit"> {{ hasPublished ? '保存并发布' : '保存' }} </a-button>
            <a-button
              type="default"
              class="button"
              :disabled="loading"
              @click="
                preview = false;
                searchValue = '';
              "
            >
              返回编辑
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { useStore } from 'vuex';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import richtextEditor from '@/components/richtextEditor.vue';
import API from '@/constants/api/API';
import { textUpload } from '@/apis/teaching';
import reviewContentVue from '../review/reviewContent.vue';
import reviewTableVue from '../review/reviewTable.vue';
import search from '@/components/search';
import { publishStatusKeys } from '../../../../competitionConfig';
dayjs.locale('zh-cn');

// 路由、store
const route = useRoute();
const router = useRouter();
const store = useStore();

// 表单ref
const formRef = ref(null);

// 响应式数据
const formData = reactive({
  cid: route.params.competitionId,
  replyCreateSta: false,
  richtextEditor: '',
  rangePicker: [],
  replySubmitType: [],
  teamIds: [],
});
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const richTextUrl = ref('');
const preview = ref(false);
const loading = ref(false);
const tableLoading = ref(false);
const previewDetails = reactive({
  cid: 0,
  replyEndTime: '',
  replyNarrate: '',
  replyStartTime: '',
  replySubmitType: [],
  teamIds: [],
});
const searchValue = ref('');
const editTableData = ref({});

// 计算属性
const rowGutter = computed(() => [24, 0]);
const currentManageCompetition = computed(() => store.state.competition.currentManageCompetition);
const hasPublished = computed(() => currentManageCompetition.value.releaseSta === publishStatusKeys.PUBLISHED);

// 生命周期
onMounted(() => {
  API.competition_model.getFileReply({ cid: route.params.competitionId }).then((res) => {
    if (res.state === 'OK') {
      richTextUrl.value = res.body.replyNarrate;
      Object.assign(formData, {
        rangePicker: [dayjs(res.body.replyStartTime || currentManageCompetition.value.StartTime), dayjs(res.body.replyEndTime || currentManageCompetition.value.EndTime)],
        replySubmitType: res.body.replySubmitType ? (+res.body.replySubmitType === 1 ? ['2', '3'] : [res.body.replySubmitType]) : [],
      });
    }
  });
  API.competition_model.getFileReplyScope({ cid: route.params.competitionId }).then((fileReplyScope) => {
    if (fileReplyScope.state === 'OK') {
      Object.assign(formData, {
        replyCreateSta: fileReplyScope.body.replyCreateSta,
      });
    }
  });
});

// 方法
function getRichtextEditor(val) {
  formData.richtextEditor = val;
  formRef.value.validateFields(['richtextEditor']);
}
function richtextEditorValidator(rule, value, callback) {
  if (value?.trim().length <= 0) {
    callback('请输入');
  }
  callback();
}
function gobackBtn(noLeaveConfirm) {
  router.push({
    path: `/competition/competition-management/${route.params.competitionId}`,
    query: { tabId: 5, subtabId: 3, noLeaveConfirm },
  });
}
function gotoPreview() {
  const { teamIds } = previewDetails;
  formRef.value
    .validate()
    .then((values) => {
      if (values) {
        Object.assign(previewDetails, {
          cid: route.params.competitionId,
          replyCreateSta: formData.replyCreateSta,
          replyStartTime: formData.rangePicker[0].format('YYYY-MM-DD') + ' 00:00:00',
          replyEndTime: formData.rangePicker[1].format('YYYY-MM-DD') + ' 23:59:59',
          replyNarrate: formData.richtextEditor,
          replySubmitType: formData.replySubmitType.length === 1 ? formData.replySubmitType[0] : 1,
          teamIds,
        });
        preview.value = true;
        searchValue.value = '';
      }
    })
    .catch((error) => {
      throw new Error(error);
    });
}
function submit() {
  loading.value = true;
  textUpload({ baseString: previewDetails.replyNarrate }).then((res) => {
    if (res.state === 'OK') {
      const params = { ...previewDetails };
      params.replyNarrate = res.body.url;
      API.competition_model
        .fileReplySubmitUpdate(params)
        .then((result) => {
          if (result.state === 'OK') {
            message.success(`编辑结果文件提交设置成功`);
            gobackBtn(true);
          } else {
            message.error(`编辑结果文件提交设置失败`);
          }
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
      message.error(`编辑结果文件提交设置失败`);
    }
  });
}
function disabledDate(current) {
  return current && (current < dayjs(currentManageCompetition.value.StartTime, 'YYYY-MM-DD').endOf() || current > dayjs(currentManageCompetition.value.EndTime, 'YYYY-MM-DD').endOf());
}
function getTableSelectedRowKeys(e) {
  previewDetails.teamIds = e.keys;
}
function getTableData(tableData) {
  editTableData.value = tableData;
}
</script>
<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import '~@/views/competition/competitionManagement/competitionRank/form-common.less';
.wrap {
  h1 {
    .competition-edit-header();
  }
}
.form-wrap {
  padding: 32px 0 43px;
}
.checkbox-group {
  :deep(.ant-form-item-children) {
    display: flex;
  }
  :deep(.ant-form-item-control) {
    transition: all 0.3s;
    position: relative;
    top: 0;
    &.has-error {
      transition: none;
      top: 10px;
    }
  }
}
.button-group {
  display: flex;
  margin-left: 32px;
  .button {
    &:nth-of-type(1) {
      width: 120px;
      margin-right: 8px;
    }
    &:nth-of-type(2) {
      width: 88px;
    }
  }
}
.result-content {
  padding: 0 32px 64px;
  .button-group {
    margin-left: 0;
  }
}
.reviewEditTable {
  padding: 0 32px;
  margin-top: 14px;
  :deep(.table) {
    margin-top: 0;
  }
  :deep(.jt-pagination) {
    padding-bottom: 32px;
  }
}
.tab-extra-container {
  .search-input {
    width: 240px;
    margin-right: 9px;
  }
  :deep(.ant-input-affix-wrapper .ant-input:not(:last-child)) {
    padding-right: 10px;
  }
}
</style>
