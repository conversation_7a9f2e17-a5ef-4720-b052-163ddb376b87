<template>
  <div class="empty-container">
    <a-empty v-if="empty" class="empty-content" :style="emptyStyle" :image="emptyImage" :image-style="loading ? { visibility: 'hidden', ...imageStyle } : imageStyle">
      <template #description>
        <p v-if="emptyTitle && !loading" class="empty-title">{{ emptyTitle }}</p>
        <p v-if="emptyText && !loading" class="empty-text">{{ emptyText }}</p>
        <slot name="empty-operation"></slot>
      </template>
      <slot class="empty-content" name="empty-content"></slot>
    </a-empty>
    <slot v-else></slot>
  </div>
</template>

<script>
import empty from '@/assets/image/emptys2x.png';
export default {
  props: {
    emptyImage: {
      default: empty,
      type: String,
    },
    imageStyle: {
      default() {
        return {
          width: '416px',
          height: '416px',
          'margin-bottom': '0px',
        };
      },
      type: Object,
    },
    empty: { type: <PERSON>olean, default: false },
    emptyStyle: {
      type: Object,
      default() {
        return {};
      },
    },
    emptyTitle: { type: String, default: '暂无内容' },
    emptyText: { type: String, default: '' },
    loading: { type: Boolean, default: false },
  },
};
</script>

<style lang="less" scoped>
.empty-container {
  flex: 1;
  min-height: 134px;
}
.empty-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  margin: 0;
  background: #fff;
  position: relative;
  :deep(.ant-empty-description) {
    position: absolute;
    top: calc(50% + 72px);
  }
  .empty-title {
    font-size: 18px;
    font-weight: 600;
    color: #121f2c;
    padding-bottom: 9px;
  }
  .empty-text {
    color: #606972;
    font-size: 14px;
  }
}
</style>
