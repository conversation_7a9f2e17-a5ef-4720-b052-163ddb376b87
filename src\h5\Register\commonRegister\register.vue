<template>
  <div class="register">
    <div class="register-cover-1">
      <img :src="introHeader" alt="" />
    </div>
    <!-- <div class="register-cover-2">
      <img :src="giftHeader" />
    </div> -->

    <van-form @submit="onSubmit" class="register-form" validate-trigger="onChange">
      <img :src="registerHeaderbg" alt="" />
      <div class="register-form-title">九天·毕昇平台</div>
      <div class="register-form-item" @input="handleRegisterformChange">
        <van-field class="item" v-model="phoneNum" @input="handlePhoneInput" name="phonenum" label="" placeholder="手机号" type="tel" :rules="[{ required: true, validator: telPhoneValidator }]" />
        <div class="item-errormsg">{{ phoneError }}</div>
        <van-field
          class="item"
          v-model="validateNumber"
          @input="
            () => {
              codeError = '';
            }
          "
          name="code"
          center
          label=""
          placeholder="验证码"
          maxlength="6"
          type="digit"
          :rules="[{ required: true }]"
        >
          <template #button>
            <span class="validate-divider"></span>
            <span v-if="showCountdown">
              <van-count-down class="validate-code" ref="countDown" :time="countdownTime" :auto-start="false" format="重新获取ss秒" @finish="resetCountdown" />
            </span>
            <a @click="sendSms" v-else :class="validateSms ? 'validate-code' : 'validate-code invalid'">获取验证码</a>
          </template>
        </van-field>
        <div v-if="activityAvailable" class="checkbox-item">
          <van-checkbox class="register-ecloud-label" v-model="registEcloud" name="registEcloud">
            {{ `同时注册移动云账号，算力豆+${ecloudRegisterBeanBount}` }}
          </van-checkbox>
        </div>
        <div class="item-errormsg sendcode-error">{{ codeError }}</div>
        <van-field
          class="item"
          v-model="userName"
          name="username"
          @input="
            () => {
              userError = '';
            }
          "
          label=""
          placeholder="用户名"
          maxlength="20"
          :rules="[{ required: true, validator: userValidator }]"
        />
        <div class="item-errormsg">{{ userError }}</div>
        <p :class="validatedUser ? 'item-info' : 'item-info wrong'">6-20个字符，只能包含字母、下划线和数字，不支持全数字，用户名不可修改，请谨慎设置</p>

        <!-- { require: true, message: '密码过于简单或存在安全风险，请修改', validator: (value) => value && zxcvbn(value).score >= 2 }, -->
        <van-field class="item" v-model="password" type="password" name="password" label="" placeholder="密码" maxlength="20" :rules="[{ required: true, validator: passwordValidator }]" />
        <p :class="validatedPassword ? 'item-info' : 'item-info wrong'">8-20个字符，必须包含大、小写字母和数字</p>

        <div class="school-item">
          <div class="left">
            <van-field readonly label="" :value="cityValue" name="city" placeholder="地区" @click="handleCityList" :rules="[{ required: true }]" />
            <van-popup v-model="showCityPicker" round position="bottom" @close="handleCityCancel">
              <van-search v-model="inputCity" @input="searchCity" name="search" placeholder="请输入搜索学校所在地区" />
              <div v-if="schoolCityList && schoolCityList.length === 0 && !schoolLoading" class="emptySelect">
                <img src="../../../assets/image/h5/empty.png" alt="empty" />
              </div>
              <van-picker v-else show-toolbar :columns="schoolCityList" :loading="cityLoading" @cancel="handleCityCancel" @change="handleRegisterformChange" @confirm="handleCity" />
            </van-popup>
          </div>
          <div class="right">
            <div v-if="showCustome">
              <van-field class="item" v-model="schoolValue" name="school" label="" placeholder="学校名称" maxlength="50" @change="handleRegisterformChange" :rules="[{ required: true, validator: schoolnameValidator }]" />
              <p v-if="!validatedSchoolname" class="item-info wrong">50个字符以内中英文，可包含空格</p>
            </div>
            <div v-else>
              <van-field readonly label="" :value="schoolValue" name="school" placeholder="在读/就职/毕业学校" @click="handleSchoolList" />
              <van-popup v-model="showSchoolPicker" round position="bottom" @close="handleSchoolCancel">
                <van-search v-model="inputSchool" name="search" @input="searchSchool" placeholder="请输入搜索学校名称" />
                <div v-if="schoolList && schoolList.length === 0 && !schoolLoading" class="emptySelect">
                  <img src="../../../assets/image/h5/empty.png" alt="empty" />
                </div>
                <van-picker v-else show-toolbar :columns="schoolList" :loading="schoolLoading" @cancel="handleSchoolCancel" @change="handleRegisterformChange" @confirm="handleSchools" />
              </van-popup>
            </div>
          </div>
        </div>
        <div class="checkbox-item">
          <van-checkbox v-model="checkTrims" @change="handleRegisterformChange" name="checkTrims"> 我已阅读并同意九天人工智能平台 <a href="/portal/common-helpcenter#/h5/trims" target="_blank" rel="noopenner noreferrer" style="color: #2567f5">服务条款</a> </van-checkbox>
        </div>
      </div>
      <div class="register-form-submit">
        <van-button block type="info" :loading="submitLoading" loading-text="提交中..." native-type="submit" :disabled="disbaledSubmit">注册</van-button>
      </div>
    </van-form>

    <van-popup v-model="showResult" class="popup" :close-on-click-overlay="ifSuccess ? false : true">
      <div class="popup-header">
        <img :src="closeicon" @click="closePopup" alt="" />
      </div>
      <div class="popup-content-success" v-if="ifSuccess">
        <img :src="successicon" alt="" />
        <p class="success-title">{{ successMessage }}</p>
        <van-divider />
        <p class="success-text">在电脑端登录使用九天·毕昇平台</p>
        <p class="success-link" id="successLink" @click="pasteLink('successLink')">https://jiutian.10086.cn/edu</p>
        <p class="success-info">开启AI之旅，赢取更多福利</p>
      </div>
      <div class="popup-content-fail" v-else>
        <img :src="failicon" alt="" />
        <p class="fail-title">{{ failMessage || '很遗憾，注册失败' }}</p>
        <p class="fail-info">请您稍后重试</p>
      </div>
    </van-popup>
    <deny-invite v-if="!ifInvitedopen"></deny-invite>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
import API from '@/constants/api/API.js';
import { pasteContent, debounce } from '@/utils/utils.js';
import { Notify } from 'vant';
import denyInvite from './denyInvite';
import { POST, GET } from '@/request';
// const zxcvbn = require('zxcvbn');

export default {
  name: 'commonRegister',
  components: {
    denyInvite,
  },
  data() {
    return {
      ifInvitedopen: false,
      userName: '',
      password: '',
      phoneNum: '',
      phoneError: '',
      codeError: '',
      userError: '',
      validatedUser: true,
      validatedPassword: true,
      validatedSchoolname: true,
      passwordError: '',
      validateNumber: '',
      validateSms: false,
      phoneValidator: /^[1](([3][0-9])|([4][0,1,4-9])|([5][0-3,5-9])|([6][2,5,6,7])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/,
      passwordformatValidator: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[0-9a-zA-Z!@#$%^&*()-_=+{}[\]\\|;:'",.<>/?]{8,20}$/,
      usernameValidator: /^([A-Za-z0-9_]){6,20}$/,
      usernameNumberValidator: /^\d+$/,
      schoolNameValidator: /^([\u4e00-\u9fa5_A-Za-z0-9 ]){1,50}$/,
      countdownTime: 60 * 1000,
      showCountdown: false,
      ifSuccess: false,
      showResult: false,
      disbaledSubmit: true,
      introHeader: require('../../../assets/image/h5/develop.png'),
      giftHeader: require('../../../assets/image/h5/registerfuli.png'),
      registerHeaderbg: require('../../../assets/image/h5/register-header.png'),
      successicon: require('../../../assets/image/h5/success.png'),
      failicon: require('../../../assets/image/h5/fail.png'),
      closeicon: require('../../../assets/image/h5/close.png'),
      cityValue: '',
      schoolValue: '',
      inputCity: '',
      inputSchool: '',
      showCustome: false,
      showCityPicker: false,
      showSchoolPicker: false,
      schoolColumns: [],
      cityColumns: [],
      submitLoading: false,
      ecloudRegisterBeanBount: 0,
      registEcloud: false,
      checkTrims: false,
      activityAvailable: false,
      successMessage: '',
      failMessage: '',
      eCloudCodeAvailable: false,
      eCloudRegistered: false,
      codeSended: false,
    };
  },
  created() {
    this.getActivityStatus();
    this.getEcloudRegisterBeanMessage();
    API.h5_campus
      .getMarketingFeature()
      .then((res) => {
        if (res.state === 'OK') {
          this.ifInvitedopen = +res.body === 1 ? true : false;
        }
      })
      .catch(() => {
        this.this.ifInvitedopen = false;
      });
  },
  computed: {
    ...mapState('h5', ['cityLoading', 'schoolLoading', 'schoolCityList', 'schoolList', 'currentProvince']),
  },
  watch: {
    cityValue(newValue) {
      this.schoolValue = '';
      this.handleRegisterformChange();
      if (newValue === '其他') {
        this.showCustome = true;
      } else {
        this.showCustome = false;
      }
    },
  },
  methods: {
    // zxcvbn,
    ...mapMutations('h5', ['updateCurrentCity']),
    ...mapActions('h5', ['getCityList', 'searchCityList', 'getSchoolList', 'searchSchoolList']),
    handleCityList() {
      this.showCityPicker = true;
      this.getCityList();
    },
    handleSchoolList() {
      this.showSchoolPicker = this.cityValue !== '' ? true : false;
      this.getSchoolList({ province: this.currentProvince });
    },
    searchCity: debounce(function (city) {
      this.searchCityList({ keyword: city });
    }),
    searchSchool: debounce(function (school) {
      this.searchSchoolList({ province: this.currentProvince, keyword: school });
    }),
    handleCityCancel() {
      this.showCityPicker = false;
      this.inputCity = '';
    },
    handleCity(value) {
      this.cityValue = value;
      this.showCityPicker = false;
      this.updateCurrentCity(value);
      this.handleRegisterformChange();
    },
    handleSchoolCancel() {
      this.showSchoolPicker = false;
      this.inputSchool = '';
    },
    handleSchools(value) {
      this.schoolValue = value;
      this.showSchoolPicker = false;
      this.handleRegisterformChange();
    },
    telPhoneValidator(val) {
      const isValidated = this.phoneValidator.test(val);
      if (!isValidated) {
        this.phoneError = '手机号码格式不正确';
      } else {
        this.phoneError = '';
      }
      return isValidated;
    },
    userValidator(val) {
      const nameValidator = this.usernameValidator.test(val);
      const numberValidator = this.usernameNumberValidator.test(val);
      if (numberValidator) {
        this.validatedUser = false;
      } else if (!nameValidator) {
        this.validatedUser = false;
      } else {
        this.validatedUser = true;
      }
      return nameValidator && !numberValidator;
    },
    passwordValidator(val) {
      const isvalidated = this.passwordformatValidator.test(val);
      if (!isvalidated) {
        this.validatedPassword = false;
      } else {
        this.validatedPassword = true;
      }
      return isvalidated;
    },
    schoolnameValidator(val) {
      const isvalidated = this.schoolNameValidator.test(val);
      if (!isvalidated) {
        this.validatedSchoolname = false;
      } else {
        this.validatedSchoolname = true;
      }
      return isvalidated;
    },
    checkEcloudCodeAvailable() {
      if (this.codeSended && this.eCloudCodeAvailable !== this.registEcloud) {
        if (this.registEcloud) {
          Notify({ type: 'danger', message: '您已勾选同时注册移动云，请重新获取移动云验证码' });
        } else {
          Notify({ type: 'danger', message: '您已取消注册移动云，移动云验证码失效，请重新获取验证码' });
        }
        return;
      }
      return true;
    },
    onSubmit(values) {
      if (!this.checkEcloudCodeAvailable()) {
        return;
      }
      this.submitLoading = true;
      let params = {};
      Object.keys(values).forEach((key) => {
        if (key && values[key] && values[key].length !== 0) {
          params[key] = values[key];
        }
      });
      delete params.city;
      delete params.search;
      params.channel = 0; //0=H5端自主注册
      params.registEcloud = +this.registEcloud;
      const url = this.registEcloud && !this.eCloudRegistered ? '/marketing/web/ecloud/pcUserRegistration' : '/marketing/web/userRegistration';
      POST(url, params)
        .then((res) => {
          if (res.state === 'OK') {
            this.showResult = true;
            this.ifSuccess = true;
            this.successMessage = '恭喜，您已成功注册';
          } else {
            // 立即注册接口：
            // WRONG_SMS_CODE("1020", "验证码有误，请重新输入"),
            // BAD_SMS_CODE("1021", "验证码已过期，请重新获取"),
            // BAD_FORMAT_USERNAME("1022", "用户名格式不正确"),
            // BAD_FORMAT_PASSWORD("1023", "密码格式不正确"),
            // REGIST_FAIL("1040", "用户名已被注册，请重新输入");
            // 调用keycloak注册接口返回的几个错误码：
            // CREATE_USER_ERROR("-106", "keycloak创建用户失败"),
            // PHONE_USED_ERROR("-104", "该手机号已绑定至其他账号，请解绑后再试"),
            // QUERY_USER_ERROR("-107", "keycloak查询用户失败"),
            if (res.errorCode === '1020' || res.errorCode === '1021') {
              this.phoneError = '';
              this.codeError = res.errorMessage;
              this.userError = '';
              this.passwordError = '';
            } else if (res.errorCode === '1022' || res.errorCode === '1040') {
              this.codeError = '';
              this.phoneError = '';
              this.userError = res.errorMessage;
              this.passwordError = '';
            } else if (res.errorCode === '1023') {
              this.codeError = '';
              this.phoneError = '';
              this.userError = '';
              this.passwordError = res.errorMessage;
            } else if (res.errorCode === '-104') {
              this.codeError = '';
              this.phoneError = res.errorMessage;
              this.userError = '';
              this.passwordError = '';
            } else if (res.errorCode === '-136') {
              this.failMessage = '注册功能暂未开放';
              this.showResult = true;
              this.ifSuccess = false;
            } else if (res.errorCode === '1111') {
              this.failMessage = '毕昇平台账号注册失败';
              this.ifSuccess = false;
              this.showResult = true;
            } else {
              this.showResult = true;
              this.ifSuccess = false;
              this.failMessage = res.errorMessage;
            }
          }
          this.submitLoading = false;
        })
        .catch(() => {
          this.submitLoading = false;
        });
    },
    handleRegisterformChange() {
      //  && zxcvbn(this.password).score >= 2
      const ifValidedForm = this.validateNumber !== '' && this.userName !== '' && this.password !== '' && this.phoneNum !== '' && this.validatedSchoolname && this.cityValue !== '' && this.schoolValue !== '' && this.checkTrims;
      this.disbaledSubmit = !ifValidedForm;
    },
    handlePhoneInput() {
      const validedPhone = this.telPhoneValidator(this.phoneNum);
      this.validateSms = validedPhone;
    },
    sendSms() {
      const ifPhoneValidated = this.telPhoneValidator(this.phoneNum);
      if (!this.validateSms) {
        return;
      }
      if (!ifPhoneValidated) {
        this.phoneError = '手机号码格式不正确';
        return;
      }
      this.codeSended = true;
      this.eCloudCodeAvailable = this.registEcloud;
      if (this.registEcloud) {
        this.sendEcloudCode();
      } else {
        this.sendBiShengCode();
      }
    },

    sendEcloudCode() {
      const url = '/marketing/web/ecloud/sendSmsCode';
      const param = { phoneNum: this.phoneNum };
      GET(url, param).then((res) => {
        if (res.state === 'OK') {
          this.startCountdown();
          this.eCloudRegistered = false;
          Notify({ type: 'success', message: '验证码已成功发送' });
        } else {
          if (res.errorCode === '1020' || res.errorCode === '1021') {
            this.phoneError = '';
            this.codeError = res.errorMessage;
          } else if (res.errorCode === '1109') {
            this.eCloudRegistered = true;
            this.sendBiShengCode();
          } else {
            this.codeError = '';
            this.phoneError = res.errorMessage;
          }
        }
      });
    },

    sendBiShengCode() {
      const url = '/marketing/web/sendSmsCode';
      const param = { phoneNum: this.phoneNum };
      GET(url, param).then((res) => {
        if (res.state === 'OK') {
          this.startCountdown();
          Notify({ type: 'success', message: '验证码已成功发送' });
        } else {
          if (res.errorCode === '1020' || res.errorCode === '1021') {
            this.phoneError = '';
            this.codeError = res.errorMessage;
          } else {
            this.codeError = '';
            this.phoneError = res.errorMessage;
          }
        }
      });
    },
    resetCountdown() {
      this.showCountdown = false;
      this.$refs.countDown.reset();
    },
    startCountdown() {
      this.showCountdown = true;
      this.$nextTick(() => {
        this.$refs.countDown.start();
      });
    },
    closePopup() {
      this.showResult = false;
    },
    pasteLink(id) {
      const paste = document.getElementById(id).innerHTML;
      pasteContent(paste);
      Notify({ type: 'success', message: '复制成功' });
    },
    getActivityStatus() {
      GET('/marketing/web/ecloud/activityStatus').then((res) => {
        if (res.state === 'OK') {
          this.activityAvailable = res.body;
          this.registEcloud = this.activityAvailable;
        }
      });
    },
    getEcloudRegisterBeanMessage() {
      GET('/marketing/web/ecloud/getRegisterBeanMessage').then((res) => {
        if (res.state === 'OK') {
          this.ecloudRegisterBeanBount = (res.body && res.body.ecloudRegisterBeanBount) || 0;
        }
      });
    },
  },
};
</script>

<style lang="less">
@import '~@/assets/styles/index.less';

.register {
  padding: 16px 16px 32px;
  background: rgba(139, 182, 249, 1);
  background-image: url('~@/assets/image/h5/home.jpg');
  background-size: 100%;
  background-repeat: no-repeat;
  .register-cover-1 {
    margin-top: 115%;
    margin-bottom: 20px;
    img {
      width: 100%;
    }
  }
  // .register-cover-2 {
  //   margin: 20px 0;
  //   img {
  //     width: 100%;
  //   }
  // }
  .register-form {
    width: 100%;
    background: rgba(255, 255, 255, 0.43);
    box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
    border-radius: 8px;
    border: 1px solid #ffffff;
    text-align: center;
    img {
      width: 200px;
      margin-top: -1px;
    }
    .register-form-title {
      margin: 20px 0;
      text-align: center;
      font-size: 22px;
      font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
      font-weight: 600;
      color: #2567f5;
    }
    .register-form-item {
      text-align: left;
      .validate-divider {
        height: 20px;
        margin-right: 10px;
        border-left: 1px solid #ddd;
      }
      .validate-code {
        white-space: nowrap;
        font-size: 17px;
        font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
        font-weight: 400;
        color: #4576ff;
        margin-right: 10px;
      }
      .invalid {
        color: #d8d8d8;
        pointer-events: none;
        cursor: default;
      }
      .item {
        border: none;
      }

      .school-item {
        display: flex;
        .left {
          flex: 1;
          .emptySelect {
            height: 308px;
            overflow: hidden;
            img {
              width: 100%;
            }
            .empty-text {
              position: absolute;
              bottom: 15%;
              left: 50%;
              transform: translateX(-50%);
              text-align: center;
              font-size: 18px;
              font-weight: @jt-font-weight-medium;
              color: #121f2c;
            }
          }
          .van-icon {
            line-height: 2;
            font-size: 20px;
          }
        }
        .right {
          flex: 2;
          .van-icon {
            line-height: 2;
            font-size: 20px;
          }
          .van-cell {
            padding-left: 0;
          }
          .item-info {
            margin-left: 0;
          }
          .emptySelect {
            height: 308px;
            overflow: hidden;
            img {
              width: 100%;
            }
            .empty-text {
              position: absolute;
              bottom: 15%;
              left: 50%;
              transform: translateX(-50%);
              text-align: center;
              font-size: 18px;
              font-weight: @jt-font-weight-medium;
              color: #121f2c;
            }
          }
        }
      }
      .item-errormsg {
        margin: -10px 15px 10px 15px;
        font-size: 14px;
        font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
        font-weight: 400;
        color: #f63f48;
        &.sendcode-error {
          margin-top: 4px;
          margin-bottom: 0;
        }
      }
      .item-info {
        margin: 0px 17px 10px;
        text-align: left;
        font-size: 14px;
        font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
        font-weight: 400;
        color: #41497a;
      }
      .wrong {
        color: #f63f48;
      }
    }
    .register-form-submit {
      margin: 25px 16px 16px;
      background: linear-gradient(180deg, #487bfd 0%, #4056ff 100%);
      border-radius: 4px;
      font-size: 17px;
      font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
      font-weight: @jt-font-weight-medium;
      color: #ffffff;
    }
    .van-cell {
      border-radius: 4px;
      background: inherit;
    }
  }
  .popup {
    width: 85%;
    background: #ffffff;
    box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
    border-radius: 12px;
    border: 2px solid #ffffff;
    text-align: center;
    padding: 15px;
    .popup-header {
      text-align: right;
      img {
        width: 24px;
      }
    }
    .popup-content-success {
      color: #41497a;
      font-size: 17px;
      img {
        width: 180px;
      }
      .success-title {
        font-size: 22px;
        color: #f63f48;
        margin-bottom: 7px;
        font-weight: 600;
      }
      .success-subtitle {
        font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
        font-weight: 600;
        .red {
          color: #f63f48;
        }
      }
      .van-divider {
        color: #d8d8d8 !important;
        border-color: inherit;
      }
      .success-text {
        font-weight: 400;
      }
      .success-link {
        margin: 8px;
        font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
        font-weight: @jt-font-weight-medium;
        color: #4777fd;
      }
      .success-info {
        margin-bottom: 15px;
        font-weight: 600;
      }
    }
    .popup-content-fail {
      font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
      font-weight: 600;
      img {
        width: 72px;
        margin: 16px;
      }
      .fail-title {
        font-size: 22px;
        color: #f63f48;
        margin-bottom: 7px;
      }
      .fail-info {
        font-size: 17px;
        color: #41497a;
        margin-bottom: 25px;
      }
    }
  }
}
</style>
<style lang="less">
.van-field__button {
  display: flex !important;
  align-items: center;
}
.van-field__value .van-field__body {
  border-radius: 4px;
  border: none;
  background: #ffffff !important;
}
.van-field__value .van-field__body input {
  padding: 10px;
  border-radius: 4px;
}
.van-field__error-message {
  font-size: 14px;
  font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
  font-weight: 400;
  color: #f63f48;
}
.van-field__value {
  .van-field__body {
    input {
      font-size: 17px;
    }
    input::-webkit-input-placeholder {
      color: #abafc5;
      font-size: 17px;
    }
  }
}
.van-cell::after {
  border: none;
}

.van-button--normal {
  font-size: 17px;
}

.checkbox-item {
  padding: 0 16px;
}

.van-checkbox__icon .van-icon {
  border-color: #2567f5;
}

.register-ecloud-label {
  .van-checkbox__label {
    color: #41497a;
  }
}
</style>
