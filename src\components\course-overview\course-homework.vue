<script setup>
import { InfoCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div class="contents">
    <div class="contents-header">
      <div>
        <span class="title">作业</span>
        <span class="progress">
          {{ `共${data.length}次` }}
        </span>
      </div>
    </div>
    <jt-common-content :empty="data.length === 0" :loading="loading">
      <ul class="contents-list">
        <li v-for="(item, i) in data" :key="item.id">
          <div class="left" :title="item.name">
            <a-tag class="learn-status-tag" :class="getSubmitedStatusClass(item)">{{ getSubmitedStatusText(item) }}</a-tag>
            <span class="catlog-name">{{ `第${toChinesNum(i + 1)}次：${item.name}` }}</span>
            <a-tag class="filled-tag" :class="getHomeworkTypeClass(item)">{{ getHomeworkType(item) }}</a-tag>
          </div>
          <div class="right">
            <a-space class="end-time" :class="getEndTimeClass(item)">
              <jt-icon type="icontime-circle" style="font-size: 14px" />
              <span>
                {{ getOutDatedStatus(item) ? '提交已经截止' : '提交截止时间' }}
              </span>
              {{ item.endTime }}
            </a-space>
            <div class="flex-align-center">
              <span @click="handleShowScoreDetail(item)" v-show="getReadStatus(item)" class="score" :class="getScoreStatusClass(item)">
                <span class="num">{{ item.score }}</span>
                <span class="branch">分</span>
              </span>
              <a-button @click="handleRestart(item)" v-show="getStartedStatus(item) && !getOutDatedStatus(item) && !getDeletedStatus(item)" type="link">重新作答</a-button>
              <a-button @click="handleRemove(item)" v-show="getDeletedStatus(item)" type="link" class="danger">删除作业</a-button>
              <a-button @click="handleContinue(item)" v-show="getStartedStatus(item) && !getOutDatedStatus(item)" type="link">继续作答</a-button>
              <a-button @click="handleStart(item)" v-show="!getStartedStatus(item) && !getOutDatedStatus(item)" type="link">开始作答</a-button>
              <a-button @click="handleCheck(item)" v-show="getOutDatedStatus(item)" type="link">查看作答</a-button>
            </div>
          </div>
          <div v-show="getDeletedStatus(item)" class="course-status-tag removed">{{ '教师已删除' }}</div>
        </li>
      </ul>
    </jt-common-content>
    <score-detail @cancel="scoreDetailVisible = false" :visible="scoreDetailVisible" :score="currentScore" :remark="currentRemark" :level="currentLevel" />
    <confirm-modal :visible="deleteConfirmVisible" type="danger" okText="删除" cancelText="取消" title="确定删除作业吗？" :confirmLoading="false" :showCancel="true" @cancel="deleteConfirmVisible = false" @ok="handleConfirmDelete">
      <template #icon>
        <InfoCircleFilled style="font-size: 18px; color: #ff454d" />
      </template>
      <div class="column">
        <p>作业实例将自动关闭，您对实例所做的任何修改都不会被保留，请确保您已做好备份。</p>
        <p style="color: #ff454d; margin-top: 8px">删除后作业不可找回。</p>
      </div>
    </confirm-modal>
    <confirm-modal :visible="restartConfirmVisible" type="primary" okText="确定" cancelText="取消" title="确定重新作答吗？" :confirmLoading="false" :showCancel="true" @cancel="restartConfirmVisible = false" @ok="handleConfirmRestart">
      <template #icon>
        <InfoCircleFilled style="font-size: 18px; color: #fa8014" />
      </template>
      <div class="column">
        <p>作业实例将自动重启，您对实例所做的任何修改都不会保留，请确保您已做好备份。</p>
        <p style="color: #ff454d; margin-top: 8px">已提交的作业记录不会撤销。</p>
      </div>
    </confirm-modal>
  </div>
</template>

<script>
import toChinesNum from '@/lib/toChinesNum';
import scoreDetail from './score-detail.vue';
import { GET, POST } from '@/request';
import confirmModal from '@/components/confirmModal';
import { checkAuth } from '@/utils/utils';
import { SUBMIT_STATUS, TYPE, OUTDATED_STATUS, DELETED_STATUS, STARTED_STATUS } from './homework-maps';

export default {
  name: 'homework',
  components: {
    scoreDetail,
    confirmModal,
  },
  props: {
    courseId: {
      type: [Number, String],
      required: true,
    },
    courseName: {
      type: [String],
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      data: [],
      currentScore: 0,
      currentRemark: '',
      currentLevel: '',
      scoreDetailVisible: false,
      deleteConfirmVisible: false,
      restartConfirmVisible: false,
      currentItem: {},
    };
  },
  computed: {},
  methods: {
    toChinesNum,
    getHomework() {
      this.loading = true;
      GET('/course_model/web/course_student/course/studentCourseAssignment', { courseId: this.courseId }, { useError: false })
        .then((res) => {
          if (res.state === 'OK') {
            this.data = res.body;
          } else {
            if (!checkAuth(res.errorCode, '-802', '/course')) {
              return;
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getSubmitedStatusClass({ submitStatus }) {
      return SUBMIT_STATUS[submitStatus] === '已提交' ? 'finished' : '';
    },
    getSubmitedStatus({ submitStatus }) {
      return SUBMIT_STATUS[submitStatus] === '已提交';
    },
    getSubmitedStatusText({ submitStatus }) {
      return SUBMIT_STATUS[submitStatus] || '未提交';
    },
    getStartedStatus({ studentStatus }) {
      return STARTED_STATUS[studentStatus] === '已作答';
    },
    getDeletedStatus({ deleteFlag }) {
      return DELETED_STATUS[deleteFlag] === '已删除';
    },
    getOutDatedStatus({ abort }) {
      return OUTDATED_STATUS[abort] === '已截止';
    },
    getScoreStatusClass({ score }) {
      if (!score) {
        
        return '';
      }
      if (score >= 90) {
        return 'good';
      } else if (score >= 60) {
        return 'normal';
      } else {
        return 'bad';
      }
    },
    getHomeworkTypeClass({ assignmentType }) {
      return TYPE[assignmentType] === '项目' ? 'item' : 'test';
    },
    getHomeworkType({ assignmentType }) {
      return TYPE[assignmentType];
    },
    getReadStatus({ score }) {
      return score !== null && score !== undefined;
    },
    handleStart(item) {
      this.currentItem = item;
      this.navigateToDetail();
    },
    handleRestart(item) {
      this.restartConfirmVisible = true;
      this.currentItem = item;
    },
    handleContinue(item) {
      this.currentItem = item;
      this.navigateToDetail();
    },
    handleCheck(item) {
      this.currentItem = item;
      this.navigateToDetail();
    },
    handleRemove(item) {
      this.deleteConfirmVisible = true;
      this.currentItem = item;
    },
    getEndTimeClass({ abort }) {
      return OUTDATED_STATUS[abort] === '已截止' ? 'end-time-outdated' : '';
    },
    handleShowScoreDetail(item) {
      this.currentScore = item.score;
      this.currentRemark = item.remark || '暂无评语';
      this.currentLevel = this.getScoreStatusClass(item);
      this.scoreDetailVisible = true;
    },
    handleConfirmDelete() {
      const obj = {
        courseId: this.courseId,
        assignmentId: this.currentItem.id,
      };
      POST('/course_model/web/course_student/student/studentAssignmentDelete', obj).then((res) => {
        if (res.state === 'OK') {
          this.$message.success('删除成功');
          this.getHomework();
          this.deleteConfirmVisible = false;
        }
      });
    },
    handleConfirmRestart() {
      const obj = {
        courseId: this.courseId,
        assignmentId: this.currentItem.id,
      };
      POST('/course_model/web/course_student/student/againRespondence', obj).then((res) => {
        if (res.state === 'OK') {
          this.navigateToDetail();
        }
      });
    },
    navigateToDetail() {
      this.$router.push({
        path: `/course/homework-detail/${this.courseId}/${this.currentItem.id}/`,
        query: {
          courseName: this.courseName,
        },
      });
    },
  },
  mounted() {
    this.getHomework();
  },
};
</script>

<style lang="less" scoped>
@import './course-homework.less';
</style>
