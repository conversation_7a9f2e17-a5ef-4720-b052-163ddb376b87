<script setup>
import { CaretDownOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="my-school-course-container">
    <div v-if="upgrading" class="upgrading-container">
      <div class="empty-box">
        <img :src="upgradingImage" alt="" />
        <div class="empty-title">
          <h3>{{ upgradingTitle }}</h3>
          <span>{{ upgradingContent }}</span>
        </div>
      </div>
    </div>
    <template v-else>
      <bread-crumb :value="breadCrumbs" />
      <div class="tabs-container jt-box-shadow"><span>我学习的校内课</span></div>
      <div class="main-content">
        <ul class="content-list">
          <jt-common-content :loading="loading" :empty-style="emptyStyle" :empty="list.length === 0" :empty-image="emptyImage" :empty-title="emptyDescription.title" :empty-text="emptyDescription.text">
            <div class="common-content">
              <li v-for="item in list" :key="item.courseId" class="content-item" @click="goDetail(item.ude.url)">
                <img :src="item.ude.coverImageURL" alt="" />
                <span class="class-title" :title="item.name">{{ item.name }}</span>
                <p>前往学习</p>
              </li>
            </div>
          </jt-common-content>
          <jt-row v-if="list.length != 0" class="pagination">
            <jt-col :span="6"
              >共 {{ total }} 条<span class="pageOption" style="margin-left: 15px">每页显示</span>
              <jt-select v-model="pageSize" :default-value="pageSize" style="min-width: 50px; margin: 0 5px" @change="pageSizeChange">
                <template #suffixIcon>
                  <CaretDownOutlined :style="{ color: '#606266', marginRight: '-7px' }" />
                </template>
                <a-select-option :value="4">4</a-select-option>
                <a-select-option :value="8">8</a-select-option>
                <a-select-option :value="16">16</a-select-option>
              </jt-select>
              <span>条</span>
            </jt-col>
            <jt-col :span="18">
              <jt-pagination v-model:page-size="pageSize" v-model="pageNum" show-quick-jumper :default-current="1" :total="total" style="text-align: right" @change="pageNumChange" />
            </jt-col>
          </jt-row>
        </ul>
      </div>
    </template>
  </div>
</template>

<script>
import breadCrumb from '../../components/breadCrumb';
import { Row as JtRow, Col as JtCol, Select as JtSelect, Pagination as JtPagination } from 'ant-design-vue';
import { courseStudentApi } from '@/apis';
import { getTeachingStatus } from '../../apis/teaching.js';
import { checkLogin, keycloak } from '@/keycloak';
import { openInNewTab } from '@/utils/utils';
let upgrading = false;
let upgradingTitle = '';
let upgradingContent = '';
export default {
  components: { breadCrumb, JtRow, JtCol, JtSelect, JtPagination },
  beforeRouteEnter(to, from, next) {
    // 登录了且无升级中访问权限
    if (keycloak.authenticated && keycloak.idTokenParsed.DLP_USER_ALLOW_ENTER_DURING_UPGRADING != '1') {
      getTeachingStatus()
        .then((res) => {
          if (res.state === 'OK' && res.body.teachingSwitch) {
            upgrading = true;
            upgradingTitle = res.body.title || '维护升级中';
            upgradingContent = res.body.content || '等会再来看看吧';
          }
          next();
        })
        .catch(() => {
          next();
        });
    } else {
      next();
    }
  },
  data() {
    return {
      breadCrumbs: [{ name: '学习', path: '/course' }, { name: '我学习的校内课' }],
      loading: false,
      list: [],
      total: 5,
      pageSize: 8,
      pageNum: 1,
      upgradingImage: require('@/assets/image/teaching/system-maintenance.png'),
      upgrading, // 希冀升级状态
      upgradingTitle,
      upgradingContent,
      emptyStyle: {
        'min-height': '416px',
        height: 'calc(100vh - 675px)',
      },
    };
  },
  computed: {
    emptyImage() {
      return require('@/assets/image/emptys2x.png');
    },
    emptyDescription() {
      return { title: '您暂无课程', text: '加油学习哦~' };
    },
  },
  mounted() {
    if (!checkLogin) return;
    this.getCourseList();
  },
  methods: {
    getCourseList() {
      this.loading = true;
      courseStudentApi.getSchoolCourseList({ userType: 0, page: this.pageNum, limitInPage: this.pageSize }).then((res) => {
        this.loading = false;
        if (!res.errorCode) {
          this.total = res.body.total;
          this.list = res.body.course || [];
        }
      });
    },
    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getCourseList();
    },
    pageNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getCourseList();
    },
    goDetail(url) {
      openInNewTab(url);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.upgrading-container {
  padding: 40px 0px;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 640px;
  height: calc(100vh - @jt-footer-height + @jt-header-height);

  .empty-box {
    position: relative;
    img {
      width: 416px;
      height: 416px;
    }
    .empty-title {
      position: absolute;
      bottom: 90px;
      left: 0px;
      right: 0px;
      text-align: center;
      h3 {
        font-size: 18px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
      }
      span {
        font-size: 12px;
        color: #606972;
      }
    }
  }
}
.tabs-container {
  display: flex;
  justify-content: center;
  background: #fff;
  padding: 8px 0 24px 0;
  span {
    width: 1200px;
    font-size: 24px;
    font-weight: @jt-font-weight-medium;
  }
}
.main-content {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-bottom: 48px;
  background: #f4f8fa;
  .content-list {
    width: 1272px;
    // padding: 20px;
    .common-content {
      display: flex;
      flex-wrap: wrap;
      .content-item {
        background: #ffffff;
        display: inline-block;
        width: 288px;
        height: 263px;
        margin: 0 20px 20px 0;
        border-radius: 2px;
        border: 1px solid #ecedee;
        overflow: hidden;
        box-shadow: 0px 7px 24px -4px rgba(7, 45, 82, 0.12);
        transition: 0.3s all;
        &:hover {
          border-color: transparent;
          box-shadow: 0px 9px 28px 8px rgba(5, 11, 23, 0.05), 0px 6px 16px 0px rgba(5, 11, 23, 0.08), 0px 3px 6px -4px rgba(5, 11, 23, 0.12);
          span {
            color: #0082ff;
          }
        }
        img {
          cursor: pointer;
          width: 288px;
          height: 170px;
        }
        span {
          display: block;
          cursor: pointer;
          margin: 19px 9px 9px 19px;
          font-size: 18px;
        }
        p {
          cursor: pointer;
          color: #0885ff;
          margin-left: 19px;
        }
        :deep(.ant-btn-link) {
          padding-left: 19px;
          display: block;
        }
        .class-title {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      li:nth-of-type(4n) {
        margin-right: 0;
      }
    }
    .pagination {
      width: 1210px;
      margin-top: 4px;
    }
  }
}
</style>
