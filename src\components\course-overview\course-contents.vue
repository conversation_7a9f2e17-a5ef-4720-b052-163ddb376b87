<template>
  <div class="contents">
    <div class="contents-header">
      <div>
        <span class="title">课节</span>
        <span class="progress">
          {{ `已学${mapFlag || 0}节 / 共${catalogNum || 0}节` }}
        </span>
      </div>
      <div class="course-icon" :class="{ 'course-bean-active': giveBeansByCourse.feature }" @click="handleShowCertificate">
        <img v-if="finished" src="@/assets/image/course/badge-thumb-active.png" alt="" />
        <img v-else src="@/assets/image/course/badge-thumb.png" alt="" />
      </div>
      <receive-bean-button v-if="giveBeansByCourse.feature" :course-name="courseName" :course-id="courseId" :learn-complete="finished" />
    </div>
    <jt-common-content :empty="courseContents.length === 0" :loading="loading">
      <ul class="contents-list">
        <li v-for="(item, i) in courseContents" :key="item.id">
          <div class="left">
            <a-tag class="learn-status-tag" :class="getLearnStatus(item.status).value">{{ getLearnStatus(item.status).label }}</a-tag>
            <span class="catlog-name">{{ `第${toChinesNum(i + 1)}节：${item.catalogName}` }}</span>
            <span v-if="+item.videoFlag" class="catlog-icon1">视频</span>
            <span v-if="+item.itemFlag" class="catlog-icon2">项目</span>
            <span v-if="+item.documentFlag" class="catlog-icon3">文档</span>
          </div>
          <div>
            <a-button v-show="getRestartStatus(item)" :disabled="courseDisabled(item)" type="link" @click="handleRestart(item)">重新学习</a-button>
            <a-button v-show="getLearnStatus(item.status).value === 'finished'" type="link" @click="handleContinue(item)">继续学习</a-button>
            <a-button v-show="getCourseStatus(item.status).value === 'removed' && getLearnStatus(item.status).value === 'finished'" type="link" class="danger" @click="handleRemove(item)">删除课节</a-button>
            <a-button v-show="getLearnStatus(item.status).value === 'unstarted'" :disabled="courseDisabled(item)" type="link" @click="handleStart(item)">开始学习</a-button>
          </div>
          <div v-show="getCourseStatus(item.status).value" class="course-status-tag" :class="getCourseStatus(item.status).value">{{ getCourseStatus(item.status).label }}</div>
        </li>
      </ul>
    </jt-common-content>
    <student-certificate :institute-name="instituteName" :end-study-time="endStudyTime" :user-name="$keycloak.idTokenParsed.preferred_username" :course-name="courseName" :visible="certificateVisible" @cancel="certificateVisible = false"></student-certificate>
  </div>
</template>

<script lang="jsx">
// NOTE:不要删除，虽然h未被使用，但是需要引入，否则会报错
import { h } from 'vue';
import toChinesNum from '@/lib/toChinesNum';
import studentCertificate from '../studentCertificate.vue';
import receiveBeanButton from './receive-bean-button.vue';
import { mapState } from 'vuex';

const learnStatusMap = {
  0: { value: 'unstarted', label: '未学习' },
  1: { value: 'finished', label: '已学习' },
  2: { value: 'finished', label: '已学习' },
  3: { value: 'finished', label: '已学习' },
};
const courseStatusMap = {
  0: { value: '', label: '' },
  1: { value: '', label: '' },
  2: { value: 'updated', label: '教师已更新' },
  3: { value: 'removed', label: '教师已删除' },
};

const articleStatusMap = {
  0: '您对实例做的任何修改都不会保留，请确保您已做好备份',
  1: '',
  2: '项目内容将更新为教师最新发布的版本，您对实例做的任何修改都不会保留，请确保您已做好备份',
  3: '项目内容已被教师删除，课节实例将自动关闭，您对实例做的任何修改都不会保留，请确保您已做好备份',
};

export default {
  components: { studentCertificate, receiveBeanButton },
  props: {
    catalogNum: { type: Number, default: 0 },
    mapFlag: { type: [Number, String], default: '' },
    finished: { type: Boolean, default: false },
    courseName: { type: String, default: '' },
    courseId: { type: [String, Number], default: '' },
    instituteName: { type: String, default: '' },
    endStudyTime: { type: String, default: '' },
  },
  emits: ['reload'],
  data() {
    return {
      certificateVisible: false,
      courseContents: [],
      loading: false,
    };
  },
  computed: {
    ...mapState(['giveBeansByCourse']),
  },
  watch: {
    courseId: 'getCatalogs',
  },
  mounted() {
    this.getCatalogs();
  },
  methods: {
    toChinesNum,
    getCatalogs() {
      if (!this.courseId) {
        return;
      }
      const obj = {
        courseId: this.courseId,
      };
      this.loading = true;
      this.$GET('/course_model/web/course_student/studentCatalog/studentCatalogList', obj).then((res) => {
        this.loading = false;
        this.courseContents = res.body;
      });
    },
    handleShowCertificate() {
      if (!this.finished) {
        return;
      }
      this.certificateVisible = true;
    },
    getLearnStatus(key) {
      return learnStatusMap[key || 0];
    },
    getCourseStatus(key) {
      return courseStatusMap[key || 0];
    },
    getRestartStatus(item) {
      return this.getLearnStatus(item.status).value === 'finished' && (this.getCourseStatus(item.status).value === 'updated' || (!this.getCourseStatus(item.status).value && +item.itemFlag));
    },
    handleRestart(item) {
      const self = this;
      let message = articleStatusMap[item.articleStatus];
      if (this.getCourseStatus(item.status).value === 'updated') {
        message = articleStatusMap[item.articleStatus];
        if (message === '您对实例做的任何修改都不会保留，请确保您已做好备份' && !+item.itemFlag) {
          message = '';
        }
      } else {
        message = articleStatusMap[0];
      }
      const obj = { courseId: this.courseId, catalogId: item.id };
      if (!message) {
        this.$GET('/course_model/web/course_student/course/againStudy', obj).then((res) => {
          if (res.errorCode) {
            return;
          }
          self.$router.push({ path: `/course/course-learn/${self.courseId}/${item.id}`, query: { courseName: self.courseName, catalogName: item.catalogName } });
        });
      } else {
        this.$confirm({
          centered: true,
          icon: () => <jt-icon style="color:rgba(255, 69, 77, 1)" type="iconwarning-circle-fill"></jt-icon>,
          title: '重新学习提示',
          content: message,
          onOk() {
            self.$store.commit('SET_LOADING', true);
            self.$GET('/course_model/web/course_student/course/againStudy', obj).then((res) => {
              self.$store.commit('SET_LOADING', false);
              if (res.errorCode) {
                return;
              }
              self.$router.push({ path: `/course/course-learn/${self.courseId}/${item.id}`, query: { courseName: self.courseName, catalogName: item.catalogName } });
            });
          },
        });
      }
    },

    handleStart(item) {
      const obj = {
        catalogId: item.id,
        courseId: this.courseId,
      };
      this.$GET('/course_model/web/course_student/student/studentCatalogAdd', obj, { userError: true }).then((res) => {
        if (res.body) {
          this.$router.push({ path: `/course/course-learn/${this.courseId}/${item.id}`, query: { courseName: this.courseName, catalogName: item.catalogName } });
        }
      });
    },
    handleContinue(item) {
      this.$router.push({ path: `/course/course-learn/${this.courseId}/${item.id}`, query: { courseName: this.courseName, catalogName: item.catalogName } });
    },
    handleRemove(item) {
      const self = this;
      if (+item.itemFlag) {
        this.$confirm({
          centered: true,
          icon: () => <jt-icon style="color:rgba(255, 69, 77, 1);" type="iconwarning-circle-fill"></jt-icon>,
          title: '删除课节提示',
          content: '课节实例将自动关闭，您对实例做的任何修改都不会保留，请确保您已做好备份',
          onOk() {
            self.$GET('/course_model/web/course_student/student/studentCatalogDelete', { catalogId: item.id, courseId: self.courseId }).then(() => {
              self.$notification.success({
                message: '成功',
                description: '课节删除成功',
              });
              self.getCatalogs();
              self.$emit('reload');
            });
          },
        });
      } else {
        self.$GET('/course_model/web/course_student/student/studentCatalogDelete', { catalogId: item.id, courseId: self.courseId }).then(() => {
          self.$notification.success({
            message: '成功',
            description: '课节删除成功',
          });
          self.getCatalogs();
          self.$emit('reload');
        });
      }
    },
    courseDisabled(item) {
      if (!(+item.videoFlag || +item.itemFlag || +item.documentFlag)) {
        return true;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

div,
ul,
li {
  display: flex;
}
p {
  margin: 0;
}
li {
  margin: 0;
}
.contents {
  flex: 1;
  flex-direction: column;
  background: #fff;
  .contents-header {
    position: relative;
    justify-content: space-between;
    align-items: center;
    height: 65px;
    padding: 0 32px;
    border-bottom: 1px solid #e0e1e1;
    .title {
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      margin-right: 32px;
      color: #121f2c;
    }
    .progress {
      align-items: center;
      display: inline-flex;
      font-size: 14px;
      color: #606972;
    }
    .course-icon {
      cursor: pointer;
    }
    .course-bean-active {
      margin-right: 100px;
    }
  }
  .contents-list {
    flex: 1;
    flex-direction: column;
    padding: 0 24px;
    li {
      height: 72px;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px;
      position: relative;
      &:not(:last-child) {
        box-shadow: 0px 1px 0px 0px #eee;
      }
      margin-bottom: 1px;
      &:hover {
        background: #f8f9fa;
      }
      .left {
        display: block;
        .learn-status-tag {
          &.unstarted {
            color: #bec2c5;
            border: 1px solid #bec2c5;
          }
          &.unfinished {
            color: #ff7b00;
            border: 1px solid #ff7b00;
          }
          &.finished {
            color: #17c189;
            border: 1px solid #17c189;
          }
        }
        .catlog-name {
          margin-right: 12px;
          font-size: 16px;
          color: #121f2c;
        }
        .catlog-icon {
          display: inline-block;
          width: 48px;
          text-align: center;
          height: 24px;
          line-height: 24px;
          font-size: 12px;
          border-radius: 2px;
          margin-right: 8px;
        }
        .catlog-icon1 {
          color: #389bff;
          background: rgba(56, 155, 255, 0.1);
          .catlog-icon();
        }
        .catlog-icon2 {
          color: #f79032;
          background: rgba(247, 144, 50, 0.1);
          .catlog-icon();
        }
        .catlog-icon3 {
          color: #b563fc;
          background: rgba(181, 99, 252, 0.1);
          .catlog-icon();
        }
      }
      .course-status-tag {
        position: absolute;
        top: 0;
        right: 0;
        width: 84px;
        height: 20px;
        border-radius: 0px 0px 0px 100px;
        padding-left: 16px;
        color: #fff;
        font-size: 12px;
        &.updated {
          background: #ff7b00;
        }
        &.removed {
          background: #ff454d;
        }
      }
      .ant-btn-link {
        padding: 0;
        padding-left: 16px;
      }
      .danger {
        color: #ff454d;
      }
    }
  }
}
</style>
