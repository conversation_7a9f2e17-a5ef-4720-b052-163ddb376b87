export const list = [
  //banner图
  {
    id: '0',
    h1: '九天 · 毕昇',
    h2: '一站式人工智能学习和实战平台',
    p: '基于中移九天深度学习平台，为AI学习者提供充沛的GPU算力、丰富的数据和学习实战资源，服务课程学习、比赛打榜、工作求职等全流程场景，并面向高校提供在线教学、科研开发的一站式解决方案',
    btn: '',
    img: require('@/assets/image/home/<USER>'),
    jumpPath: '',
  },
  {
    id: '1',
    h1: '',
    h2: '人工智能学习课程',
    p: '联手名师名校，打造AI精品课程 ',
    p2: '理论实战结合，带你打开AI世界的大门',
    btn: '立即查看',
    img: require('@/assets/image/home/<USER>'),
    jumpPath: '/course',
  },
  {
    id: '4',
    h1: '',
    h2: '畅享算力大礼包',
    p: '多种方式赢取算力豆，即刻开启模型训练之旅',
    btn: '立即参与',
    img: require('@/assets/image/home/<USER>'),
    textClass: 'gain-suanli',
    imgClass: 'gain-suanli',
    jumpPath: '/user-center?activeTab=2',
  },
  {
    id: '2',
    h1: '不服来战',
    h2: '汇集国内外顶尖AI大赛',
    p: '挑战性难题、同场竞技、一较高下',
    btn: '立即查看',
    img: require('@/assets/image/home/<USER>'),
    jumpPath: '/competition',
  },
  {
    id: '3',
    h1: '',
    h2: 'AI一站式开发环境',
    p: '提供在线开发和模型调试工具，集成主流人工智能开源算法框架，为模型训练提供一站式服务',
    btn: '立即使用',
    img: require('@/assets/image/home/<USER>'),
    flag: 'model-training',
  },
];
