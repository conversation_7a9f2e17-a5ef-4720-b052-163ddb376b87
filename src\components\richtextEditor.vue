<template>
  <div style="position: relative">
    <div :id="id" :bindId="bindId"></div>
    <span v-if="showValidator && validate" class="validator-txt">请输入</span>
    <upload-modal :visible="uploadModalVisible" @ok="handleUploadModalOk" @cancel="handleUploadModalCancel" />
  </div>
</template>

<script>
import wangEditor from 'wangeditor';
import { GET } from '@/request';
import UploadMenu from '@/utils/wangEditorUpload.js';
import { uploadMenuKey } from '@/utils/wangEditorUpload.js';
import uploadModal from '@/components/chunkUploadModal';
import { mapState } from 'vuex';
wangEditor.registerMenu(uploadMenuKey, UploadMenu);

export default {
  name: 'RichtextEditor',
  components: {
    uploadModal,
  },
  props: {
    id: {
      // div元素唯一值
      type: String,
    },
    bindId: {
      // div元素唯一值
      type: String,
    },
    value: {
      type: String,
    },
    modelValue: {
      type: String,
    },
    zIndex: {
      // 富文本编辑器z-index值
      type: Number,
      default: 1,
    },
    url: {
      // 初始化url路径值
      type: String,
      default: null,
    },
    // 在非表单中，可以单独进行为空的校验
    validate: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue', 'input', 'change'],
  data() {
    return {
      editorInstance: null,
      showValidator: false,
      uploadModalVisible: false,
    };
  },
  watch: {
    url: {
      handler(val) {
        if (val) {
          GET(val, {}).then((res) => {
            this.editorInstance.txt.html(res);
          });
        }
      },
      immediate: true,
    },
    refreshToken() {
      if (this.editorInstance) {
        this.editorInstance.config.uploadImgHeaders = {
          Authorization: 'Bearer ' + this.$keycloak.token,
        };
      }
    },
  },
  computed: {
    ...mapState(['refreshToken']),
  },
  mounted() {
    this.initEditor();
  },
  methods: {
    handleUploadModalOk(uploadParams) {
      this.uploadModalVisible = false;
      const { downloadUrl, fileName } = uploadParams;
      this.editorInstance.cmd.do('insertHTML', `<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
    },
    handleUploadModalCancel() {
      this.uploadModalVisible = false;
    },
    handleUploadBtnClick() {
      this.uploadModalVisible = true;
    },
    initEditor() {
      this.editorInstance = new wangEditor(this.bindId ? `[bindId]` : `#${this.id}`);
      this.editorInstance.uploadBtnClick = this.handleUploadBtnClick;
      this.editorInstance.config.onchange = (val) => {
        // wangeditor删除内容后会返回一个空格字符串，导致内容不为空
        const newHtml = val === ' ' ? '' : val;
        this.$emit('input', newHtml);
        this.$emit('update:modelValue', newHtml);
        this.$emit('change', newHtml);
        this.validate && this.validator(newHtml);
      };
      this.editorInstance.config.uploadImgShowBase64 = false;
      this.editorInstance.config.uploadFileName = 'file';
      this.editorInstance.config.uploadImgServer = './object/web/storage/image/upload';
      this.editorInstance.config.uploadImgHeaders = {
        Authorization: 'Bearer ' + this.$keycloak.token,
      };
      this.editorInstance.config.uploadImgHooks = {
        customInsert: function (insertImgFn, result) {
          insertImgFn(result.body.url);
        },
      };
      this.editorInstance.config.zIndex = this.zIndex;
      this.editorInstance.config.focus = false;
      this.editorInstance.create();
    },
    validator(val) {
      const validateValue = val === undefined ? this.value || this.modelValue : val;
      if (validateValue && validateValue !== ' ') {
        this.showValidator = false;
      } else {
        this.showValidator = true;
      }
      return !this.showValidator;
    },
  },
};
</script>

<style lang="less" scoped>
.validator-txt {
  position: absolute;
  left: 0px;
  bottom: -24px;
  color: red;
}
</style>
