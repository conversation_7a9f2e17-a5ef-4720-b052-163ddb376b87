<template>
  <div class="course-introduction-edit">
    <h3 class="introduction-title">编辑课程介绍</h3>
    <div style="padding: 0px 32px">
      <div class="edit-item">
        <div class="item-title">课程描述</div>
        <div id="description-editor"></div>
      </div>

      <div class="edit-item">
        <div class="item-title">前置知识</div>
        <div id="preknowledge-editor"></div>
      </div>

      <div class="edit-item">
        <div class="item-title">课程目标</div>
        <div id="courseOutline-editor"></div>
      </div>
    </div>

    <a-space class="btn-group">
      <a-button class="save" type="primary" :disabled="loading" @click="handleSaveClick">保存</a-button>
      <a-button class="cancel" :disabled="loading" @click="handleCancelClick">取消</a-button>
    </a-space>

    <upload-modal :visible="descriptionUploadModalVisible" @cancel="descriptionUploadModalCancel" @ok="descriptionUploadModalOk"></upload-modal>
    <upload-modal :visible="preknowledgeUploadModalVisible" @cancel="preknowledgeUploadModalCancel" @ok="preknowledgeUploadModalOk"></upload-modal>
    <upload-modal :visible="courseGoalUploadModalVisible" @cancel="courseGoalUploadModalCancel" @ok="courseGoalUploadModalOk"></upload-modal>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import wangEditor from 'wangeditor';
import UploadModal from '@/components/chunkUploadModal';
import { textUpload, updateOrSaveCourseIntro } from '@/apis/teaching.js';
import UploadMenu from '@/utils/wangEditorUpload.js';
import { uploadMenuKey } from '@/utils/wangEditorUpload.js';
wangEditor.registerMenu(uploadMenuKey, UploadMenu);
let preknowledgeEditor = null;

export default {
  name: 'CourseIntroductionEdit',
  components: {
    UploadModal,
  },
  data() {
    return {
      courseId: this.$route.params.courseId,
      descriptionEditor: null,
      descriptionUploadModalVisible: false,
      preknowledgeEditor: null,
      preknowledgeUploadModalVisible: false,
      courseGoalEditor: null,
      courseGoalUploadModalVisible: false,
      courseDescriptionInfo: '',
      preknowledgeInfo: '',
      courseGoalInfo: '',
      loading: false,
    };
  },
  computed: {
    ...mapState(['refreshToken']),
    ...mapState('course', ['courseIntroductionData']),
  },
  watch: {
    refreshToken() {
      if (this.descriptionEditor) {
        this.descriptionEditor.config.uploadImgHeaders = {
          Authorization: 'Bearer ' + this.$keycloak.token,
        };
      }
      if (this.preknowledgeEditor) {
        this.preknowledgeEditor.config.uploadImgHeaders = {
          Authorization: 'Bearer ' + this.$keycloak.token,
        };
      }
      if (this.courseGoalEditor) {
        this.courseGoalEditor.config.uploadImgHeaders = {
          Authorization: 'Bearer ' + this.$keycloak.token,
        };
      }
    },
  },
  mounted() {
    this.initEditors();
    // 初始化数据
    this.descriptionEditor.txt.html(this.courseIntroductionData.courseDescData);
    this.preknowledgeEditor.txt.html(this.courseIntroductionData.courseFrontKnowledgeData);
    this.courseGoalEditor.txt.html(this.courseIntroductionData.courseGoalData);
  },
  beforeUnmount() {
    this.descriptionEditor.destroy();
    this.descriptionEditor = null;
    this.preknowledgeEditor.destroy();
    this.preknowledgeEditor = null;
    this.courseGoalEditor.destroy();
    this.courseGoalEditor = null;
  },
  methods: {
    ...mapMutations('course', ['SET_COURSEINTRODUCTION_DATA']),

    // 提交保存数据
    handleSaveClick() {
      if (this.courseDescriptionInfo && this.courseDescriptionInfo !== ' ' && this.preknowledgeInfo && this.preknowledgeInfo !== ' ' && this.courseGoalInfo && this.courseGoalInfo !== ' ') {
        // 提交数据
        this.updateAllText();
      } else {
        this.$message.warning('信息不可为空，请确认后再保存');
      }
    },
    updateAllText() {
      this.loading = true;
      const loadingCallback = this.$message.loading('课程介绍保存中...');
      const self = this;
      Promise.all([textUpload({ baseString: this.courseDescriptionInfo }), textUpload({ baseString: this.preknowledgeInfo }), textUpload({ baseString: this.courseGoalInfo })])
        .then((res) => {
          if (res[0].state === 'OK' && res[1].state === 'OK' && res[2].state === 'OK') {
            // 新增课程介绍，每次保存时都要新增，因为后台需要保存上一次的数据
            updateOrSaveCourseIntro({
              courseId: self.courseId,
              courseDesc: res[0].body.key,
              courseFrontKnowledge: res[1].body.key,
              courseGoal: res[2].body.key,
            })
              .then((res1) => {
                if (res1.state === 'OK') {
                  self.SET_COURSEINTRODUCTION_DATA({
                    courseId: self.courseId,
                    courseDesc: res[0].body.key,
                    courseFrontKnowledge: res[1].body.key,
                    courseGoal: res[2].body.key,
                    courseDescData: self.courseDescriptionInfo,
                    courseFrontKnowledgeData: self.preknowledgeInfo,
                    courseGoalData: self.courseGoalInfo,
                    pubTime: self.courseIntroductionData.pubTime,
                    status: '0',
                  });
                  self.$message.success('课程介绍保存成功');
                  this.backToCourseManage(true);
                } else {
                  this.$message.error('课程介绍保存失败');
                }
              })
              .finally(() => {
                this.loading = false;
                loadingCallback();
              });
          } else {
            this.loading = false;
            loadingCallback();
            this.$message.error('课程介绍保存失败');
          }
        })
        .catch(() => {
          this.loading = false;
          loadingCallback();
        });
    },
    handleCancelClick() {
      this.backToCourseManage();
    },
    backToCourseManage(noLeaveConfirm) {
      // eslint-disable-next-line no-unused-vars
      const { subPage, ...params } = this.$route.params;
      this.$router.replace({
        name: '课程管理',
        params,
        query: {
          noLeaveConfirm,
        },
      });
    },
    // 初始三个wangEditor
    initEditors() {
      const descriptionEditor = new wangEditor(`#description-editor`);
      descriptionEditor.uploadBtnClick = this.handleDescriptionUploadBtnClick;
      descriptionEditor.config.onchange = (newHtml) => {
        this.courseDescriptionInfo = newHtml;
      };
      descriptionEditor.config.uploadImgShowBase64 = false;
      descriptionEditor.config.uploadFileName = 'file';
      descriptionEditor.config.uploadImgServer = './object/web/storage/image/upload';
      descriptionEditor.config.uploadImgHeaders = {
        Authorization: 'Bearer ' + this.$keycloak.token,
      };
      descriptionEditor.config.uploadImgHooks = {
        customInsert: function (insertImgFn, result) {
          insertImgFn(result.body.url);
        },
      };
      descriptionEditor.config.zIndex = 99;
      descriptionEditor.create();
      this.descriptionEditor = descriptionEditor;

      preknowledgeEditor = new wangEditor(`#preknowledge-editor`);
      preknowledgeEditor.uploadBtnClick = this.handlePreknowledgeUploadBtnClick;
      preknowledgeEditor.config.onchange = (newHtml) => {
        this.preknowledgeInfo = newHtml;
      };
      preknowledgeEditor.config.uploadImgShowBase64 = false;
      preknowledgeEditor.config.uploadFileName = 'file';
      preknowledgeEditor.config.uploadImgServer = './object/web/storage/image/upload';
      preknowledgeEditor.config.uploadImgHeaders = {
        Authorization: 'Bearer ' + this.$keycloak.token,
      };
      preknowledgeEditor.config.uploadImgHooks = {
        customInsert: function (insertImgFn, result) {
          insertImgFn(result.body.url);
        },
      };
      preknowledgeEditor.config.zIndex = 99;
      preknowledgeEditor.create();
      this.preknowledgeEditor = preknowledgeEditor;

      const courseGoalEditor = new wangEditor(`#courseOutline-editor`);
      courseGoalEditor.uploadBtnClick = this.handleCourseGoalUploadBtnClick;
      courseGoalEditor.config.onchange = (newHtml) => {
        this.courseGoalInfo = newHtml;
      };
      courseGoalEditor.config.uploadImgShowBase64 = false;
      courseGoalEditor.config.uploadFileName = 'file';
      courseGoalEditor.config.uploadImgServer = './object/web/storage/image/upload';
      courseGoalEditor.config.uploadImgHeaders = {
        Authorization: 'Bearer ' + this.$keycloak.token,
      };
      courseGoalEditor.config.uploadImgHooks = {
        customInsert: function (insertImgFn, result) {
          insertImgFn(result.body.url);
        },
      };
      courseGoalEditor.config.zIndex = 99;
      courseGoalEditor.create();
      this.courseGoalEditor = courseGoalEditor;
    },
    // 关闭课程描述上传弹框
    descriptionUploadModalCancel() {
      this.descriptionUploadModalVisible = false;
    },
    // 课程描述弹框上传确认
    descriptionUploadModalOk(uploadParams) {
      this.descriptionUploadModalVisible = false;
      const { downloadUrl, fileName } = uploadParams;
      this.descriptionEditor.cmd.do('insertHTML', `<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
      // this.descriptionEditor.txt.append(`<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
    },
    // 打开课程描述弹框
    handleDescriptionUploadBtnClick() {
      this.descriptionUploadModalVisible = true;
    },
    preknowledgeUploadModalCancel() {
      this.preknowledgeUploadModalVisible = false;
    },
    preknowledgeUploadModalOk(uploadParams) {
      this.preknowledgeUploadModalVisible = false;
      const { downloadUrl, fileName } = uploadParams;
      this.preknowledgeEditor.cmd.do('insertHTML', `<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
      // this.preknowledgeEditor.txt.append(`<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
    },
    handlePreknowledgeUploadBtnClick() {
      this.preknowledgeUploadModalVisible = true;
    },
    courseGoalUploadModalCancel() {
      this.courseGoalUploadModalVisible = false;
    },
    courseGoalUploadModalOk(uploadParams) {
      this.courseGoalUploadModalVisible = false;
      const { downloadUrl, fileName } = uploadParams;
      this.courseGoalEditor.cmd.do('insertHTML', `<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
      //this.courseGoalEditor.txt.append(`<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
    },
    handleCourseGoalUploadBtnClick() {
      this.courseGoalUploadModalVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.course-introduction-edit {
  .introduction-title {
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    border-bottom: 1px solid #e0e1e1;
    height: 64px;
    line-height: 64px;
    padding-left: 32px;
    margin-bottom: 32px;
  }
  .edit-item {
    position: relative;
    margin-bottom: 48px;
    .item-title {
      margin-bottom: 20px;
      padding-left: 16px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      font-size: 14px;
      &:before {
        position: absolute;
        content: '';
        top: 3px;
        left: 0px;
        display: block;
        width: 4px;
        height: 16px;
        background-color: #0082ff;
      }
      &:after {
        content: ' *';
        color: red;
      }
    }
    :deep(.w-e-text-container) {
      ul li {
        list-style: disc;
      }
      ol li {
        list-style: decimal;
      }
    }
    :deep(.w-e-droplist) {
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
.btn-group {
  margin-bottom: 64px;
  padding: 0px 32px;
  .save {
    width: 120px;
  }
  .cancel {
    width: 88px;
  }
}
</style>

<style lang="less">
@import '~@/assets/styles/index.less';
.ant-message {
  z-index: 10000;
}
.select-file-modal {
  .add-file-container {
    .upload-operations {
      font-size: @jt-font-size-base;
      color: @jt-primary-color;
      display: flex;
      justify-content: space-between;
      p {
        cursor: pointer;
        span {
          margin-left: @jt-gap-base;
        }
      }
    }
  }
}
</style>
