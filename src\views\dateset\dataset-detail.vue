<template>
  <div class="home" style="width: 100%">
    <header>
      <div class="inner">
        <jt-breadcrumb class="jt-breadcrumb">
          <jt-breadcrumb-item>
            <router-link to="/dataset">数据集</router-link>
          </jt-breadcrumb-item>
          <jt-breadcrumb-item>
            <router-link
              :to="{
                name: '数据集',
                params: { datasetClassId: formdata.datasetClassId },
              }"
            >
              {{ !formdata.datasetClassId || formdata.datasetClassId == '0' ? '全部数据集' : formdata.datasetClassId == '1' ? '公开数据集' : '中国移动数据集' }}
            </router-link>
          </jt-breadcrumb-item>
          <!-- <jt-breadcrumb-item>{{ $route.name }}</jt-breadcrumb-item> -->
          <jt-breadcrumb-item style="color: #121f2c">{{ formdata.dataName }}</jt-breadcrumb-item>
        </jt-breadcrumb>
      </div>
    </header>
    <div class="content-box">
      <div class="card-container">
        <div class="card-title">
          <div class="title-box">
            <h1>
              {{ formdata.dataName }}
              <!-- <span>{{formdata.dataClassificationId == '1'?'公开':'中国移动'}}</span> -->
              <jt-button type="primary" class="filter-item" ghost style="display: inline-block">{{ formdata.dataClassificationId == '1' ? '公开' : '中国移动' }}</jt-button>
            </h1>
            <p>{{ formdata.dataDescribe }}</p>
            <span style="color: #a0a6ab">
              <jt-icon type="user" style="padding-right: 10px"></jt-icon>
              <span>{{ formdata.dataUserNumber }}人使用</span>
              <span style="margin-left: 25px">
                <span style="padding-right: 10px" class="iconfont">&#xe607;</span>
                {{ formdata.dataTime }}
              </span>
            </span>
          </div>
          <div v-if="!$keycloak.idTokenParsed">
            <jt-button class="button" type="primary" @click="gotoLogin">立即训练</jt-button>
          </div>
          <div v-else>
            <jt-button v-if="hasInstance" class="button" type="primary" @click="gotoInstanceListPage">继续训练</jt-button>
            <jt-button v-else class="button" type="primary" @click="gotoCreatePage">立即训练</jt-button>
          </div>
        </div>
        <div class="table-content">
          <div class="tabel-title">
            <h1>数据集介绍</h1>
            <!-- <p class="p1">Yanderifier数据集包括</p> -->
            <p>{{ formdata.dataIntroduce }}</p>
          </div>
          <div class="table-list">
            <h1>文件列表</h1>
            <jt-configProvider>
              <template #renderEmpty>
                <jt-empty
                  :image="emptyImage"
                  :image-style="{
                    margin: '20px auto',
                    width: '300px',
                    height: '300px',
                  }"
                >
                  <template #description>
                    <div class="emptyDescriptS"></div>
                  </template>
                </jt-empty>
              </template>
              <jt-table :columns="columns" :data-source="data" row-key="fileName" :pagination="false" class="date-tabel">
                <template #name="{ text }">
                  <a>{{ text }}</a>
                </template>
                <!-- <span slot="customTitle">Name</span> -->
                <template #action="{ record }">
                  <span>
                    <a>{{ record.name }}</a>
                  </span>
                </template>
              </jt-table>
            </jt-configProvider>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Breadcrumb as JtBreadcrumb, Icon as JtIcon, Button as JtButton, Table as JtTable, ConfigProvider as jtConfigProvider, Empty as JtEmpty } from 'ant-design-vue';

import API from '@/constants/api/API.js';
import { getLocalConfig } from '@/config';
import { login } from '@/keycloak';
import { gotoConslePlatform } from '@/utils/utils';

export default {
  components: {
    JtBreadcrumb,
    JtBreadcrumbItem: JtBreadcrumb.Item,
    JtIcon,
    JtButton,
    JtTable,
    JtEmpty,
    jtConfigProvider,
  },
  data() {
    return {
      data: [],
      columns: [
        {
          title: '序号',
          dataIndex: 'serialNumber',
          key: 'serialNumber',
        },
        {
          title: '名称',
          dataIndex: 'fileName',
          key: 'fileName',
        },
        {
          title: '大小',
          dataIndex: 'fileSize',
          key: 'fileSize',
        },
        {
          title: '描述',
          key: 'fileDescribe',
          dataIndex: 'fileDescribe',
        },
      ],
      formdata: {},
      hasInstance: false, // 实例是否存在
      emptyImage: require('@/assets/image/emptys2x.png'),
    };
  },
  computed: {
    CONSOLE_URL() {
      return getLocalConfig('CONSOLE_URL');
    },
  },

  created() {
    this.pageInit();
  },
  methods: {
    pageInit() {
      this.formdata = {
        dataId: this.$route.query.dataId,
        dataName: this.$route.query.dataName,
        dataUserNumber: this.$route.query.dataUserNumber,
        dataDescribe: this.$route.query.dataDescribe,
        dataIntroduce: this.$route.query.dataIntroduce,
        dataTime: this.$route.query.dataTime,
        dataClassificationId: this.$route.query.dataClassificationId,
        projectId: this.$route.query.projectId,
        datasetClassId: this.$route.query.datasetClassId,
      };
      API.dasteset_model.getDataFileList(this.formdata).then((res) => {
        this.data = res.body;
      });
      // 获取实例是否已存在
      API.dp_platform.getDatasetInstance_repeat({ projectId: this.$route.query.projectId }).then((res) => {
        if (res.code === 200) {
          this.hasInstance = res.data.projectIdRepeat;
        }
      });
    },
    /**
     * 跳转登录页
     */
    gotoLogin() {
      login();
    },
    /**
     * 跳转 实例列表页面显示当前单条实例
     */
    gotoInstanceListPage() {
      this.getInsertData('gotoInstanceListPage');
    },
    gotoCreatePage() {
      this.getInsertData('gotoCreatePage');
    },
    // 人数计量统计接口  getInsert
    async getInsertData(type) {
      await API.dasteset_model.getInsert({ projectId: this.formdata.projectId });

      if (type === 'gotoCreatePage') {
        gotoConslePlatform(`${this.CONSOLE_URL}/home/<USER>/instance-form?projectId=${this.formdata.projectId}&projectName=${this.formdata.dataName}`);
      } else {
        gotoConslePlatform(`${this.CONSOLE_URL}/home/<USER>
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/var.less';
header {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
}
.jt-breadcrumb {
  line-height: 56px;
  // margin-left: 120px;
}
.home {
  overflow: hidden;
  background-color: #f4f8fa;
}
.content-box {
  width: 1200px;
  margin: auto;
}
.headerWrapper {
  display: flex;
  font-size: 14px;
  height: 56px;
  padding: 10px 120px;
  align-items: center;
  /* margin-top: 90px; */
  box-sizing: border-box;
  background-color: #fff;
}
.card-container {
  /* background: #ffff; */
  overflow: hidden;
  // padding: 0px 120px;
  margin-top: 20px;
  .card-title {
    height: 168px;
    background: #ffff;
    padding: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-box {
      h1 {
        span {
          height: 17px;
          font-size: 12px;
          font-weight: 400;
          color: #0082ff;
          padding: 4px 12px;
          line-height: 17px;
          background: #ffffff;
          border: 1px solid #389bff;
          border-radius: 2px;
          margin-left: 8px;
        }
      }
      p {
        color: #606972;
        margin: 16px 0;
      }
    }
    .title-foot {
      font-size: 14px;
      width: 106px;
      height: 40px;
      background: #0082ff;
      color: #ffffff;
      text-align: center;
      line-height: 42px;
      cursor: pointer;
      border-radius: 2px;
    }
  }
  .table-content {
    margin-top: 26px;
    background: #ffff;
    padding: 32px;
    .tabel-title {
      .p1 {
        color: #606972;
        margin: 16px 0;
      }
      p {
        color: #121f2c;
        margin-top: 10px;
      }
    }
    .table-list {
      margin-top: 49px;
      h1 {
        margin-bottom: 32px;
      }
    }
  }
}
.filter-item {
  margin-right: 12px;
  display: block;
  padding: 2px 11px;
  line-height: 20px;
  height: auto;
  font-size: 14px;
  &.ant-btn-default {
    border-color: transparent;
    box-shadow: none;
  }
}
:deep(.date-tabel .ant-table-thead) {
  background: #f7f9fa;
}
</style>
