@import '~@/assets/styles/index.less';

div,
ul,
li {
  display: flex;
}
p {
  margin: 0;
}
li {
  margin: 0;
}
.flex-align-center {
  align-items: center;
}
.column {
  display: flex;
  flex-direction: column;
}
.contents {
  flex: 1;
  flex-direction: column;
  background: #fff;
  .contents-header {
    justify-content: space-between;
    align-items: center;
    height: 65px;
    padding: 0 32px;
    border-bottom: 1px solid #e0e1e1;
    .title {
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      margin-right: 32px;
      color: #121f2c;
    }
    .progress {
      align-items: center;
      display: inline-flex;
      font-size: 14px;
      color: #606972;
    }
    .course-icon {
      cursor: pointer;
    }
  }
  .contents-list {
    flex: 1;
    flex-direction: column;
    padding: 0 24px;
    li {
      height: 72px;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px;
      position: relative;
      &:not(:last-child) {
        box-shadow: 0px 1px 0px 0px #e0e1e1;
      }
      margin-bottom: 1px;
      &:hover {
        background: #f8f9fa;
      }
      .left {
        display: flex;
        width: 360px;
        .learn-status-tag {
         
          &.unstarted {
            color: #bec2c5;
            border: 1px solid #bec2c5;
          }
          &.unfinished {
            color: #ff7b00;
            border: 1px solid #ff7b00;
            background-color: #fff;
          }
          &.finished {
            color: #17c189;
            border: 1px solid #17c189;
            background-color: #fff;
          }
        }
        .catlog-name {
          margin-right: 12px;
          font-size: 16px;
          color: #121f2c;
          max-width: 230px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        img {
          margin-right: 4px;
          width: 24px;
        }
      }
      .right {
        flex: 1;
        justify-content: space-between;
      }
      .course-status-tag {
        position: absolute;
        top: 0;
        right: 0;
        width: 84px;
        height: 20px;
        border-radius: 0px 0px 0px 100px;
        padding-left: 16px;
        color: #fff;
        font-size: 12px;
        &.updated {
          background: #ff7b00;
        }
        &.removed {
          background: #ff454d;
        }
      }
      .ant-btn-link {
        padding: 0;
        padding-left: 16px;
      }
      .danger {
        color: #ff454d;
      }
      .end-time {
        color: #f78500;
        font-size: 12px;
        &-outdated {
          color: #A0A6AB;
        }
      }
      .filled-tag {
        border: none;
        &.item {
          color: #f79032;
          background: rgba(247, 144, 50, 0.1);
        }
        &.test {
          color: #389bff;
          background: rgba(56, 155, 255, 0.1);
        }
      }
      .score {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 52px;
        height: 52px;
        border-radius: 50%;
        border: 1px solid #e0e1e1;
        cursor: pointer;
        .num{
          font-size: 16px;
          font-weight: 600;
        }
        .branch{
          font-size: 12px;
        }
        &.good {
          background: #e7fff7;
          border-color: #17c189;
          color: #17c189;
        }
        &.bad {
          background: #ffebe0;
          border-color: #ff5d00;
          color: #ff5d00;
        }
        &.normal {
          background: #ebf5ff;
          border-color: #0082ff;
          color: #0082ff;
        }
      }
    }
  }
}