import temp1 from './temp1.vue';
import temp2 from './temp2.vue';
import temp3 from './temp3.vue';
import temp4 from './temp4.vue';
import temp5 from './temp5.vue';
import temp6 from './temp6.vue';
import temp7 from './temp7.vue';

export const weightMap = {
  video: 1,
  pdf: 2,
  jupyter: 4,
};

export const tempMap = [temp1, temp2, temp3, temp4, temp5, temp6, temp7];

export function tempSwitcher(type) {
  return tempMap[type - 1];
}
