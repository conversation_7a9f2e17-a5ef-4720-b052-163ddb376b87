<script lang="jsx" setup>
import { EditOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <a-spin v-if="tableData.length > 0" :spinning="publishLoading" tip="发布中...">
      <a-table row-key="id" :data-source="tableData" :columns="tableColumns" :expanded-row-keys="expandedRowKeys" :pagination="false" :show-header="false" :expand-icon-column-index="-1" :expand-icon-as-cell="false" :custom-row="customRow">
        <template #name-slot="{ text, record, index }">
          <a-space>
            <div v-if="isNotOpenAndPublishedCourse" class="sort-btns" style="margin-right: 12px">
              <jt-icon :class="index == tableData.length - 1 ? 'icon-disable' : ''" type="icondown" @click="handlesortByIcon(record, index, 'down')" />
              <jt-icon :class="index == 0 ? 'icon-disable' : ''" type="iconup" @click="handlesortByIcon(record, index, 'up')" />
            </div>
            <EditOutlined v-if="isNotOpenAndPublishedCourse" class="edit-icon" @click="editCourseName(record)" />
            <a-tag color="#63B3FF" style="margin-left: 10px">课节{{ toChinesNum(index + 1) }}</a-tag>
            <span class="course-name">{{ text }}</span>
          </a-space>
        </template>
        <template #id-slot="{ record }">
          <a-space>
            <a-button v-if="isNotOpenAndPublishedCourse" :disabled="record.haveProject == '1'" :class="record.haveProject == '1' && 'btn-disable'" class="handle-btn" @click="handleAddProject(record.id)"><jt-icon type="iconitem-add"></jt-icon>项目</a-button>
            <a-button v-if="isNotOpenAndPublishedCourse" class="handle-btn" @click="handleAddVideo(record.id)"><jt-icon type="iconvideo-add"></jt-icon>视频</a-button>
            <a-button v-if="isNotOpenAndPublishedCourse" class="handle-btn" @click="handleAddDoc(record.id)"><jt-icon type="iconfile-add"></jt-icon>文档</a-button>

            <span v-if="isNotOpenAndPublishedCourse" class="delete-icon"><jt-icon type="iconshanchu1" @click="deleteCourse(record)" /></span>
            <!-- <span class="expand-icon" @click="handleExpand(record)"><jt-icon :type="expandedRowKeys.includes(record.id) ? 'iconjiantoushang' : 'iconjiantouxia'" /></span> -->
            <span class="expand-icon" @click="handleExpand(record)">
              <UpOutlined v-if="expandedRowKeys.includes(record.id)" />
              <DownOutlined v-else />
            </span>
          </a-space>
        </template>
        <!-- 展开项table -->
        <template #expandedRowRender="parentRecord">
          <a-table v-if="parentRecord.subs && parentRecord.subs.length > 0" class="sub-table" row-key="id" :data-source="parentRecord.subs" :columns="subTableColumn" :pagination="false" size="middle" :custom-row="subCustomRow" :custom-header-row="subCustomHeaderRow">
            <template #subname-slot="{ value, record, index }">
              <div class="sub-name-container">
                <div v-if="isNotOpenAndPublishedCourse" class="sort-btns">
                  <jt-icon :class="record.downArrow ? '' : 'icon-disable'" type="icondown" @click="handlesortByIcon(record, index, 'down')" />
                  <jt-icon :class="record.upArrow ? '' : 'icon-disable'" type="iconup" @click="handlesortByIcon(record, index, 'up')" />
                </div>
                <div v-else class="sort-btns" style="width: 32px"></div>
                <!-- 发布或创建中编辑按钮暂时隐藏 -->
                <EditOutlined v-if="isNotOpenAndPublishedCourse" :class="record.status === '3' ? 'icon-disable' : ''" class="edit-icon" @click="handleSubTitleEdit(record)" />
                <span class="sub-name-text" :title="value">{{ value }}</span>
                <a-space v-if="record.spec" style="margin-left: 8px">
                  <a-tag v-for="item in record.spec.split(',')" :key="item" class="resource-tag">
                    {{ item == 'cpu' ? 'CPU' : 'vGPU' }}
                  </a-tag>
                </a-space>
                <span v-if="record.instanceModel && record.resourseType === '1'" :class="record.instanceModel === 'Jupyter' ? 'jt-jupyter-icon' : 'jt-vscode-icon'">{{ record.instanceModel === 'Jupyter' ? 'J' : 'V' }}</span>
              </div>
            </template>

            <template #type-slot="value">
              <a-tag :class="value == '3' ? 'doc-tag' : value == '1' ? 'project-tag' : 'video-tag'">
                <span>{{ toTypeName(value) }}</span>
              </a-tag>
            </template>

            <template #status-slot="value">
              <div :class="value == '1' ? 'published' : value == '3' ? 'verifying' : 'unpublish'">{{ value == '1' ? '已发布' : value == '3' ? `${currentActiveCourse.courseFlag == '1' ? '创建中' : '发布中'}` : '未发布' }}</div>
            </template>

            <!-- <a-tag slot="status-slot" slot-scope="value" :color="value == '1' ? 'green' : ''">
              {{ value == '1' ? '已发布' : '未发布' }}
            </a-tag> -->
            <template #updateTime-slot="{ record }">
              <span>{{ record.updateTime ? record.updateTime : record.createTime }}</span>
            </template>

            <template #subid-slot="{ record, id }">
              <a-space size="large" class="operation-group">
                <span v-if="isNotOpenAndPublishedCourse" @click="deletePart(id, record)">删除</span>
                <span v-if="currentActiveCourse.courseFlag != '1' && isNotOpenAndPublishedCourse" :class="record.status == '1' || record.status == '3' ? 'disable' : ''" @click="publishPart(id, record)">发布</span>
                <span v-if="record.resourseType != '1'" @click="previewFile(parentRecord.id, id, record)">预览</span>
              </a-space>
            </template>
          </a-table>

          <div v-else class="subtable-empty">
            <jt-icon type="iconzanwushuju" style="font-size: 48px; color: #e0e1e1"></jt-icon>
            <p style="margin: 16px 0px 10px">您暂未添加任何课节内容</p>
            <div v-if="isNotOpenAndPublishedCourse">请立即 <span @click="handleAddProject(parentRecord.id)">新增项目</span> | <span @click="handleAddDoc(parentRecord.id)">新增文档</span> | <span @click="handleAddVideo(parentRecord.id)">新增视频</span></div>
          </div>
        </template>
      </a-table>
    </a-spin>
    <div v-else class="empty-tooltip">
      <div style="position: relative">
        <img src="@/assets/image/emptys2x.png" alt="" />
        <div class="tootip-txt">
          <p class="intro">您暂未添加任何课节</p>
          <p v-if="isNotOpenAndPublishedCourse" class="route">请立即 <span style="color: #0082ff; cursor: pointer" @click="showCourseModal('add')"> 新增课节</span></p>
        </div>
      </div>
    </div>
    <!-- 新增或编辑课节弹框 -->
    <a-modal v-model:open="courseModelVisible" :title="courseOperationType === 'add' ? '新增课节' : '编辑课节'" destroy-on-close>
      <a-form ref="addForm" :model="addFormData" :colon="false" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item
          label="课节名称"
          name="courseName"
          :rules="{
            required: true,
            message: '20个字符以内',
            trigger: 'change',
            max: 20,
          }"
        >
          <a-input v-model:value="addFormData.courseName" placeholder="请输入课节名称" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-space style="margin: 6px 16px">
          <a-button @click="hideCourseModal"> 取消 </a-button>
          <a-button type="primary" :disabled="submitBtnDisable" @click="handleAddCourseConfirm"> 确定 </a-button>
        </a-space>
      </template>
    </a-modal>

    <!-- 新增或编辑项目 -->
    <project-modal v-if="projectModalVisible" :select-resource="selectResource" :course-id="courseId" :editing="editing" :catalog-id="catalogId" :visible="projectModalVisible" @hideModal="hideProjectModal" @createInterval="createIntervalByProject" />
    <!-- 新增或编辑文档 -->
    <doc-modal v-if="docModalVisible" :select-resource="selectResource" :course-id="courseId" :editing="editing" :catalog-id="catalogId" :visible="docModalVisible" @hideModal="hideDocModal" />
    <!-- 新增视频 -->
    <video-modal v-if="videoModalVisible" :select-resource="selectResource" :course-id="courseId" :editing="editing" :catalog-id="catalogId" :visible="videoModalVisible" @hideModal="hideVideoModal" />
  </div>
</template>

<script lang="jsx">
import ProjectModal from './ProjectModal.vue';
import DocModal from './DocModal.vue';
import VideoModal from './VideoModal.vue';
import { mapState } from 'vuex';
import toChinesNum from '@/lib/toChinesNum';
import { getCatalogAndResourceById, addCatalogByCourseId, updateCatalogById, deleteCatalogById, sortCatalog, sortResource, deleteVideoOrDoc, publishResource, deleteCatalogProject, getProjectStatusById } from '@/apis/teaching.js';
import _ from 'lodash';
import { openInNewTab } from '@/utils/utils';

let sourceRecord = null;
let targetRecord = null;
let intervalMap = new Map();

const PROJECT_STATUS = {
  UNPUBLISH: '1',
  PUBLISHING: '3',
  PUBLISHED: '0',
};

const COURSE_TYPE = {
  PUBLIC: '1', // 公开课
  CLOSE: '2', // 封闭课
};

export default {
  name: 'CourseContent',
  components: {
    ProjectModal,
    DocModal,
    VideoModal,
  },
  data() {
    return {
      isDestroying: false, // 由于在组件退出的时候还有可能有settimeout的接口调用异步任务在执行，用该字段进行标识
      editing: false,
      courseId: this.$route.params.courseId,
      publishLoading: false,
      editResourceData: {
        resourceName: '',
      },
      selectResource: {}, // 当前操作的资源对象
      catalogId: 0,
      courseModelVisible: false,
      projectModalVisible: false,
      docModalVisible: false,
      videoModalVisible: false,
      submitBtnDisable: true,
      sourceObj: null,
      targetObj: null,
      expandedRowKeys: [],
      addFormData: {
        courseName: '',
      },
      courseOperationType: 'add', // 新增或编辑课节 add or edit
      tableData: [],
      tableColumns: [
        {
          dataIndex: 'catalogName',
          key: 'catalogName',
          slots: { customRender: 'name-slot' },
        },
        {
          dataIndex: 'id',
          key: 'id',
          slots: { customRender: 'id-slot' },
          align: 'right',
        },
      ],
      subTableColumn: [
        {
          title: '名称',
          dataIndex: 'resourseName',
          key: 'resourseName',
          width: '30%',
          ellipsis: true,
          slots: { customRender: 'subname-slot' },
          customHeaderCell() {
            return {
              style: {
                'padding-left': '70px',
                'background-color': 'white',
              },
            };
          },
        },
        {
          title: '类型',
          dataIndex: 'resourseType', // 3 文档 2 视频 1项目
          key: 'resourseType',
          width: '10%',
          slots: { customRender: 'type-slot' },
          customHeaderCell() {
            return { style: { 'background-color': 'white' } };
          },
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: '10%',
          slots: { customRender: 'status-slot' },
          customHeaderCell() {
            return { style: { 'background-color': 'white' } };
          },
        },
        {
          title: '最新发布时间',
          dataIndex: 'pubTime',
          key: 'pubTime',
          width: '15%',
          customHeaderCell() {
            return { style: { 'background-color': 'white' } };
          },
        },
        {
          title: '最后编辑时间',
          dataIndex: 'updateTime',
          key: 'updateTime',
          width: '15%',
          slots: { customRender: 'updateTime-slot' },
          customHeaderCell() {
            return { style: { 'background-color': 'white' } };
          },
        },
        {
          title: '操作',
          dataIndex: 'id',
          key: 'id',
          width: '15%',
          slots: { customRender: 'subid-slot' },
          customHeaderCell() {
            return { style: { 'background-color': 'white' } };
          },
        },
      ],
    };
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
    isNotOpenAndPublishedCourse() {
      return !(this.currentActiveCourse.courseFlag == '1' && this.currentActiveCourse.coursePublish == '1');
    },
    subTab() {
      return this.$route.params.subTab;
    },
  },
  mounted() {
    if (this.subTab === 'course-content') {
      this.getTableData(true, this.initLoadingProjectInterval);
    }
  },
  beforeUnmount() {
    this.isDestroying = true;
    this.destroyAllIntervals();
  },
  methods: {
    toChinesNum,
    toTypeName(name) {
      return name === '3' ? '文档' : name === '1' ? '项目' : '视频';
    },
    /**
     * @description 获取table信息
     * @params bol 控制初始化的展开项
     * @params cb 数据加载完成的回调函数
     */
    async getTableData(bol = false, cb) {
      const res = await getCatalogAndResourceById({ courseId: this.courseId });
      if (res.state === 'OK' && res.body && res.body.length > 0) {
        // 这里需要按照项目 文档 视频进行排序，按钮的排序只能在类别内部 在这里计算是否需要去显示向上和向下的排序按钮
        res.body.forEach((catalog) => {
          const catalogSources = catalog.subs;
          if (catalogSources) {
            const len = catalogSources.length;
            for (let i = 0; i < len; i++) {
              if (i == 0) {
                // 第一个
                catalogSources[i].upArrow = false;
                if (len > 1 && catalogSources[0].resourseType == catalogSources[1].resourseType) {
                  catalogSources[0].downArrow = true;
                } else {
                  catalogSources[0].downArrow = false;
                }
              } else if (i < len - 1) {
                if (catalogSources[i].resourseType == catalogSources[i - 1].resourseType) {
                  catalogSources[i].upArrow = true;
                } else {
                  catalogSources[i].upArrow = false;
                }
                if (catalogSources[i].resourseType == catalogSources[i + 1].resourseType) {
                  catalogSources[i].downArrow = true;
                } else {
                  catalogSources[i].downArrow = false;
                }
              } else {
                // 最后一个
                catalogSources[i].downArrow = false;
                if (catalogSources[i].resourseType == catalogSources[i - 1].resourseType) {
                  catalogSources[i].upArrow = true;
                } else {
                  catalogSources[i].upArrow = false;
                }
              }
            }
          }
        });

        this.tableData = res.body;
        cb && cb();
        // 控制初始化的展开项
        if (bol) {
          if (this.catalogId) {
            // 展开该catalogId项
            if (!this.expandedRowKeys.includes(this.catalogId)) {
              this.expandedRowKeys.push(this.catalogId);
            }
          } else {
            // 默认展开第一个
            this.expandedRowKeys.push(this.tableData[0].id);
          }
        }
      } else {
        this.tableData = [];
      }
    },
    initLoadingProjectInterval() {
      this.tableData.forEach((paragraph) => {
        if (paragraph.subs && paragraph.subs.length > 0) {
          paragraph.subs.forEach((item) => {
            // 初始化如果有正在发布的项目则进行轮询
            if (item.status === PROJECT_STATUS.PUBLISHING && item.resourseType === '1') {
              this.createIntervalByProject(item);
            }
          });
        }
      });
    },
    // 发布中的项目创建定时器轮循项目的发布状态
    createIntervalByProject(projectItem, isClickTrigger = false) {
      if (intervalMap.get(projectItem.id)) {
        return;
      }
      const self = this;
      const actionType = this.currentActiveCourse.courseFlag == COURSE_TYPE.PUBLIC ? '创建' : '发布';
      isClickTrigger &&
        this.$message.success({
          content: <span style="color: #666666;">{`项目“${projectItem.resourseName}”${actionType}中，请稍后查看`}</span>,
          icon: <jt-icon style="color: #0082FF;" type="icondengdaizhong"></jt-icon>,
        });
      createTimeout();
      // 定时查询项目的状态
      function createTimeout() {
        const timeoutId = setTimeout(() => {
          getProjectStatusById({ resourseId: projectItem.id }).then((res) => {
            if (self.isDestroying) {
              return;
            }
            if (res.state === 'OK') {
              // 发布或创建成功
              if (res.body?.itemStatus !== PROJECT_STATUS.PUBLISHING) {
                clearTimeout(intervalMap.get(projectItem.id));
                intervalMap.delete(projectItem.id);
                self.getTableData(true);
                self.$message.success(`项目“${projectItem.resourseName}”${actionType}成功`);
              } else {
                createTimeout();
              }
            } else if (res.state === 'ERROR' && res.errorCode == '920') {
              // 发布或创建失败
              const timeoutId = intervalMap.get(projectItem.id);
              self.getTableData(true);
              if (timeoutId) {
                clearTimeout(intervalMap.get(projectItem.id));
                intervalMap.delete(projectItem.id);
                self.$message.error({
                  content: <span style="color: #FF454D;">{`项目“${projectItem.resourseName}”${actionType}失败，请勿在${actionType}过程中更改模型训练实例内容`}</span>,
                });
              }
            }
          });
        }, 1000);
        intervalMap.set(projectItem.id, timeoutId);
      }
    },
    // 清除所有定时器
    destroyAllIntervals() {
      for (let timeoutId of intervalMap.values()) {
        clearTimeout(timeoutId);
      }
      intervalMap.clear();
    },
    handleSubTitleEdit(record) {
      this.editing = true;
      this.selectResource = record; // 选中的资源，在新增的时候需要置空
      this.editResourceData.resourceName = record.resourseName; // 编辑的资源名称
      if (record.resourseType === '3') {
        // 文档
        this.docModalVisible = true;
      } else if (record.resourseType === '2') {
        // 视频
        this.videoModalVisible = true;
      } else {
        // 项目
        this.projectModalVisible = true;
      }
    },

    handleAddCourseConfirm() {
      this.$refs.addForm
        .validate()
        .then(() => {
          this.submitBtnDisable = true;
          if (this.courseOperationType === 'add') {
            addCatalogByCourseId({
              catalogName: this.addFormData.courseName,
              courseId: this.courseId,
            }).then((res) => {
              if (res.state === 'OK') {
                this.submitBtnDisable = false;
                this.$message.success('新增课节成功');
                this.hideCourseModal();
                this.getTableData(true);
              } else {
                this.submitBtnDisable = false;
              }
            });
          } else {
            updateCatalogById({
              id: this.catalogId,
              catalogName: this.addFormData.courseName,
            }).then((res) => {
              if (res.state === 'OK') {
                this.submitBtnDisable = false;
                this.$message.success('修改课节成功');
                this.hideCourseModal();
                this.getTableData();
              } else {
                this.submitBtnDisable = false;
              }
            });
          }
        })
        .catch((err) => {
          this.submitBtnDisable = false;
          throw new Error(err);
        });
    },
    hideCourseModal() {
      this.courseModelVisible = false;
      this.addFormData.courseName = '';
    },
    showCourseModal(type) {
      this.courseOperationType = type;
      this.courseModelVisible = true;
    },
    hideProjectModal(bol) {
      this.projectModalVisible = false;
      bol && this.getTableData(true);
    },
    hideDocModal(bol) {
      this.docModalVisible = false;
      bol && this.getTableData(true);
    },
    hideVideoModal(bol) {
      this.videoModalVisible = false;
      bol && this.getTableData(true);
    },
    handleAddProject(id) {
      this.editing = false;
      this.projectModalVisible = true;
      this.catalogId = id;
    },
    handleAddDoc(id) {
      this.editing = false;
      this.docModalVisible = true;
      this.catalogId = id;
    },
    handleAddVideo(id) {
      this.editing = false;
      this.videoModalVisible = true;
      this.catalogId = id;
    },
    // 查找该课节中是否有正在发布/创建中的项目
    findPendingProjectId(record) {
      let resourceId = null;
      if (record.subs && record.subs.length > 0) {
        record.subs.forEach((item) => {
          if (item.resourseType === '1' && intervalMap.get(item.id)) {
            resourceId = item.id;
          }
        });
      }
      return resourceId;
    },
    deleteCourse(record) {
      let self = this;
      let confirmModal = this.$confirm({
        content: (
          <div class="delete-course-msg">
            <div class="title">
              <jt-icon style="color:rgba(255, 69, 77, 1)" type="iconwarning-circle-fill"></jt-icon>
              <span style="padding-left:10px">确定删除该课节吗？</span>
            </div>
            <p>
              删除课节：
              <span style="color:#0082FF">{record.catalogName}</span>
            </p>
            <p>课节中的所有内容将被删除，删除后无法恢复，请谨慎操作；</p>
            <p>您删除后学生将收到提示，可自行同步删除</p>
          </div>
        ),
        icon: () => '',
        okType: 'danger',
        okText: '删除',
        cancelText: '取消',
        onOk() {
          const resourceId = self.findPendingProjectId(record);
          if (resourceId) {
            clearTimeout(intervalMap.get(resourceId));
            intervalMap.delete(resourceId);
          }
          deleteCatalogById({
            courseCatalogId: record.id,
          }).then((res) => {
            if (res.state === 'OK') {
              self.$message.success('删除课节成功');
              confirmModal.destroy();
              self.getTableData();
            }
          });
        },
        onCancel() {
          confirmModal.destroy();
        },
      });
    },
    deletePart: _.throttle(
      function (id, record) {
        if (record.status == '2') {
          return;
        }
        // 删除项目
        if (record.resourseType == '1') {
          const intervalId = intervalMap.get(id);
          if (intervalId) {
            clearTimeout(intervalId);
            intervalMap.delete(id);
          }
          deleteCatalogProject({ resourseId: id }).then((res) => {
            if (res.state === 'OK') {
              this.$message.success('删除成功');
              this.getTableData();
            } else {
              this.$message.error('删除失败');
            }
          });
        } else {
          // 删除视频或文档
          deleteVideoOrDoc({
            resourseId: id,
          }).then((res) => {
            if (res.state === 'OK') {
              this.$message.success('删除成功');
              this.getTableData();
            } else {
              this.$message.error('删除失败');
            }
          });
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 发布资源
    publishPart(id, record) {
      // 已发布 和 发布中
      if (record.status == '1' || record.status == '3') {
        return;
      }
      this.publishLoading = true;
      publishResource({ resourseId: id })
        .then((res) => {
          if (res.state === 'OK') {
            // 如果是项目的发布则需要启动定时器轮训，需要返回时间
            if (record.resourseType == '1') {
              this.getTableData();
              this.createIntervalByProject(record, true);
            } else {
              this.$message.success('发布成功');
              this.getTableData();
            }
          } else {
            if (record.resourseType == '1') {
              let errorMsg = `项目${record.resourseName}发布失败，请勿在发布过程中更改模型训练实例内容`;
              if (res.errorCode === '-913' || res.errorCode === '-912' || res.errorCode === '-911') {
                errorMsg = res.errorMessage;
              }
              this.$message.error(errorMsg);
            } else {
              this.$message.error('发布失败');
            }
          }
        })
        .finally(() => {
          this.publishLoading = false;
        });
    },
    // 预览视频和pdf
    previewFile(parentId, id, record) {
      if (record.resourseType == '2') {
        let routeUrl = this.$router.resolve({
          path: '/video-viewer',
          query: { id: record.id },
        });
        openInNewTab(routeUrl.href, '_blank');
      } else {
        let routeUrl = this.$router.resolve({
          path: '/pdf-viewer',
          query: { id: record.id },
        });
        openInNewTab(routeUrl.href, '_blank');
      }
    },
    // 传入展开项
    handleExpand(record) {
      const id = record.id;
      if (this.expandedRowKeys.includes(id)) {
        const index = this.expandedRowKeys.indexOf(id);
        this.expandedRowKeys.splice(index, 1);
      } else {
        this.expandedRowKeys.push(id);
      }
    },
    editCourseName(record) {
      this.addFormData.courseName = record.catalogName;
      this.catalogId = record.id;
      this.showCourseModal('edit');
    },
    // 对课节下面资源的排序
    subCustomRow(record) {
      return {
        style: {
          'font-size': '12px',
          'background-color': 'white',
          cursor: 'move',
        },
        // 鼠标移入
        onMouseenter: (event) => {
          const ev = event || window.event;
          ev.target.draggable = true;
        },
        // 开始拖拽
        onDragstart: (event) => {
          const ev = event || window.event;
          ev.stopPropagation();
          sourceRecord = record;
        },
        // 拖动元素经过的元素
        onDragover: (event) => {
          const ev = event || window.event;
          ev.preventDefault();
        },
        // 鼠标松开
        onDrop: (event) => {
          const ev = event || window.event;
          ev.stopPropagation();
          targetRecord = record;
          // 已发布的公开课不可排序 不同类型之间不进行排序
          if (!this.isNotOpenAndPublishedCourse || sourceRecord.resourseType !== targetRecord.resourseType) {
            return;
          }
          if (sourceRecord.resourseType == '1') {
            return;
          } // 项目始终在前，不可改变序列
          for (let i = 0; i < this.tableData.length; i++) {
            let subs = this.tableData[i].subs;
            if (subs) {
              const sourceIndex = subs.findIndex((item) => item.id === sourceRecord.id);
              if (sourceIndex !== -1) {
                // 找到对应的subs, 对subs的序列重新进行组装进行更新

                const targetIndex = subs.findIndex((item) => item.id === targetRecord.id);
                let maps = subs.map((item) => {
                  return { id: item.id, resourseType: item.resourseType, catalogId: item.catalogId };
                });

                let items = maps.splice(sourceIndex, 1);
                maps.splice(targetIndex, 0, items[0]);
                let videoArr = [],
                  docArr = [],
                  videoIndex = 1,
                  docIndex = 1;

                for (let j = 0; j < maps.length; j++) {
                  if (maps[j].resourseType == '2') {
                    // 视频
                    videoArr.push({
                      id: maps[j].id,
                      catalogId: maps[j].catalogId,
                      sort: videoIndex++,
                    });
                  }
                  if (maps[j].resourseType == '3') {
                    docArr.push({
                      id: maps[j].id,
                      catalogId: maps[j].catalogId,
                      sort: docIndex++,
                    });
                  }
                }

                let data = [...docArr, ...videoArr];
                sortResource(data).then((res) => {
                  if (res.state === 'OK') {
                    this.throttleSortAlert();
                    this.getTableData();
                  }
                });
                return;
              }
            }
          }
        },
      };
    },
    subCustomHeaderRow() {
      return {
        style: {
          'font-size': '12px',
          'background-color': 'white',
          color: '#121F2C',
          height: '40px',
          'font-weight': '600',
        },
      };
    },
    // 对课节进行拖动排序
    customRow(record) {
      return {
        style: {
          cursor: 'move',
          'background-color': '#F7F9FA',
        },
        // 鼠标移入
        onMouseenter: (event) => {
          const ev = event || window.event;
          ev.target.draggable = true;
        },
        // 开始拖拽
        onDragstart: (event) => {
          const ev = event || window.event;
          ev.stopPropagation();
          sourceRecord = record;
        },
        // 拖动元素经过的元素
        onDragover: (event) => {
          const ev = event || window.event;
          ev.preventDefault();
        },
        // 鼠标进行课程排序
        onDrop: (event) => {
          const ev = event || window.event;
          ev.stopPropagation();
          targetRecord = record;
          if (!this.isNotOpenAndPublishedCourse) {
            return;
          } // 已发布的公开课不可排序
          const sourceIndex = this.tableData.findIndex((item) => item.id === sourceRecord.id); // 原位置
          const targetIndex = this.tableData.findIndex((item) => item.id === targetRecord.id); // 目标位置
          let maps = this.tableData.map((item) => {
            return { id: item.id };
          });
          let items = maps.splice(sourceIndex, 1);
          maps.splice(targetIndex, 0, items[0]);
          let data = maps.map((item, index) => {
            return { id: item.id, sort: index + 1 };
          });

          sortCatalog(data).then((res) => {
            if (res.state === 'OK') {
              this.throttleSortAlert();
              this.getTableData();
            }
          });
        },
      };
    },
    // 根据源索引和目标索引进行数组项位置移动
    swapRecordByIndex(dataList, sourceIndex, targetIndex) {
      let items = dataList.splice(sourceIndex, 1);
      dataList.splice(targetIndex, 0, items[0]);
    },

    /**
     * @description 根据点击类型对课程条目进行重新排序
     * @param {Object} - record
     * @param {Number} -index talbe项的索引
     * @param {String} -tpye up or dowm
     */
    handlesortByIcon(record, index, type) {
      if (index === 0 && type === 'up') {
        // 最上面的up icon不可点击
        return;
      }
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].id === record.id) {
          // 对课节的排序
          // 向下按钮且是最后一个则不执行操作
          if (type === 'down' && this.tableData.length - 1 === i) {
            return;
          }
          // 交换
          const targetIndex = type === 'up' ? i - 1 : i + 1;
          const sourceIndex = i;
          let maps = this.tableData.map((item) => {
            return { id: item.id };
          });
          let items = maps.splice(sourceIndex, 1);
          maps.splice(targetIndex, 0, items[0]);
          let data = maps.map((item, index) => {
            return { id: item.id, sort: index + 1 };
          });

          sortCatalog(data).then((res) => {
            if (res.state === 'OK') {
              this.getTableData();
              this.throttleSortAlert();
            }
          });
          return;
        } else {
          // 对课节资源的排序
          if (this.tableData[i].subs) {
            for (let j = 0; j < this.tableData[i].subs.length; j++) {
              if (this.tableData[i].subs[j].id === record.id) {
                // 向下按钮且是最后一个
                if (type === 'down' && this.tableData[i].subs.length - 1 === j) {
                  return;
                }

                const sourceIndex = j;
                const targetIndex = type === 'up' ? j - 1 : j + 1;
                let subs = this.tableData[i].subs;

                let maps = subs.map((item) => {
                  return { id: item.id, resourseType: item.resourseType, catalogId: item.catalogId };
                });

                let items = maps.splice(sourceIndex, 1);
                maps.splice(targetIndex, 0, items[0]);
                let videoArr = [],
                  docArr = [],
                  videoIndex = 1,
                  docIndex = 1;

                for (let j = 0; j < maps.length; j++) {
                  if (maps[j].resourseType == '2') {
                    // 视频
                    videoArr.push({
                      id: maps[j].id,
                      catalogId: maps[j].catalogId,
                      sort: videoIndex++,
                    });
                  }
                  if (maps[j].resourseType == '3') {
                    docArr.push({
                      id: maps[j].id,
                      catalogId: maps[j].catalogId,
                      sort: docIndex++,
                    });
                  }
                }

                let data = [...docArr, ...videoArr];
                sortResource(data).then((res) => {
                  if (res.state === 'OK') {
                    this.throttleSortAlert();
                    this.getTableData();
                  }
                });
                return;
              }
            }
          }
        }
      }
    },
    throttleSortAlert: _.throttle(function () {
      this.$message.success('修改排序成功');
    }, 3000),
  },
  watch: {
    'addFormData.courseName': {
      handler(newVal) {
        if (newVal === '' || newVal.length > 20) {
          this.submitBtnDisable = true;
        } else {
          this.submitBtnDisable = false;
        }
      },
    },
    subTab() {
      if (this.subTab === 'course-content') {
        this.getTableData(true, this.initLoadingProjectInterval);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.sub-name-container {
  display: flex;
  align-items: center;
  margin-left: 8px;
  .edit-icon {
    margin-right: 16px;
  }
  .icon-disable {
    visibility: hidden;
  }
  .sub-name-text {
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .jt-vscode-icon {
    .jt-vscode-icon();
  }
  .jt-jupyter-icon {
    .jt-jupyter-icon();
  }
}
.sort-btns {
  display: flex;
  margin-right: 20px;
  i {
    font-size: 16px;
    color: #aaacb4;
    cursor: pointer;
    &:hover {
      color: #0082ff;
    }
  }
  .icon-disable {
    visibility: hidden;
  }
}
.resource-tag {
  background-color: #e1ecff;
  color: #337dff;
  font-size: 12px;
  border-radius: 12px;
  border: none;
}
.course-name {
  font-weight: @jt-font-weight-medium;
  color: #121f2c;
}
.handle-btn {
  width: 76px;
  height: 28px;
  font-size: 12px;
  display: flex;
  align-items: center;
  i {
    font-size: 14px;
  }
  &:hover {
    border: 1px solid #0082ff;
    color: #0082ff;
  }
  &.btn-disable {
    &:hover {
      color: rgba(0, 0, 0, 0.25);
      background-color: #f5f5f5;
      border-color: #d9d9d9;
    }
  }
}
.edit-icon {
  font-size: 13px;
  color: #999999;
  cursor: pointer;
  &:hover {
    color: #0082ff;
  }
}

.delete-icon,
.expand-icon {
  border-radius: 2px;
  border: 1px solid #cbcfd2;
  cursor: pointer;
  display: inline-block;
  width: 28px;
  height: 28px;
  text-align: center;
  line-height: 26px;
}
.delete-icon {
  &:hover {
    border: 1px solid red;
    color: red;
  }
}

.expand-icon {
  &:hover {
    border: 1px solid #0082ff;
    color: #0082ff;
  }
}
.operation-group {
  span {
    color: #0082ff;
    cursor: pointer;
    font-size: 12px;
  }
  .disable {
    cursor: default;
    color: grey;
  }
}

.published,
.verifying,
.unpublish {
  height: 20px;
  line-height: 18px;
  padding: 0px 8px;
  border-radius: 2px;
  font-size: 12px;
  display: inline-block;
}
.published {
  border: 1px solid #17bb85;
  color: #17bb85;
}
.verifying {
  border: 1px solid #389bff;
  color: #389bff;
}

.unpublish {
  border: 1px solid #a0a6ab;
  color: #606972;
}

.doc-tag,
.project-tag,
.video-tag {
  width: 52px;
  height: 20px;
  font-size: 12px;
  border: none;
  text-align: center;
  border-radius: 2px;
}

.doc-tag {
  background: #f7effe;
  color: #b563fc;
}
.project-tag {
  background: #fef3ea;
  color: #f79032;
}
.video-tag {
  background: rgba(56, 155, 255, 0.1);
  color: #389bff;
}
.subtable-empty {
  text-align: center;
  color: #606972;
  width: 450px;
  margin: 48px auto;
  span {
    color: #0082ff;
    cursor: pointer;
  }
}

.empty-tooltip {
  display: flex;
  justify-content: center;
  .tootip-txt {
    position: absolute;
    bottom: 90px;
    width: 100%;
    text-align: center;
    color: #a6a0ab;
    .intro {
      color: #121f2c;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .route {
      font-size: 14px;
      color: #606972;
      span {
        color: #0082ff;
        cursor: pointer;
      }
    }
  }
}

/* 去除展开子table的paading 缩进 */
:deep(tr.ant-table-expanded-row td:first-child) {
  padding: 0px;
}
:deep(tr.ant-table-expanded-row),
tr.ant-table-expanded-row:hover {
  background-color: white;
}
.sub-table {
  :deep(.ant-table-thead) {
    tr > th {
      color: #121f2c;
      font-weight: 400;
    }
  }
  :deep(.ant-table-tbody) {
    tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
}
</style>

<style lang="less">
@import '~@/assets/styles/index.less';

.delete-course-msg {
  .title {
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    margin-bottom: 16px;
    i {
      font-size: 20px;
    }
  }
  p {
    line-height: 26px;
    padding-left: 30px;
  }
}
</style>
