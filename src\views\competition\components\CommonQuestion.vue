<template>
  <div class="competition-question">
    <jt-common-content :loading="loading" :empty="questionList.length == 0" :emptyStyle="{ height: '416px' }" emptyTitle="暂无数据">
      <div class="problemBox">
        <div v-for="(v, i) in questionList" :key="i">
          <competition-introduce-rich-text :url="v.question"></competition-introduce-rich-text>
        </div>
      </div>
    </jt-common-content>
  </div>
</template>
<script>
import API from '@/constants/api/API_competition_model.js';
import CompetitionIntroduceRichText from './CompetitionIntroduceRichText';
export default {
  components: { CompetitionIntroduceRichText },
  data() {
    return {
      loading: true,
      questionList: [],
    };
  },
  created() {
    this.getQuestionList();
  },
  methods: {
    getQuestionList() {
      API.getCompetitionQuestion({ cid: this.$route.query.id }).then((res) => {
        if (res.state === 'OK') {
          this.questionList = res.body;
          this.loading = false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.competition-question {
  .problemBox {
    padding: 16px 16px 0;
  }
}
</style>
