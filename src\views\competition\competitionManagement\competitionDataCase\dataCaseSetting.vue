<template>
  <div class="data-case-settings">
    <a-spin :spinning="spinning" tip="加载中">
      <div class="data-case-settings-editbtn">
        <a-tooltip v-if="openDataCaseMaps[dateCaseObj.copysta] === '同步中'">
          <template #title> 请等待数据完成同步后进行编辑 </template>
          <a-button type="primary" disabled="disabled" @click="handleEdit">
            <EditOutlined />
            编辑
          </a-button>
        </a-tooltip>
        <a-button v-else type="primary" @click="handleEdit">
          <EditOutlined />
          编辑
        </a-button>
      </div>
      <div class="data-settings">
        <head-title title="数据设置" style="margin-bottom: 24px"></head-title>
        <a-row style="margin: -8px -12px">
          <a-col :span="4" align="right"> 是否开放数据：</a-col>
          <a-col :span="19"> {{ allowOpenDataCase[dateCaseObj.allowOpenData] }} </a-col>
        </a-row>
        <a-row v-if="isOpenData && isBishengCompetition" style="margin: -8px -12px; display: flex; width: 100%">
          <a-col :span="4" align="right" style="width: 194px"> 开放数据集：</a-col>
          <a-col> {{ dateCaseObj.dataSetName || '--' }} </a-col>
          <a-col v-if="dateCaseObj.copysta && dateCaseObj.dataSetName" :span="10">
            <jt-tag :type="getOpenDataCaseClass">{{ openDataCaseMaps[dateCaseObj.copysta] }}</jt-tag>
          </a-col>
        </a-row>
        <a-row v-if="isOpenData" style="margin: -8px -12px">
          <a-col :span="4" align="right"> 是否允许数据下载：</a-col>
          <a-col :span="19"> {{ allowDownloadDataCase[dateCaseObj.allowDownloadData] }} </a-col>
        </a-row>
      </div>
      <div v-if="isBishengCompetition" class="case-settings">
        <head-title title="实例设置" style="margin-bottom: 24px"></head-title>
        <a-row style="margin: -8px -12px">
          <a-col :span="4" align="right"> 单实例最高可用资源：</a-col>
          <a-col :span="19"> {{ dateCaseObj.instanceResource || '1算力卡' }} </a-col>
        </a-row>
      </div>
    </a-spin>
  </div>
</template>
<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>
<script>
import headTitle from '@/components/headTitle';
import JtTag from '@/components/tag';
import { openDataCaseMaps, openDataCaseClassMaps, allowDownloadDataCase, allowOpenDataCase } from '../../competitionConfig/index';
import { GET } from '@/request';
import { mapState } from 'vuex';

export default {
  name: 'DataCaseSetting',
  components: { headTitle, JtTag },
  data() {
    return {
      openDataCaseMaps,
      openDataCaseClassMaps,
      allowDownloadDataCase,
      allowOpenDataCase,
      dateCaseObj: {
        allowOpenData: 0,
        dataSetName: undefined,
        allowDownloadData: 0,
        instanceResource: '1算力卡',
      },
      setTimmer: null,
      spinning: false,
    };
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
    // 是否是毕昇训练比赛
    isBishengCompetition() {
      return this.currentManageCompetition.competitionTraining === 1;
    },
    // 是否开放数据
    isOpenData() {
      return Boolean(Number(this.dateCaseObj.allowOpenData));
    },
    getOpenDataCaseClass() {
      let type = '';
      for (const [, item] of Object.entries(this.openDataCaseClassMaps)) {
        if (item.value == this.dateCaseObj.copysta) {
          type = item.key;
        }
      }
      return type;
    },
  },
  watch: {
    '$route.query.tabId'() {
      if (this.$route.query.tabId == 4) {
        this.getDataset();
      }
      this.clearTimeoutHandle();
    },
  },
  mounted() {
    this.getDataset();
  },
  beforeUnmount() {
    this.clearTimeoutHandle();
  },
  methods: {
    getDataset(spinningShow = true) {
      const { competitionId } = this.$route.params;
      if (spinningShow) {
        this.spinning = true;
      }
      GET('/competiton/web/manage/dataset/get', { cid: competitionId }).then((res) => {
        if (res.state === 'OK') {
          this.dateCaseObj = res.body;
          this.intervalGetDataset();
        } else {
          this.dateCaseObj = {};
        }
        this.spinning = false;
      });
    },
    intervalGetDataset() {
      const { changeDataSetName } = this.$route.query;
      if (changeDataSetName === 'noChange' || changeDataSetName === 'default') return;
      const { copysta, dataSetName } = this.dateCaseObj;
      if (this.openDataCaseMaps[copysta] !== '同步中') {
        if (this.openDataCaseMaps[copysta] === '同步成功' && changeDataSetName && dataSetName) {
          this.$message.success(`开放数据集"${dataSetName}"同步成功`);
        } else if ((this.openDataCaseMaps[copysta] !== '同步中' || this.openDataCaseMaps[copysta] !== '同步成功') && changeDataSetName && dataSetName) {
          this.$message.error(`开放数据集"${dataSetName}"同步失败`);
        }
      } else {
        this.setTimmer = setTimeout(() => {
          const spinningShow = false;
          this.getDataset(spinningShow);
        }, 5000);
      }
    },
    handleEdit() {
      const { competitionId } = this.$route.params;
      this.$router.push(`${competitionId}/datacase-edit`);
    },
    clearTimeoutHandle() {
      if (this.setTimmer) {
        clearTimeout(this.setTimmer);
        this.setTimmer = null;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.data-case-settings {
  color: #121f2c;

  :deep(.ant-col) {
    padding: 12px !important;
  }

  .data-case-settings-editbtn {
    position: absolute;
    top: 0px;
    right: 32px;
  }
}

.case-settings {
  margin: 20px 0px 46px;
}
</style>
