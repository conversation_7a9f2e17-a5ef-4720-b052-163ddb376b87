<template>
  <div>
    <a-button :loading="loading" :disabled="disabled" type="primary" class="detail-button" @click="handleClick">{{ loading ? '' : text }}</a-button>
  </div>
</template>
<script>
export default {
  props: {
    loading: Boolean,
    competitionStatus: String, //比赛状态
    signupEnd: <PERSON>olean, //报名是否结束
    logined: Boolean, //是否登录
    // 移动云比赛
    isMobileCloud: Boolean, //是否为移动云比赛
    mobileCloudStatus: String, //移动云比赛报名状态
    mobileCloudRegistered: Boolean, //是否已注册移动云
    // 非移动云比赛
    signUp: Boolean, //是否已报名
    hasInstance: Boolean, //是否有实例
    isOfficial: Boolean, //是否为正式赛
  },
  emits: ['handleClick'],
  data() {
    return {
      text: '',
      disabled: false,
      callbackName: '',
    };
  },
  computed: {
    mobileCloudSignupBegin() {
      return this.isMobileCloud && !this.signupEnd;
    },
    mobileCloudSignupEnd() {
      return this.isMobileCloud && this.signupEnd;
    },
    competitionSignupBegin() {
      return !this.isMobileCloud && this.competitionStatus === '进行中';
    },
    // 已报名的正式赛
    signUpOfficialCompetition() {
      return this.isOfficial && !this.signUp;
    },
  },
  watch: {
    loading() {
      this.getText();
    },
  },
  created() {
    this.getText();
  },
  methods: {
    getText() {
      // 比赛开始前
      if (this.competitionStatus === '即将开始') {
        this.text = '即将开始';
        this.disabled = true;
        return;
      }
      // 比赛进行中(移动云比赛报名开始)
      if (this.mobileCloudSignupBegin) {
        if (!this.logined) {
          this.text = '立即报名';
          this.callbackName = 'gotoLogin';
          return;
        }
        if (this.logined && this.mobileCloudStatus === '立即报名') {
          this.text = '立即报名';
          this.callbackName = 'gotoMobileCloudSignUp';
          return;
        }
        if (this.logined && this.mobileCloudStatus === '继续报名') {
          this.text = this.mobileCloudRegistered ? '继续报名' : '立即报名';
          this.callbackName = 'gotoMobileCloudSignUp';
          return;
        }
        if (this.logined && this.mobileCloudStatus === '完成报名') {
          this.text = '进入深度学习平台';
          this.callbackName = 'gotoDeepLearn';
          return;
        }
      }
      // 比赛进行中(移动云比赛报名结束)
      if (this.mobileCloudSignupEnd) {
        if (!this.logined) {
          this.text = '报名已结束';
          this.callbackName = 'gotoLogin';
          return;
        }
        if (this.logined) {
          this.text = this.mobileCloudStatus === '完成报名' ? '进入深度学习平台' : '报名已结束';
          this.disabled = !(this.mobileCloudStatus === '完成报名');
          this.callbackName = this.mobileCloudStatus === '完成报名' ? 'gotoDeepLearn' : '';
          return;
        }
      }
      // 比赛进行中(非移动云比赛)
      if (this.competitionSignupBegin) {
        if (this.isOfficial && !this.logined && !this.signupEnd) {
          this.text = '立即报名';
          this.callbackName = 'gotoLogin';
          return;
        }
        if (this.isOfficial && this.logined && !this.signupEnd && !this.signUp) {
          this.text = '立即报名';
          this.callbackName = 'gotoSignUp';
          return;
        }
        if (this.isOfficial && this.logined && !this.signupEnd && this.signUp) {
          this.text = '进入实例';
          this.callbackName = this.hasInstance ? 'gotoInstanceListPage' : 'gotoCreatePage';
          return;
        }
        if (this.isOfficial && !this.logined && this.signupEnd) {
          this.text = '报名已结束';
          this.callbackName = 'gotoLogin';
          return;
        }
        if (this.isOfficial && this.logined && this.signupEnd && !this.signUp) {
          this.text = '报名已结束';
          this.disabled = true;
          return;
        }
        if (this.isOfficial && this.logined && this.signupEnd && this.signUp) {
          this.text = this.hasInstance ? '进入实例' : '立即参赛';
          this.callbackName = this.hasInstance ? 'gotoInstanceListPage' : 'gotoCreatePage';
          return;
        }
        if (!this.isOfficial && !this.logined) {
          this.text = '立即参赛';
          this.callbackName = 'gotoLogin';
          return;
        }
        if (!this.isOfficial && this.logined) {
          this.text = this.hasInstance ? '进入实例' : '立即参赛';
          this.callbackName = this.hasInstance ? 'gotoInstanceListPage' : 'gotoCreatePage';
          return;
        }
      }
      // 比赛结束
      if (this.competitionStatus === '已结束') {
        if (!this.logined) {
          this.text = '比赛已结束';
          this.callbackName = 'gotoLogin';
          return;
        }
        if (this.logined && this.signUpOfficialCompetition) {
          this.text = '比赛已结束';
          this.disabled = true;
          return;
        }
        if (this.logined && !this.signUpOfficialCompetition && this.mobileCloudStatus === '完成报名') {
          this.text = '进入深度学习平台';
          this.callbackName = 'gotoDeepLearn';
          return;
        }
        if (this.logined && !this.signUpOfficialCompetition && !(this.mobileCloudStatus === '完成报名')) {
          this.text = this.hasInstance ? '进入实例' : '比赛已结束';
          this.callbackName = this.hasInstance ? 'gotoInstanceListPage' : '';
          this.disabled = !this.hasInstance;
          return;
        }
      }
    },
    handleClick() {
      this.$emit('handleClick', this.callbackName);
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.detail-button {
  min-width: 116px;
  font-size: @jt-font-size-base;
  height: 40px;
}
</style>
