/* 对antd及其它组件样式class的复写 */
/* 定义less全局变量 */
.router-link-active {
  color: #0082FF;
}
.ant-divider {
  background-color: #A0A6AB;
}
/* antd button样式统一修改 */
.ant-btn-primary:focus {
  background-color: #0082FF;
}
.ant-btn-primary:hover {
  background-color: #0277e7;
}
.ant-btn-background-ghost.ant-btn-primary:hover {
  background: #F0F7FF !important;
}
.ant-breadcrumb > span:last-child a {
  color: #121f2c;
}
/* form 表单extra文字字体统一设置 */
.ant-form-extra {
  font-size: 12px;
  color: #A0A6AB;
}
.ant-modal-title {
  font-weight: 600;
  color: #121F2C;
}
.ant-modal-close-x {
  color: #A0A6AB;
  font-size: 12px;
}
.w-e-text-container a {
  color: #0082FF;
}
.ant-table-thead > tr > th {
  font-weight: 400;
}
.ant-radio-button-wrapper {
  width: 160px;
  text-align: center;
  font-size: 14px;
  color: #606972;
}
.ant-radio-button-wrapper.ant-radio-button-wrapper-checked {
  background: #f0f8ff;
  border: 1px solid #0082ff;
  color: #0082ff;
}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled),
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  box-shadow: none;
}
.ant-table-placeholder {
  border-bottom: none;
}
.ant-tabs-top-bar {
  margin: 0;
}
.ant-tabs-nav .ant-tabs-tab-active {
  font-family: PingFang-SC-Medium, PingFang-SC, sans-serif;
  font-weight: 600;
  color: #0082ff;
}
.ant-tabs-tab {
  font-size: 18px;
  font-family: PingFang-SC-Regular, PingFang-SC, sans-serif;
  font-weight: 400;
  color: #121f2c;
}
