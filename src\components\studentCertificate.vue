<template>
  <a-modal v-model:open="open" title="" :centered="true" @cancel="$emit('cancel')">
    <template #footer> <div></div> </template>
    <div class="main-content">
      <div class="content-container">
        <p class="user-name">
          {{ `${userName}：` }}
        </p>
        <p class="content">
          您已完成 <span class="course-name">{{ `《${courseName}》` }}</span>
        </p>
        <p class="content">所有课节的学习，特颁此证！</p>
        <div class="footer">
          <p v-if="!isEntranceBySasac()">{{ '九天·毕昇平台' }}</p>
          <p>{{ endStudyTime }}</p>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { isEntranceBySasac } from '@/utils/utils';

export default {
  props: {
    visible: { type: Boolean, default: false },
    userName: { type: String, default: '' },
    courseName: { type: String, default: '' },
    instituteName: { type: String, default: '' },
    endStudyTime: { type: String, default: '' },
  },
  emits: ['cancel'],
  data() {
    return {
      open: false,
      isEntranceBySasac,
    };
  },
  watch: {
    visible(val) {
      this.open = val;
    },
  },
  created() {
    this.open = this.visible;
  },
};
</script>

<style lang="less" scoped>
.main-content {
  background-image: url('../assets/image/course/certificate.png');
  background-size: 1000px 666px;
  width: 1000px;
  height: 666px;
  padding-left: 104px;
  padding-top: 320px;
  padding-right: 114px;
  box-sizing: border-box;
  overflow: auto;
}
.user-name {
  font-size: 32px;
  margin-bottom: 24px;
  font-family: MicrosoftYaHei, sans-serif;
  color: #915224;
}
.content {
  font-size: 24px;
  font-weight: 400;
  color: #915224;
}
.course-name {
  text-decoration: underline;
}
.footer {
  margin-top: 64px;
  text-align: right;
  font-size: 18px;
  font-weight: 400;
  color: #915224;
}
.content-container {
  // margin-top: 320px;
  // margin-left: 104px;
}
:deep(.ant-modal-body) {
  padding: 0;
}
:deep(.ant-modal-footer) {
  padding: 0;
}
:deep(.ant-modal) {
  width: 1000px !important;
}
</style>
