<template>
  <div class="check" @mousedown="handleMoveStartEvent($event)" @touchstart="handleMoveStartEvent($event, 'touch')" @touchmove="handleMoveEvent($event, 'touch')" @touchend="handleMoveEndEvent($event, 'touch')">
    <h1>
      <jt-icon style="color: #62c978; font-size: 26px" type="icondunpai"></jt-icon>
      安全验证
    </h1>
    <div class="img-con">
      <img v-if="imgUrl" class="verify-img" :src="imgUrl" :style="{ transform: imgAngle }" alt="" />
      <div v-else class="empty-block"></div>
      <div v-if="result" class="check-state">{{ result }}</div>
    </div>
    <p>拖动滑块使图片角度为正</p>
    <div ref="sliderCon" class="slider-con" :class="{ 'err-anim': showError }">
      <div id="slider" ref="slider" class="slider" :class="{ sliding }" :style="{ '--move': `${slidMove}px` }">
        <jt-icon style="font-size: 16px; color: #fff" type="iconhuakuaishuxian1"></jt-icon>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RotateCaptch',
  props: {
    imgUrl: String,
    result: {
      validator(value) {
        return ['正确', '错误', '验证中', '已过期'].includes(value);
      },
    },
  },
  emits: ['onSliding', 'end'],
  data() {
    return {
      sliding: false,
      slidMove: 0,
      isMouseDown: false,
    };
  },
  computed: {
    angle() {
      let sliderConWidth = this.sliderConWidth || 0;
      let sliderWidth = this.sliderWidth || 0;
      let ratio = this.slidMove ? this.slidMove / (sliderConWidth - sliderWidth) : 0;
      return 360 * ratio;
    },
    imgAngle() {
      return `rotate(${this.angle}deg)`;
    },
    showError() {
      return this.result === '错误' || this.result === '已过期';
    },
    checking() {
      return this.result === '验证中';
    },
  },
  mounted() {
    this.bindEvents();
  },
  unmounted() {
    document.removeEventListener('mousemove', this.handleMoveEvent);
    document.removeEventListener('mouseup', this.handleMoveEndEvent);
  },
  methods: {
    bindEvents() {
      document.addEventListener('mousemove', this.handleMoveEvent);
      document.addEventListener('mouseup', this.handleMoveEndEvent);
    },
    // 重置滑块
    resetSlider() {
      this.sliding = false;
      this.slidMove = 0;
    },
    onStart(event, type) {
      const sliderEl = document.getElementById('slider');
      if (event.target.id !== 'slider' && !sliderEl.contains(event.target)) {
        return;
      }
      if (this.checking) return;
      this.$emit('onSliding');
      // 设置状态为滑动中
      this.sliding = true;

      // 下面三个变量不需要监听变化，因此不放到 data 中
      this.sliderLeft = type === 'mouse' ? event.clientX : event.changedTouches[0].pageX; // 记录鼠标按下时的x位置
      this.sliderConWidth = this.$refs.sliderCon.clientWidth; // 记录滑槽的宽度
      this.sliderWidth = this.$refs.slider.clientWidth; // 记录滑块的宽度
    },
    onEnd() {
      if (this.sliding && this.checking === false) {
        this.$emit('end', this.angle);
      }
    },
    onMove(event, type = 'mouse') {
      const clientX = type === 'mouse' ? event.clientX : event.changedTouches[0].pageX;
      if (this.sliding && this.checking === false) {
        // 滑块向右的平移距离等于鼠标移动事件的X坐标减去鼠标按下时的初始坐标。
        let m = clientX - this.sliderLeft;
        if (m < 0) {
          // 如果m小于0表示用户鼠标向左移动超出了初始位置，也就是0
          // 所以直接等于 0，以防止越界
          m = 0;
        } else if (m > this.sliderConWidth - this.sliderWidth) {
          // 滑块向右移动的最大距离是滑槽的宽度减去滑块的宽度。
          // 因为css的 translateX 函数是以元素的左上角坐标计算的
          // 所以要减去滑块的宽度，这样滑块在最右边时，才不会左上角和滑槽右上角重合。
          m = this.sliderConWidth - this.sliderWidth;
        }
        this.slidMove = m;
      }
    },

    handleMoveStartEvent(e, type = 'mouse') {
      this.isMouseDown = true;
      this.onStart(e, type);
    },
    // 处理函数抽离
    handleMoveEvent(e, type = 'mouse') {
      if (!this.isMouseDown) return false;
      this.onMove(e, type);
    },
    handleMoveEndEvent(e, type = 'mouse') {
      this.isMouseDown = false;
      this.onEnd(e, type);
    },
  },
};
</script>

<style lang="less" scoped>
.check {
  --slider-width: 40px;
  --slider-height: 20px;
  --slider-border-radius: 14px;
  width: 100%;
  height: 100%;
  background: white;
  box-shadow: 0 0 5px grey;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  h1 {
    margin-bottom: 20px;
    margin-top: 40px;
    font-size: 24px;
    font-weight: 400;
    color: #121f2c;
    line-height: 33px;
  }
  p {
    margin-top: 20px;
    font-size: 18px;
    font-weight: 400;
    color: #606972;
    line-height: 25px;
  }
}
.check .img-con {
  position: relative;
  overflow: hidden;
  width: 172px;
  height: 172px;
  // border-radius: 50%;
}
.check .img-con img {
  width: 100%;
  height: 100%;
  user-select: none;
}

.empty-block {
  width: 100%;
  height: 100%;
}

.check .slider-con {
  width: 80%;
  height: var(--slider-height);
  border-radius: var(--slider-border-radius);
  margin-top: 20px;
  position: relative;
  background: #efefef;
}

.slider-con .slider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--slider-width);
  height: var(--slider-height);
  background: #418fff;
  border-radius: var(--slider-border-radius);
  cursor: move;
  --move: 0px;
  transform: translateX(var(--move));
}

.slider-con .slider.sliding {
  background: #4ed3ff;
}

.check-state {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

body {
  padding: 0;
  margin: 0;
  background: #fef5e0;
}

@keyframes jitter {
  20% {
    transform: translateX(-5px);
  }
  40% {
    transform: translateX(10px);
  }
  60% {
    transform: translateX(-5px);
  }
  80% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0);
  }
}

.slider-con.err-anim {
  animation: jitter 0.5s;
}

.slider-con.err-anim .slider {
  background: #ff4e4e;
}
</style>
