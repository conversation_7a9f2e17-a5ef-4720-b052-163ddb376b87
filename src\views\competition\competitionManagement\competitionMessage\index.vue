<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="competition-messsage">
    <div class="competition-messsage-head">
      <a-radio-group v-model:value="subtabId" class="competition-messsage-radios" @change="radioChange">
        <a-radio-button value="1">基本信息</a-radio-button>
        <a-radio-button value="2">赛制介绍</a-radio-button>
        <a-radio-button value="3">赛题说明</a-radio-button>
        <a-radio-button value="4">常见问题</a-radio-button>
      </a-radio-group>
      <a-button style="width: 84px" type="primary" @click="handleEdit"><EditOutlined />编辑</a-button>
    </div>
    <competitionInfo v-if="subtabId === '1'" />
    <competition-system-intro v-if="subtabId === '2'" @toEdit="handleEdit" />
    <competition-question-intro v-if="subtabId === '3'" @toEdit="handleEdit" />
    <common-questions v-if="subtabId === '4'" @toEdit="handleEdit" />
  </div>
</template>

<script>
import competitionInfo from './competitionInfo.vue';
import { mapState } from 'vuex';
import competitionSystemIntro from './competitionSystemIntro.vue';
import competitionQuestionIntro from './competitionQuestionIntro.vue';
import commonQuestions from './commonQuestions.vue';

// 不同tab对应编辑的路由
const editRouteMaps = {
  1: 'info-edit',
  2: 'system-edit',
  3: 'question-edit',
  4: 'common-question-edit',
};

export default {
  name: 'CompetitionMessage',
  components: {
    competitionInfo,
    competitionSystemIntro,
    competitionQuestionIntro,
    commonQuestions,
  },
  data() {
    return {
      ctpBegin: false,
      subtabId: this.$route.query.subtabId || '1',
    };
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
  },
  methods: {
    radioChange(event) {
      const subtabId = event.target.value;
      this.$router.replace({ query: { tabId: '2', subtabId } });
    },
    handleEdit() {
      const { competitionId } = this.$route.params;
      this.$router.push(`${competitionId}/${editRouteMaps[this.subtabId]}`);
    },
  },
};
</script>

<style lang="less" scoped>
.competition-messsage {
  padding: 0px 32px 64px;
  .competition-messsage-head {
    display: flex;
    justify-content: space-between;
    .competition-messsage-radios {
      margin-bottom: 32px;
    }
  }
}
</style>
