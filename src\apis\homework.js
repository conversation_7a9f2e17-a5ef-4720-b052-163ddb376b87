import { GET, POST } from '../request';

// 获取作业列表
export const homeworkList = (id) => GET('/course_model/web/teaching/assignment/list', { courseId: id });

// 删除作业条目
export const deleteHomeworkItem = (id) => GET('course_model/web/teaching/assignment/delete', { assignmentId: id });

// 获取作业详情
export const getHomeworkDetail = (id) => GET('/course_model/web/teaching/assignment/detail', { assignmentId: id });

// 发布作业
export const publishHomework = (id) => GET('/course_model/web/teaching/assignment/publish', { assignmentId: id });

// 保存并发布作业
export const saveAndPublishHomework = (data) => POST('/course_model/web/teaching/assignment/add_pub', data);

// 查询作业状态
export const getHomeworkStatus = (id) => GET('/course_model/web/teaching/assignment/status', { assignmentId: id });

// 更新作业
export const updateHomework = (data) => POST('/course_model/web/teaching/assignment/update', data);

// 添加作业
export const addHomework = (data) => POST('/course_model/web/teaching/assignment/add', data);

// 作业排序
export const sortHomework = (data) => POST('/course_model/web/teaching/assignment/sort', data);

// 分片上传初始化
export const multipartInit = (data) => POST('/course_model/web/teaching/fileupload/multipartinit', data, { useError: false });

// 文件合并
export const multipartCompose = (data) => GET('/course_model/web/teaching/fileupload/compose', data);

// 合并状态查询(这个接口是异步的，需要轮询)
export const UploadState = (data) => GET('/course_model/web/teaching/fileupload/info', data);

// 获取下载地址
export const getDownloadUrl = (data) => GET('/course_model/web/teaching/fileupload/url', data);

// 获取评阅列表
export const getCorrectList = (data) => POST(`/course_model/web/teaching/assignment/correct/list`, data);

// 获取评阅页面详情
export const getCorrectDetails = (data) => POST(`/course_model/web/teaching/assignment/correct/num`, data);

// 导出成绩
export const scoreExport = (data) => GET(`/course_model/web/teaching/assignment/score/export`, data);

// 发布成绩
export const scorePublish = (data) => GET(`/course_model/web/teaching/assignment/score/pub`, data);
