<template>
  <div class="course-overview-container">
    <!-- <div class="jt-box-shadow">
    </div> -->
    <bread-crumb :value="breadcrumbs"></bread-crumb>
    <div class="content">
      <div class="side">
        <div class="thumb">
          <img :src="courseInfo.imageUrl || emptyImg" alt="" />
          <p class="title">{{ courseInfo.name }}</p>
          <p class="institute">{{ courseInfo.instituteName }}</p>
          <p class="time">{{ `${courseInfo.startTime || ''}-${courseInfo.endTime || ''}` }}</p>
        </div>
        <a-menu class="navigator" :selected-keys="[currentKey]" mode="vertical" @click="handleNavigateTo">
          <a-menu-item key="课节">
            <jt-icon type="iconkejie" class="tab-icon" />
            课节
          </a-menu-item>
          <a-menu-item key="作业" :disabled="homeworkDisabled">
            <jt-icon type="iconzuoye" class="tab-icon" />
            作业
          </a-menu-item>
          <a-menu-item key="课程介绍">
            <jt-icon type="iconkecheng" class="tab-icon" />
            课程介绍
          </a-menu-item>
          <a-menu-item key="课程资源">
            <jt-icon type="iconkechengziyuan" class="tab-icon" />
            课程资源
          </a-menu-item>
          <a-menu-item key="教师团队">
            <jt-icon type="iconjiaoshituandui" class="tab-icon" />
            教师团队
          </a-menu-item>
        </a-menu>
        <div class="footer">
          <a-button v-if="courseInfo.courseFlag === '1'" class="btn" @click="handleQuitCourse">退出课程</a-button>
        </div>
      </div>
      <course-contents v-if="currentKey === '课节'" class="common-content-container" :end-study-time="courseInfo.endStudyTime" :institute-name="courseInfo.instituteName" :course-name="courseInfo.name" :course-id="courseId" :finished="finished" :catalog-num="courseInfo.catalogNum" :map-flag="courseInfo.mapFlag" @reload="getCourseInfo"></course-contents>
      <course-homework v-if="currentKey === '作业'" :course-id="courseId" :course-name="courseInfo.name"></course-homework>
      <course-introduction v-if="currentKey === '课程介绍'" class="common-content-container" :course-desc="courseInfo.courseDesc" :course-front-knowledge="courseInfo.courseFrontKnowledge" :course-goal="courseInfo.courseGoal"></course-introduction>
      <course-resource v-if="currentKey === '课程资源'" class="common-content-container" :course-id="courseId"></course-resource>
      <course-teacher v-if="currentKey === '教师团队'" class="common-content-container" :course-id="courseId"></course-teacher>
    </div>
    <delete-input-confirm :visible="confirmVisible" title="确定退出该课程吗？" :comfirm-text="'退出'" content="这将永久删除您的学习记录及所有课节实例，您对实例做的任何修改都不会保留，且不再享有“课程结束后仍可继续学习”的权利" @ok="onQuit" @cancel="confirmVisible = false"></delete-input-confirm>
  </div>
</template>

<script lang="jsx">
import breadCrumb from '../../components/breadCrumb';
import courseContents from '../../components/course-overview/course-contents.vue';
import courseIntroduction from '../../components/course-overview/course-introduction.vue';
import courseResource from '../../components/course-overview/course-resource.vue';
import courseTeacher from '../../components/course-overview/course-teacher.vue';
import courseHomework from '../../components/course-overview/course-homework.vue';
import deleteInputConfirm from '../../components/deleteInputConfirm.vue';
import { checkAuth } from '@/utils/utils';
import { checkLogin } from '@/keycloak';
import { FINISHED, COURSE_FLAG } from './course-overview';

export default {
  components: { breadCrumb, courseContents, courseIntroduction, courseResource, courseTeacher, deleteInputConfirm, courseHomework },
  data() {
    return {
      courseId: this.$route.params.courseId,
      courseName: '',
      breadcrumbs: [{ name: '学习', path: '/course' }, { name: '我学习的公开课', path: '/course/my-course' }, { name: '课程主页' }],
      courseInfo: {
        instituteDto: {},
      },
      currentKey: '课节',
      courseContents: [],
      emptyImg: require('@/assets/image/empty2x.png'),
      confirmVisible: false,
      deletePosting: false,
    };
  },
  computed: {
    finished() {
      return FINISHED[this.courseInfo.courseStudentStudyFlag] === '已完成';
    },
    homeworkDisabled() {
      return COURSE_FLAG[this.courseInfo.courseFlag] === '公开课';
    },
  },
  watch: {
    currentKey(val) {
      if (val === '课节' || val === '课程介绍') {
        this.getCourseInfo();
      }
    },
  },
  mounted() {
    if (!checkLogin(true)) {
      return;
    }
    this.getCourseInfo();
    this.initTab();
  },

  methods: {
    getCourseInfo() {
      const obj = {
        courseId: this.courseId,
      };
      return this.$GET('/course_model/web/course_student/course/courseFindByCourseId', obj, { useError: false }).then((res) => {
        this.loading = false;
        if (!checkAuth(res.errorCode, '-802', '/course')) {
          return;
        }
        if (!res.errorCode) {
          this.courseInfo = res.body;
        }
      });
    },
    handleNavigateTo(val) {
      if (this.currentKey === val.key) {
        return;
      }
      this.currentKey = val.key;
      this.$router.replace({ query: { ...this.$route.query, currentKey: val.key } });
    },
    handleQuitCourse() {
      this.confirmVisible = true;
    },
    onQuit() {
      if (this.deletePosting) {
        return;
      }
      this.deletePosting = true;
      this.$GET('/course_model/web/course_student/student/studentCourseDelete', { courseId: this.courseId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.confirmVisible = false;
          this.$router.replace('/course/my-course');
        }
        this.deletePosting = false;
      });
    },
    initTab() {
      this.currentKey = this.$route.query.currentKey || '课节';
    },
  },
};
</script>

<style lang="less" scoped>
@import './course-overview.less';
</style>
