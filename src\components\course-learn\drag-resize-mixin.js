import { getMinWidth, getMaxWidth, getAdaptiveHeight, setWidth, getWidth } from './drag-resize';
const dragResizeMixin = {
  data() {
    return {
      minWidth: getMinWidth(),
      maxWidth: getMaxWidth(),
      adaptiveHeight: '',
    };
  },
  computed: {
    initialWidth() {
      return getWidth();
    },
  },
  mounted() {
    this.adaptiveHeight = getAdaptiveHeight(this.initialWidth);
  },
  methods: {
    resize(newRect) {
      const { width } = newRect;
      setWidth(width);
      this.adaptiveHeight = getAdaptiveHeight(width);
    },
  },
};

export default dragResizeMixin;
