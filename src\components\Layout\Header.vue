<template>
  <div id="header" class="header-shadow">
    <header v-if="!getPathMatchHeaderFooter" :class="{ 'have-border': headerHaveBorder, 'home-header-fixed': show }">
      <div class="header-left header-col">
        <div v-if="!isEntranceBySasac()" class="header-logo">
          <router-link :to="{ path: '/home' }" class="homepage-link">
            <!-- eslint-disable-next-line no-irregular-whitespace -->
            <span class="logo-text">九天 • 毕昇</span>
            <img v-if="showPreviewLogo" class="preview-logo" src="@/assets/image/preview2x.png" alt="" />
          </router-link>
        </div>
        <ul class="header-menu">
          <li v-if="!isEntranceBySasac()">
            <router-link to="/home">首页</router-link>
          </li>
          <li>
            <router-link to="/course" :class="{ 'router-link-active': $route.path.startsWith('/course') }">学习</router-link>
          </li>
          <li>
            <router-link to="/competition" :class="{ 'router-link-active': $route.path.startsWith('/competition') }">比赛</router-link>
          </li>

          <!-- NOTE:  20250509上云改造，求职模块下线-->
          <!-- 非国资委的平台，才会显示求职 -->
          <!-- <li v-if="showExamDropdown">
            <jt-dropdown placement="bottomCenter" :overlayStyle="{ 'z-index': '10001' }">
              <jt-menu slot="overlay">
                <jt-menu-item key="1">
                  <router-link to="/job"> 求职题库 </router-link>
                </jt-menu-item>
                <jt-menu-item key="2">
                  <a @click="handleNavigateToExam"> 在线考试</a>
                </jt-menu-item>
              </jt-menu>
              <router-link to="/job">求职</router-link>
            </jt-dropdown>
          </li> -->
          <!-- 非国资委的平台，才会显示求职 -->
          <!-- <li v-if="!$store.state.course.examUrl & !isEntranceBySasac()">
            <router-link to="/job">求职</router-link>
          </li> -->

          <!-- NOTE: 20250610 教学模块隐藏 -->
          <!-- <li v-if="!showMore & !isEntranceBySasac()">
            <router-link to="/teaching">教学</router-link>
          </li> -->
          <li v-if="!showMore & !isEntranceBySasac()">
            <a :href="CONSOLE_URL + '/home/<USER>'">科研</a>
          </li>
          <li v-if="showMore & !isEntranceBySasac()">
            <jt-dropdown>
              <a class="ant-dropdown-link" @click="(e) => e.preventDefault()"> 更多 </a>
              <template #overlay>
                <a-menu>
                  <!-- NOTE: 20250610 教学模块隐藏 -->
                  <!-- <a-menu-item style="width: 120px">
                    <router-link to="/teaching">教学</router-link>
                  </a-menu-item> -->
                  <a-menu-item style="width: 120px">
                    <a :href="CONSOLE_URL + '/home/<USER>'">科研</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </jt-dropdown>
          </li>
        </ul>
      </div>
      <div class="header-right header-col">
        <ul class="header-menu">
          <li v-if="signGainSuanliSwitch" class="sign-gain-content">
            <!-- 赢算力的圆以及中间的iocn图片 -->
            <p class="sign-gain-circul">
              <img v-if="!signGainSuanliToday" src="@/assets/image/sign-gain-suanli/no-sign-icon.png" class="sign-icon" alt="" />
              <img v-else src="@/assets/image/sign-gain-suanli/sign-icon.png" class="sign-icon" alt="" />
            </p>
            <p class="sign-gain-suanli" @click="signGainSuanliClick">{{ !signGainSuanliToday ? '签到赢算力' : '今日已签到' }}</p>
          </li>
          <li v-if="!isEntranceBySasac()">
            <a @click="handleNavigateToHelpcenter">帮助中心</a>
          </li>
          <li style="margin-right: 0">
            <a v-if="isEntranceBySasac()" :href="sasacLocation + '/edu/console?entrance=sasac#/home/<USER>'">控制台</a>
            <a v-else :href="CONSOLE_URL + '/home/<USER>'">控制台</a>
          </li>
        </ul>
        <!-- 登录成功 -->
        <jt-dropdown v-if="logined" v-model="showLogoutBtn" :overlay-style="{ 'z-index': '10001' }">
          <div class="user-box">
            <a @click="handleNavigateToUserCenter">个人中心</a>
          </div>
        </jt-dropdown>
      </div>
    </header>
    <feedback-modal :visible="modalVisible" @cancel="modalVisible = false" @ok="modalVisible = false"></feedback-modal>
    <sign-gain-suanli-modal ref="signGainSuanliModal" v-model="signGainVisible" :is-count="isSignGainCount"></sign-gain-suanli-modal>
  </div>
</template>

<script>
import { GET } from '@/request';
import { Dropdown as JtDropdown, Menu as JtMenu } from 'ant-design-vue';
import { getUserInfo } from '@/apis/teaching.js';
// import messageAlert from './messageAlert.vue';
import { checkImgUrl } from '@/utils/utils.js';
import { getEnvConfig, getLocalConfig } from '@/config';
import { logout, checkLogin, login, register } from '@/keycloak';
import FeedbackModal from '@/components/feedbackModal.vue';
import { getSessionKey } from '../systemNotice';
import SignGainSuanliModal from '@/views/userCenter/signGainSuanli/signGainSuanliModal.vue';
import { mapState, mapMutations, mapActions } from 'vuex';
import debounce from 'lodash/debounce';
import { openInNewTab, isEntranceBySasac } from '@/utils/utils';

const ScrollTopOffset = 140;

export default {
  components: {
    JtDropdown,
    // JtMenu,
    // JtMenuItem: JtMenu.Item,
    FeedbackModal,
    SignGainSuanliModal,
  },
  data() {
    return {
      isEntranceBySasac,
      sasacLocation: window.location.origin,
      showLogoutBtn: false,
      show: false, //头部fixed定位判断，动态绑定 class= " home-header-fixed "
      modalVisible: false,
      signGainVisible: false,
      isSignGainCount: null,
      showMore: false,
    };
  },
  computed: {
    // ...mapState(['userInfo']),
    showExamDropdown() {
      return this.$store.state.course.examUrl && !isEntranceBySasac();
    },
    headerHaveBorder() {
      const noBorderPathArr = ['/home', '/course', '/teaching', '/certification', '/competition'];
      return !noBorderPathArr.find((x) => x === this.$route.path);
    },
    getPathMatchHeaderFooter() {
      return this.$route.name === '404' || this.$route.name === '401';
    },
    CONSOLE_URL() {
      return getLocalConfig('CONSOLE_URL');
    },
    logined() {
      return this.checkLogin();
    },
    showPreviewLogo() {
      return getEnvConfig('SHOW_PREVIEW_LOGO') === '1';
    },
    showMessageCenter() {
      return getEnvConfig('FEATURE_MESSAGECENTER') === '1';
    },
    showFeatureTicket() {
      return getEnvConfig('SHOW_FEATURE_TICKET') === '1';
    },
    ...mapState('suanLiBean', ['signGainSuanliSwitch', 'signGainSuanliToday']),
  },
  mounted() {
    // 监听页面滚动事件
    window.addEventListener('scroll', this.showSearch);
    window.addEventListener('resize', this.clientWidthResize);
    if (this.checkLogin()) {
      this.getUserInfo();
      this.getCheckinStatus();
      this.setSignGainSuanliSwitch();
    }
    this.clientWidthResize();
  },
  methods: {
    checkLogin() {
      return checkLogin(this.$route.meta.requiresLogin);
    },
    handleNavigateToExam() {
      getSessionKey().then((res) => {
        const path = location.pathname;
        openInNewTab(`${path}#/job/online-exam?hideSystemPopup=${!!localStorage[res]}`);
      });
    },
    getUserInfo() {
      getUserInfo().then((res) => {
        if (res.state === 'OK') {
          if (!checkImgUrl(res.body.image)) {
            res.body.image = '';
          }
          this.$store.commit('SET_USERINFO_DATA', res.body);
        }
      });
    },
    handleRegister() {
      register();
    },
    gotoLogin() {
      login();
    },
    logout() {
      logout();
    },
    //头部fixed定位
    showSearch() {
      let scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      if (scrollTop > ScrollTopOffset) {
        this.show = true;
      } else {
        this.show = false;
      }
    },
    handleNavigateToUserCenter() {
      this.$router.push('/user-center');
    },
    handleNavigateToHelpcenter() {
      const helpcenterUrl = getEnvConfig('HELPCENTER_URL_PATH');
      openInNewTab(`/${helpcenterUrl}`);
    },
    ...mapMutations('suanLiBean', ['SET_SIGNGAINSUANLITODAY']),
    ...mapActions('suanLiBean', ['setSignGainSuanliSwitch', 'getCheckinStatus']),
    // 签到赠送算力豆
    signGainSuanliClick() {
      if (this.signGainSuanliToday) {
        this.isSignGainCount = false;
        this.signGainVisible = true;
        this.$refs.signGainSuanliModal.getCheckinDetail();
      } else {
        this.debounceSignGainSuan();
      }
    },
    debounceSignGainSuan: debounce(function () {
      GET('/marketing/web/checkin/set', {}).then((res) => {
        if (res.state === 'OK') {
          this.signGainVisible = true;
          this.$refs.signGainSuanliModal.getCheckinDetail();
          this.SET_SIGNGAINSUANLITODAY(true);
          this.isSignGainCount = true;
        }
      });
    }, 500),
    clientWidthResize() {
      const w = document.documentElement.clientWidth;
      if (w <= 1280) {
        this.showMore = true;
      } else {
        this.showMore = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
// header
header {
  height: @jt-header-height;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  min-width: 1200px;
  position: absolute;
  left: 0;
  top: 60px;
  width: 100%;
  z-index: 999;
  border-bottom: 1px solid transparent;
  &.have-border {
    background: #ffffff;
    box-shadow: 0px 1px 14px 0px rgba(102, 118, 153, 0.16);
    border-bottom: 1px solid #edf1f3;
  }
  &.home-header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10000;
    background-color: #fff;
    box-shadow: 0px 1px 14px 0px rgba(102, 118, 153, 0.16);
  }

  .register-btn {
    width: 106px;
    height: 40px;
  }
}
.header-shadow {
  position: relative;
  z-index: 101;
  box-shadow: 0px 1px 14px 0px rgba(102, 118, 153, 0.16);
}
.hidden-header {
  height: 80px;
}
.header-col {
  display: flex;
}
.header-menu {
  display: flex;
  align-items: center;
  font-family: PingFang-SC-Medium, PingFang-SC, sans-serif !important;
  li {
    font-size: 14px;
    color: #121f2c;
    line-height: 20px;
    &:hover {
      color: @jt-primary-color;
    }
  }
}
.header-logo {
  width: 160px;
  margin-right: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  .homepage-link {
    display: flex;
    align-items: center;
  }
  img {
    width: 207px;
    // width: 187px; // 视觉走查
    height: 35px;
    &.preview-logo {
      width: 44px;
      height: 18px;
    }
  }
}
.header-left {
  .header-menu li {
    margin-right: 48px;
  }
}
.header-right {
  .button {
    cursor: pointer;
  }
  .header-menu li {
    margin-right: 32px;
  }
}
.user-box {
  display: flex;
  align-items: center;
  margin-left: 32px;
  cursor: pointer;
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    border-radius: 50%;
  }
  p {
    font-size: 14px;
    line-height: 20px;
  }
}
.sign-gain-content {
  position: relative;
  .sign-gain-circul {
    display: flex;
    justify-content: center;
    position: absolute;
    width: 32px;
    height: 32px;
    background: linear-gradient(360deg, #ff7f00 0%, #ff5900 100%);
    border-radius: 16px;
    &:hover {
      background: linear-gradient(360deg, #ff6800 0%, #ff4e00 100%);
    }
  }

  .sign-gain-suanli {
    width: 113px;
    height: 32px;
    background: linear-gradient(90deg, #ffb300 0%, #ff950f 100%);
    border-radius: 16px;
    text-align: right;
    line-height: 32px;
    font-size: @jt-font-size-base;
    color: @jt-color-white;
    padding-right: 8px;
    cursor: pointer;
    &:hover {
      background: linear-gradient(90deg, #ffad00 0%, #f98b00 100%);
    }
  }
}
.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #121f2c;
}
</style>
<style lang="less">
.menu-auto-width {
  display: inline-block;
}
</style>
