const mutations = {
  updateSchoolcity(state, data) {
    state.schoolCityList = data;
  },
  initCityList(state, data) {
    state.schoolCityList = data;
    state.schoolCityList.push('其他');
  },
  updateSchool(state, data) {
    state.schoolList = data;
  },
  updateCityLoading(state, loading) {
    state.cityLoading = loading;
  },
  updateSchoolLoading(state, loading) {
    state.schoolLoading = loading;
  },
  updateCurrentCity(state, city) {
    state.currentProvince = city;
  },
};

export default mutations;
