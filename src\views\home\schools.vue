<template>
  <section class="content-box school-box">
    <div class="inner">
      <div ref="schoolList" class="school-innner">
        <h1 class="portal-title" style="margin-bottom: 56px">合作高校和机构</h1>
        <jt-skeleton :loading="schoolListLoading" :rowStyle="{ height: '70px', 'margin-bottom': '18px', 'border-radius': '4px' }" :rows="4">
          <div class="school-list">
            <div class="school-item" v-for="x in schoolList" :key="x.id">
              <img :src="x.schoolImage" :alt="x.schoolName" />
            </div>
          </div>
        </jt-skeleton>
      </div>
    </div>
  </section>
</template>

<script>
import JtSkeleton from '@/components/skeleton';
import { GET } from '@/request';

export default {
  name: 'home-schools',
  components: {
    JtSkeleton,
  },
  data() {
    return {
      schoolList: [],
      schoolListLoading: false,
    };
  },
  mounted() {
    this.getSchoolList();
  },
  methods: {
    getSchoolList() {
      this.schoolListLoading = true;
      GET('/course_model/web/school/list', { requestId: this.$store.state.requestId }).then((res) => {
        if (res.state === 'OK') {
          this.schoolList = res.body;
          this.schoolListLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import url('./common.less');

.school-box {
  padding-bottom: 0;
  h2 {
    margin-top: 10px;
  }
}
.school-list {
  display: flex;
  // margin: 56px 0 138px;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
}
.school-item {
  width: 224px;
  height: 88px;
  border-radius: 2px;

  &:last-of-type {
    margin-right: 0;
  }
  img {
    width: 224px;
    height: 88px;
  }
}
</style>
