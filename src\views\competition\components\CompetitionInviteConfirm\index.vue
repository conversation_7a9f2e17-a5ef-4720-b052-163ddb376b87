<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <confirm-modal v-model="inviteVisible" :cancelText="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].cancelText" :okText="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].okText" :title="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].title" :showCancel="MYTEAM_JOIN_TEAM_MODAL_TEXT[current].cancelText !== ''" @ok="inviteOk" @cancel="inviteCancel">
    <template #icon>
      <ExclamationCircleFilled
        class="invited-icon"
        :style="{
          color: MYTEAM_JOIN_TEAM_MODAL_TEXT[current].title !== '团队邀请' ? '#ff454d' : '#0082ff',
        }"
      />
    </template>
    <div class="dlg-body">
      <p class="invite-content">{{ joinTeamModalContent || MYTEAM_JOIN_TEAM_MODAL_TEXT[current].content }}</p>
    </div>
  </confirm-modal>
</template>

<script>
import API from '@/constants/api/API.js';
import { checkLogin } from '@/keycloak';

import confirmModal from '@/components/confirmModal/index.vue';
import { MYTEAM_JOIN_TEAM_MODAL_TEXT, MYTEAM_JOIN_TEAM_MODAL_TYPE } from '../../competitionConfig/index';

export default {
  components: { confirmModal },
  props: {
    formDataInfo: {
      type: Object,
    },
  },
  data() {
    return {
      current: MYTEAM_JOIN_TEAM_MODAL_TYPE.NOT_OPEN,
      MYTEAM_JOIN_TEAM_MODAL_TEXT,
      inviteVisible: false,
      joinTeamModalContent: '',
    };
  },
  mounted() {
    // 如url中有teamid和res(是团队的code，避免在url中暴露信息，改用res)，查看是否已经加入过团队或未报名或已经互斥的比赛，未登录跳转登录页
    const { teamId, res } = this.$route.query;
    if (teamId && res) {
      this.invitationJoinCheckTeam();
      checkLogin(true);
    }
  },
  methods: {
    inviteOk() {
      this.inviteModalOk(this.current);
    },
    invitationJoinCheckTeam() {
      const { teamId, res } = this.$route.query;
      const reqData = {
        teamId: teamId,
        code: res,
      };
      API.competition_model.checkJoinTeamStatus(reqData).then((res) => {
        if (res.state != 'OK') {
          this.current = res.errorCode;
          if (res.errorCode === MYTEAM_JOIN_TEAM_MODAL_TYPE.REGISTERED) {
            this.joinTeamModalContent = `您已经报名${res.errorParams[0]}，不可同时报名本比赛，无法加入团队`;
          }
          if (res.errorCode === MYTEAM_JOIN_TEAM_MODAL_TYPE.TO_COMPETITION_LIST) {
            this.$router.push({
              path: '/competition',
            });
          }
          if (res.errorCode in MYTEAM_JOIN_TEAM_MODAL_TEXT && res.errorCode !== MYTEAM_JOIN_TEAM_MODAL_TYPE.JOINED_NOT_LEADER && res.errorCode !== MYTEAM_JOIN_TEAM_MODAL_TYPE.USER_JOINED_EXPIRED) {
            this.inviteVisible = true;
          }
        }
      });
    },
    inviteModalOk(val) {
      this.inviteVisible = false;
      const { cid, typeName, typeId, flag } = this.formDataInfo;
      if (val === MYTEAM_JOIN_TEAM_MODAL_TYPE.NOT_REGISTERED) {
        this.$router.push({
          path: '/competition/competition-register',
          query: {
            id: cid,
            name: typeName,
            typeId,
            flag,
          },
        });
      }
    },
    inviteCancel() {
      this.inviteVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.invited-icon {
  font-size: @jt-font-size-lger;
}
.invite-content {
  margin-bottom: 24px;
  font-size: @jt-font-size-base;
}
</style>
