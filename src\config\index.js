import defaultConfig from './default.config';
import { proxyPrefix, proxyTargetHost } from './proxy.config';

export function getDefaultConfig() {
  const config = { ...defaultConfig };
  config.KEYCLOAK_URL = `${proxyTargetHost}${config.KEYCLOAK_URL}`;
  return config;
}

export function getEnvConfig(key) {
  const config = getEnvConfigFromBody(key) || getDefaultConfig()[key];
  return config;
}

export function getLocalConfig(key) {
  const config = getDefaultConfig()[key];
  return config;
}

function getEnvConfigFromBody(key) {
  const target = document.querySelector('body');
  const value = target?.getAttribute(key.replace(/_/g, ''));
  return value || '';
}

export function getProxyPrefix() {
  return proxyPrefix;
}
