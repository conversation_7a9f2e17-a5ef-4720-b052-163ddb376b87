<script setup>
import { SoundFilled } from '@ant-design/icons-vue';
</script>
<template>
  <div class="new-course-message">
    <SoundFilled style="color: #ff9d00; font-size: 16px" />
    <span class="message-text">课程陆续上新，学习<span style="color: #ff9d00">赢算力！</span></span>
    <span class="route-icon" @click="() => (modalVisible = true)">查看详情<jt-icon type="iconright" /> </span>
    <a-modal v-model:open="modalVisible" class="new-course-message-modal" :width="480" :footer="null" @cancel="() => (modalVisible = false)">
      <div class="message-modal-body">
        <h2>课程陆续上新，学习赢算力</h2>
        <div class="content-paragraph">
          <p>“新学期，新课程”，毕昇平台将陆续新增热门公开课，权威研发，课程、课件、实验指导书、代码统统具备，支持边学边练。为激励课程学习，开展“学课程，赢算力”的活动。</p>
          <p>在九天·毕昇平台进行课程学习，每学完一门奖励“{{ giveBeansByCourse.beanCount }}算力豆”作为奖学金，算力豆有效期{{ giveBeansByCourse.beanPeriod }}天。</p>
          <p>活动日期：{{ giveBeansByCourse.startTime }} - {{ giveBeansByCourse.endTime }}，请及时领取，活动结束后，不可进行算力豆领取。</p>
          <p>以上活动解释权归九天·毕昇所有。</p>
        </div>
        <div style="text-align: right">
          <a-button style="width: 96px" type="primary" @click="() => (modalVisible = false)">知道了</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'NewCourseMessage',
  data() {
    return {
      modalVisible: false,
    };
  },
  computed: {
    ...mapState(['giveBeansByCourse']),
  },
};
</script>

<style lang="less" scoped>
.new-course-message {
  display: flex;
  align-items: center;
  height: 40px;
  color: #333333;
  font-size: 12px;
  padding-left: 16px;
  background: #fef6e7;
  border-radius: 2px;
  border: 1px solid #ffd666;
  .message-text {
    padding: 0px 8px 0px 10px;
  }
  .route-icon {
    color: #0082ff;
    cursor: pointer;
  }
}
.new-course-message-modal {
  :deep(.ant-modal-content) {
    border-radius: 4px;
  }
  :deep(.ant-modal-body) {
    padding: 40px;
    background-image: url('~@/assets/image/course/message-modal-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    .message-modal-body {
      h2 {
        font-size: 20px;
        font-weight: 600;
        color: #0082ff;
        line-height: 28px;
        position: relative;
        &::before {
          content: '';
          width: 32px;
          height: 4px;
          background: #0082ff;
          position: absolute;
          top: 32px;
        }
      }
      .content-paragraph {
        color: #121f2c;
        line-height: 22px;
        margin: 40px 0px 12px;
        p {
          padding-bottom: 12px;
        }
      }
    }
  }
}
</style>
