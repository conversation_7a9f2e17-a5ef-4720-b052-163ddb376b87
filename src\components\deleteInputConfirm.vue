<template>
  <a-modal v-model:open="open" :title="titleWithIcon" :footer="null" @cancel="$emit('cancel')">
    <template #icon>
      <jt-icon style="font-size: 18px; color: rgba(255, 69, 77, 1)" type="iconwarning-circle-fill"></jt-icon>
    </template>
    <div class="dlg-body">
      <p class="content">{{ content }}</p>
      <a-form ref="ruleForm" :colon="false" class="form-content" :model="form" :rules="rules">
        <a-form-item label="" name="text">
          <a-input v-model:value="form.text" class="input-el" placeholder='请输入"确定退出"'></a-input>
        </a-form-item>
      </a-form>
      <div style="text-align: right">
        <a-button :type="comfirmBtnType" @click="handleOk">
          {{ comfirmText }}
        </a-button>
        <a-button style="margin-left: 8px" @click="$emit('cancel')">
          {{ cancelText }}
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script lang="jsx">
// NOTE:不要删除，虽然h未被使用，但是需要引入，否则会报错
import { h } from 'vue';

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    visible: Boolean,
    comfirmText: {
      default: '确定',
      type: String,
    },
    cancelText: {
      default: '取消',
      type: String,
    },
    comfirmBtnType: {
      default: 'danger',
      type: String,
    },
    content: {
      type: String,
      default: '',
    },
  },
  emits: ['cancel', 'ok'],
  data() {
    return {
      open: true,
      text: '',
      form: {
        text: '',
      },
      rules: {
        text: [{ validator: this.checkText, message: '请输入"确定退出"', trigger: 'blur' }],
      },
    };
  },
  computed: {
    titleWithIcon() {
      return (
        <span>
          <jt-icon style="margin-right:8px;color:rgba(255, 69, 77, 1);" type="iconwarning-circle-fill"></jt-icon>
          {this.title}
        </span>
      );
    },
  },
  watch: {
    visible(val) {
      this.open = val;
      if (val) {
        this.$refs['ruleForm'] && this.$refs['ruleForm'].resetFields();
      }
    },
  },
  created() {
    this.open = this.visible;
  },
  methods: {
    checkText(rule, value, callback) {
      if (value === '确定退出') {
        callback();
      } else {
        callback(new Error('请输入'));
      }
    },
    handleOk() {
      this.$refs['ruleForm']
        .validate()
        .then(() => {
          this.$emit('ok');
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.ant-modal-content),
:deep(.ant-modal-body) {
  width: 480px;
}
:deep(.ant-modal-body) {
  width: 480px;
  padding: 0 32px 24px 60px;
}
:deep(.confirm-modal-content) {
  padding: 0px 0px 0px 26px;
}
:deep(.ant-form-explain) {
  text-align: right;
  font-size: 12px;
}
:deep(.ant-form-item) {
  margin-bottom: 32px;
}
:deep(.ant-form-item-with-help) {
  margin-bottom: 13px;
}
.content {
  margin-bottom: 24px;
}
.input-el {
  border: none;
  border-bottom: 1px solid #0082ff;
  &:focus {
    box-shadow: none;
  }
}
:deep(.ant-modal-header) {
  border-bottom: none;
  padding: 33px 33px 17px;
}
</style>
