#!/bin/bash
sed -i  "s|<body>|<body KEYCLOAKURL=\"$KEYCLOAK_URL\" ECLOUDURL=\"$ECLOUD_URL\" CONSOLEECLOUDURL=\"$CONSOLE_ECLOUD_URL\" SHOWPREVIEWLOGO=\"$SHOW_PREVIEW_LOGO\" FEATUREMESSAGECENTER=\"$FEATURE_MESSAGECENTER\" MESSAGEURLPATH=\"$MESSAGE_URL_PATH\" HELPCENTERURLPATH=\"$HELPCENTER_URL_PATH\" FEATUREMODELTASK=\"$FEATURE_MODEL_TASK\" SHOWFEATURETICKET=\"$SHOW_FEATURE_TICKET\" >|" /usr/local/tomcat/webapps/ROOT/index.html
/bin/bash ${TOMCAT_BASE_DIR}/bin/catalina.sh run 
