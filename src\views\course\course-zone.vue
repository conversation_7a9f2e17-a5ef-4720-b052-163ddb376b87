<template>
  <div class="my-course-container">
    <bread-crumb :value="breadCrumbs">
      <div class="tab-extra-container">
        <a-input v-model:value="keywords" class="search" placeholder="搜索课程" allow-clear>
          <template #prefix>
            <jt-icon type="iconsousuo" />
          </template>
        </a-input>
      </div>
    </bread-crumb>
    <div class="filter-list flex jt-box-shadow">
      <jt-button-filter v-model="filterValue.status" class="filter-row" :options="statusMap" :label="'课程状态'" @change="changeFilterValue"></jt-button-filter>
      <!-- <div class="filter-row">
        <p>课程状态</p>
        <a-button v-for="(item, index) in statusMap" :type="item.value === filterValue.status ? 'primary' : 'default'" class="filter-item" :key="index" @click="changeFilterValue(item)">{{ item.label }}</a-button>
      </div> -->
    </div>
    <div class="main-content flex">
      <div class="cover-container">
        {{ `${courseName}课程专区` }}
        <img src="" alt="" />
      </div>
      <ul class="content-list flex common-content-container">
        <jt-common-content :empty="list.length === 0" :loading="loading" :empty-image="emptyImage" :empty-title="emptyTitle" :empty-style="{ height: '576px' }">
          <li v-for="item in list" :key="item.id" class="content-item flex" @click="handleNavigateTo(item.id)">
            <div class="left flex">
              <div class="img-container">
                <img :src="item.imageUrl" alt="" />
              </div>
              <div class="info">
                <div class="info-title-container">
                  <span class="title">{{ item.name }}</span>
                  <jt-tag style="margin: 0px 8px">{{ item.courseFlag == 1 ? '公开课' : '封闭课' }}</jt-tag>
                  <jt-tag :type="courseStatusTag(item.courseStatus).class">{{ courseStatusTag(item.courseStatus).text }}</jt-tag>
                </div>
                <p class="text description">{{ item.courseIntroduce }}</p>
                <p>
                  <span class="number">{{ `${item.studyNum || 0}人学习` }}</span>
                </p>
              </div>
            </div>
            <div class="right">
              <span class="text">
                {{ item.instituteName }}
                <a-divider type="vertical"> </a-divider>
                {{ item.catalogNum || 0 }}节
                <a-divider type="vertical"></a-divider>
                {{ `开课时间：${item.startTime} - ${item.endTime}` }}
              </span>
            </div>
          </li>
          <jt-pagination :page-size="pageSize" :page-num="pageNum" :total="total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
          <!-- <div class="pagination flex">
            <div class="left flex">
              <span class="total">{{ `共${total}条` }}</span>
              <span>
                <span>每页显示</span>
                <a-select size="small" v-model="pageSize" class="select-box">
                  <a-select-option :value="5">5</a-select-option>
                  <a-select-option :value="10">10</a-select-option>
                  <a-select-option :value="20">20</a-select-option>
                </a-select>
                <span>条</span>
              </span>
            </div>
            <div class="right">
              <a-pagination :pageSize="pageSize" v-model="pageNum" show-quick-jumper :total="total"></a-pagination>
            </div>
          </div> -->
        </jt-common-content>
      </ul>
    </div>
  </div>
</template>

<script>
import breadCrumb from '../../components/breadCrumb';
import JtTag from '@/components/tag/index.vue';

const statusMap = [
  { label: '全部', value: '' },
  { label: '即将开始', value: 0 },
  { label: '进行中', value: 1 },
  { label: '已结束', value: 2 },
];

export default {
  components: { breadCrumb, JtTag },
  data() {
    return {
      breadCrumbs: [{ name: '学习', path: '/course' }],
      list: [],
      pageSize: 5,
      pageNum: 1,
      total: 5,
      keywords: '',
      type: '1',
      loading: false,
      filterValue: {
        status: '',
      },
      statusMap,
      courseName: '',
    };
  },
  computed: {
    emptyImage() {
      return this.keywords ? require('@/assets/image/empty2x.png') : require('@/assets/image/emptys2x.png');
    },
    emptyTitle() {
      let title = '';
      title = this.keywords ? '暂无结果' : '暂无课程';
      return title;
    },
  },
  watch: {
    'filterValue.status': 'init',
    keywords: 'init',
  },
  mounted() {
    this.zoneId = this.$route.params.zoneId;
    this.courseName = this.$route.query.name;
    this.getCourseList();
    this.breadCrumbs.push({ name: `${this.courseName}课程专区` });
  },
  methods: {
    pageNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getCourseList();
    },
    pageSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.getCourseList();
    },
    courseStatusTag(val) {
      const tagMap = {
        0: { text: '即将开始', class: 'tobegin' },
        1: { text: '进行中' },
        2: { text: '已结束', class: 'end' },
      };
      const tag = tagMap[val];
      return tag || {};
    },
    init() {
      this.pageNum = 1;
      this.getCourseList();
    },
    getCourseList() {
      const obj = {
        courseStatus: this.filterValue.status, // 课程状态 0：未开始；1：进行中； 2：结束
        pageNum: this.pageNum, // 页号
        pageSize: this.pageSize, // 一页条数
        courseName: this.keywords,
        courseId: this.zoneId,
      };
      this.list = [];
      this.loading = true;
      this.$POST('/course_model/web/course_student/course/coursePrefectureList', obj).then((res) => {
        this.loading = false;
        this.total = res.body.total;
        this.list = res.body.data;
      });
    },
    onTabChange(val) {
      this.type = val;
      this.getCourseList();
    },
    handleExplore() {
      this.$router.push('/course/course-list');
    },
    handleNavigateTo(courseId) {
      this.$router.push({ path: `/course/course-detail`, query: { courseId } });
    },
    changeFilterValue(value) {
      this.filterValue.status = value;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

p {
  margin: 0;
}
a {
  color: #0082ff;
}
.flex {
  display: flex;
}

.main-content {
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  padding-bottom: 48px;
  background: #f4f8fa;
  .content-list {
    flex-direction: column;
    width: 1200px;
    background: #fff;
    li {
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 176px;
      padding: 32px;
      margin-bottom: 1px;
      background: #ffffff;
      box-shadow: 0px 1px 0px 0px #e0e1e1;
      cursor: pointer;

      &:hover {
        background: #f8f9fa;
      }

      .img-container {
        margin-right: 32px;
        img {
          width: 148px;
          height: 112px;
        }
      }
      .info-title-container {
        display: flex;
        align-items: center;
      }
      .title {
        font-size: 18px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
      }
      &:hover {
        .title {
          color: #0082ff;
        }
      }
      .text {
        font-size: 14px;
        font-weight: 400;
        color: #606972;
      }
      .description {
        padding: 16px 0;
      }
      .number {
        font-size: 14px;
        font-weight: 400;
        color: #a0a6ab;
      }
      .right {
        display: flex;
        height: 100%;
        align-items: flex-start;
        .progress {
          margin-right: 24px;
          font-size: 14px;
          font-weight: 400;
          color: #606972;
        }
        a {
          font-size: 16px;
          font-weight: @jt-font-weight-medium;
        }
        .badge-container {
          margin-right: 36px;
        }
      }
    }
    .pagination {
      padding: 32px;
      justify-content: space-between;
      align-items: center;
      .left {
        align-items: center;
        .total {
          margin-right: 16px;
        }
      }
      .select-box {
        width: 52px;
        margin: 0 4px;
        // height: 28px;
      }
    }
  }
}
.cover-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1200px;
  height: 180px;
  background: url('../../assets/image/course/course-zone-banner.png');
  font-size: 45px;
  font-weight: @jt-font-weight-medium;
  color: #001a68;
}
.filter-list {
  background: #fff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  .filter-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-top: 16px;
    width: 1200px;
    > p {
      width: 76px;
      line-height: 20px;
      margin-right: 13px;
    }
  }
  justify-content: center;
  .filter-item {
    margin-right: 12px;
    display: block;
    padding: 3px 11px;
    line-height: 20px;
    height: auto;
    font-size: 14px;
    &.ant-btn-default {
      border-color: transparent;
      box-shadow: none;
    }
  }
}
.empty {
  background: #fff;
  text-align: center;
}
</style>
