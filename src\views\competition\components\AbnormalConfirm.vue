<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <confirm-modal v-model="abnormalModalVisibleCopy" :ok-text="ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE[current].okText" :cancel-text="ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE[current].cancelText" :title="ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE[current].title" :show-cancel="ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE[current].cancelText !== ''" :spinning="spinning" @ok="handleOk" @cancel="abnormalModalCancel">
    <template #icon>
      <ExclamationCircleFilled class="abnormal-icon" style="color: #0082ff" />
    </template>
    <div class="dlg-body abnormal-content">
      <p :class="!isUpdateInfo ? ' abnormal-user-text' : ''">
        <span>{{ ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE[current].content }}</span>
        <span v-if="isClosedToEcloud"
          ><em class="phone">{{ abnormalMobileCurrentBody.phoneNum }}</em
          >{{ ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE[current].secondContent }}</span
        >
      </p>

      <div v-if="isUpdateInfo" class="abnormal-user-info">
        <p>移动云手机号：{{ abnormalMobileCurrentBody.ecloudPhone }}</p>
        <p>移动云用户名：{{ abnormalMobileCurrentBody.ecloudUserName }}</p>
      </div>
    </div>
  </confirm-modal>
</template>

<script>
import API from '@/constants/api/API.js';

import confirmModal from '@/components/confirmModal/index.vue';
import { openInNewTab } from '@/utils/utils';
import { ECLOUD_URL_CONFIG, ECLOUD_URL, CONSOLE_ECLOUD_URL, ECLOUD_UPDATE_INFO_MODAL_STATE, ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE } from '@/common/ecloud';
import { addUrlParams } from '@/utils/utils';
export default {
  components: { confirmModal },
  props: {
    current: [Number],
    abnormalModalVisible: {
      type: Boolean,
      require: true,
    },
    abnormalMobileCurrentBody: {
      type: Object,
      require: true,
    },
    formDataInfo: {
      type: Object,
      require: true,
    },
    mobileCloudStatus: {
      type: String,
      require: true,
    },
  },
  emits: ['abnormalModalCancel'],
  data() {
    return {
      ECLOUD_UPDATE_INFO_MODAL_STATE,
      ECLOUD_UPDATE_INFO_MODAL_TEXT_STATE,
      spinning: false,
      abnormalModalVisibleCopy: !!this.abnormalModalVisible,
    };
  },
  computed: {
    isUpdateInfo() {
      return this.current === ECLOUD_UPDATE_INFO_MODAL_STATE.UPDATED;
    },
    isClosedToEcloud() {
      return this.current === ECLOUD_UPDATE_INFO_MODAL_STATE.CLOSED_TO_ECLOUD;
    },
  },
  watch: {
    abnormalModalVisible(val) {
      this.abnormalModalVisibleCopy = !!val;
    },
  },
  methods: {
    handleOk() {
      if (this.isUpdateInfo) {
        this.updatedInfo();
      } else if (this.isClosedToEcloud) {
        this.closedToEcloud();
      } else {
        // 如移动云手机号已不存在，且在毕昇平台已进入报名流程,跳转到注册页面
        this.goCompetitionRegister();
      }
    },
    // 移动云手机号发生变化更新
    updatedInfo() {
      this.spinning = true;
      const { ecloudPhone, ecloudUserId, ecloudUserName } = this.abnormalMobileCurrentBody;
      const ecloudUpdateUserInfoRequest = {
        ecloudPhone: ecloudPhone,
        ecloudUserId: ecloudUserId,
        ecloudUserName: ecloudUserName,
      };
      API.competition_model.updateEcloudInfo(ecloudUpdateUserInfoRequest).then(() => {
        if (!(this.mobileCloudStatus != '完成报名')) {
          // 如发生变化，且已完成报名（按钮显示的是“进入深度学习平台”）,跳转移动云页
          this.ecloudSSOCheck();
        } else {
          // 如发生变化，且在毕昇平台已进入报名流程（按钮显示的是“继续报名”），跳转注册页
          this.goCompetitionRegister();
        }
        this.abnormalModalCancel();
        this.spinning = false;
      });
    },
    goCompetitionRegister() {
      const { cid, typeName, typeId, flag } = this.formDataInfo;
      this.$router.push({
        path: '/competition/competition-register',
        query: {
          id: cid,
          name: typeName,
          typeId,
          flag,
        },
      });
    },
    // 如移动云手机号已不存在，且在毕昇平台已完成报名,跳转到移动云页面
    closedToEcloud() {
      openInNewTab(`${ECLOUD_URL}/home/<USER>
      this.abnormalModalCancel();
    },
    ecloudSSOCheck() {
      API.competition_model.ecloudSSOCheck().then((res) => {
        if (res.state === 'OK') {
          const destUrl = window.escape(`${CONSOLE_ECLOUD_URL}${ECLOUD_URL_CONFIG.CONSOLE_DEEP_LEARNING_URL}`);
          const url = `${ECLOUD_URL}${ECLOUD_URL_CONFIG.ECLOUD_SSO_CHECK_URL}`;
          const urlParams = {
            token: res.body,
            destUrl: destUrl,
            systemSource: 'BiSheng',
          };
          const deepLearnUrl = addUrlParams(url, urlParams);
          openInNewTab(deepLearnUrl);
        }
      });
    },
    abnormalModalCancel() {
      this.$emit('abnormalModalCancel');
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.abnormal-icon {
  font-size: @jt-font-size-lger;
}
.abnormal-content {
  font-size: @jt-font-size-base;
}

.abnormal-user-text {
  margin-bottom: 32px;
}
.abnormal-user-info {
  margin-top: 12px;
  margin-bottom: 32px;
}

.phone {
  color: @jt-primary-color;
  font-style: normal;
}
</style>
