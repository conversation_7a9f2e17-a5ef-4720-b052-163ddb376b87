<template>
  <div class="competition-info-finish">
    <div>
      <img src="@/assets/image/h5/successinvite.png" alt="" />
      <p>{{ isCreateCompetition ? '恭喜，您已成功创建比赛' : '编辑比赛基本信息成功！' }}</p>
      <p v-if="isCreateCompetition">您可点击”管理比赛“，继续完善比赛设置，并向用户发布比赛</p>
    </div>
    <a-space class="form-btns">
      <a-button v-if="showRouteBtn" @click="goCheck" style="width: 158px"> {{ this.isCreateCompetition ? '进入我举办的比赛' : '查看线上比赛页' }} </a-button>
      <a-button type="primary" @click="goBack" style="width: 158px">{{ this.isCreateCompetition ? '管理比赛' : '返回比赛管理' }}</a-button>
    </a-space>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { publishStatusKeys } from '../../../competitionConfig';
export default {
  name: 'infoPreview',
  props: {
    isCreateCompetition: Boolean,
  },
  data() {
    return {};
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
    /* 编辑未发布的比赛则不显示 查看线上比赛的按钮 */
    showRouteBtn() {
      return this.isCreateCompetition || (!this.isCreateCompetition && this.currentManageCompetition.releaseSta === publishStatusKeys.PUBLISHED);
    },
  },
  methods: {
    goCheck() {
      if (this.isCreateCompetition) {
        this.$router.push({
          path: '/competition/held-competition',
        });
        return;
      }
      const { competitionId } = this.$route.params;
      this.$router.push({
        path: '/competition/competition-detail',
        query: {
          id: competitionId, // 当前比赛id
          // flag: item.flag, // 进行中 即将开始 未开始
          // name: competitionName, // 产品需求 进入详情不显空白
        },
      });
    },
    goBack() {
      let competitionId = null;
      if (this.isCreateCompetition) {
        competitionId = this.currentManageCompetition.cid;
      } else {
        competitionId = this.$route.params.competitionId;
      }
      this.$router.push({
        path: `/competition/competition-management/${competitionId}`,
        query: {
          tabId: this.isCreateCompetition ? '1' : '2',
          noLeaveConfirm: true,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.competition-info-finish {
  width: 100%;
  text-align: center;
  img {
    width: 48px;
    height: 48px;
  }
  p {
    font-size: 16px;
    color: #031129;
    margin: 16px 0 0px;
  }
}
.form-btns {
  margin-top: 38px;
}
</style>
