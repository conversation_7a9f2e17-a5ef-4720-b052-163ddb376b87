<template>
  <div class="my-course-container">
    <bread-crumb :value="breadCrumbs">
      <a-popover placement="bottomRight">
        <template #content>
          <div class="popover-content">
            <p>当有新增课节时，已学公开课将移至在学公开课。</p>
            <p>当有删减课节时，在学公开课可能移至已学公开课。</p>
            <p>当有更新课节时（非新增课节），如已学完则保持在已学列表中，请您进入课程进行重新学习。</p>
          </div>
        </template>
        <template #title>
          <span>公开课学习更新说明</span>
        </template>
        <a class="tips">
          <jt-icon class="icon" type="iconinfo-circle"></jt-icon>
          <span>公开课学习更新说明</span>
        </a>
      </a-popover>
    </bread-crumb>
    <div class="tabs-container flex jt-box-shadow">
      <a-tabs v-model:activeKey="type" class="tabs" default-active-key="0">
        <a-tab-pane key="0" :tab="`在学公开课 ${learningCount}`"></a-tab-pane>
        <a-tab-pane key="1" :tab="`已学公开课 ${finishCount}`"></a-tab-pane>
        <template #rightExtra>
          <div class="tab-extra-container">
            <a-input v-model:value="keywords" allow-clear class="search" placeholder="搜索课程">
              <template #prefix>
                <jt-icon type="iconsousuo" />
              </template>
            </a-input>
            <a-button type="primary" @click="handleExplore">发现新公开课</a-button>
          </div>
        </template>
      </a-tabs>
    </div>
    <div class="main-content flex">
      <div class="content-container">
        <ul class="content-list flex common-content-container">
          <jt-common-content :loading="loading" :empty-style="emptyStyle" :empty="list.length === 0 && !loading" :empty-image="emptyImage" :empty-title="emptyDescription.title" :empty-text="emptyDescription.text">
            <li v-for="item in list" :key="item.courseId" class="content-item flex">
              <div class="left flex">
                <div class="img-container">
                  <img :src="item.imageUrl" alt="" />
                </div>
                <div class="info">
                  <div class="info-title-container">
                    <span class="title">{{ item.name }}</span>
                    <jt-tag style="margin: 0px 8px">{{ item.courseFlag == 1 ? '公开课' : '封闭课' }}</jt-tag>
                    <jt-tag :type="courseStatusTag(item.courseStatus).class">{{ courseStatusTag(item.courseStatus).text }}</jt-tag>
                  </div>
                  <p class="text description ellipsis-text">{{ item.courseIntroduce }}</p>
                  <p>
                    <span class="text">
                      {{ item.instituteName }}
                      <a-divider type="vertical"></a-divider>
                      {{ `开课时间：${item.startTime} - ${item.endTime}` }}
                    </span>
                    <span class="number">{{ `${item.studyNum || 0}人学习` }}</span>
                  </p>
                </div>
              </div>
              <div v-if="type === '0'" class="right">
                <span class="progress">{{ `已学${item.mapFlag || 0}节 / 共${item.catalogNum || 0}节` }}</span>
                <a-button type="link" :disabled="courseStatusTag(item.courseStatus).text === '即将开始'" @click="handleNavigateTo(item.id)">前往学习<jt-icon type="iconright" /></a-button>
              </div>
              <div v-else class="right">
                <div class="badge-container">
                  <img src="@/assets/image/course/finish-badge.png" alt="" />
                </div>
                <a-button type="link" @click="handleNavigateTo(item.id)">查看课程<jt-icon type="iconright" /></a-button>
              </div>
            </li>
          </jt-common-content>
          <jt-pagination v-if="list.length > 0" class="pagination-box" :page-size="pageSize" :page-num="pageNum" :total="total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import breadCrumb from '../../components/breadCrumb';
import { courseStudentApi } from '@/apis';
import { checkLogin } from '@/keycloak';
import debounce from 'lodash/debounce';
import JtTag from '@/components/tag/index.vue';

const labelMap = {
  0: '在学课程',
  1: '已学课程',
};

export default {
  components: { breadCrumb, JtTag },
  data() {
    return {
      breadCrumbs: [{ name: '学习', path: '/course' }, { name: '我学习的公开课' }],
      list: [],
      pageSize: 5,
      pageNum: 1,
      total: 5,
      keywords: '',
      type: '0',
      loading: false,
      learningCount: 0,
      finishCount: 0,
      emptyStyle: {
        'min-height': '416px',
        height: 'calc(100vh - 672px)', // 根据头部及底部高度动态计算空态高度撑满
      },
      emptyDescription: {},
      emptyImage: '',
    };
  },
  watch: {
    type() {
      this.keywords = '';
      this.pageNum = 1;
      this.getCourseList();
    },
    keywords: debounce(function () {
      this.pageNum = 1;
      this.getCourseList();
    }, 500),
    list(val) {
      if (val.length === 0) {
        const description = {};
        description.title = this.keywords ? '抱歉，没有找到相关课程' : `您暂无${labelMap[this.type]}`;
        description.text = this.keywords ? '您可以换一个关键词试试哦~' : '加油学习哦~';
        this.emptyImage = this.keywords ? require('@/assets/image/empty2x.png') : require('@/assets/image/emptys2x.png');
        this.emptyDescription = description;
      }
    },
  },
  mounted() {
    if (!checkLogin()) {
      return;
    }
    this.getCourseList();
    this.getCourseCount();
  },
  // computed: {
  // emptyImage() {
  //   return this.keywords ? require('@/assets/image/empty2x.png') : require('@/assets/image/emptys2x.png');
  // },
  // emptyDescription() {
  //   const description = {};
  //   description.title = this.keywords ? '抱歉，没有找到相关课程' : `您暂无${labelMap[this.type]}`;
  //   description.text = this.keywords ? '您可以换一个关键词试试哦~' : '加油学习哦~';
  //   return description;
  // },
  // },
  methods: {
    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getCourseList();
    },
    pageNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getCourseList();
    },
    courseStatusTag(val) {
      const tagMap = {
        0: { text: '即将开始', class: 'tobegin' },
        1: { text: '进行中' },
        2: { text: '已结束', class: 'end' },
      };
      const tag = tagMap[val || 1];
      return tag;
    },
    getCourseCount() {
      const obj = {
        status: 1,
      };
      courseStudentApi.getCourseStudentFindname(obj).then((res) => {
        if (!res.errorCode) {
          this.finishCount = res.body.total;
        }
      });
      obj.status = 0;
      courseStudentApi.getCourseStudentFindname(obj).then((res) => {
        if (!res.errorCode) {
          this.learningCount = res.body.total;
        }
      });
    },
    getCourseList() {
      const obj = {
        status: this.type,
        courseName: this.keywords, // 课程名称
        pageNum: this.pageNum, // 页号
        pageSize: this.pageSize, // 一页条数
        requestId: this.$store.state.requestId, // 请求唯一id
      };
      // this.list = [];
      // this.total = 0;
      this.loading = true;
      courseStudentApi.getCourseStudentFindname(obj).then((res) => {
        this.loading = false;
        if (!res.errorCode) {
          this.total = res.body.total;
          this.list = res.body.data;
        }
      });
    },
    handleExplore() {
      this.$router.push('/course/course-list');
    },
    handleNavigateTo(courseId) {
      this.$router.push(`/course/course-overview/${courseId}`);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

p {
  margin: 0;
}
a {
  color: #0082ff;
}
.flex {
  display: flex;
}
.tips {
  display: inline-flex;
  align-items: center;
  .icon {
    color: #0082ff;
    font-size: 18px;
    margin-right: 4px;
  }
}

.tabs-container {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  background: #fff;
  .tabs {
    width: 1200px;
  }
  :deep(.ant-tabs-bar) {
    margin: 0;
    border-bottom: none;
  }
  .tab-extra-container {
    // padding-bottom: 20px;
    .search {
      width: 240px;
      margin-right: 16px;
    }
  }
}
.main-content {
  justify-content: center;
  margin-top: 20px;
  padding-bottom: 48px;
  background: #f4f8fa;
  .content-container {
    width: 1200px;
  }
  .content-list {
    flex-direction: column;
    background: #fff;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    li {
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 176px;
      padding: 32px;
      margin-bottom: 1px;
      background: #ffffff;
      box-shadow: 0px 1px 0px 0px #e0e1e1;

      &:hover {
        background: #f8f9fa;
      }

      .img-container {
        margin-right: 32px;
        img {
          width: 148px;
          height: 112px;
        }
      }
      .info-title-container {
        display: flex;
        align-items: center;
      }
      .title {
        font-size: 18px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
      }
      .text {
        display: inline-block;
        font-size: 14px;
        font-weight: 400;
        color: #606972;
      }
      .description {
        width: 700px;
        padding: 16px 0;
      }
      .number {
        margin-left: 32px;
        font-size: 14px;
        font-weight: 400;
        color: #a0a6ab;
      }
      .right {
        display: flex;
        align-items: center;
        .progress {
          margin-right: 24px;
          font-size: 14px;
          font-weight: 400;
          color: #606972;
        }
        a {
          font-size: 16px;
          font-weight: @jt-font-weight-medium;
        }
        .badge-container {
          margin-right: 36px;
          img {
            width: 96px;
          }
        }
      }
    }
    .pagination {
      padding: 32px;
      justify-content: space-between;
      align-items: center;
      .left {
        align-items: center;
        .total {
          margin-right: 16px;
        }
      }
      .select-box {
        width: 52px;
        margin: 0 4px;
      }
    }
  }
}
.popover-content {
  width: 322px;
}
.flex {
  display: flex;
}
.tabs-container {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  background: #fff;
  .tabs {
    width: 1200px;
  }
  :deep(.ant-tabs-bar) {
    margin: 0;
    border-bottom: none;
  }
  :deep(.ant-tabs-nav-container) {
    font-size: 18px;
  }
  .tab-extra-container {
    // padding-bottom: 20px;
    .search {
      width: 240px;
      margin-right: 16px;
    }
  }
}
:deep(.ant-tabs-ink-bar) {
  bottom: 2px;
}
.pagination-box {
  margin-top: 0;
  height: 92px;
}
</style>
