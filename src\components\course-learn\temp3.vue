<template>
  <div class="temp-container">
    <VueDragResize @resizing="resize" :w="initialWidth" :minw="minWidth" :parentW="maxWidth" :parentLimitation="true" :isActive="true" :isDraggable="false" :sticks="['mr']" class="drageble-item" :preventActiveBehavior="true">
      <div class="left">
        <video-viewer :style="{ height: adaptiveHeight + 'px' }" v-bind="$attrs"></video-viewer>
      </div>
    </VueDragResize>
    <div class="right">
      <pdf-viewer :style="{ 'min-height': adaptiveHeight + 'px' }" v-bind="$attrs" class="middle-size"></pdf-viewer>
    </div>
  </div>
</template>

<script>
import videoViewer from '../videoViewer';
import pdfViewer from '../pdfViewer';
import dragResizeMixin from './drag-resize-mixin';
import VueDragResize from 'vue-drag-resize';

export default {
  mixins: [dragResizeMixin],
  components: {
    videoViewer,
    pdfViewer,
    VueDragResize,
  },
};
</script>

<style lang="less" scoped>
@import './common.less';
@import './drag-resize.less';
</style>
