/**
 * keycloak配置文件
 * @module Axios 封装后的axios模块，这里指向axios.create后的对象
 * @module configJson 配置文件
 * @module keycloak keycloak模块
 */
import { axios } from './request';
import { getEnvConfig } from './config';
import store from './store';
import { isH5device, getQueryByName } from '@/utils/utils.js';
import Keycloak from 'keycloak-js';

export function getKeycloakUrl() {
  const keycloakUrl = getEnvConfig('KEYCLOAK_URL');
  return keycloakUrl;
}

const initOptions = {
  onLoad: 'login-required',
  realm: 'TechnicalMiddlePlatform',
  url: getKeycloakUrl(),
  clientId: 'dlp-train-front',
  'ssl-required': 'external',
  resource: 'dlp-train-front',
  'public-client': true,
  'confidential-port': 0,
};
export const keycloak = new Keycloak(initOptions);
window.$keycloak = keycloak;
export const PREVIEW_AUTH = () => keycloak.authenticated && keycloak.idTokenParsed.DLP_USER_ALLOW_PREVIEW_FEATURE == '1';
export const UPGRADING = () => !keycloak.authenticated || (keycloak.authenticated && keycloak.idTokenParsed.DLP_USER_ALLOW_ENTER_DURING_UPGRADING == '1');

/**
 * 使用export将keycloakDone方法暴露出去，return一个promise对象
 *
 * 这样在引用处就可以使用import()配合.then的方法在下面的else处执行想要执行的代码
 * 防止有接口返回时出现异步的错误
 * @returns promise
 */
export function init() {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    const authenticated = await keycloak.init({
      onLoad: 'check-sso',
      checkLoginIframe: false, // 检测登陆状态
    });
    if (!authenticated) {
      reject();
    } else {
      store.commit('UPDATE_RETRESHTOKEN', keycloak.token);
      axios.interceptors.request.use((config) => {
        config.headers.Authorization = 'Bearer ' + keycloak.token;
        return config;
      });
      resolve();
    }
    // 检测token是否刷新
    setInterval(() => {
      if (keycloak.authenticated) {
        keycloak.updateToken(5).then(() => {
          store.commit('UPDATE_RETRESHTOKEN', keycloak.token);
        });
      }
    }, 60000);
  });
}

// 根据项目情况定义该变量,同时兼容互联网和办公网版本,可根据环境变量控制该变量
// 互联网环境下为 '',
const keycloakLogoutPrefix = '';

/**
 *  退出登陆
 *  @param {string} redirectUrl - 需要重定向的地址
 */
export async function logout(redirectUrl = window.location.href) {
  document.cookie = '';
  const url = new URL(keycloak.createLogoutUrl());
  const params = new URLSearchParams(url.search);
  params.set('post_logout_redirect_uri', redirectUrl);
  url.search = params.toString();
  const replaceUrl = keycloakLogoutPrefix ? `${keycloakLogoutPrefix}${url.href}` : url.href;
  window.location.replace(replaceUrl);
}

export function checkLogin(redirect) {
  if (!keycloak.authenticated) {
    if (redirect) {
      login();
    }
    return false;
  }
  return true;
}

export function login() {
  let loginUrl = '';
  if (isH5device()) {
    loginUrl = keycloak.createLoginUrl();
  } else {
    const idp_preferred = getQueryByName('idp_preferred');
    if (idp_preferred) {
      loginUrl = keycloak.createLoginUrl({ idpHint: idp_preferred });
    } else {
      loginUrl = keycloak.createLoginUrl();
    }
  }
  window.location.replace(loginUrl);
}

export const checkPublishCompetitionPermission = () => {
  return keycloak?.idTokenParsed.DLP_USER_ALLOW_PUBLISH_COMPETITION === '1';
};

export function isLogin() {
  return keycloak.authenticated;
}

export function register() {
  return keycloak.register();
}
