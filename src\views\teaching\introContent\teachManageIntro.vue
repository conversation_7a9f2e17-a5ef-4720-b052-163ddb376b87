<template>
  <div style="background-color: #f4f8fa">
    <div class="teach-manage-intro">
      <h1 class="head-title">{{ teachingManage.title }}</h1>
      <div class="teach-manage-content">
        <div class="left-intro">
          <div class="intro-item" style="margin-top: 56px">
            <span v-for="dataItem in teachingManage.teachManage.list[0].data" :key="dataItem">{{ dataItem }}</span>
          </div>
          <div class="intro-item" style="margin-top: 46px">
            <span v-for="dataItem in teachingManage.teachManage.list[1].data" :key="dataItem">{{ dataItem }}</span>
          </div>
          <div class="intro-item" style="margin-top: 48px">
            <span v-for="dataItem in teachingManage.teachManage.list[2].data" :key="dataItem">{{ dataItem }}</span>
          </div>
        </div>
        <div class="center-content">
          <span>{{ teachingManage.teachManage.list[0].title }}</span>
          <span>{{ teachingManage.teachManage.list[1].title }}</span>
          <span>{{ teachingManage.teachManage.list[2].title }}</span>
          <span>{{ teachingManage.systemManage.list[0].title }}</span>
          <span>{{ teachingManage.systemManage.list[1].title }}</span>
          <span>{{ teachingManage.teachManage.title }}</span>
          <span>{{ teachingManage.systemManage.title }}</span>
          <p class="course-p">{{ teachingManage.teachManage.subtitle }}</p>
          <p class="system-p">{{ teachingManage.systemManage.subtitle }}</p>
        </div>
        <div class="right-intro">
          <div style="margin-top: 100px">
            <span v-for="dataItem in teachingManage.systemManage.list[0].data" :key="dataItem">
              {{ dataItem }}
            </span>
          </div>
          <div style="margin-top: 54px">
            <span v-for="dataItem in teachingManage.systemManage.list[1].data" :key="dataItem">
              {{ dataItem }}
            </span>
          </div>
        </div>
      </div>
      <div style="text-align: center; margin-top: 5px">
        <consult-button />
      </div>
    </div>
  </div>
</template>

<script>
import { teachingManage } from './config.js';
import ConsultButton from './../components/ConsultButton.vue';
export default {
  name: 'teachManageIntro',
  components: {
    ConsultButton,
  },
  data() {
    return {
      teachingManage,
    };
  },
};
</script>

<style lang="less" scoped>
.teach-manage-intro {
  height: 525px;
  width: 1200px;
  margin: 0px auto;
  .head-title {
    padding-top: 40px;
    padding-bottom: 10px;
    color: #121f2c;
    font-size: 32px;
    text-align: center;
  }
  .teach-manage-content {
    display: flex;
  }
  .left-intro {
    width: 144px;
    margin-right: 14px;
    .intro-item {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
  .center-content {
    position: relative;
    width: 928px;
    height: 337px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-image: url('~@/assets/image/teaching/introduction-bac.png');
    span {
      position: absolute;
      font-size: 19px;
    }
    p {
      width: 100px;
      color: white;
      font-size: 12px;
      position: absolute;
    }
    .course-p {
      left: 308px;
      top: 150px;
    }
    .system-p {
      left: 520px;
      top: 150px;
    }
    span:nth-child(1) {
      left: 112px;
      top: 60px;
      color: #0082ff;
    }
    span:nth-child(2) {
      left: 72px;
      top: 150px;
      color: #0082ff;
    }
    span:nth-child(3) {
      left: 110px;
      top: 240px;
      color: #0082ff;
    }
    span:nth-child(4) {
      right: 86px;
      top: 106px;
      color: #019ab0;
    }
    span:nth-child(5) {
      right: 86px;
      top: 196px;
      color: #019ab0;
    }
    span:nth-child(6) {
      top: 108px;
      left: 316px;
      color: #ffffff;
    }
    span:nth-child(7) {
      top: 108px;
      left: 526px;
      color: #ffffff;
    }
  }
  .right-intro {
    margin-left: 14px;
    div {
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
