<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div>
    <div ref="subtitle">
      <a-space>
        <a-button v-if="!clearSettingsDisabled" :disabled="clearSettingsDisabled" @click="handleClearSettings">清空设置</a-button>
        <a-button type="primary" @click="gotoEdit"><EditOutlined /> 编辑 </a-button>
      </a-space>
    </div>
    <reviewContentVue ref="reviewContent" @getSearchValue="(val) => (searchValue = val)" @updateCleared="handleUpdateCleared" />
    <reviewTableVue ref="reviewTable" :search-value="searchValue" />
    <reviewRecordTableVue />
  </div>
</template>

<script>
import reviewContentVue from './reviewContent.vue';
import reviewTableVue from './reviewTable.vue';
import reviewRecordTableVue from './reviewRecordTable.vue';
import { clearSettings } from '../../common';

const settingsType = '审查及答辩材料提交';

export default {
  components: {
    reviewContentVue,
    reviewTableVue,
    reviewRecordTableVue,
  },
  emits: ['getSubtitle'],
  data() {
    return {
      searchValue: '',
      cid: '',
      clearSettingsDisabled: false,
    };
  },
  mounted() {
    this.$emit('getSubtitle', this.$refs.subtitle);
    this.cid = this.$route.params.competitionId;
  },
  methods: {
    gotoEdit() {
      const { competitionId } = this.$route.params;
      this.$router.push(`${competitionId}/review-edit`);
    },
    handleClearSettings() {
      clearSettings(this.cid, settingsType, () => {
        this.$refs.reviewContent.getData();
        this.$refs.reviewTable.getTableData();
      });
    },
    handleUpdateCleared(val) {
      this.clearSettingsDisabled = val;
    },
  },
};
</script>
