.table {
    margin-top: 20px;
    .common-middile-table-style();
  }
  .filter-status-select {
    div {
      cursor: pointer;
      width: 120px;
      height: 40px;
      font-size: 12px;
      color: #606972;
      line-height: 40px;
      &:hover {
        background-color: #f0f8ff;
      }
    }
    .all {
      padding-left: 10px;
    }
    .already-study,
    .old-study,
    .un-study {
      position: relative;
      padding-left: 30px;
      &::before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 8px;
        position: absolute;
        left: 10px;
        top: 18px;
      }
    }
    .already-study::before {
      background-color: #17bb85;
    }
    .old-study::before {
      background-color: #0082ff;
    }
    .un-study::before {
      background-color: #f87b0a;
    }
  
    .select {
      background-color: #f0f8ff;
    }
  }
  .status-icon {
    display: inline-block;
    width: 7px;
    height: 7px;
    border-radius: 8px;
    &.orange {
      background-color: #f87b0a;
    }
    &.green {
      background-color: #17bb85;
    }
    &.blue {
      background-color: #0082ff;
    }
    &.grey {
      background-color: #c2c5cf;
    }
  }
  .jt-pagination {
    display: flex;
    justify-content: space-between;
    padding: 16px 0px 40px;
  }
  .empty-content {
    p:nth-of-type(1) {
      color: #121f2c;
      font-size: 18px;
    }
  }