<template>
  <micro-components module-name="common-feedback-modal" :data="componentData" @cancel="$emit('cancel')" @ok="$emit('ok')" />
</template>

<script>
import { GET, POST } from '@/request';
export default {
  name: 'CommonFeedbackModalRemote',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['cancel', 'ok'],
  data() {
    return {
      requester: { GET, POST },
    };
  },
  computed: {
    componentData() {
      return {
        requester: this.requester,
        visible: this.visible,
        config: {
          hostPlatform: '毕昇',
        },
      };
    },
  },
};
</script>
