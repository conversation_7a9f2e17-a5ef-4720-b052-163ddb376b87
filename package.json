{"name": "jt-education-frontend-portal", "version": "0.1.0", "private": true, "scripts": {"serve": "cross-env TARGET_HOST=http://************** vue-cli-service serve", "serve:test": "cross-env NODE_OPTIONS=--max-old-space-size=4096 PROXY_ENV=TEST TARGET_HOST=http://************** vue-cli-service serve", "vite": "cross-env TARGET_HOST=http://************** vite", "vite:test": "cross-env PROXY_ENV=TEST TARGET_HOST=http://************** vite", "serve-setnode": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "serve-test-setnode": "SET NODE_OPTIONS=--openssl-legacy-provider && cross-env PROXY_ENV=TEST TARGET_HOST=http://************** vue-cli-service serve", "build": "vue-cli-service build", "build-setnode": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "6.1.0", "@micro-zoe/micro-app": "0.8.11", "ant-design-vue": "4.2.6", "axios": "1.8.0", "clean-webpack-plugin": "3.0.0", "core-js": "3.41.0", "js-md5": "0.7.3", "keycloak-js": "21.1.2", "less-vars-to-js": "1.3.0", "lodash": "4.17.21", "mavon-editor": "3.0.2", "mini-css-extract-plugin": "1.6.2", "moment": "2.30.1", "path": "0.12.7", "reqwest": "2.0.5", "screenfull": "5.1.0", "sockjs-client": "1.4.0", "stompjs": "2.3.3", "vant": "4.9.6", "vite-plugin-html": "3.2.2", "vue": "3.5.13", "vue-drag-resize": "1.5.4", "vue-loader": "17.4.2", "vue-router": "4.5.0", "vue3-pdf-app": "1.0.3", "vuex": "4.1.0", "wangeditor": "4.7.15", "webpack": "4.47.0"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/preset-env": "7.26.9", "@originjs/vite-plugin-commonjs": "1.0.3", "@originjs/vite-plugin-require-context": "1.0.9", "@vitejs/plugin-vue": "2.3.4", "@vitejs/plugin-vue-jsx": "1.3.10", "@vue/babel-helper-vue-jsx-merge-props": "1.4.0", "@vue/babel-preset-jsx": "1.4.0", "@vue/cli-plugin-babel": "4.5.19", "@vue/cli-plugin-eslint": "4.5.19", "@vue/cli-plugin-router": "4.5.19", "@vue/cli-plugin-vuex": "4.5.19", "@vue/cli-service": "4.5.19", "@vue/compiler-sfc": "3.5.13", "@vue/eslint-config-prettier": "6.0.0", "babel-eslint": "10.1.0", "babel-plugin-import": "1.13.0", "compression-webpack-plugin": "5.0.1", "cross-env": "7.0.3", "cz-conventional-changelog": "3.3.0", "eslint": "6.8.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "7.20.0", "faker": "5.5.3", "json-server": "0.16.1", "less": "4.2.2", "less-loader": "7.3.0", "lint-staged": "11.0.0", "prettier": "2.8.8", "rap": "1.2.2", "swiper": "5.4.5", "thread-loader": "3.0.1", "unplugin-auto-import": "0.17.8", "validate-commit-msg": "2.14.0", "vite": "6.3.0-beta.2", "vite-plugin-path-resolve": "1.0.1", "vite-plugin-require-transform": "1.0.21", "webpack-cli": "3.3.11"}, "overrides": {"axios": "1.8.0", "braces": "3.0.3", "micromatch": "4.0.8", "debug": "3.1.0", "glob-parent": "5.1.2", "async-validator": "4.2.5", "highlight.js": "10.4.1", "vite": "6.3.0-beta.2"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}