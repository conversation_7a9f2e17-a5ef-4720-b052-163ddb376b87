<template>
  <div>
    <div v-if="isEcloudCompetition" class="section-steps-mobile-cloud">
      <a-steps :current="current">
        <a-step v-for="item in ECLOUD_COMPETITION_STEPS_TITLE" :key="item" :title="item" />
      </a-steps>
    </div>
    <div v-else class="section-steps">
      <a-steps :current="current">
        <a-step v-for="item in COMPETITION_STEPS_TITLE" :key="item" :title="item" />
      </a-steps>
    </div>
    <jt-common-content :loading="loading">
      <div class="section-form">
        <register-form v-if="current == 0" :is-ecloud-competition="isEcloudCompetition" @changeCurrent="changeCurrent" @changeCompleteSuccess="changeCompleteSuccess"></register-form>
        <mobile-cloud-account v-if="current == 1 && isEcloudCompetition" @changeCurrent="changeCurrent"></mobile-cloud-account>
        <deeplearn-order v-if="current == 2" @changeCurrent="changeCurrent" @changeCompleteSuccess="changeCompleteSuccess"></deeplearn-order>
      </div>
      <div v-if="(current == 1 && !isEcloudCompetition) || current == 3" class="section-result">
        <a-result status="success" title="报名成功">
          <template #subTitle>
            <p class="success-text">
              <span v-if="successStatusObject.beannum == 0">恭喜，您已成功报名。</span>
              <span v-else>恭喜，您已成功报名，免费获取{{ successStatusObject.beannum }}个算力豆（有效期{{ successStatusObject.daynum }}天）</span>
            </p>
            <p v-if="!isEcloudCompetition" class="success-text">您可使用比赛打榜实例进行模型训练，{{ completeSuccessPackageName }}</p>
            <p v-else class="success-text">您可前往移动云深度学习平台，即刻开始比赛~</p>
          </template>
          <template #extra>
            <p class="tips">您必须创建自己的团队，或者加入已有团队，方可提交结果文件，参与排行榜</p>
            <a-button key="console" type="primary" @click="createModalVisible = true"> 创建团队 </a-button>
            <a-button v-if="teamMaxNum > 1" key="buy" @click="inviteModalVisible = true"> 加入团队 </a-button>
            <a-tooltip v-else overlay-class-name="join-dis-title">
              <template #title> 团队成员数量上限为1，不支持加入团队 </template>
              <a-button type="primary" disabled> 加入团队 </a-button>
            </a-tooltip>
          </template>
        </a-result>
      </div>
    </jt-common-content>

    <modal v-model:open="createModalVisible" title="创建团队" :spinning="spinning" :confirm-loading="createTeamForm.teamName == '' || disabledCreateTeamBtn || createTeamForm.teamName.length > 20" @ok="createTeamOk" @cancel="goCompetitionDetail(COMPETITION_DETAIL_TABS.MY_TEAM)">
      <a-form ref="ruleForm" :colon="false" :label-col="labelCol" :wrapper-col="wrapperCol" :model="createTeamForm" :rules="formRules">
        <a-form-item label="团队名称：" name="teamName">
          <a-input v-model:value="createTeamForm.teamName" placeholder="请输入" style="width: 320px"></a-input>
        </a-form-item>
      </a-form>
    </modal>

    <modal v-model:open="inviteModalVisible" title="加入团队" ok-text="知道了" :show-cancel="false" @ok="inviteTeamModal"> 您可接受其他团队邀请，加入已有团队，请联系团队队长获取邀请链接 </modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import API from '@/constants/api/API.js';
import modal from '@/components/modal/modalSpin.vue';
import RegisterForm from './RegisterForm';
import MobileCloudAccount from './MobileCloudAccount';
import DeeplearnOrder from './DeeplearnOrder';
import { ECLOUD_COMPETITION_TYPE_ID_LIST } from '@/common/ecloud';
import { COMPETITION_DETAIL_TABS, COMPETITION_STEPS_TITLE, ECLOUD_COMPETITION_STEPS_TITLE, ECLOUD_COMPETITION_JOIN_STEP_TYPE, COMPETITION_STEPS_TYPE } from '../competitionConfig/index';

const route = useRoute();
const router = useRouter();

const COMPETITION_STEPS_TITLE_REF = ref(COMPETITION_STEPS_TITLE);
const ECLOUD_COMPETITION_STEPS_TITLE_REF = ref(ECLOUD_COMPETITION_STEPS_TITLE);
const cid = ref(route.query.id);
const cname = ref(route.query.name);
const typeId = ref(route.query.typeId);
const labelCol = ref({ span: 4 });
const wrapperCol = ref({ span: 12 });
const createModalVisible = ref(false);
const inviteModalVisible = ref(false);
const successStatusObject = reactive({
  beannum: '0',
  daynum: '0',
  specName: '0',
});
const createTeamForm = reactive({
  teamName: '',
});
const disabledCreateTeamBtn = ref(false);
const teamMaxNum = ref('');
const current = ref(0);
const isEcloudCompetition = ref(true);
const loading = ref(false);
const spinning = ref(false);
const COMPETITION_DETAIL_TABS_REF = ref(COMPETITION_DETAIL_TABS);

const formRules = computed(() => ({
  teamName: [{ max: 20, min: 1, message: '20个字符以内', trigger: ['blur', 'change'] }],
}));

const completeSuccessPackageName = computed(() => {
  const specLimitObject = successStatusObject.specLimit || {};
  const data = Object.keys(specLimitObject).length === 0 ? { cpuLimit: '0', vgpuLimit: '0', gpuLimit: '0' } : specLimitObject;
  let tips = '';
  if (Number(data.cpuLimit) && Number(data.vgpuLimit) && Number(data.gpuLimit)) {
    tips = `最高可用资源为${data.gpuLimit}算力卡`;
  } else if (Number(data.cpuLimit) && Number(data.vgpuLimit) && !Number(data.gpuLimit)) {
    tips = `最高可用资源为虚拟化算力卡`;
  } else if (Number(data.cpuLimit) && !Number(data.vgpuLimit) && !Number(data.gpuLimit)) {
    tips = `最高可用资源为CPU`;
  } else {
    tips = `最高可用资源为${data.gpuLimit}算力卡`;
  }
  return tips;
});

onMounted(() => {
  isEcloudCompetition.value = ECLOUD_COMPETITION_TYPE_ID_LIST.includes(Number(typeId.value));
  if (isEcloudCompetition.value) {
    getEcloud();
  }
});

function getEcloud() {
  loading.value = true;
  API.competition_model.getEcloudInfo({ cid: cid.value }).then((res) => {
    if (res.state === 'OK') {
      current.value = ECLOUD_COMPETITION_JOIN_STEP_TYPE[res.body.joinSta];
      Object.assign(successStatusObject, res.body.joinCompleteResult);
    } else if (res.state === 'ERROR' && res.errorCode == '-509') {
      message.error('系统繁忙，请稍后重试');
    }
    loading.value = false;
  });
}

function goCompetitionDetail(activeKey, teamId = null) {
  router.push({
    path: '/competition/competition-detail',
    query: {
      id: cid.value,
      name: cname.value,
      activeKey,
      teamId,
    },
  });
}

function createTeamOk() {
  disabledCreateTeamBtn.value = true;
  spinning.value = true;
  API.competition_model.createTeam({ owner: cid.value, teamName: createTeamForm.teamName }).then((res) => {
    if (res.state === 'OK') {
      goCompetitionDetail(COMPETITION_DETAIL_TABS.MY_TEAM, res.body.teamId);
    } else {
      message.error(res.errorMessage || '已有同名团队，请重新设置');
    }
    disabledCreateTeamBtn.value = false;
    spinning.value = false;
  });
}

function inviteTeamModal() {
  inviteModalVisible.value = false;
  goCompetitionDetail(COMPETITION_DETAIL_TABS.MY_TEAM);
}

function getConfig() {
  API.competition_model.getTeamConfig({ cid: cid.value }).then((res) => {
    if (res.state === 'OK') {
      teamMaxNum.value = res.body.teamMaxNum;
    } else {
      teamMaxNum.value = '';
    }
  });
}

function changeCurrent(currentStatus) {
  current.value = currentStatus;
  if (currentStatus === COMPETITION_STEPS_TYPE.FINISH) getConfig();
}

function changeCompleteSuccess(successObject) {
  Object.assign(successStatusObject, successObject);
}
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.section-result {
  display: flex;
  justify-content: center;

  // 参考蓝湖 修改a-success样式
  :deep(.ant-result) {
    width: 601px;
    padding: 16px 32px 48px;
  }
  :deep(.ant-result-extra) {
    margin-top: 0;
    .tips {
      color: @jt-error-color;
      margin-bottom: 32px;
    }
  }

  :deep(.anticon-check-circle) {
    font-size: 48px;
  }

  :deep(.ant-result-success .ant-result-icon > .anticon) {
    color: #17bb85;
  }
  :deep(.ant-result-icon) {
    margin-bottom: 16px;
  }
  :deep(.ant-result-title) {
    height: 28px;
    font-size: @jt-font-size-lger;
    color: #031129;
    line-height: 28px;
    margin-bottom: 10px;
  }
  :deep(.ant-btn) {
    width: 126px;
    height: @jt-btn-height-base;
  }
  .success-text {
    color: @jt-text-color;
    &:nth-last-child(1) {
      margin-bottom: 10px;
    }
  }
}

// 步骤条
.section-steps {
  display: flex;
  justify-content: center;
  margin: 48px 0;
  :deep(.ant-steps) {
    width: 392px;
  }
}
.section-steps-mobile-cloud {
  display: flex;
  justify-content: center;
  margin: 48px 86px;
}
</style>
