// antd4.0版本以上，样式定义，参考：https://www.antdv.com/docs/vue/customize-theme-cn#seedtoken
const theme = {
  token: {
    colorPrimary: '#0082ff',
    colorError: '#FF454D',
    colorErrorText: '#FF454D',
    borderRadiusSM: 2,
    borderRadius: 2,
  },
  // components: {
  //   Table: {
  //     colorTextHeading: '#00141A',
  //     fontWeightStrong: '400', // 取消table头的加粗效果
  //   },
  //   Tabs: {
  //     fontSize: '16px',
  //     colorSplit: 'rgba(0,20,26,0.08)',
  //     borderRadius: '2px',
  //   },
  //   Radio: {
  //     borderRadius: '2px',
  //   },
  //   Button: {
  //     borderRadius: '2px',
  //   },
  //   Input: {
  //     borderRadius: '2px',
  //   },
  //   Pagination: {
  //     borderRadius: '2px',
  //   },
  //   Select: {
  //     borderRadius: '2px',
  //     borderRadiusLG: '2px',
  //   },
  //   Notification: {
  //     borderRadiusLG: '4px',
  //     colorTextHeading: '#00141A',
  //   },
  //   Message: {
  //     borderRadiusLG: '2px',
  //   },
  //   Dropdown: {
  //     borderRadiusLG: '2px',
  //   },
  //   Modal: {
  //     borderRadiusLG: '4px',
  //   }
  // },

}

export default theme;