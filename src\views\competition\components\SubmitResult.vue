<template>
  <div class="competition-result">
    <a-radio-group v-model:value="selectRadio" class="radio-group" @change="radioChange">
      <a-radio-button :value="SUBMIT_RESULT_MAPS.SUBMIT_FILES">提交文件</a-radio-button>
      <a-radio-button :value="SUBMIT_RESULT_MAPS.SUBMIT_RECORD">提交记录</a-radio-button>
    </a-radio-group>
    <!-- 提交结果文件 -->
    <submit-file v-if="selectRadio === SUBMIT_RESULT_MAPS.SUBMIT_FILES" :title="resultTitle" :disabled="resultDisabled" :message="resultMessage" :mobile-cloud-tip="ecloudResultTip" :submit-require="result.narrate" :result-picture-path="result.resultpicturepath" :filetype="'1'" :file-submit-type="resultSubmitType" :is-mobile-cloud="isMobileCloud" :is-leader="isLeader" :competition-id="competitionId" @switchToMyteamTab="switchToMyteamTab" @uploadModalOk="uploadModalOk" />
    <!-- 提交答辩材料 -->
    <submit-file v-if="selectRadio === SUBMIT_RESULT_MAPS.SUBMIT_FILES && replyShow" :title="replyTitle" :disabled="replyDisabled" :message="replyMessage" :mobile-cloud-tip="ecloudReplyTip" :submit-require="reply.narrate" :result-picture-path="null" :filetype="'0'" :file-submit-type="replySubmitType" :is-mobile-cloud="isMobileCloud" :is-leader="isLeader" :competition-id="competitionId" @switchToMyteamTab="switchToMyteamTab" @uploadModalOk="uploadModalOk" />
    <!-- 提交记录 -->
    <submit-record v-if="selectRadio === SUBMIT_RESULT_MAPS.SUBMIT_RECORD" :team-have="teamHave" :show-button="showButton" :update-begin="updateBegin" :type-id="typeId" />
  </div>
</template>

<script>
import SubmitFile from './SubmitFile.vue';
import SubmitRecord from './SubmitRecord.vue';
import { handleCopy } from '@/utils/utils';
import API from '@/constants/api/API.js';
import { COMPETITION_DETAIL_TABS, SUBMIT_RESULT_MAPS, SUBMIT_RESULT_FILES_STATE } from '../competitionConfig/index';
import { ECLOUD_COMPETITION_TYPE_ID_LIST } from '@/common/ecloud';
export default {
  components: {
    SubmitFile,
    SubmitRecord,
  },
  props: {
    isMobileCloud: Boolean,
    typeId: Number,
    activeKey: String,
  },
  emits: ['tabChange'],
  data() {
    return {
      SUBMIT_RESULT_MAPS,
      competitionId: this.$route.query.id,
      selectRadio: SUBMIT_RESULT_MAPS.SUBMIT_FILES,
      fileSubmitType: '1',
      isLeader: false,
      ecloudResultTip: '',
      ecloudReplyTip: '',
      filetype: undefined, // 1 点击结果上传按钮 0 附件上传
      resultTitle: '提交结果文件',
      replyTitle: '提交审查及答辩材料',
      resultMessage: '',
      replyMessage: '',
      teamHave: true,
      submitNum: 0,
      submitTotal: 0,
      resultSubmitType: '1',
      replySubmitType: '1',
      updateBegin: false,
      result: {
        narrate: '', //提交要求
        resultpicturepath: '', //提交要求照片路径
      },
      showButton: true, //提交及审查是否显示
      reply: {
        narrate: '', //提交要求
      },
    };
  },
  computed: {
    replyShow() {
      return this.showButton || ((this.typeId == 4 || this.typeId != 5) && this.isLeader && this.showButton);
    },
    resultDisabled() {
      return !this.isLeader || this.submitNum >= this.submitTotal;
    },
    replyDisabled() {
      return !this.isLeader || !this.showButton;
    },
  },
  watch: {
    activeKey(val) {
      if (val === COMPETITION_DETAIL_TABS.SUBMIT_RESULT) {
        this.initData();
      }
    },
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.getTeamState();
      this.getMessageAndSubmitType();
      this.getReplyButtonShow();
      this.getSubmitRequire();
      this.getMobileCloudOrder();
    },
    radioChange(event) {
      const v = event.target.value;
      this.getTeamState();
      if (v == SUBMIT_RESULT_MAPS.SUBMIT_FILES) this.getMessageAndSubmitType();
      this.selectRadio = v;
    },
    openJoinTeamModal() {
      this.modalForm.joinTeam.visible = true;
    },
    getTeamState() {
      API.competition_model.getByMyTeam({ cid: this.competitionId }).then((res) => {
        this.teamHave = res.state === 'OK' ? true : false;
      });
    },
    handleReplyMsg(i) {
      const status = {
        1: '答辩材料提交入口未开放',
        4: '需创建自己的团队或加入已有的团队后，才可提交答辩材料',
        5: '审查及答辩材料需由队长统一提交',
        6: '可以提交答辩材料',
      };
      return status[i];
    },
    getMessageAndSubmitType() {
      // 结果文件温馨提示
      API.competition_model.getCompetitionResultMsg({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          let { resultSta, resultValue, resultValuetwo } = res.body;
          this.isLeader = false;

          switch (resultSta) {
            case SUBMIT_RESULT_FILES_STATE.NOT_OPEN:
              this.resultMessage = '结果文件提交入口未开放';
              this.isLeader = true;
              break;
            case SUBMIT_RESULT_FILES_STATE.MAX_LIMIT:
              this.resultMessage = `您每天最多可提交${Number(resultValue)}次，今日提交次数已达上限`;
              this.submitTotal = Number(resultValue);
              this.submitNum = Number(resultValue);
              this.isLeader = true;
              break;
            case SUBMIT_RESULT_FILES_STATE.SUBMIT_SEVERAL_TIMES:
              this.resultMessage = `您每天最多可提交${Number(resultValue)}次，今日已提交${Number(resultValuetwo)}次`;
              this.submitTotal = Number(resultValue);
              this.submitNum = Number(resultValuetwo);
              this.isLeader = true;
              break;
            case SUBMIT_RESULT_FILES_STATE.CREATE_TEAM_SUBMIT:
              this.resultMessage = '需创建自己的团队或加入已有的团队后，才可提交结果文件';
              break;
            case SUBMIT_RESULT_FILES_STATE.LEADER_SUBMIT:
              this.resultMessage = '结果文件需由队长统一提交';
              break;
            default:
              break;
          }
        }
      });
      // 答辩材料温馨提示
      API.competition_model.getCompetitionReplyMsg({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.replyMessage = this.handleReplyMsg(Number(res.body.replySta));
          if (this.replyMessage === '可以提交答辩材料') this.replyMessage = '';
        }
      });
      // 获取比赛结果文件/答辩材料 上传方式(1是本地跟团队共享，2是本地，3是团队共享)
      API.competition_model.getCompetitionSubmitType({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          const { submitType, replySubmitType } = res.body;
          this.resultSubmitType = String(submitType);
          this.replySubmitType = String(replySubmitType);
        }
      });
    },
    getMobileCloudOrder() {
      if (!ECLOUD_COMPETITION_TYPE_ID_LIST.includes(this.typeId)) return;
      // 结果文件一键复制命令
      API.competition_model.getCompetitionMobileCloudOrder({ cid: this.competitionId, type: 1 }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.ecloudResultTip = res.body;
        }
      });
      // 答辩材料一键复制命令
      API.competition_model.getCompetitionMobileCloudOrder({ cid: this.competitionId, type: 0 }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.ecloudReplyTip = res.body;
        }
      });
    },
    clickCopy(text) {
      handleCopy(text);
      this.$message.success('复制成功');
    },
    // 提交要求 & 图片 & 附件下载地址
    getSubmitRequire() {
      if (!this.teamHave) return;
      API.competition_model.getCompetitionResultRequire({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          const { resultpicturepath, resultnarrate, replynarrate } = res.body;
          this.result.resultpicturepath = resultpicturepath || '';
          if (resultnarrate) {
            //请求下来的是富文本，没有数据状态
            API.competition_model.getCompetitionDescInfo(resultnarrate).then((narrate) => {
              this.result.narrate = narrate;
            });
          }
          if (replynarrate) {
            API.competition_model.getCompetitionDescInfo(replynarrate).then((narrate) => {
              this.reply.narrate = narrate;
            });
          }
        }
      });
    },
    getReplyButtonShow() {
      API.competition_model.getCompetitionShowButton({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK') {
          this.showButton = res.body;
        }
      });
    },
    uploadModalOk() {
      this.radioChange({ target: { value: 2 } });
      this.updateBegin = true;
    },
    //跳转到我的团队tab
    switchToMyteamTab() {
      this.$emit('tabChange', COMPETITION_DETAIL_TABS.MY_TEAM);
    },
  },
};
</script>

<style lang="less" scoped>
.competition-result {
  .radio-group {
    margin-top: 24px;
  }
}
</style>
