<template>
  <div>
    <div class="edit-header">{{ isEdit ? '编辑' : '预览' }}常见问题</div>
    <div class="edit-content">
      <a-spin :spinning="loading">
        <richtextEditor v-show="isEdit" id="competition-commonquestion-editor" v-model:value="commonQuestionText" :url="commonQuestionsInfo.question" @change="getRichtextEditor" />
        <jt-common-content v-if="!isEdit" :empty="!commonQuestionText" empty-title="您暂未设置常见问题">
          <div class="cpt-explain markdown-body" v-html="commonQuestionText"></div>
        </jt-common-content>
        <a-space style="margin-top: 32px">
          <a-button type="primary" style="width: 120px" @click="handleOk">{{ isEdit ? '预览' : hasPublished ? '保存并发布' : '保存' }}</a-button>
          <a-button style="width: 88px" @click="handleCancel">{{ isEdit ? '取消' : '返回编辑' }}</a-button>
        </a-space>
      </a-spin>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { publishStatusKeys } from '../../../competitionConfig';
import richtextEditor from '@/components/richtextEditor.vue';
import { competitionApi } from '@/apis/index';
export default {
  name: 'CommonQuestionsEdit',
  components: {
    richtextEditor,
  },
  data() {
    return {
      commonQuestionText: '',
      commonQuestionsInfo: {},
      isEdit: true,
      loading: false,
    };
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
    hasPublished() {
      return this.currentManageCompetition.releaseSta === publishStatusKeys.PUBLISHED;
    },
  },
  mounted() {
    this.initCommonQuestionIntro();
  },
  methods: {
    async initCommonQuestionIntro() {
      this.loading = true;
      const cid = this.$route.params.competitionId;
      const res = await competitionApi.getCompetitionCommonQuestion({ cid });
      if (res.state === 'OK' && res.body && res.body.length > 0) {
        this.commonQuestionsInfo = res.body[0];
      }
      this.loading = false;
    },
    getRichtextEditor(val) {
      this.commonQuestionText = val;
    },
    handleOk() {
      if (this.isEdit) {
        this.isEdit = false;
      } else {
        this.publish();
      }
    },
    async publish() {
      this.loading = true;
      let path = '';
      if (this.commonQuestionText) {
        const textUploadRes = await competitionApi.textUpload({ baseString: this.commonQuestionText });
        if (textUploadRes.state === 'OK') {
          path = textUploadRes.body.url;
        } else {
          this.loading = false;
          this.$message.error('编辑常见问题失败');
          return;
        }
      }
      const updateRes = await competitionApi.updateCompetitionRefData({
        cid: this.$route.params.competitionId,
        type: 2,
        path,
      });
      if (updateRes.state === 'OK') {
        this.$message.success('编辑常见问题成功');
        this.backToTab(true);
      } else {
        this.$message.error('编辑常见问题失败');
      }
      this.loading = false;
    },
    handleCancel() {
      if (this.isEdit) {
        this.backToTab();
      } else {
        this.isEdit = true;
      }
    },
    backToTab(noLeaveConfirm) {
      const { competitionId } = this.$route.params;
      this.$router.push({
        path: `/competition/competition-management/${competitionId}`,
        query: {
          tabId: '2',
          subtabId: '4',
          noLeaveConfirm,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.edit-header {
  .competition-edit-header();
}
.edit-content {
  padding: 32px 32px 48px;
  .cpt-explain {
    color: #606972;
    white-space: pre-line;
  }
}
</style>
