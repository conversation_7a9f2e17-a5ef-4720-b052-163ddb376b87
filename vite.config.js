import { defineConfig } from 'vite';
const path = require('path');
import vue from '@vitejs/plugin-vue';
const fs = require('fs');
const lessToJs = require('less-vars-to-js');
const themeVariables = lessToJs(fs.readFileSync(path.join(__dirname, './src/assets/styles/theme/theme.less'), 'utf8'));
import requireTransform from 'vite-plugin-require-transform';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { createHtmlPlugin } from 'vite-plugin-html';
import AutoImport from 'unplugin-auto-import/vite';
import ViteRequireContext from '@originjs/vite-plugin-require-context';
const proxyTable = require('./proxy-table');

export default defineConfig({
  define: {
    'process.env': {
      PROXY_ENV: process.env.PROXY_ENV,
      TARGET_HOST: process.env.TARGET_HOST,
    },
  },
  resolve: {
    alias: { '@': path.resolve(__dirname, 'src'), '~@': path.resolve(__dirname, 'src') },
    extensions: ['.vue', '.js', '.jsx'],
  },
  plugins: [
    vue(),
    createHtmlPlugin({
      minify: true,
      /**
       * 在这里写entry后，你将不需要在`index.html`内添加 script 标签，原有标签需要删除
       * @default
       */
      entry: '/src/main.js',
      /**
       * 如果你想将 `index.html`存放在指定文件夹，可以修改它，否则不需要配置
       * @default index.html
       */
      template: 'public/index.html',
    }),
    requireTransform({
      fileRegex: /.js$|.vue$/,
    }),
    ViteRequireContext(),
    vueJsx(),
    AutoImport({
      imports: ['vue'],
      eslintrc: {
        enabled: true,
      },
      dts: false,
    }),
  ],
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: themeVariables,
        javascriptEnabled: true,
      },
    },
  },
  assetsInclude: ['**/*.jpg', '**/*.JPG', '**/*.jpeg', '**/*.png', '**/*.svg'],
  // 生产环境
  build: {
    terserOptions: {
      // 生产环境移除console
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  server: {
    host: true,
    proxy: {
      ...proxyTable,
    },
  },
});
