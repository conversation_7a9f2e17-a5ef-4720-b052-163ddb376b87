<template>
  <div>
    <div class="chrastic-intro">
      <h1 class="head-title">{{ chrastic.title }}</h1>
      <div class="content-box">
        <div class="secure-rely">
          <span>{{ chrastic.list[0].title }}</span>
          <a-tooltip title="请发送邮件至：<EMAIL>">
            <div>立即咨询</div>
          </a-tooltip>
        </div>
        <div class="elastic-resource">
          <span>{{ chrastic.list[1].title }}</span>
          <a-tooltip title="请发送邮件至：<EMAIL>">
            <div>立即咨询</div>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { chrastic } from './config.js';
export default {
  name: 'chrasticIntro',
  data() {
    return {
      chrastic,
    };
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.chrastic-intro {
  height: 325px;
  width: 1200px;
  margin: 0px auto;
  .head-title {
    margin-top: 48px;
    margin-bottom: 30px;
    color: #121f2c;
    font-size: 32px;
    text-align: center;
  }
  .content-box {
    display: flex;
    justify-content: space-between;
    .secure-rely,
    .elastic-resource {
      width: 590px;
      height: 154px;
      border-radius: 2px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-weight: @jt-font-weight-medium;
        color: #ffffff;
        font-size: 24px;
        margin-left: 121px;
      }
      div {
        margin-right: 32px;
        width: 128px;
        height: 40px;
        border-radius: 2px;
        border: 1px solid #ffffff;
        transition: 0.3s all;
        color: #ffffff;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:hover {
          background-color: #ffffff;
          color: #178cf9;
        }
      }
    }
    .secure-rely {
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-image: url('~@/assets/image/teaching/ai-bac1.png');
    }
    .elastic-resource {
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-image: url('~@/assets/image/teaching/ai-bac2.png');
    }
  }
}
</style>
