<template>
  <div>
    <a-modal v-model:open="open" title="" :footer="null" :centered="true" :dialog-style="dialogStyle" :body-style="bodyStyle" :get-container="container" @cancel="$emit('cancel')">
      <div>
        <div class="main-content">
          <div class="title">我的成绩</div>
          <div class="content-container">
            <div class="score">
              <p class="score-content">
                <a-space>
                  <span class="score-num">{{ score }}</span>
                  <span class="score-unit">分</span>
                </a-space>
              </p>
            </div>
          </div>
        </div>
        <div class="remark">
          <p style="word-break: break-all">评语：{{ remark || '- -' }}</p>
        </div>
      </div>
    </a-modal>
    <!-- 这里是为了让三张图片提前加载出来，提升用户体验 -->
    <img v-show="false" :src="goodPng" alt="" />
    <img v-show="false" :src="badPng" alt="" />
    <img v-show="false" :src="normalPng" alt="" />
  </div>
</template>

<script>
export default {
  props: {
    visible: { type: Boolean, default: false },
    score: { type: [Number, String], default: '' },
    remark: { type: String, default: '' },
    level: { type: String, default: '' },
  },
  emits: ['cancel'],
  data() {
    return {
      open: false,
      dialogStyle: {
        width: '480px !important',
        height: '200px',
        background: '',
        backgroundSize: 'cover',
        'border-radius': '4px 4px 0px 0px',
      },
      bodyStyle: {
        background: 'transparent',
        padding: '0',
      },
      goodPng: require('@/assets/image/course/bg-good.png'),
      badPng: require('@/assets/image/course/bg-bad.png'),
      normalPng: require('@/assets/image/course/bg-normal.png'),
    };
  },
  computed: {
    container() {
      return () => document.querySelector('#jupyter-viewer');
    },
  },
  watch: {
    visible(val) {
      this.open = val;
      if (val) {
        this.dialogStyle.background = `url(${this.getBackgroundImage()}) no-repeat`;
      }
    },
  },
  created() {
    this.open = this.visible;
  },
  methods: {
    getBackgroundImage() {
      const level = this.level;
      const pngName = `bg-${level}.png`;
      return require(`@/assets/image/course/${pngName}`);
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.ant-modal-content) {
  background: transparent;
  height: 200px;
}
:deep(.ant-modal) {
  width: 480px !important;
}
.main-content {
  height: 200px;
  .title {
    padding: 16px 24px;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
  }
  .score {
    display: flex;
    justify-content: center;
    .score-content {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 108px;
      height: 108px;
      // box-shadow: 2px 4px 4px 0px rgba(0, 0, 0, 0.2);
      // border: 2px solid #fff;
      // border-radius: 50%;
    }
    .score-num {
      font-size: 32px;
      font-weight: 600;
      color: #ffffff;
    }
    .score-unit {
      position: relative;
      top: 6px;
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
.remark {
  display: flex;
  align-items: center;
  // height: 60px;
  padding: 16px;
  font-size: 12px;
  font-weight: 400;
  background: #fff;
  border-radius: 0 0 4px 4px;
}
:deep(.ant-modal-close-icon) {
  color: #fff;
}
:deep(.ant-modal-centered .ant-modal) {
  vertical-align: inherit;
}
</style>
