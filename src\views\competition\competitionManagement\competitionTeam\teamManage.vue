<template>
  <div class="competition-user-manage">
    <div class="user-manage-operation">
      <a-space>
        <jt-search v-model="keywords" placeholder="团队名称/队长姓名/队员姓名" @handSearch="handleSearch"></jt-search>
        <a-button :disabled="tableEmpty" type="primary" ghost @click="handleDownload"><jt-icon type="icondaochu2"></jt-icon>导出</a-button>
      </a-space>
    </div>
    <a-config-provider>
      <template #renderEmpty>
        <jt-common-content :empty="true" :loading="loading" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text"> </jt-common-content>
      </template>
      <a-table :loading="tableData.length > 0 ? loading : undefined" row-key="teamName" :data-source="tableData" :columns="columns" :pagination="false" size="middle" @change="handleTableChange"> </a-table>
    </a-config-provider>
    <jt-pagination v-if="!tableEmpty" :page-size="pagination.pageSize" :page-num="pagination.pageNum" :total="pagination.total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
  </div>
</template>

<script>
import jtSearch from '@/components/search/index.vue';
import { signStatusMaps } from '../../competitionConfig/index';
import { competitionApi } from '@/apis/index';
import { downloadFileWithToken } from '@/utils/file';
export default {
  name: 'UserManage',
  components: { jtSearch },
  data() {
    return {
      signStatusMaps,
      columns: [
        {
          key: 'teamName',
          title: '团队名称',
          dataIndex: 'teamName',
          ellipsis: true,
          width: '12%',
        },
        {
          key: 'teamNum',
          title: '团队人数',
          dataIndex: 'teamNum',
          width: '8%',
        },
        {
          key: 'teamLeaderUserName',
          title: '队长用户名',
          dataIndex: 'teamLeaderUserName',
          ellipsis: true,
          width: '15%',
        },
        {
          key: 'teamLeaderFullName',
          title: '队长姓名',
          dataIndex: 'teamLeaderFullName',
          ellipsis: true,
          width: '18%',
        },
        {
          key: 'teamLeaderPhone',
          title: '队长手机号',
          dataIndex: 'teamLeaderPhone',
          width: '15%',
        },
        {
          key: 'members',
          title: '队员姓名',
          dataIndex: 'members',
          customRender(text) {
            return text || '--';
          },
          ellipsis: true,
          width: '18%',
        },
        {
          key: 'createTime',
          title: '创建时间',
          dataIndex: 'createTime',
          width: '15%',
          sorter: true,
        },
      ],
      tableData: [],
      loading: false,
      teamSta: '',
      createTimeFlag: 0,
      keywords: '',
      pagination: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    tableEmpty() {
      return this.tableData.length === 0;
    },
    emptyStatus() {
      if (this.keywords) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关团队',
          text: '您可以换一个关键词试试哦～',
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '暂无团队',
          text: '',
        };
      }
    },
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    handleSearch(val) {
      this.keywords = val;
      this.getTableData();
    },
    handleDownload() {
      downloadFileWithToken({ url: `/competiton/web/manage/edit/team/export?cid=${this.$route.params.competitionId}` });
    },
    async getTableData() {
      const params = {
        cid: this.$route.params.competitionId,
        keyWord: this.keywords,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        createTimeFlag: this.createTimeFlag,
      };
      this.loading = true;
      const res = await competitionApi.getTeamManageList(params);
      if (res.state === 'OK') {
        this.pagination.total = res.body.total;
        this.tableData = res.body.list;
      } else {
        this.pagination.total = 0;
        this.tableData = [];
      }
      this.loading = false;
    },
    handleTableChange(page, filter, sort) {
      this.createTimeFlag = sort.order == 'ascend' ? 1 : 0;
      if (!sort.order) {
        this.createTimeFlag = 0;
      }
      this.getTableData();
    },
    pageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.pagination.pageNum = 1;
      this.getTableData();
    },
    pageNumChange(pageNum) {
      this.pagination.pageNum = pageNum;
      this.getTableData();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.competition-user-manage {
  .user-manage-operation {
    position: absolute;
    top: 0px;
    right: 32px;
  }
  .common-middile-table-style();
}
</style>
