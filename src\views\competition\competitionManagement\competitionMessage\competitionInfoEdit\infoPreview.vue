<template>
  <div class="competition-info-preview">
    <a-spin :spinning="spinning">
      <headTitle title="比赛列表展示效果" />
      <div class="preview-top">
        <div class="head-left">
          <img :src="previewData.imageUrl" alt="比赛封面" />
        </div>
        <div class="head-right">
          <div class="right-top">
            <p>
              <span class="competition-name">{{ previewData.typeName }}</span>
              <jt-tag v-if="!isCreateCompetition" class="competition-tag" :type="getCompetitionState(currentManageCompetition.flag)">{{ runningStatusMaps[currentManageCompetition.flag] }}</jt-tag>
              <jt-tag class="competition-tag">{{ competitionTypeMaps[previewData.competitionType] }}</jt-tag>
            </p>
            <div class="competition-prize">奖池￥{{ handlePrize(previewData.amount) }}</div>
          </div>
          <div class="right-bottom">
            <p>{{ previewData.summary }}</p>
            <div>
              <span
                >标签：<span v-for="(tag, i) in previewData.tag" :key="i">{{ tag }}{{ i == previewData.tag.length - 1 ? '' : '，' }}</span></span
              >
              <span
                >时间：<span>{{ dateConvert(previewData.startTime) }} - {{ dateConvert(previewData.endTime) }}</span></span
              >

              <span>
                举办方：
                <img v-for="url in previewData.leader" :key="url" :src="url" alt="比赛logo" />
              </span>
            </div>
          </div>
        </div>
      </div>
      <headTitle v-if="previewData.banner" title="比赛详情页宣传图展示效果" />
      <div v-if="previewData.banner" class="preview-bottom">
        <img :src="previewData.banner" alt="宣传图" />
      </div>
      <a-space class="form-btns">
        <!--按钮文案～ 创建比赛：创建；编辑未发布比赛：保存 -->
        <a-button type="primary" style="width: 120px" @click="saveAndPublish">{{ isCreateCompetition ? '创建' : hasPublished ? '保存并发布' : '保存' }} </a-button>
        <a-button style="width: 88px" @click="handleBack">上一步</a-button>
      </a-space>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import headTitle from '@/components/headTitle';
import JtTag from '@/components/tag';
import API from '@/constants/api/API.js';
import { competitionApi } from '@/apis/index.js';
import { dateConvert, handlePrize, dateStartTime, dateEndTime } from '@/utils/utils';
import { competitionTypeMaps, runningStatusMaps, runningStatusKeys, publishStatusKeys } from '../../../competitionConfig';

const props = defineProps({
  previewData: {
    type: Object,
    required: true,
  },
  isCreateCompetition: Boolean,
});
const emit = defineEmits(['changeCurrent']);

const store = useStore();
const route = useRoute();
const spinning = ref(false);

const currentManageCompetition = computed(() => store.state.competition.currentManageCompetition);

const hasPublished = computed(() => {
  return currentManageCompetition.value.releaseSta === publishStatusKeys.PUBLISHED;
});

function getCompetitionState(flag) {
  if (flag === runningStatusKeys.STARTING) {
    return 'tobegin';
  } else if (flag === runningStatusKeys.RUNNING) {
    return 'running';
  } else {
    return 'end';
  }
}

function saveAndPublish() {
  let startTime = dateStartTime(props.previewData.startTime);
  let endTime = dateEndTime(props.previewData.endTime);
  spinning.value = true;

  let leader = [];
  for (let i = 0; i < props.previewData.leader.length; i++) {
    leader.push({ imageUrl: props.previewData.leader[i] });
  }
  let requestParams = {
    amount: props.previewData.amount,
    banner: props.previewData.banner,
    competitionName: props.previewData.typeName,
    competitionTraining: props.previewData.competitionTraining,
    competitionType: props.previewData.competitionType,
    endTime,
    startTime,
    imageUrl: props.previewData.imageUrl,
    leader: leader,
    summary: props.previewData.summary,
    tag: props.previewData.tag,
  };
  if (props.isCreateCompetition) {
    createCompetition(requestParams);
  } else {
    updateCompetitionInfo({ ...requestParams, cid: route.params.competitionId });
  }
}

function updateCompetitionInfo(params) {
  API.competition_model.getCompetitionEditBasicInfo(params).then((res) => {
    if (res.state === 'OK' && res.body && res.body === 'success') {
      const { competitionId } = route.params;
      competitionApi.getCompetitionDetail({ cid: competitionId }).then((res1) => {
        if (res1.state === 'OK') {
          store.commit('competition/SET_CURRENTMANAGECOMPETITION', res1.body);
        }
        emit('changeCurrent', 1);
        spinning.value = false;
      });
    } else {
      spinning.value = false;
      getCurrentInstance().proxy.$message.error(`编辑比赛基本信息失败`);
    }
  });
}

function createCompetition(params) {
  competitionApi.createCompetition(params).then((res) => {
    if (res.state === 'OK') {
      competitionApi.getCompetitionDetail({ cid: res.body }).then((res1) => {
        if (res1.state === 'OK') {
          store.commit('competition/SET_CURRENTMANAGECOMPETITION', res1.body);
        }
        emit('changeCurrent', 1);
        spinning.value = false;
      });
    } else {
      spinning.value = false;
      getCurrentInstance().proxy.$message.error('创建比赛失败');
    }
  });
}

function handleBack() {
  emit('changeCurrent', -1);
}
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.competition-info-preview {
  width: 1136px;
  position: relative;
  left: -72px;
}
.preview-top {
  width: 100%;
  margin: 24px 0 40px;
  display: flex;
  justify-content: space-between;
  .head-left {
    img {
      width: 128px;
      height: 128px;
    }
  }
  .head-right {
    width: 984px;
    img {
      height: 23px;
      margin-right: 16px;
    }
    .right-top {
      display: flex;
      justify-content: space-between;
      .competition-name {
        margin-right: 8px;
        font-size: 20px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
      }
      .competition-tag {
        margin-right: 8px;
      }
      .competition-prize {
        font-size: 24px;
        font-weight: @jt-font-weight-medium;
        color: #ff4945;
        white-space: nowrap;
      }
    }
    .right-bottom {
      p {
        color: #606972;
        line-height: 22px;
        margin: 8px 0 16px;
      }
      & > div:nth-of-type(1) {
        & > span {
          margin-right: 24px;
          color: #a0a6ab;
          span {
            color: rgb(96, 105, 114);
          }
        }
      }
    }
  }
}
.preview-bottom {
  img {
    width: 1136px;
    height: 378px;
    margin: 24px 0 48px;
  }
}
</style>
