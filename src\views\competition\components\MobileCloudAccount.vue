<template>
  <jt-common-content :loading="loading">
    <div class="section-form">
      <div class="top-tips-text">
        <p>
          {{ ECLOUD_COMPETITION_REGISTER_TEXT_STATE[currentState].tips }}
          <!-- 未认证、失败显示 -->
          <a v-if="isUnAuditing || isFailed" target="_blank" rel="noopener noreferrer" style="color: #0082ff" @click="goMobileCloudRealName">实名认证</a>
        </p>
        <!-- 未认证显示 -->
        <p v-if="isAuditing">
          {{ ECLOUD_COMPETITION_REGISTER_TEXT_STATE[currentState].secondTips }}
        </p>
      </div>
      <!-- 未注册 -->
      <a-form v-if="isUnRegister" ref="form" :colon="false" :rules="rules" :label-col="{ span: 6, offset: 1 }" :wrapper-col="wrapperCol" :model="ecloudFormData">
        <a-form-item label="注册手机号" name="phone">
          <a-input v-model:value="ecloudFormData.phone" class="phoneNum-input" placeholder="请输入手机号" disabled style="width: 260px" />
        </a-form-item>

        <a-form-item
          class="phoneNum-item"
          label="短信验证码"
          :rules="[
            {
              required: true,
              message: '',
            },
          ]"
        >
          <a-space>
            <a-form-item name="code">
              <a-input v-model:value="ecloudFormData.code" placeholder="请输入验证码" style="width: 260px" />
            </a-form-item>
            <a-form-item>
              <a-button :disabled="timmer > 0" :class="{ 'input-disabled': timmer > 0 }" @click="handleSendCode">{{ timmer > 0 ? `重新获取 ${timmer}` : `${sended ? '重新获取' : '获取验证码'}` }}</a-button>
            </a-form-item>
          </a-space>
          <p class="code-tips">您将收到【移动云】发送的短信验证码，用于为您注册移动云账号</p>
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 17, offset: 7 }">
          <a-button type="primary" :disabled="isBtnLoading" @click="unregisteredNext">下一步</a-button>

          <a-button style="margin-left: 10px" @click="unregisteredUp"> 上一步 </a-button>
        </a-form-item>
      </a-form>
      <!-- 已认证、审核中... -->
      <div v-if="!isUnRegister">
        <div class="reg-success">
          <ul class="user-information-list">
            <li><span class="title">移动云用户名:</span>{{ checkTextEmpty(ecloudFormData.ecloudUserName) }}</li>
            <li><span class="title">手机号:</span>{{ checkTextEmpty(ecloudFormData.phone) }}</li>
            <li>
              <span class="title">实名认证:</span><strong class="status" :class="getStatusClass">{{ checkTextEmpty(getStatusText) }}</strong>
            </li>
          </ul>
        </div>

        <div class="handle-btn">
          <a-button type="primary" @click="goMobileCloudRealName"> {{ ECLOUD_COMPETITION_REGISTER_TEXT_STATE[currentState].upStep }} </a-button>
          <a-button v-if="!isAuditing" style="margin-left: 10px" @click="goCompetitionDetail"> {{ ECLOUD_COMPETITION_REGISTER_TEXT_STATE[currentState].nextStep }} </a-button>
        </div>
      </div>

      <confirm-modal v-model="visible" title="移动云账号实名认证" ok-text="已提交认证" cancel-text="稍后认证" @cancel="goCompetitionDetail" @ok="handleModalOk">
        <template #icon>
          <ExclamationCircleFilled class="invited-icon" style="color: #0082ff" />
        </template>
        <div class="popup-title">请确认您已提交移动云账号实名认证</div>
        <div class="popup-title" style="margin-bottom: 18px">您需要完成实名认证及深度学习平台订购，才能参加本次比赛哟~</div>
      </confirm-modal>
    </div>
  </jt-common-content>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import API from '@/constants/api/API.js';
import { sendSmsCodeMolileCloud, openInNewTab, addUrlParams, checkTextEmpty } from '@/utils/utils';
import confirmModal from '@/components/confirmModal';
import { telephoneNumberRegex, vetifyCodeRegex } from '@/utils/regex';
import { ECLOUD_URL_CONFIG, ECLOUD_URL, ECLOUD_COMPETITION_REGISTER_TEXT_STATE, ECLOUD_COMPETITION_AUDIT_STATE_CLASS, ECLOUD_COMPETITION_AUDIT_STATE_TEXT, ECLOUD_COMPETITION_AUDIT_STATE } from '@/common/ecloud';
import { ECLOUD_COMPETITION_STEPS_TYPE, ECLOUD_COMPETITION_JOIN_TYPE } from '@/views/competition/competitionConfig/index';

const route = useRoute();
const router = useRouter();
const emit = defineEmits(['changeCurrent']);

const VERIFIED_ECLOUD_URL = `${ECLOUD_URL}/api/page/op-usercenter-static/#/securitysetting/verified`;

const ECLOUD_COMPETITION_REGISTER_TEXT_STATE_REF = ECLOUD_COMPETITION_REGISTER_TEXT_STATE;
const ECLOUD_COMPETITION_AUDIT_STATE_REF = ECLOUD_COMPETITION_AUDIT_STATE;

const cid = ref(route.query.id);
const cname = ref(route.query.name);
const ecloudFormData = reactive({
  phone: '',
  code: '',
});
const registerEcloudInfo = reactive({
  smsCaptcha: '',
  telephone: '',
});
const timmer = ref(0);
const sended = ref(false);
const wrapperCol = reactive({ span: 12 });
const currentState = ref(ECLOUD_COMPETITION_AUDIT_STATE.UNREGISTER);
const visible = ref(false);
const isEcloudRegister = ref(false);
const isBtnLoading = ref(true);
const loading = ref(false);
const form = ref();

const isAuditing = computed(() => currentState.value === ECLOUD_COMPETITION_AUDIT_STATE.AUDITING);
const isPass = computed(() => currentState.value === ECLOUD_COMPETITION_AUDIT_STATE.PASS);
const isFailed = computed(() => currentState.value === ECLOUD_COMPETITION_AUDIT_STATE.FAILED);
const isUnAuditing = computed(() => currentState.value === ECLOUD_COMPETITION_AUDIT_STATE.UNAUDITING);
const isUnRegister = computed(() => currentState.value === ECLOUD_COMPETITION_AUDIT_STATE.UNREGISTER);
const getStatusClass = computed(() => ECLOUD_COMPETITION_AUDIT_STATE_CLASS[currentState.value]);
const getStatusText = computed(() => ECLOUD_COMPETITION_AUDIT_STATE_TEXT[currentState.value]);
const rules = computed(() => ({
  phone: [
    { required: true, message: '请输入', trigger: ['blur', 'change'] },
    { pattern: telephoneNumberRegex, message: '请输入正确格式手机号', trigger: ['blur', 'change'] },
  ],
  code: [
    { required: true, message: '请输入', trigger: ['blur', 'change'] },
    { pattern: vetifyCodeRegex, message: '请输入正确验证码', trigger: ['blur', 'change'] },
  ],
}));

watch(
  () => ecloudFormData.code,
  (val) => {
    isBtnLoading.value = val.length !== 6;
  }
);

onMounted(() => {
  getEcloudInfo();
});

function goEClound() {
  loading.value = true;
  API.competition_model.ecloudSSOCheck().then((res) => {
    loading.value = false;
    const url = `${ECLOUD_URL}${ECLOUD_URL_CONFIG.ECLOUD_SSO_CHECK_URL}`;
    const urlParams = {
      token: res.body,
      destUrl: window.escape(VERIFIED_ECLOUD_URL),
      systemSource: 'BiSheng',
    };
    const deepLearnUrl = addUrlParams(url, urlParams);
    openInNewTab(deepLearnUrl);
  });
}

function getEcloudInfo(isMessage = false) {
  loading.value = true;
  API.competition_model.getEcloudInfo({ cid: cid.value }).then((res) => {
    if (res.state === 'OK') {
      if (res.body.ecloudRegistSta) {
        const { phone, ecloudUserName, realSta } = res.body;
        ecloudFormData.phone = phone;
        ecloudFormData.ecloudUserName = ecloudUserName;
        ecloudFormData.realSta = realSta;
      } else {
        ecloudFormData.phone = res.body.phone;
        currentState.value = ECLOUD_COMPETITION_AUDIT_STATE.UNREGISTER;
      }
      isEcloudRegister.value = res.body.ecloudRegistSta;
      currentState.value = res.body.realSta;
      if (isMessage) {
        if (isFailed.value) {
          message?.error?.('抱歉，我们检测到您的实名认证状态为审核失败，请再次提交认证') || window.message?.error?.('抱歉，我们检测到您的实名认证状态为审核失败，请再次提交认证');
        }
        if (isUnAuditing.value) {
          message?.error?.('抱歉，我们检测到您的实名认证状态为未认证，请再次提交认证') || window.message?.error?.('抱歉，我们检测到您的实名认证状态为未认证，请再次提交认证');
        }
      }
      visible.value = false;
    } else if (res.state === 'ERROR' && res.errorCode == '-509') {
      message?.error?.('系统繁忙，请稍后重试') || window.message?.error?.('系统繁忙，请稍后重试');
    }
    loading.value = false;
  });
}

function addEcloudInfo() {
  const ecloudJoinRequest = {};
  const { phone, code } = ecloudFormData;
  registerEcloudInfo.telephone = phone;
  registerEcloudInfo.smsCaptcha = code;
  ecloudJoinRequest.ecloudUserInfo = isEcloudRegister.value ? ecloudFormData : registerEcloudInfo;
  ecloudJoinRequest.cid = cid.value;
  ecloudJoinRequest.joinSta = ECLOUD_COMPETITION_JOIN_TYPE.ACCOUNT_OPEN;
  isBtnLoading.value = true;
  loading.value = true;
  API.competition_model.addEcloudInfo(ecloudJoinRequest).then((res) => {
    if (res.state === 'OK') {
      if (isUnRegister.value) {
        currentState.value = ECLOUD_COMPETITION_AUDIT_STATE.UNAUDITING;
        getEcloudInfo();
      }
      if (isPass.value) {
        emit('changeCurrent', ECLOUD_COMPETITION_STEPS_TYPE.DEEPLEARN_SUBSCRIBED);
      }
    } else if (res.state === 'ERROR' && res.errorCode == '-509') {
      message?.error?.('系统繁忙，请稍后重试') || window.message?.error?.('系统繁忙，请稍后重试');
    } else {
      message?.error?.(res.errorMessage) || window.message?.error?.(res.errorMessage);
    }
    isBtnLoading.value = false;
    loading.value = false;
  });
}

function goMobileCloudRealName() {
  if (isFailed.value || isUnAuditing.value) {
    goEClound();
    visible.value = true;
  } else if (isPass.value) {
    addEcloudInfo();
  } else {
    goCompetitionDetail();
  }
}

function handleModalOk() {
  getEcloudInfo(true);
}

function goCompetitionDetail() {
  router.push({
    path: '/competition/competition-detail',
    query: {
      id: cid.value,
      name: cname.value,
    },
  });
}

function unregisteredNext() {
  form.value
    .validate()
    .then(() => {
      addEcloudInfo();
    })
    .catch((err) => {
      throw new Error(err);
    });
}

function unregisteredUp() {
  emit('changeCurrent', ECLOUD_COMPETITION_STEPS_TYPE.REGISTER);
}

function handleSendCode() {
  sendSmsCodeMolileCloud(ecloudFormData.phone).then((res) => {
    if (res.state === 'OK') {
      sended.value = true;
      timmer.value = 60;
      message?.success?.('发送成功') || window.message?.success?.('发送成功');
      timmerDecrease();
    } else if (res.state !== 'OK' && res.errorCode === '-117') {
      message?.error?.('验证码发送过于频繁') || window.message?.error?.('验证码发送过于频繁');
    } else {
      message?.error?.(res.errorMessage || '发送失败') || window.message?.error?.(res.errorMessage || '发送失败');
    }
  });
}

function timmerDecrease() {
  if (timmer.value > 0) {
    timmer.value--;
    setTimeout(() => {
      timmerDecrease();
    }, 1000);
  }
}
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.top-tips-text {
  font-size: @jt-font-size-base;
  text-align: center;
  color: @jt-text-color-primary;
  margin-bottom: 32px;
}
.mgb0 {
  margin-bottom: 0px;
}

.code-tips {
  font-size: @jt-font-size-base;
  color: @jt-text-color-secondary;
  margin-top: 4px;
}

.reg-success {
  width: 434px;
  height: 135px;
  background: #f9fafb;
  border-radius: @jt-border-radius;
  margin: auto;
  padding: 24px 24px 0;

  .user-information-list {
    li {
      margin-bottom: 16px;
      color: @jt-text-color;
    }

    .title {
      display: inline-block;
      min-width: 84px;
      height: 18px;
      line-height: 18px;
      text-align: right;
      font-size: @jt-font-size-base;
      color: @jt-text-color-primary;
      margin-right: 16px;
    }
    .status {
      font-weight: @jt-font-weight;
    }
    .status-notcre {
      color: @jt-error-color;
    }
    .status-complete {
      color: #17bb85;
    }
    .status-auditmiddle {
      color: @jt-warn-color;
    }
  }
}
.handle-btn {
  width: 434px;
  margin: 32px auto 20px;
}

.phoneNum-item {
  :deep(.ant-form-item-children) {
    display: flex;
    flex-direction: column;
  }
  :deep(.ant-form-item-label) {
    line-height: 32px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 0;
  }

  .phoneNum-input,
  .email-input {
    width: 232px;
  }

  :deep(.ant-space-item) {
    height: 33px;
  }
}

.agree-btn {
  color: @jt-primary-color;
  cursor: pointer;
  margin-left: -8px;
}

.popup-title {
  font-size: 14px;
}
</style>
