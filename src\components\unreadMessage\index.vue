<template>
  <micro-components moduleName="unread-message" :data="{ requester, config, titleStyle }" />
</template>

<script>
import { GET, POST } from '@/request/index';
import { getEnvConfig } from '@/config';
import { isLogin } from '@/keycloak';
export default {
  name: 'unread-message-remote',
  data() {
    return {
      requester: { GET, POST },
      config: {
        isLogin: isLogin(),
        messageUrl: `/${getEnvConfig('MESSAGE_URL_PATH')}`,
      },
      titleStyle: {
        height: '64px',
        'line-height': '64px',
        'font-size': '14px',
        color: '#121f2c',
        background: 'rgba(255, 255, 255, 0)',
        hoverTitleColor: `${isLogin() ? '#121f2c' : '#00b3cc'}`,
      },
    };
  },
};
</script>
