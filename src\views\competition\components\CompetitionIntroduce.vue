<template>
  <div class="competition-introduce">
    <jt-common-content :loading="introduceLoading || richTextLoading" :empty="cptIntroduce.length === 0 && !introduceLoading" :emptyStyle="{ height: '416px' }" emptyTitle="暂无数据">
      <div v-show="cptIntroduce.length > 0" v-for="(v, i) in cptIntroduce" :key="i">
        <div class="title" v-if="!richTextLoading">{{ v.title }}</div>
        <competition-introduce-rich-text :url="v.content" @updateRichLoading="updateRichLoading"></competition-introduce-rich-text>
      </div>
    </jt-common-content>
  </div>
</template>
<script>
import CompetitionIntroduceRichText from './CompetitionIntroduceRichText';
export default {
  components: {
    CompetitionIntroduceRichText,
  },
  props: {
    cptIntroduce: Array,
    introduceLoading: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      richTextLoading: false,
    };
  },
  methods: {
    updateRichLoading(val) {
      this.richTextLoading = val;
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.competition-introduce {
  min-height: 460px;

  :deep(.ant-spin-nested-loading) {
    min-height: 460px;
  }
  .title {
    height: 25px;
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    line-height: 25px;
    margin: 40px 0 16px 0;
  }
}
</style>
