<template>
  <div>
    <div class="container">
      <div class="left">
        <h1 class="dir-title">帮助中心</h1>
        <a-auto-complete :value="keyword" :data-source="dataSource" class="search" @select="handleSlect">
          <a-input-search v-model:value="keyword" placeholder="搜索本产品文档" :loading="searchLoading" @change="onSearch" />
        </a-auto-complete>
        <directory-tree :active-key="documentId" :tree-data="directoryData" @clickItem="handleClickItem"></directory-tree>
      </div>
      <div class="right">
        <a-breadcrumb>
          <a-breadcrumb-item>操作指南</a-breadcrumb-item>
          <a-breadcrumb-item><a href="">注册登录</a></a-breadcrumb-item>
        </a-breadcrumb>
        <document-viewer :titles="titles" :document="document"></document-viewer>
        <div class="navigator">
          <a-button class="cus-btn" type="link" :disabled="onFirstOne" @click="handlePrev">
            <jt-icon type="iconleft"></jt-icon>
            上一篇：
            {{ '快速入门' }}
          </a-button>
          <a-button class="cus-btn" type="link" :disabled="onLastOne" @click="handleNext">
            下一篇：
            {{ '学习' }}
            <jt-icon type="iconright"></jt-icon>
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GET } from '@/request';
import DirectoryTree from './directoryTree';
import DocumentViewer from './documentViewer';

export default {
  name: 'Document',
  components: {
    DirectoryTree,
    DocumentViewer,
  },
  data() {
    return {
      directoryLoading: false,
      searchLoading: false,
      documentLoading: false,
      directoryData: [],
      document: {
        pageName: '',
        pageContent: '',
        updateTime: '',
        titles: '[]',
      },
      documentId: '',
      dataSource: [],
      keyword: '',
    };
  },
  computed: {
    titles() {
      try {
        return JSON.parse(this.document.titles);
      } catch (error) {
        return [];
      }
    },
    flatData() {
      return this.directoryData.flatMap((item) => (item.children ? item.children : [item]));
    },
    onFirstOne() {
      return this.currentIndex === 0;
    },
    onLastOne() {
      return this.currentIndex === this.flatData.length - 1;
    },
    currentIndex() {
      return this.flatData.findIndex((item) => item.id === this.documentId);
    },
  },
  watch: {
    '$route.params.id'(val) {
      this.documentId = val;
    },
    documentId(val) {
      this.getDocument(val);
      this.$router.push(`/document/${val}`);
    },
  },
  mounted() {
    this.documentId = this.$route.params.id;
    this.getDirectory();
  },
  methods: {
    handleSlect(value) {
      this.documentId = value;
      this.keyword = '';
      this.dataSource = [];
    },
    handleClickItem(id) {
      this.documentId = id;
    },
    getDocument() {
      this.documentLoading = true;
      GET('/mock/help/page/detail', { pageId: this.documentId }).then((res) => {
        this.document = res.body;
        this.documentLoading = false;
      });
    },
    getDirectory() {
      this.directoryLoading = true;
      GET('/mock/help/page/list').then((res) => {
        this.directoryData = res.body;
        this.directoryLoading = false;
      });
    },
    onSearch() {
      this.searchLoading = true;
      GET('/mock/help/page/query/list', { pageName: this.keyword }).then((res) => {
        this.dataSource = res.body.map((item) => ({
          value: item.pageId,
          text: item.pageName,
        }));
        this.searchLoading = false;
      });
    },
    handleNext() {
      this.documentId = this.flatData[this.currentIndex + 1].id;
    },
    handlePrev() {
      this.documentId = this.flatData[this.currentIndex - 1].id;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.container {
  width: 1200px;
  margin: 24px auto;
  display: flex;
  min-height: calc(100vh - @jt-footer-height);
  .left {
    width: 280px;
    padding: 24px;
    background: #f4f8fa;
    border-radius: 2px;
    .directory-tree {
      height: 100%;
    }
    .dir-title {
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
    }
    .search {
      width: 232px;
      margin: 12px 0;
    }
  }
  .right {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    .document-viewer {
      height: 100%;
    }
    .navigator {
      display: flex;
      justify-content: space-between;
      padding-top: 24px;
      border-top: 1px solid #d8d8d8;
    }
  }
  .cus-btn {
    display: inline-flex;
    align-items: center;
    &:not(:hover, [disabled]) {
      color: #121f2c;
    }
    :deep(.ant-btn > .anticon) {
      line-height: 0;
    }
  }
}
</style>
