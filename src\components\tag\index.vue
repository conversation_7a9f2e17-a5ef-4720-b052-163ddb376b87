<template>
  <span class="jt-tag" :class="`jt-${type}-tag`"><slot /></span>
</template>
<script>
export default {
  name: 'tag',
  /**
   * normal(默认) - 正式赛/练习赛
   * running - 进行中
   * tobegin - 即将开始
   * end - 已结束
   * published - 已发布
   * unpublish - 未发布
   * verifying - 校验中
   * error - 失败
   */
  props: {
    type: {
      type: String,
      default: 'normal',
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.jt-tag {
  padding: 0px 12px;
  border-radius: 2px;
  font-size: 12px;
  white-space: nowrap;
  display: inline-block;
  height: 24px;
  line-height: 24px;
  box-sizing: border-box;
}
.noborder-tag-style() {
  color: #ffffff;
}
.jt-normal-tag {
  color: #0082ff;
  border: 1px solid #0082ff;
}
.jt-running-tag {
  background: #389bff;
  .noborder-tag-style();
}
.jt-tobegin-tag {
  background: #48c8e4;
  .noborder-tag-style();
}
.jt-end-tag {
  background: linear-gradient(270deg, #cbcfd2 0%, #cbcfd2 100%);
  .noborder-tag-style();
}
.jt-published-tag {
  border: 1px solid #17bb85;
  color: #17bb85;
}
.jt-unpublish-tag {
  border: 1px solid #a0a6ab;
  color: #606972;
}
.jt-verifying-tag {
  border: 1px solid #ff8415;
  color: #ff8415;
}
.jt-error-tag {
  border: 1px solid #ff454d;
  color: @jt-error-color;
}
</style>
