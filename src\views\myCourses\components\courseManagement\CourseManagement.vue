<template>
  <div class="course-management">
    <div class="header">
      <div key="basic-info" :class="activeKey === 'basic-info' ? 'active-btn' : ''" class="basic-msg" @click="handleTypechange('course-management')">基本信息</div>
      <!-- 公开课是不显示 -->
      <div v-if="currentActiveCourse.courseFlag != '1'" key="student-manage" class="student-manage" :class="activeKey == 'student-manage' ? 'active-btn' : ''" @click="handleTypechange('student-manage')">学生管理</div>
      <a-button v-if="currentActiveCourse.courseFlag == '2' || (currentActiveCourse.courseFlag == '1' && (currentActiveCourse.coursePublish == '0' || !currentActiveCourse.coursePublish))" type="primary" @click="handleEdit">
        <EditOutlined />
        编辑
      </a-button>
    </div>
    <div v-if="activeKey === 'basic-info'" class="content">
      <div v-for="(info, i) in basicInfoColumn" :key="i" :class="{ cover: info.type === 'courseImage' }" class="content-item">
        <span class="content-title"> {{ info.title }}: </span>
        <span v-if="info.type === 'courseImage'" class="content-content">
          <img :src="currentActiveCourse[info.type]" style="width: 180px" alt="" />
        </span>
        <span v-else-if="info.type === 'time'" class="content-content">{{ currentActiveCourse.startTime }} - {{ currentActiveCourse.endTime }}</span>
        <span v-else class="content-content">{{ basicInfoMaps[currentActiveCourse[info.type]] ? basicInfoMaps[currentActiveCourse[info.type]] : currentActiveCourse[info.type] }}</span>
      </div>
    </div>

    <div v-if="activeKey === 'student-manage'"><StudentManage /></div>
    <!-- 封闭课显示 -->
  </div>
</template>
<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>

<script>
import { basicInfoColumn, basicInfoMaps } from './constants';
import StudentManage from './StudentManage.vue';
import { mapState } from 'vuex';

export default {
  name: 'CourseManagement',
  components: {
    StudentManage,
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
    activeKey() {
      return this.$route.params.subTab;
    },
  },
  watch: {
    '$route.params.tab'() {
      this.initSubTab();
    },
  },
  mounted() {
    this.initSubTab();
  },
  data() {
    return {
      basicInfoColumn,
      basicInfoMaps,
    };
  },
  methods: {
    handleTypechange(activeKey) {
      this.$router.push({ params: { ...this.$route.params, subTab: activeKey } });
    },
    handleEdit() {
      this.$router.push({
        name: '编辑课程',
        params: {
          ...this.$route.params,
          subPage: 'edit-course',
        },
      });
    },
    initSubTab() {
      if (this.$route.params.tab === 'course-management') {
        const subTab = this.$route.params.subTab;
        if (!subTab) {
          this.$router.replace({ params: { ...this.$route.params, subTab: 'basic-info' } });
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.course-management {
  position: relative;
  padding: 0px 32px;
  .header {
    display: flex;
    justify-content: space-between;
    margin: 24px 0;
    cursor: pointer;
    div {
      border: 1px solid #cbcfd2;
      text-align: center;
      width: 160px;
      height: 32px;
      line-height: 32px;
      color: #606972;
    }
    .active-btn {
      border-color: #0082ff;
      background-color: #f0f8ff;
      color: #0082ff;
    }

    .basic-msg {
      border-radius: 2px 0px 0px 2px;
    }

    .student-manage {
      border-radius: 0px 2px 2px 0px;
    }

    .basic-msg:not(.active-btn) {
      border-right-width: 0;
    }
    .student-manage:not(.active-btn) {
      border-left-width: 0;
    }
  }
  .content {
    margin: 20px 0px 64px;
    .cover {
      display: flex;
    }
    .content-item {
      margin-bottom: 24px;
    }
    .content-title {
      display: inline-block;
      width: 100px;
      text-align: right;
    }
    .content-content {
      display: inline-block;
      margin-left: 16px;
    }
  }
  .button {
    width: 120px;
    margin: 16px 0px 0px;
  }
}
</style>
