export const rankTabsList = [
  {
    value: '1',
    label: '结果文件提交',
  },
  {
    value: '2',
    label: '排行榜',
  },
  {
    value: '3',
    label: '审查及答辩材料提交',
  },
];
// 提交方式
export const getSubmitType = (type) => {
  switch (type) {
    case 1:
      return '本地上传；从团队共享存储空间中选择';
    case 2:
      return '本地上传';
    case 3:
      return '从团队共享存储空间中选择';
    default:
      return '-';
  }
};
// 覆盖table的样式
export const subCustomHeaderRow = () => {
  return {
    style: {
      'font-size': '12px',
      'background-color': '#EDF1F3;',
      color: '#121F2C',
      height: '40px',
    },
  };
};
export const subCustomRow = () => {
  return {
    style: {
      height: '44px',
      color: '#121F2C',
      'font-size': '12px',
    },
  };
};
export const ajaxTitleStyle = {
  overflow: 'hidden',
  'text-overflow': 'ellipsis',
  'max-width': '80px',
};
export const getResultStatusObj = (status) => {
  switch (status) {
    case 1:
      return '成功';
    case 3:
    case 5:
      return '评分中';
    case 4:
      return '提交中';
    default:
      return '失败';
  }
};

export const getReviewRecordStatusObj = (status) => {
  switch (status) {
    case 1:
      return '成功';
    case 3:
      return '提交中';
    case 4:
      return '提交中';
    default:
      return '失败';
  }
};

export const ClearType = {
  1: '结果文件提交',
  2: '审查及答辩材料提交',
  结果文件提交: '1',
  审查及答辩材料提交: '2',
};
