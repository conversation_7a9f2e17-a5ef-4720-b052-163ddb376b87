<template>
  <div class="contents">
    <div class="contents-header">
      <div>
        <span class="title">课节</span>
        <span class="progress">
          {{ `共${courseContents.length || 0}节` }}
        </span>
      </div>
    </div>
    <jt-common-content :empty="courseContents.length === 0" :loading="loading">
      <ul class="contents-list">
        <li v-for="(item, i) in courseContents" :key="item.id">
          <div class="left">
            <span class="catlog-name">{{ `第${toChinesNum(i + 1)}节：${item.catalogNamePre}` }}</span>
            <span v-if="+item.videoFlag" class="catlog-icon1">视频</span>
            <span v-if="+item.itemFlag" class="catlog-icon2">项目</span>
            <span v-if="+item.documentFlag" class="catlog-icon3">文档</span>
          </div>
          <div>
            <a-tooltip v-if="getTip(item)" :title="getTip(item)">
              <a-button :disabled="getTip(item)" @click="handleContinue(item)" type="link">查看课节</a-button>
            </a-tooltip>
            <a-button v-else :disabled="courseDisabled(item)" @click="handleContinue(item)" type="link">查看课节</a-button>
          </div>
        </li>
      </ul>
    </jt-common-content>
    <student-certificate :userName="$keycloak.idTokenParsed.preferred_username" :courseName="courseName" :visible="certificateVisible" @cancel="certificateVisible = false"></student-certificate>
  </div>
</template>

<script>
import toChinesNum from '@/lib/toChinesNum';
import studentCertificate from '../studentCertificate.vue';

const ITEMSTATUS = {
  0: '发布中',
  1: '已发布',
};

const COURSEFLAG = {
  1: '公开课',
  0: '封闭课',
};

export default {
  components: { studentCertificate },
  props: {
    catalogNum: Number,
    mapFlag: Number | String,
    finished: Boolean,
    courseName: String,
    courseId: String | Number,
    courseFlag: Number | String,
  },
  mounted() {
    this.getCatalogs();
  },
  data() {
    return {
      certificateVisible: false,
      loading: false,
      courseContents: [],
    };
  },
  watch: {
    courseId: 'getCatalogs',
  },
  methods: {
    toChinesNum,
    getCatalogs() {
      if (!this.courseId) {
        return;
      }
      const obj = {
        courseId: this.courseId,
      };
      this.loading = true;
      const coursePublish = this.$route.query.coursePublish;
      const url = coursePublish === '1' ? '/course_model/web/course_student/course/teacherLookCourseCatalogList' : '/course_model/web/course_student/course/teacherCourseCatalogList';
      this.$GET(url, obj).then((res) => {
        this.loading = false;
        this.courseContents = res.body;
      });
    },

    handleContinue(item) {
      this.$router.push({ path: `/course/teaching/course-learn/${this.courseId}/${item.id}`, query: { courseName: this.courseName, catalogName: item.catalogNamePre, preview: true, restart: true, coursePublish: this.$route.query.coursePublish } });
    },
    courseDisabled(item) {
      if (!(+item.videoFlag || +item.itemFlag || +item.documentFlag)) {
        return true;
      }
      if (this.haveUnPublishedSource(item)) {
        return true;
      }
    },
    haveUnPublishedSource(item) {
      return !!+item.haveUnPublishedSource;
    },
    itemPublishing(item) {
      return ITEMSTATUS[item.itemStatus] === '发布中';
    },
    getTip(item) {
      if (this.itemPublishing(item)) {
        if (COURSEFLAG[this.courseFlag] === '公开课') {
          return '项目创建中，请稍后查看';
        } else {
          return '项目发布中，请稍后查看';
        }
      }
      if (this.haveUnPublishedSource(item)) {
        return '该课节有未发布的内容，请发布后查看';
      }
      return;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

div,
ul,
li {
  display: flex;
}
p {
  margin: 0;
}
li {
  margin: 0;
}
.contents {
  flex: 1;
  flex-direction: column;
  background: #fff;
  .contents-header {
    justify-content: space-between;
    align-items: center;
    height: 65px;
    padding: 0 32px;
    border-bottom: 1px solid #e0e1e1;
    .title {
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      margin-right: 32px;
      color: #121f2c;
    }
    .progress {
      align-items: center;
      display: inline-flex;
      font-size: 14px;
      color: #606972;
    }
    .course-icon {
      cursor: pointer;
    }
  }
  .contents-list {
    flex: 1;
    flex-direction: column;
    padding: 0 24px;
    li {
      height: 72px;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px;
      position: relative;
      box-shadow: 0px 1px 0px 0px #eee;
      margin-bottom: 1px;
      &:hover {
        background: #f8f9fa;
      }
      .left {
        display: block;
        .learn-status-tag {
          &.unstarted {
            color: #bec2c5;
            border: 1px solid #bec2c5;
          }
          &.unfinished {
            color: #ff7b00;
            border: 1px solid #ff7b00;
          }
          &.finished {
            color: #17c189;
            border: 1px solid #17c189;
          }
        }
        .catlog-name {
          margin-right: 12px;
          font-size: 16px;
          color: #121f2c;
        }
        .catlog-icon {
          display: inline-block;
          width: 48px;
          text-align: center;
          height: 24px;
          line-height: 24px;
          font-size: 12px;
          border-radius: 2px;
          margin-right: 8px;
        }
        .catlog-icon1 {
          color: #389bff;
          background: rgba(56, 155, 255, 0.1);
          .catlog-icon();
        }
        .catlog-icon2 {
          color: #f79032;
          background: rgba(247, 144, 50, 0.1);
          .catlog-icon();
        }
        .catlog-icon3 {
          color: #b563fc;
          background: rgba(181, 99, 252, 0.1);
          .catlog-icon();
        }
      }
      .course-status-tag {
        position: absolute;
        top: 0;
        right: 0;
        width: 84px;
        height: 20px;
        border-radius: 0px 0px 0px 100px;
        padding-left: 16px;
        color: #fff;
        font-size: 12px;
        &.updated {
          background: #ff7b00;
        }
        &.removed {
          background: #ff454d;
        }
      }
      .ant-btn-link {
        padding: 0;
        padding-left: 16px;
      }
      .danger {
        color: #ff454d;
      }
    }
  }
}
</style>
