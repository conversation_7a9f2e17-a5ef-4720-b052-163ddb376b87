<template>
  <div class="competition-ranking">
    <jt-common-content :loading="ranking.loading" :empty="rankingList.length == 0" :empty-style="{ height: '416px' }" empty-title="暂无数据">
      <p>{{ rankingTitle }}</p>
      <a-table class="ranking-table" :pagination="false" :columns="rankingColumns" :data-source="rankingList" :scroll="{ x: rankingColumnX }" :row-key="(r, i) => i.toString()">
        <!-- <template #ranknum="{ ranknum }">
          <span :class="[RANKING_NUM_CLASS[ranknum]]">{{ ranknum }}</span>
        </template> -->
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column && column.dataIndex === 'ranknum'">
            <span :class="[RANKING_NUM_CLASS[text]]">{{ text }}</span>
          </template>
          <template v-else>
            <span v-if="column && column.dataIndex">{{ text }}</span>
          </template>
        </template>
      </a-table>
    </jt-common-content>
    <jt-pagination v-if="rankingList.length !== 0" :page-size="ranking.pageSize" :page-num="ranking.pageNum" :total="ranking.total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
  </div>
</template>
<script>
import API from '@/constants/api/API_competition_model.js';
const RANKING_NUM_CLASS = {
  1: 'top1',
  2: 'top2',
  3: 'top3',
};
export default {
  components: {},
  props: {
    showRanking: { type: Boolean, default: false },
    rankingTitle: { type: String, default: '' },
  },
  data() {
    return {
      RANKING_NUM_CLASS,
      rankingColumns: [],
      rankingList: [],
      resultTitleLength: 0,
      ranking: {
        loading: true,
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    isRankinTitleBeyondFour() {
      return this.resultTitleLength > 4;
    },
    rankingColumnX() {
      return this.isRankinTitleBeyondFour ? (this.rankingColumns.length - 2) * 150 + 270 : 0;
    },
  },
  created() {
    this.getCptRankingList();
  },
  methods: {
    // 排行榜列表
    getCptRankingList() {
      this.ranking.loading = true;
      if (!this.showRanking) {
        this.ranking.loading = false;
        return;
      }
      const reqObj = { cid: this.$route.query.id, pageNum: this.ranking.pageNum, pageSize: this.ranking.pageSize };
      API.getCompetitionRankingList(reqObj).then((res) => {
        if (res.state === 'OK' && res.body && res.body.list) {
          if (res.body.list.length == 0) {
            this.rankingList = [];
            this.ranking.total = 0;
            this.ranking.loading = false;
          } else {
            for (let i = 0; i < res.body.list.length; i++) {
              const obj = res.body.list[i].scoreJson;
              for (const key in obj) {
                res.body.list[i][key] = obj[key];
              }
            }
            this.getCompetitionScoreName(res);
          }
        }
      });
    },
    getCompetitionScoreName(res) {
      API.getCompetitionGetScoreName({ cid: this.$route.query.id }).then((scoreNameRes) => {
        if (scoreNameRes.state === 'OK' && scoreNameRes.body != null) {
          const obj = scoreNameRes.body;
          this.resultTitleLength = Object.keys(obj).length;
          this.rankingColumns = [
            { title: '排名', dataIndex: 'ranknum', key: 'ranknum', fixed: this.isRankinTitleBeyondFour ? 'left' : '', width: 100, ellipsis: true },
            { title: '团队名', dataIndex: 'teamname', key: 'teamname', fixed: this.isRankinTitleBeyondFour ? 'left' : '', width: 150, ellipsis: true },
            { title: '提交时间', dataIndex: 'createtime', key: 'createtime', fixed: this.isRankinTitleBeyondFour ? 'right' : '', width: 170, ellipsis: true },
          ];
          let targetColumn = [];
          for (let i in obj) {
            targetColumn.push({ title: i, dataIndex: obj[i], key: obj[i], width: 150, ellipsis: true });
          }
          for (let i = targetColumn.length - 1; i >= 0; i--) {
            this.rankingColumns.splice(2, 0, targetColumn[i]);
          }
          this.rankingList = res.body.list;
          this.ranking.total = res.body.total;
          this.ranking.loading = false;
        }
      });
    },
    pageNumChange(page) {
      this.ranking.pageNum = page;
      this.getCptRankingList();
    },
    pageSizeChange(current) {
      this.ranking.pageSize = current;
      this.ranking.pageNum = 1;
      this.getCptRankingList();
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.competition-ranking {
  p {
    height: 25px;
    line-height: 25px;
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    margin: 40px 0 24px 0;
  }
  .ranking-table {
    margin: 24px 0 16px 0;
    .topsapn {
      display: inline-block;
      width: 24px;
      height: 24px;
      line-height: 24px;
      font-size: 14px;
      border-radius: 50%;
      font-weight: @jt-font-weight;
      color: @jt-color-white;
      text-align: center;
    }
    .top1 {
      .topsapn();
      background: linear-gradient(180deg, #ffa82f 0%, #ffc851 100%);
    }
    .top2 {
      .topsapn();
      background: linear-gradient(180deg, #b1b5b7 0%, #d4d7d7 100%);
    }
    .top3 {
      .topsapn();
      background: linear-gradient(180deg, #edbc6a 0%, #f9d69b 100%);
    }
  }
}
</style>
