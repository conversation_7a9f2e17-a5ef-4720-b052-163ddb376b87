<template>
  <vue-pdf-app :config="config" class="pdf-viewer" :pdf="url"></vue-pdf-app>
</template>

<script>
import { GET } from '@/request';
// import VuePdfApp from 'vue3-pdf-app';
// import this to use default icons for buttons
import 'vue3-pdf-app/dist/icons/main.css';
const config = {
  sidebar: {
    viewThumbnail: true,
    viewOutline: true,
    viewAttachments: true,
  },
  secondaryToolbar: {
    secondaryPresentationMode: true,
    secondaryOpenFile: true,
    secondaryPrint: true,
    secondaryDownload: true,
    secondaryViewBookmark: true,
    firstPage: false,
    lastPage: false,
    pageRotateCw: true,
    pageRotateCcw: true,
    cursorSelectTool: true,
    cursorHandTool: true,
    scrollVertical: false,
    scrollHorizontal: false,
    scrollWrapped: false,
    spreadNone: false,
    spreadOdd: false,
    spreadEven: false,
    documentProperties: false,
  },
  toolbar: {
    toolbarViewerLeft: {
      findbar: false,
      previous: true,
      next: true,
      pageNumber: true,
    },
    toolbarViewerRight: {
      presentationMode: true,
      openFile: false,
      print: false,
      download: false,
      viewBookmark: false,
    },
    toolbarViewerMiddle: {
      zoomOut: false,
      zoomIn: false,
      scaleSelectContainer: false,
    },
  },
  errorWrapper: true,
};

export default {
  components: {
    VuePdfApp: () => import('vue3-pdf-app'),
  },
  data() {
    return {
      config,
      url: '',
      id: '',
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getResourceUrl();
  },
  methods: {
    getResourceUrl() {
      GET(`/course_model/web/teaching/catalog/resource/list/id`, { id: this.id }).then((res) => {
        this.url = res.body.resourseUrl;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.pdf-viewer {
  width: 100vw;
  height: 100vh;
}
</style>

<style lang="less">
.pdf-app[class] #toolbarContainer {
  background-color: #000;
}
</style>
