export const basicInfoColumn = [
  { title: '课程封面', type: 'courseImage' },
  { title: '课程名称', type: 'courseName' },
  { title: '开课范围', type: 'courseFlag' },
  { title: '开课时间', type: 'time' },
  { title: '课程分类', type: 'categoryCode' },
  { title: '能力分级', type: 'levelCode' },
  { title: '一句话简介', type: 'courseIntroduce' },
  { title: '开课机构', type: 'instituteName' },
];
export const basicInfoMaps = {
  BASE: '入门',
  MORE: '进阶',
  MOST: '实战',
  1: '公开课',
  2: '封闭课',
  AI: '机器学习/深度学习',
  TF: '工具与框架',
  CMV: '计算机视觉',
  NLP: '自然语言处理',
  IDA: '智能数据分析',
  IV: '智能语音',
};
export const studentsColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    slots: { title: 'customTitle', customRender: 'name' },
  },
  {
    title: '学号',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
  },
  {
    title: '加入时间',
    key: 'time',
    dataIndex: 'time',
    slots: { customRender: 'time' },
  },

  {
    title: '操作',
    width: 100,
    key: 'action',
    slots: { customRender: 'action' },
  },
];
