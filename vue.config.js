const path = require('path');
const fs = require('fs');
// 提取主题变量加载逻辑
const lessToJs = require('less-vars-to-js');
const loadThemeVariables = () => {
  const themePath = path.join(__dirname, './src/assets/styles/theme/theme.less');
  return lessToJs(fs.readFileSync(themePath, 'utf8'));
};

const themeVariables = loadThemeVariables();
const webpack = require('webpack');
const proxyTable = require('./proxy-table');
const IS_DEV = process.env.NODE_ENV === 'development';

module.exports = {
  publicPath: IS_DEV ? '/web' : './web',
  productionSourceMap: false,
  chainWebpack: (config) => {
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap((options) => {
        // 可以在这里修改 vue-loader 的选项
        return options;
      });
    config.module.rule('js').use('thread-loader').loader('thread-loader').before('babel-loader').end();
    config.plugin('define').tap((args) => {
      args[0]['process.env'].PROXY_ENV = JSON.stringify(process.env.PROXY_ENV);
      args[0]['process.env'].TARGET_HOST = JSON.stringify(process.env.TARGET_HOST);
      return args;
    });
  },
  configureWebpack: () => {
    return {
      plugins: [new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn$/)],
      module: {
        rules: [
          {
            test: /.mjs$/,
            include: /node_modules/,
            type: 'javascript/auto',
          },
        ],
      },
      // vue3-pdf-app 打包大小限制
      performance: {
        hints: process.env.NODE_ENV === 'production' ? 'warning' : false, // 或者设置为 'warning'
        maxAssetSize: 1024 * 1024, // 增加到 1MB
        maxEntrypointSize: 1024 * 1024, // 增加到 1MB
        // 可选：自定义资源过滤器，排除某些文件不进行大小检查
        assetFilter: function (assetFilename) {
          // 排除地图文件和字体文件
          return !/\.map$/.test(assetFilename) && !/\.(woff|woff2|eot|ttf|otf)$/.test(assetFilename);
        },
      },
    };
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: themeVariables,
          javascriptEnabled: true,
        },
      },
    },
  },
  devServer: {
    open: false,
    // port: "8080",
    proxy: proxyTable,
  },
  transpileDependencies: ['ant-design-vue', 'vue3-pdf-app'],
  lintOnSave: false,
};
