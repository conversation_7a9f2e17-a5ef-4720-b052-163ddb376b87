<script setup>
import { RightOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="competition-guide">
    <div class="competition-guide-title">
      <div class="course-msg">设置比赛基本信息</div>
      <div class="ellip-img">
        <img src="@/assets/image/teaching/guide-ellipsis.png" alt="" width="145px" />
      </div>
      <div class="teach-content">设置比赛环节</div>
      <div class="ellip-img">
        <img src="@/assets/image/teaching/guide-ellipsis.png" width="145px" alt="" />
      </div>
      <div class="publish-work">管理团队及提交</div>
    </div>
    <div class="competition-guide-main">
      <a-row>
        <a-col :span="8">
          <div v-for="(item, index) in competitionInformationItems" :key="index" class="guide-item" @click="handleItemClick(item)">
            <div>{{ item.name }} <RightOutlined class="arror-icon" /></div>
            <p>{{ item.description }}</p>
          </div>
        </a-col>
        <a-col :span="8">
          <div v-for="(item, index) in competitionSegmentItems" :key="index" class="guide-item" @click="handleItemClick(item, isDisplayCompetition)">
            <div :class="isDisplayCompetition ? 'disable-guide' : ''">{{ item.name }} <RightOutlined class="arror-icon" /></div>
            <p>{{ item.description }}</p>
          </div>
        </a-col>
        <a-col :span="8">
          <div v-for="(item, index) in competitionTeamItems" :key="index" class="guide-item" @click="handleItemClick(item, isDisplayCompetition)">
            <div :class="isDisplayCompetition ? 'disable-guide' : ''">{{ item.name }} <RightOutlined class="arror-icon" /></div>
            <p>{{ item.description }}</p>
          </div>
        </a-col>
      </a-row>
    </div>
    <div v-if="currentManageCompetition.releaseSta === publishStatusKeys.UNPUBLISH" class="bottom-background">
      <div style="height: 33px"></div>
      <div class="operation-btn">
        <span class="btn-container">
          <a-button type="primary" @click="releaseCompetition">发布比赛</a-button>
        </span>
      </div>
    </div>
    <a-modal v-model:open="publishAlertVisible" class="competition-operation-modal" centered :closable="false" :title="null" :footer="null" width="372px" :mask-closable="false">
      <a-spin :spinning="spinning" tip="发布中...">
        <div class="off-title">
          <jt-icon style="color: rgba(250, 173, 20, 1)" type="iconwarning-circle-fill"></jt-icon>
          <span>发布比赛提示</span>
        </div>
        <p class="operation-alert-msg">发布前请务必确保已完成所有必要设置<br /><span style="color: #faad14">发布后无法下架比赛，请谨慎操作</span></p>
        <a-space style="justify-content: flex-end; width: 100%">
          <a-button style="width: 64px" @click="publishAlertVisible = false">取消</a-button>
          <a-button style="width: 64px" type="primary" @click="publishOrOffSubmit">发布</a-button>
        </a-space>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import { onlyInfoDiplayCompetitionTypeIds } from '../../competitionConfig/index';
import { publishStatusKeys } from '../../competitionConfig/index';
import { mapState } from 'vuex';
import { POST } from '@/request';
import { competitionApi } from '@/apis/index.js';

export default {
  emits: ['handleTabChange'],
  data() {
    return {
      publishAlertVisible: false,
      publishStatusKeys,
      competitionInformationItems: [
        {
          name: '设置基本信息',
          tabKey: '2',
          btnKey: '1',
          description: '比赛名称、时间、类型、简介、图片',
        },
        {
          name: '发布赛制介绍',
          tabKey: '2',
          btnKey: '2',
          description: '赛事背景、规则、赛程安排',
        },
        {
          name: '发布赛题说明',
          tabKey: '2',
          btnKey: '3',
          description: '赛题背景、任务、数据及测评方案说明',
        },
        {
          name: '发布常见问题',
          tabKey: '2',
          btnKey: '4',
          description: '常见问题QA',
        },
      ],
      competitionSegmentItems: [
        {
          name: '设置报名及团队',
          tabKey: '3',
          btnKey: '1',
          description: '报名时间、算力豆发放、团队编辑时间',
        },
        {
          name: '设置数据及实例',
          tabKey: '4',
          btnKey: '1',
          description: '比赛数据、下载权限、比赛实例资源',
        },
        {
          name: '设置提交及排行',
          tabKey: '5',
          btnKey: '1',
          description: '提交时间、提交规则、排行榜',
        },
      ],
      competitionTeamItems: [
        {
          name: '管理报名用户',
          tabKey: '3',
          btnKey: '2',
          description: '报名用户查看及导出',
        },
        {
          name: '管理团队',
          tabKey: '3',
          btnKey: '3',
          description: '团队查看及导出',
        },
        {
          name: '管理提交',
          tabKey: '5',
          btnKey: '1',
          description: '提交记录查看及导出',
        },
      ],
      spinning: false,
    };
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
    isDisplayCompetition() {
      return onlyInfoDiplayCompetitionTypeIds.includes(this.currentManageCompetition.typeId);
    },
  },
  methods: {
    releaseCompetition() {
      this.publishAlertVisible = true;
    },
    handleItemClick(item, isDisplayCompetition = null) {
      if (!isDisplayCompetition) {
        this.$emit('handleTabChange', item);
      }
    },
    publishOrOffSubmit() {
      this.spinning = true;
      const { competitionId } = this.$route.params;
      POST('/competiton/web/manage/release', { cid: competitionId }).then(async (res) => {
        if (res.state === 'OK') {
          const competitionDetailRes = await competitionApi.getCompetitionDetail({ cid: competitionId });
          this.$store.commit('competition/SET_CURRENTMANAGECOMPETITION', competitionDetailRes.body);
          this.$message.success('比赛发布成功');
        } else {
          this.$message.error('比赛发布失败');
        }
        this.spinning = false;
        this.publishAlertVisible = false;
      });
    },
  },
};
</script>
<style lang="less">
.competition-operation-modal {
  .operation-alert-msg {
    line-height: 30px;
    margin-bottom: 20px;
    padding-left: 20px;
  }
}
</style>
<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.competition-guide {
  .competition-guide-title {
    margin-left: 88px;
    margin-bottom: 20px;
    display: flex;

    .ellip-img {
      line-height: 64px;
      margin-right: 24px;
    }

    .course-msg,
    .teach-content,
    .publish-work {
      height: 64px;
      width: 200px;
      background-size: 100% 100%;
      line-height: 64px;
      padding-left: 60px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      font-size: 16px;
    }

    .course-msg {
      background-image: url('~@/assets/image/teaching/guid1.png');
    }

    .teach-content {
      background-image: url('~@/assets/image/teaching/guid2.png');
    }

    .publish-work {
      background-image: url('~@/assets/image/teaching/guid3.png');
    }
  }
}

.competition-guide-main {
  margin-left: 88px;
  margin-bottom: 64px;

  .guide-item {
    display: inline-block;
    height: 64px;
    padding: 10px;
    line-height: 32px;
    margin: 10px 0px;
    border-radius: 2px;
    min-width: 300px;

    &:hover {
      background-color: #f8f9fa;
    }

    div {
      height: 25px;
      line-height: 25px;
      color: #0082ff;
      font-size: 16px;
      cursor: pointer;
    }

    .disable-guide {
      color: #a0a6ab;
      cursor: not-allowed;
    }
  }
}

.bottom-background {
  background-image: url('~@/assets/image/teaching/btns-bac.png');
  background-size: 100%;
  border-radius: 0px 0px 2px 2px;
}

.operation-btn {
  border-top: 1px solid #efefef;
  display: flex;
  justify-content: center;
  align-items: center;

  .btn-container {
    display: inline-block;
    padding: 24px 0px 40px;

    button {
      width: 160px;
    }

    .off-btn {
      &:hover {
        border-color: #ff454d;
        color: #ff454d;
      }
    }
  }
}

.off-title {
  margin: 10px 0px 20px;

  i {
    font-size: 18px;
    margin-right: 5px;
  }

  span {
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    font-size: 16px;
  }
}

.arror-icon {
  font-size: 14px;
  margin-left: 8px;
}
</style>
