<template>
  <div style="background-color: white">
    <div class="develop-enviroment">
      <h1 class="head-title">{{ developEnviroment.title }}</h1>
      <div class="enviroment-content">
        <div v-for="item in developEnviroment.list" :key="item.title">
          <div class="item-title">
            <img :src="icons[item.icon]" alt="" />
            <span>{{ item.title }}</span>
          </div>
          <ul style="max-width: 224px">
            <li v-for="liItem in item.data" :key="liItem">
              {{ liItem }}
            </li>
          </ul>
        </div>
      </div>
      <div style="text-align: center; margin-top: 30px">
        <consult-button />
      </div>
    </div>
  </div>
</template>

<script>
import { developEnviroment } from './config.js';
import ConsultButton from './../components/ConsultButton.vue';
export default {
  name: 'developEnviromentIntro',
  components: {
    ConsultButton,
  },
  data() {
    return {
      developEnviroment,
      icons: {
        jupyterIcon: require('@/assets/image/teaching/jupyter-icon.png'),
        cloudIcon: require('@/assets/image/teaching/cloud-icon.png'),
        commontestIcon: require('@/assets/image/teaching/commontest-icon.png'),
        autotestIcon: require('@/assets/image/teaching/autotest-icon.png'),
      },
    };
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.develop-enviroment {
  height: 473px;
  width: 1200px;
  margin: 0px auto;
  .head-title {
    padding-top: 48px;
    padding-bottom: 30px;
    color: #121f2c;
    font-size: 32px;
    text-align: center;
  }
  .enviroment-content {
    display: flex;
    justify-content: space-between;
    .item-title {
      font-size: 20px;
      color: #121f2c;
      font-weight: @jt-font-weight-medium;
      padding-bottom: 7px;
      display: flex;
      align-items: center;
      img {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
    }
    ul {
      margin-left: 44px;
    }
    li {
      padding-top: 12px;
      font-size: 14px;
      color: #121f2c;
      position: relative;
      &::before {
        content: '';
        display: inline-block;
        width: 2px;
        height: 2px;
        position: absolute;
        left: -9px;
        top: 21px;
        background-color: #121f2c;
      }
    }
  }
}
</style>
