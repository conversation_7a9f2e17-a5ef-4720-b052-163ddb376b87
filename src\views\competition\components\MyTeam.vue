<template>
  <div class="my-course-container">
    <jt-common-content :loading="loading" :empty="isEmptyTeamList" :empty-image="emptyImage" :empty-title="description.title" :empty-text="description.text">
      <div class="team-wrap">
        <div class="team-name">
          <span v-if="!teamNameInput"
            >{{ teamNameInputValue }}
            <EditOutlined v-if="isTeamLeader && isEditTime" class="editIcon" @click="teamNameInput = true" />

            <a-tooltip v-else>
              <template #title> 比赛当前未开放团队编辑操作 </template>
              <EditOutlined v-if="isTeamLeader" class="no-editIcon" />
            </a-tooltip>
          </span>
          <a-input v-else v-model:value="teamNameInputValue" placeholder="请输入团队名称" class="team-name-input" :class="{ 'team-name-input-bottom-red': teamNameInputValue.length > 20 }" @pressEnter="setTeamNameEnter($event, teamNameInputValue)" @blur="setTeamNameBlur(teamNameInputValue)" />
          <p v-if="teamNameInputValue.length > 20 && teamNameInput" style="color: #f5222d; font-size: 14px">20个字符以内</p>
        </div>
        <div class="team-handle flex">
          <div>
            <span style="margin-right: 14px"><label>团队成员：</label>{{ teamBody.teamNum }}/{{ teamBody.teamMaxNum || '-' }}</span>
            <span><label>最新排名：</label> {{ teamBody.ranking || '暂无' }}</span>
          </div>
          <!-- 
            1.isTeamLeader 队长显示
            2.团队总数大于1 显示
            3.当前加入的人数 是否满了 未满显示
            4.是否在编辑时间内 是显示否
           -->
          <a-button v-if="isTeamLeader && teamBody.teamMaxNum > 1 && !isTeamListEqualTeamMax && isEditTime" class="primary-color-button" @click="copyLinkShow"> 复制链接邀请队员 </a-button>
          <a-tooltip v-else-if="teamBody.teamMaxNum > 1 && isTeamListEqualTeamMax && isEditTime && isTeamLeader">
            <template #title> 团队成员数量已达上限 </template>
            <a-button disabled> 复制链接邀请队员 </a-button>
          </a-tooltip>
          <a-tooltip v-else-if="!isEditTime">
            <template #title> 比赛当前未开放团队编辑操作 </template>
            <a-button disabled> 复制链接邀请队员 </a-button>
          </a-tooltip>
        </div>
        <div class="team-list flex">
          <div v-for="item in teamList" :key="item.userId" class="member flex">
            <div v-if="item.identity === '队长'" class="tips">队长</div>
            <div v-else class="delete">
              <jt-icon v-if="isTeamLeader && isEditTime" class="iconshanchu1" type="iconshanchu1" @click="deleteVisible(item)"></jt-icon>
              <a-tooltip v-else-if="!isEditTime">
                <template #title> 比赛当前未开放团队编辑操作 </template>
                <jt-icon class="iconshanchu1 no-delete" type="iconshanchu1"></jt-icon>
              </a-tooltip>
            </div>
            <div class="avatar">
              <img v-if="item.image" :src="item.image" class="member-avatar" alt="" />
              <img v-else src="@/assets/image/avatar.png" class="member-avatar" alt="" />
            </div>
            <a-tooltip>
              <template #title>
                {{ item.fullName }}
              </template>
              <p class="card-name">{{ item.fullName.length > 12 ? item.fullName.slice(0, 12) + '...' : item.fullName }}</p>
            </a-tooltip>
            <p class="card-id">用户名：{{ item.userName }}</p>
          </div>
          <div v-if="isTeamLeader && !isTeamListEqualTeamMax" class="nomermber flex not-member">
            <div class="avatar not-avatar"></div>
            <p class="invite-member">快去邀请队员吧～</p>
          </div>
        </div>
        <div class="team-handle-btn">
          <a-button v-if="isTeamLeader && isEditTime" class="mgr8" :disabled="teamList.length === 1" @click="revolveVisible = true"> 转让队长 </a-button>
          <a-tooltip v-else-if="!isEditTime">
            <template #title> 比赛当前未开放团队编辑操作 </template>
            <a-button disabled class="mgr8"> 转让队长 </a-button>
          </a-tooltip>

          <a-button v-if="isTeamLeader && isEditTime" @click="dismissTeam"> 解散团队</a-button>
          <a-tooltip v-else-if="!isEditTime">
            <template #title> 比赛当前未开放团队编辑操作 </template>
            <a-button disabled> 解散团队 </a-button>
          </a-tooltip>
        </div>
      </div>
    </jt-common-content>
    <div v-if="isEmptyTeamList" class="handle flex">
      <a-button class="mgr8 button" @click="setTeamVisible = true"> 创建团队</a-button>

      <a-button v-if="editTimeBody.teamMaxNum > 1" class="button" @click="joinTeamVisible = true"> 加入团队 </a-button>
      <a-tooltip v-else overlay-class-name="join-dis-title">
        <template #title> 团队成员数量上限为1，不支持加入团队 </template>
        <a-button type="primary" disabled> 加入团队 </a-button>
      </a-tooltip>
    </div>
    <p class="tips">团队开放编辑操作的时间为 {{ dateConvertYearMonthDay(editTimeBody.editStartTime) }}至{{ dateConvertYearMonthDay(editTimeBody.editEndTime) }}，请您在时间窗口内操作，逾期不可修改；结果文件提交截止后，无法解散团队</p>

    <confirm-modal v-model="copyLinkVisible" ok-text="知道了" title="复制链接成功" :confirm-loading="false" :show-cancel="false" @ok="copyHandleOk">
      <template #icon>
        <jt-icon type="iconwancheng-tianchong" style="font-size: 20px; color: #17bb85" />
      </template>
      <p style="font-size: 14px">本链接仅支持邀请1名用户加入您的团队</p>
      <p style="font-size: 14px">被邀请用户需先完成比赛报名，且未加入任何团队</p>
      <p style="margin-bottom: 24px; font-size: 14px">当用户成功加入您的团队后，本链接即失效，您需再次复制，方可继续邀请其他用户</p>
    </confirm-modal>

    <modal v-model:open="setTeamVisible" title="创建团队" :spinning="spinning" :confirm-loading="rulesForm.teamName == '' || disabledCreateTeamBtn || rulesForm.teamName.length > 20" @cancel="setTeamVisible = false" @ok="createTeamInput">
      <a-form ref="ruleForm" :colon="false" class="form-content" :label-col="{ span: 4 }" :wrapper-col="{ span: 12 }" :model="rulesForm" :rules="rules">
        <a-form-item label="团队名称：" name="teamName">
          <a-input v-model:value="rulesForm.teamName" placeholder="请输入" style="width: 320px"></a-input>
        </a-form-item>
      </a-form>
    </modal>

    <modal v-model:open="joinTeamVisible" title="加入团队" ok-text="知道了" :show-cancel="false" @cancel="joinTeamVisible = false" @ok="joinTeamVisible = false"> 您可接受其他团队邀请，加入已有团队，请联系团队队长获取邀请链接 </modal>
    <!-- 转让队长 -->
    <modal v-model:open="revolveVisible" title="转让队长" :spinning="spinning" :confirm-loading="changeTeamLeaderValue === '' || disabledChangeLeaderBtn" ok-text="转让" @cancel="revolveVisible = false" @ok="changeLeaderHandleOk">
      <p class="tips" style="margin-bottom: 16px">请选择转让的队员，转让后，团队编辑、提交文件等操作权限将移交</p>
      <a-radio-group v-model:value="changeTeamLeaderValue" @change="changeLeaderOnChange">
        <a-radio v-for="item in teamList.slice(1)" :key="item.userId" :style="changeTeamLeaderRadioStyle" :value="item.userId"> {{ item.fullName }} </a-radio>
      </a-radio-group>
      <template #footer>
        <a-button>取消</a-button>
        <a-button type="primary" :disabled="changeTeamLeaderValue === ''">转让</a-button>
      </template>
    </modal>
    <!-- 删除队员 -->
    <confirm-modal v-model="deleteMemberVisible" :spinning="spinning" ok-text="移除" :title="`确定从团队中移除${memberSelf.fullName}吗？`" :confirm-loading="disabledBtn" :show-cancel="true" type="danger" @ok="deleteMemberHandleOk">
      <template #icon>
        <ExclamationCircleFilled style="font-size: 18px; color: #ff454d" />
      </template>
      <div class="dlg-body">
        <p class="modal-content">{{ isEcloudCompetition ? deleteMemberText.ecloud : deleteMemberText.biSheng }}</p>
        <a-form ref="ruleForm" :colon="false" class="form-content" :model="rulesForm" :rules="rules">
          <a-form-item label="" name="deleteMemberText">
            <a-input v-model:value="rulesForm.deleteMemberText" class="input-el-d" placeholder='请输入"确定移除"以确保您已知悉移除后的风险'></a-input>
          </a-form-item>
        </a-form>
      </div>
    </confirm-modal>
    <!-- 解散团队 -->
    <confirm-modal v-model="dismissTeamForm.dismissTeamVisible" :spinning="spinning" ok-text="解散" title="确定解散团队吗？" :confirm-loading="disabledBtn" :show-cancel="true" type="danger" @ok="dismissTeamHandleOk">
      <template #icon>
        <ExclamationCircleFilled style="font-size: 18px; color: #ff454d" />
      </template>
      <div class="dlg-body">
        <p class="modal-content">{{ dismissTeamForm.content }}</p>
        <a-form ref="ruleForm" :colon="false" class="form-content" :model="rulesForm" :rules="rules">
          <a-form-item label="" name="dismissText"> <a-input v-model:value="rulesForm.dismissText" class="input-el-d" placeholder='请输入"确定解散"以确保您已知悉解散后的风险'></a-input> </a-form-item>
        </a-form>
      </div>
    </confirm-modal>
    <!-- 
       ##复制邀请链接 
       current 显示当前第几条数据
       inviteVisible 是否弹出
    -->
    <invite-modal :current="inviteCurrent" :invite-visible="inviteVisible" :join-team-modal-content="joinTeamModalContent" @inviteOk="inviteOk" @inviteCancel="inviteVisible = false"></invite-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { EditOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import API from '@/constants/api/API.js';
import modal from '@/components/modal/modalSpin.vue';
import confirmModal from '@/components/confirmModal/index.vue';
import inviteModal from './inviteConfirm.vue';
import { dateConvertYearMonthDay, handleCopy } from '@/utils/utils';
import { ECLOUD_COMPETITION_TYPE_ID_LIST } from '@/common/ecloud';
import { COMPETITION_DETAIL_TABS, MYTEAM_JOIN_TEAM_MODAL_TYPE, MYTEAM_JOIN_TEAM_MODAL_TEXT } from '../competitionConfig/index';
import moment from 'moment';

const route = useRoute();
const router = useRouter();

const props = defineProps({
  activeKey: {
    type: String,
    default: '',
  },
  ecloudTypeId: {
    type: [Number, String],
    default: '',
  },
});

const loading = ref(false);
const spinning = ref(false);
const teamList = ref([]);
const teamBody = reactive({ teamNum: '', teamMaxNum: '' });
const setTeamVisible = ref(false);
const joinTeamVisible = ref(false);
const revolveVisible = ref(false);
const copyLinkVisible = ref(false);
const deleteMemberVisible = ref(false);
const changeTeamLeaderValue = ref('');
const changeTeamLeaderRadioStyle = { display: 'block', height: '30px', lineHeight: '30px' };
const teamNameInputValue = ref('');
const originTeamNameInputValue = ref('');
const teamNameInput = ref(false);
const dismissTeamForm = reactive({ content: '', dismissTeamVisible: false });
const rulesForm = reactive({ deleteMemberText: '', dismissText: '', teamName: '' });
const rules = {
  deleteMemberText: [{ message: '请输入"确定移除"以确保您已知悉移除后的风险', validator: deleteCheckText, trigger: ['blur', 'change'] }],
  dismissText: [{ message: '请输入"确定解散"以确保您已知悉解散后的风险', validator: dismissCheckText, trigger: ['blur', 'change'] }],
  teamName: [{ max: 20, min: 1, message: '20个字符以内', trigger: ['blur', 'change'] }],
};
const disabledBtn = ref(true);
const disabledCreateTeamBtn = ref(false);
const disabledChangeLeaderBtn = ref(false);
const inviteCurrent = ref(MYTEAM_JOIN_TEAM_MODAL_TYPE.NOT_OPEN);
const inviteVisible = ref(false);
const memberSelf = ref({});
const changeLeaderID = ref('');
const isTeamLeader = ref(false);
const copyLinkValue = ref('');
const isEnter = ref(false);
const editTimeBody = reactive({ editStartTime: '-', editEndTime: '-' });
const isEditTime = ref(true);
const isSubmitResultFiles = ref(false);
const description = reactive({
  title: '您暂未在任何团队中',
  text: '您必须创建自己的团队，或者加入已有团队，方可提交结果文件，参与排行榜',
});
const isEcloudCompetition = ref(true);
const deleteMemberText = reactive({
  ecloud: '移除后，队员将不在任何团队中，请谨慎操作',
  biSheng: `移除后，队员将不在任何团队中，队员运行中的"${route.query.name} "比赛实例将被强制停止，实例重启后将无法访问您的团队共享存储空间，请谨慎操作`,
});
const joinTeamModalContent = ref('');

const emptyImage = computed(() => {
  return route.query.keywords ? require('@/assets/image/empty2x.png') : require('@/assets/image/emptys2x.png');
});
const queryCompetitionId = computed(() => route.query.id);
const isEmptyTeamList = computed(() => teamList.value.length === 0);
const isTeamListEqualTeamMax = computed(() => teamList.value.length === teamBody.teamMaxNum);

watch(deleteMemberVisible, (val) => {
  if (val) {
    ruleForm.value && ruleForm.value.resetFields();
    rulesForm.deleteMemberText = '';
  }
});
watch(
  () => dismissTeamForm.dismissTeamVisible,
  () => {
    rulesForm.dismissText = '';
    disabledBtn.value = true;
  }
);
watch(setTeamVisible, () => {
  rulesForm.teamName = '';
  disabledCreateTeamBtn.value = false;
});
watch(
  () => props.activeKey,
  (val) => {
    if (val === COMPETITION_DETAIL_TABS.MY_TEAM) {
      getTeamDetail();
      teamNameInput.value = false;
      nowInDateBetwen();
    }
  }
);

const ruleForm = ref();

onMounted(() => {
  const { res, teamId } = route.query;
  if (teamId) {
    if (!res) {
      getTeamDetailById();
    } else {
      invitationJoinCheckTeam();
    }
  } else {
    getTeamDetail();
  }
  getTeamTimeConfig();
  isEcloudCompetition.value = ECLOUD_COMPETITION_TYPE_ID_LIST.includes(props.ecloudTypeId);
});

function getTeamDetailById(id) {
  const teamId = id || route.query.teamId;
  loading.value = true;
  API.competition_model.getByTeam({ teamId }).then((res) => {
    getTeamContent(res);
  });
}
function getTeamDetail() {
  loading.value = true;
  API.competition_model.getByMyTeam({ cid: queryCompetitionId.value }).then((res) => {
    getTeamContent(res);
  });
}
function getTeamContent(res) {
  if (res.state === 'OK') {
    const { members, teamName } = res.body;
    teamList.value = members;
    teamNameInputValue.value = teamName;
    originTeamNameInputValue.value = teamName;
    Object.assign(teamBody, res.body);
    checkLeader();
    dismissCheckTeamSubmit();
  } else {
    teamBody.teamNum = '';
    teamBody.teamMaxNum = '';
    teamList.value = [];
  }
  loading.value = false;
}
function getTeamTimeConfig() {
  API.competition_model.getTeamConfig({ cid: queryCompetitionId.value }).then((res) => {
    if (res.state === 'OK') {
      Object.assign(editTimeBody, res.body);
      isEditTime.value = nowInDateBetwen(res.body.editStartTime, res.body.editEndTime);
    }
  });
}
function checkLeader() {
  loading.value = true;
  API.competition_model.checkTeamLeader({ teamId: teamBody.teamId }).then((res) => {
    if (res.state === 'OK') {
      isTeamLeader.value = res.body;
      loading.value = false;
    }
  });
}
function setTeamNameBlur(tname) {
  if (originTeamNameInputValue.value == tname) {
    teamNameInput.value = false;
    return;
  }
  if (tname === '' || teamNameInputValue.value.trim() === '') {
    message?.error?.('请输入团队名称') || window.message?.error?.('请输入团队名称');
    return;
  }
  if (tname.length > 20) return;
  if (isEnter.value) {
    isEnter.value = false;
  }
  teamNameInput.value = false;
  const teamid = route.query.teamId || teamBody.teamId;
  const reqData = { teamid, teamname: teamNameInputValue.value.trim() };
  API.competition_model.updateTeamName(reqData).then((res) => {
    if (res.state === 'OK') {
      message?.success?.('修改成功') || window.message?.success?.('修改成功');
      originTeamNameInputValue.value = teamNameInputValue.value;
    } else {
      message?.error?.(res.errorMessage || '已有同名团队，请重新设置') || window.message?.error?.(res.errorMessage || '已有同名团队，请重新设置');
      teamNameInputValue.value = originTeamNameInputValue.value;
    }
  });
}
function setTeamNameEnter(event, tname) {
  if (originTeamNameInputValue.value == tname) {
    teamNameInput.value = false;
    return;
  }
  isEnter.value = true;
  event.target.blur(event);
}
function createTeamInput() {
  disabledCreateTeamBtn.value = true;
  spinning.value = true;
  API.competition_model.createTeam({ owner: queryCompetitionId.value, teamName: rulesForm.teamName }).then((res) => {
    if (res.state === 'OK') {
      message?.success?.('组建团队成功') || window.message?.success?.('组建团队成功');
      setTeamVisible.value = false;
      disabledCreateTeamBtn.value = false;
      getTeamDetailById(res.body.teamId);
    } else {
      message?.error?.(res.errorMessage || '已有同名团队，请重新设置') || window.message?.error?.(res.errorMessage || '已有同名团队，请重新设置');
      disabledCreateTeamBtn.value = false;
    }
    spinning.value = false;
  });
}
function changeLeaderHandleOk() {
  const reqData = { teamid: teamBody.teamId, userId: changeLeaderID.value };
  disabledChangeLeaderBtn.value = true;
  spinning.value = true;
  API.competition_model.changeLeader(reqData).then((res) => {
    if (res.state === 'OK') {
      revolveVisible.value = false;
      disabledChangeLeaderBtn.value = false;
      getTeamDetail();
    } else {
      message?.error?.(res.errorMessage) || window.message?.error?.(res.errorMessage);
    }
    spinning.value = false;
  });
}
function changeLeaderOnChange(e) {
  changeLeaderID.value = e.target.value;
}
function invitationJoinCheckTeam() {
  const { teamId, res } = route.query;
  const reqData = { teamId: teamId, code: res };
  API.competition_model.checkJoinTeamStatus(reqData).then((res) => {
    if (res.state === 'OK') {
      inviteCurrent.value = MYTEAM_JOIN_TEAM_MODAL_TYPE.INVITE_JOIN;
      inviteVisible.value = true;
      const { competitionName, leaderName, teamName } = res.body;
      if (competitionName === null) {
        joinTeamModalContent.value = `${leaderName}邀请您加入TA的团队"${teamName}"`;
      } else {
        joinTeamModalContent.value = `${leaderName}邀请您加入TA的团队"${teamName}"，确定加入后，您运行中的"${competitionName}"比赛实例将被停止，重启后可访问团队共享存储空间`;
      }
    } else {
      inviteCurrent.value = res.errorCode;
      if (res.errorCode === MYTEAM_JOIN_TEAM_MODAL_TYPE.REGISTERED) {
        joinTeamModalContent.value = `您已报名"${res.errorParams[0]}"，不可同时报名本比赛，无法加入团队`;
      }
      if (res.errorCode in MYTEAM_JOIN_TEAM_MODAL_TEXT) {
        inviteVisible.value = true;
      }
    }
    getTeamDetail();
  });
}
function invitationJoinTeam() {
  const { teamId, res } = route.query;
  const reqData = { teamId: teamId, code: res };
  API.competition_model.joinTeam(reqData).then((res) => {
    if (res.state === 'OK') {
      getTeamDetail();
    } else {
      if (res.errorCode === '-710') {
        message?.error?.(`" ${res.errorParams[0]} "团队人数已达上限，加入失败`) || window.message?.error?.(`" ${res.errorParams[0]} "团队人数已达上限，加入失败`);
      }
    }
  });
}
function inviteOk(val) {
  inviteVisible.value = false;
  const { id, name } = route.query;
  switch (val) {
    case '前往报名':
      router.push({ path: '/competition/competition-register', query: { id, name, typeId: props.ecloudTypeId } });
      break;
    case '确定加入':
      invitationJoinTeam();
      break;
    default:
      getTeamDetail();
  }
}
function copyLinkShow() {
  invitationLink();
  loading.value = true;
}
function copyHandleOk() {
  handleCopy(copyLinkValue.value);
  copyLinkVisible.value = false;
}
function invitationLink() {
  const reqData = { teamId: teamBody.teamId, baseUrl: location.origin };
  API.competition_model.invitationCode(reqData).then((res) => {
    if (res.state === 'OK') {
      const { cname, teamName, shortUrl } = res.body;
      copyLinkVisible.value = true;
      loading.value = false;
      copyLinkValue.value = `我正在九天·毕昇平台参加 ${cname}，快来加入我的团队"${teamName}"吧~  ${shortUrl}`;
    }
  });
}
function dismissTeam() {
  if (isSubmitResultFiles.value) {
    inviteVisible.value = true;
    inviteCurrent.value = MYTEAM_JOIN_TEAM_MODAL_TYPE.SUBMIT_RESULT_FILES;
    return;
  }
  dismissTeamForm.dismissTeamVisible = true;
  const isTeamMembersExceedOne = teamList.value.length > 1;
  let dismissTeamMessage = '';
  if (isEcloudCompetition.value) {
    dismissTeamMessage = isTeamMembersExceedOne ? '解散后不可恢复，您和您的队员都将离开团队，团队提交记录及团队共享存储空间将清空，请谨慎操作' : '解散后不可恢复，您将离开团队，团队提交记录及团队共享存储空间将清空，请谨慎操作';
  } else {
    const { name } = route.query;
    dismissTeamMessage = isTeamMembersExceedOne ? `解散后不可恢复，您和您的队员都将离开团队，您和所有队员运行中的"${name}"比赛实例将被强制停止，团队提交记录及团队共享存储空间将清空，请谨慎操作` : `解散后不可恢复，您将离开团队，您运行中的"${name}"比赛实例将被强制停止，团队提交记录及团队共享存储空间将清空，请谨慎操作`;
  }
  dismissTeamForm.content = dismissTeamMessage;
}
function dismissCheckText(rule, value, callback) {
  if (value === '确定解散') {
    disabledBtn.value = false;
    callback();
  } else {
    disabledBtn.value = true;
    callback(new Error());
  }
}
function dismissTeamHandleOk() {
  const reqData = { teamId: teamBody.teamId, teamName: teamBody.teamName };
  disabledBtn.value = true;
  spinning.value = true;
  API.competition_model.dismissTeam(reqData).then((res) => {
    if (res.state === 'OK') {
      disabledBtn.value = false;
      dismissTeamForm.dismissTeamVisible = false;
      getTeamDetail();
    }
    spinning.value = false;
  });
}
function dismissCheckTeamSubmit() {
  const reqData = { teamId: teamBody.teamId, teamName: teamBody.teamName };
  API.competition_model.checkDismissTeamSubmit(reqData).then((res) => {
    if (res.errorCode === MYTEAM_JOIN_TEAM_MODAL_TYPE.SUBMIT_RESULT_FILES) {
      isSubmitResultFiles.value = true;
    }
  });
}
function deleteVisible(item) {
  deleteMemberVisible.value = true;
  memberSelf.value = item;
}
function deleteMember() {
  const { teamId, userId } = memberSelf.value;
  const reqData = { teamid: teamId, userId };
  disabledBtn.value = true;
  spinning.value = true;
  API.competition_model.deleteMember(reqData).then((res) => {
    if (res.state === 'OK') {
      deleteMemberVisible.value = false;
      disabledBtn.value = false;
      getTeamDetail();
    }
    spinning.value = false;
  });
}
function deleteCheckText(rule, value, callback) {
  if (value === '确定移除') {
    disabledBtn.value = false;
    callback();
  } else {
    callback(new Error());
  }
}
function deleteMemberHandleOk() {
  ruleForm.value
    .validate()
    .then(() => {
      deleteMember();
    })
    .catch((err) => {
      throw new Error(err);
    });
}
function nowInDateBetwen(d1, d2) {
  const dateNow = moment().format('YYYY-M-DD HH:mm:ss');
  const beginDayDiff = moment(dateNow).isAfter(d1);
  const endDayDiff = moment(dateNow).isBefore(d2);
  if (endDayDiff && beginDayDiff) {
    return true;
  }
  return false;
}
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.tips {
  color: @jt-text-color-secondary;
}
.handle {
  justify-content: center;
  position: relative;
  bottom: 50px;

  .button {
    color: @jt-primary-color;
    border: 1px solid @jt-primary-color;
  }
}
.team-wrap {
  padding-top: 40px;

  .team-name {
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: @jt-title-color;
    line-height: 25px;
    span {
      color: @jt-text-color-primary;
      font-size: @jt-font-size-lger;
    }
    .team-name-input {
      width: 300px;
      border: 0;
      border-bottom: 1px solid #bec2c5;
      font-size: @jt-font-size-lger;
      font-weight: @jt-font-weight-medium;
      padding-left: 0 !important;

      &::-webkit-input-placeholder {
        font-size: @jt-font-size-base;
        text-align: left;
        margin-left: -50px;
      }
    }
    .team-name-input-bottom-red {
      border-bottom: 1px solid red;
    }

    :deep(.ant-input:focus) {
      box-shadow: 0px 0px 0px @jt-color-white;
    }
  }

  .team-handle {
    justify-content: space-between;
    line-height: 32px;
    margin: 8px 0 13px 0;
    font-size: @jt-font-size-base;
    color: @jt-text-color;
    font-weight: @jt-font-weight;

    .primary-color-button {
      color: @jt-primary-color;
      border: 1px solid @jt-primary-color;

      &:hover {
        background-color: @jt-primary-color;
        color: @jt-color-white;
      }
    }
  }
  .editIcon {
    margin-left: @jt-font-size-lger;
    & :hover {
      color: @jt-primary-color;
    }
  }
  .no-editIcon {
    margin-left: @jt-font-size-lger;
    color: @jt-disable-color;

    & :hover {
      color: @jt-disable-color;
    }
  }
}

.team-list {
  margin-bottom: 32px;

  .member,
  .nomermber {
    width: 214px;
    height: 144px;
    border-radius: @jt-border-radius;
    background: url('../../../assets/image/rectangle-bg2x.png') center no-repeat;
    position: relative;
    align-items: center;
    flex-direction: column;
    margin-right: 16px;

    &:nth-last-child(1) {
      margin-right: 0px;
    }

    .tips {
      width: 48px;
      height: 22px;
      background: linear-gradient(225deg, #f9b34c 0%, #ff7b00 100%);
      border-radius: 2px 0px 0px 100px;
      color: @jt-color-white;
      padding-left: 17px;
      font-size: @jt-font-size-sm;
      line-height: 22px;
      position: absolute;
      right: 0;
      top: 0;
    }

    .avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      margin-top: 28px;
      margin-bottom: 16px;
      background-size: cover;

      .member-avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
      }
    }

    .card-name {
      font-weight: @jt-font-weight-medium;
      color: @jt-title-color;
      margin-bottom: 4px;
    }
    .card-id {
      color: @jt-text-color;
      font-size: @jt-font-size-base;
    }
  }
  .member {
    &:hover {
      box-shadow: 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 6px 16px -8px rgba(0, 0, 0, 0.08);
    }
  }

  .not-member {
    background: #f9fafb !important;
    .not-avatar {
      margin-top: 40px;
      background: url('../../../assets/image/no-avatar.png') center no-repeat;
      background-size: cover;
    }

    .invite-member {
      color: @jt-text-color-secondary;
    }
  }
}

.member:hover .delete {
  display: block;
}

.delete {
  position: absolute;
  font-size: @jt-font-size-lger;
  cursor: pointer;
  top: 6px;
  right: 12px;
  display: none;
  color: @jt-text-color;
  &:hover {
    color: @jt-primary-color;
  }

  .no-delete {
    color: @jt-text-color;
  }
}
.team-handle-btn {
  margin-bottom: 8px;
}
.mgr8 {
  margin-right: 8px;
}
.flex {
  display: flex;
}

.modal-content {
  margin-bottom: 28px;
  font-size: @jt-font-size-base;
}

.input-el {
  border: none;
  border-bottom: 1px solid;
  &:focus {
    box-shadow: none;
  }
}
.input-el-d {
  border: none;
  border-bottom: 1px solid @jt-primary-color;
  padding-left: 0px;

  &:focus {
    box-shadow: none;
  }
}

.copy-link {
  background-color: @jt-color-white;
  margin-bottom: 24px;
  font-size: @jt-font-size-base;
  border: 0px;
  width: 100%;
  height: 103px;
  resize: none;
}

:deep(.empty-title) {
  margin-bottom: 8px;
}
:deep(.ant-form-item) {
  margin-bottom: 0;
}

.form-content {
  margin-bottom: 23px;
}
</style>

<style lang="less">
.join-dis-title .ant-tooltip-inner {
  width: 262px;
}
</style>
