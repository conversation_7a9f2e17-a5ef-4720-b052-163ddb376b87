<template>
  <div class="course">
    <course-banner></course-banner>
    <section class="content-box">
      <learn-map></learn-map>
    </section>
    <section class="content-box">
      <recommend-course></recommend-course>
    </section>
    <section class="content-box">
      <course-zone></course-zone>
    </section>
    <section class="content-box">
      <teachers></teachers>
    </section>
    <section class="content-box">
      <guide></guide>
    </section>
  </div>
</template>

<script>
import CourseBanner from './course-component/course-banner.vue';
import recommendCourse from './course-component/recommend-course.vue';
import courseZone from './course-component/course-zone.vue';
import teachers from './course-component/teachers.vue';
import guide from './course-component/guide.vue';
import learnMap from './course-component/learn-map.vue';

export default {
  components: {
    learnMap,
    CourseBanner,
    recommendCourse,
    courseZone,
    teachers,
    guide,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
@import './course-component/common.less';
</style>
