import { GET, POST } from '../request';

// 课程资源
export const getCourseExt = (data) => POST('/course_model/web/course_student/courseExt/courseExt', data);

// 查询学生课程关键字
export const getCourseStudentFindname = (data = {}) => POST('/course_model/web/course_student/course/courseStudentFindname', data);

// 获取莫烦Python
export const getListPython = (data) => POST('/course_model/web/course_student/course/istPython', data);

// ai地图查询
export const getCourseMapList = (data) => POST('/course_model/web/course_student/course/mapCourseList', data);

// 查询课程专区
export const getCoursePrefectureList = (data) => POST('/course_model/web/course_student/course/coursePrefectureList', data);

// 查询课节
export const getCatalogList = (data) => GET('/course_model/web/course_student/resourse/catalogList', data);

// 加入课程
export const studentAdd = (data) => POST('/course_model/web/course_student/student/studentAdd', data);

// 添加课节（开始学习）
export const studentCatalogAdd = (data) => POST('/course_model/web/course_student/student/studentCatalogAdd', data);

// 删除学生课节
export const studentCatalogDelete = (data) => POST('/course_model/web/course_student/student/studentCatalogDelete', data);

// 查询学生课节
export const studentCatalogList = (data) => POST('/course_model/web/course_student/studentCatalog/studentCatalogList', data);

// 学生已经学习课节个数
export const studentStudy = (data) => POST('/course_model/web/course_student/studentCatalog/studentStudy', data);

// 推荐课程
export const getCourseRecommend = (data) => GET('/course_model/web/course_student/course/courseRecommend', data);

// 根据id查询课程
export const courseFindByCourseId = (data) => GET('/course_model/web/course_student/course/courseFindByCourseId', data);

// 查询机构信息
export const getInstituteInfo = (data) => GET('/course_model/web/course_student/course/courseFindByInstituteId', data);

// 获取希冀课程信息列表
export const getSchoolCourseList = (data) => GET('/course_model/web/cg_proxy/courses', data);
