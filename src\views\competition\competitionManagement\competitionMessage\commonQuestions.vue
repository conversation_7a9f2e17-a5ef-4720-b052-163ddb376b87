<template>
  <div class="competition-common-questions">
    <jt-common-content :loading="loading" :empty="!commonQuestionsInfo.question" empty-title="您暂未设置常见问题">
      <template #empty-operation>
        <p class="to-edit-page" @click="toEditPage">编辑常见问题</p>
      </template>
      <html-viewer :text-url="commonQuestionsInfo.question" />
    </jt-common-content>
  </div>
</template>

<script>
import htmlViewer from '../components/htmlViewer.vue';
import { competitionApi } from '@/apis/index';
export default {
  name: 'CommonQuestions',
  components: {
    htmlViewer,
  },
  emits: ['toEdit'],
  data() {
    return {
      commonQuestionsInfo: {},
      loading: false,
    };
  },
  mounted() {
    this.initCommonQuestionIntro();
  },
  methods: {
    async initCommonQuestionIntro() {
      this.loading = true;
      const cid = this.$route.params.competitionId;
      const res = await competitionApi.getCompetitionCommonQuestion({ cid });
      if (res.state === 'OK' && res.body && res.body.length > 0) {
        this.commonQuestionsInfo = res.body[0];
      }
      this.loading = false;
    },
    toEditPage() {
      this.$emit('toEdit');
    },
  },
};
</script>

<style lang="less" scoped>
.to-edit-page {
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  color: #0082ff;
}
</style>
