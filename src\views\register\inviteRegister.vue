<template>
  <div v-if="!isOnH5" class="register-container">
    <register-header :show-back-to-home="!(marketingFeature && inviteLimitFeature)"></register-header>
    <div class="content-wrap">
      <a-spin :spinning="availableChecking">
        <div v-if="marketingFeature && inviteLimitFeature" class="content">
          <div class="main-content">
            <h1 class="title">欢迎注册九天·毕昇</h1>
            <div class="prompt">
              <p>
                <span>您的好友</span>
                <span class="inviter-name"> {{ inviterName }} </span>
                <span>正在参加“邀请注册赢算力”活动，</span>
              </p>
              <p>
                <span>立即注册赢取</span>
                <span class="count">{{ receiveCount }}</span>
                <span>个算力豆，并助TA赢取</span>
                <span class="count">{{ assistCount }}</span>
                <span>个算力豆</span>
              </p>
            </div>
            <div class="form-wrap">
              <register-form :ecloud-register-bean-bount="ecloudRegisterBeanBount" :activity-available="activityAvailable" @onSubmit="submit"></register-form>
            </div>
          </div>
          <a-divider class="divider" :dashed="true"></a-divider>
          <footer-prompt :activity-available="activityAvailable" :receive-count="receiveCount" :period="period" :assist-count="assistCount"></footer-prompt>
        </div>
        <div v-else class="content content-empty">
          <div class="empty-tooltip">
            <img src="@/assets/image/emptys2x.png" alt="" />
            <p class="tooltip-txt">抱歉，邀请注册{{ marketingFeature ? '链接已失效' : '赢算力活动暂未开放' }}</p>
          </div>
        </div>
      </a-spin>
    </div>
    <a-modal v-model:open="verifyVisible" width="400px" dialog-class="verify-dialog" :body-style="{ padding: 0, height: '388px' }" :footer="null" :mask-closable="false">
      <rotate-captch ref="rotateVerify" :img-url="verifyImgUrl" :result="verifyResult" @onSliding="onSliding" @end="onEnd"></rotate-captch>
    </a-modal>
  </div>
</template>

<script>
import registerHeader from './components/header.vue';
import registerForm from './components/form.vue';
import footerPrompt from './components/footer.vue';
import { axiosWithNoToken } from '@/request';
import { getKeycloakUrl } from '@/keycloak';
import { BrowserDetect } from '@/utils/browserdetect';
import rotateCaptch from '@/components/rotateCaptcha.vue';

export default {
  name: 'InviteRegister',
  components: {
    registerHeader,
    registerForm,
    footerPrompt,
    rotateCaptch,
  },
  data() {
    return {
      inviterName: '',
      receiveCount: 0,
      assistCount: 0,
      inviterToken: '',
      marketingFeature: false,
      inviteLimitFeature: false,
      period: 0,
      isOnH5: true,
      activityAvailable: false,
      ecloudRegisterBeanBount: 0,
      verifyVisible: false,
      verifyImgUrl: '',
      verifyResult: undefined,
      verifyId: '',
      angle: undefined,
      needVerify: false,
      refreshTimmer: null,
      formData: {},
      eCloudRegistered: false,
      availableCheckCount: 2, //2表示有两个查询需要loading
    };
  },
  computed: {
    availableChecking() {
      return this.availableCheckCount > 0;
    },
  },
  created() {
    this.checkRegisterAvailable();
    this.checkH5();
    this.getEcloudRegisterBeanMessage();
  },
  mounted() {
    this.inviterToken = this.$route.query.token;
    this.getInvitationFeature();
    this.getInviteLimitFeature();
    this.getInviteeBeanMessage();
    this.getInviterName();
    this.getActivityStatus();
  },
  unmounted() {
    this.clearRefreshTimmer();
  },
  methods: {
    getEcloudRegisterBeanMessage() {
      axiosWithNoToken('/marketing/web/ecloud/getRegisterBeanMessage').then((res) => {
        if (res.data.state === 'OK') {
          this.ecloudRegisterBeanBount = (res.data.body && res.data.body.ecloudRegisterBeanBount) || 0;
        }
      });
    },
    checkRegisterAvailable() {
      const keycloakUrl = getKeycloakUrl();
      axiosWithNoToken('/keycloak/web/user/registAllow').then((res) => {
        if (res.data.state === 'OK' && !res.data.body) {
          window.location.replace(`${keycloakUrl}/realms/TechnicalMiddlePlatform/login-actions/registration`);
        }
      });
    },
    getActivityStatus() {
      axiosWithNoToken('/marketing/web/ecloud/activityStatus').then((res) => {
        if (res.data.state === 'OK') {
          this.activityAvailable = res.data.body;
        }
      });
    },
    postData() {
      const url = this.formData.registEcloud && !this.eCloudRegistered ? '/marketing/web/ecloud/inviteeRegistration' : '/marketing/web/inviteeRegistration';
      const params = {
        ...this.formData,
        registEcloud: +this.formData.registEcloud,
        token: this.inviterToken,
        angle: this.angle !== undefined ? parseInt(this.angle) : undefined,
        uuid: this.verifyId,
      };
      axiosWithNoToken.post(url, params).then((res) => {
        if (res.data.state === 'OK') {
          if (this.needVerify) {
            this.verifyResult = '正确';
            setTimeout(() => {
              this.onSubmited();
            }, 1000);
          } else {
            this.onSubmited();
          }
        } else {
          // 需要图形验证码
          if (res.data.errorCode === '1121') {
            this.needVerify = true;
            this.openVerify();
            return;
          }
          // 图形验证码错误
          if (res.data.errorCode === '-702') {
            this.verifyResult = '错误';
            this.needVerify = true;
            setTimeout(() => {
              this.openVerify();
            }, 1000);
            return;
          }
          // 图形验证码过期
          if (res.data.errorCode === '-701') {
            this.verifyResult = '已过期';
            this.needVerify = true;
            setTimeout(() => {
              this.openVerify();
            }, 1000);
            return;
          }
          this.verifyVisible = false;
          if (res.data.errorCode === '1111') {
            this.$message.error('移动云账号注册成功，毕昇平台账号注册失败，算力豆未发放，请稍后重试');
            return;
          }
          this.$message.error(res.data.errorMessage || '注册失败');
        }
      });
    },
    submit(obj, eCloudRegistered) {
      this.formData = obj;
      this.eCloudRegistered = eCloudRegistered;
      this.postData();
    },
    onSubmited() {
      const successMessage = this.formData.registEcloud ? '毕昇平台账号及移动云账号注册成功，算力豆已发放' : '注册成功';
      this.$message.success(successMessage);
      window.location.replace(`${window.location.origin}${window.location.pathname}#/home`);
    },
    getInviterName() {
      axiosWithNoToken('/marketing/web/getInviterName', { params: { token: this.inviterToken } }).then((res) => {
        this.inviterName = res.data.body;
      });
    },
    getInvitationFeature() {
      axiosWithNoToken('/marketing/web/getInvitationFeature', {}).then((res) => {
        if (res.data.state === 'OK') {
          this.marketingFeature = res.data.body;
        } else {
          this.marketingFeature = false;
        }
        this.availableCheckCount--;
      });
    },
    getInviteLimitFeature() {
      axiosWithNoToken('/marketing/web/getInvitationUrlStatus', { params: { token: this.inviterToken } }).then((res) => {
        if (res.data.state === 'OK') {
          this.inviteLimitFeature = res.data.body;
        } else {
          this.inviteLimitFeature = false;
        }
        this.availableCheckCount--;
      });
    },
    getInviteeBeanMessage() {
      axiosWithNoToken('/marketing/web/getInviteeBeanMessage', {}).then((res) => {
        if (res.data.state === 'OK') {
          const { inviteeBeanCount, inviterBeanCount, period } = res.data.body;
          this.receiveCount = inviteeBeanCount;
          this.assistCount = inviterBeanCount;
          this.period = period;
        }
      });
    },
    checkH5() {
      const isOnH5 = BrowserDetect.onH5();
      this.isOnH5 = isOnH5;
      if (isOnH5) {
        this.$router.replace({ path: '/h5/inviteregister', query: { token: this.$route.query.token } });
        return;
      }
    },
    openVerify() {
      this.verifyVisible = true;
      this.$nextTick(() => {
        this.refreshVerify();
        this.setRefreshTimmer();
      });
    },
    onEnd(angle) {
      this.angle = angle;
      this.verifyResult = '验证中';
      this.postData();
    },
    refreshVerify() {
      this.verifyResult = undefined;
      this.getVerifyInfo();
      this.$refs.rotateVerify.resetSlider();
    },
    getVerifyInfo() {
      axiosWithNoToken('/messaging/web/sendImageCaptcha').then((res) => {
        if (res.data.state === 'OK') {
          this.verifyImgUrl = res.data.body.imageBase64;
          this.verifyId = res.data.body.uuid;
        } else {
          this.$message.error(res.data.errorMessage || '获取图片验证码失败，请稍后重试');
        }
      });
    },
    setRefreshTimmer() {
      if (this.refreshTimmer) {
        this.clearRefreshTimmer();
      }
      this.refreshTimmer = setInterval(() => {
        if (this.verifyVisible) {
          this.refreshVerify();
        } else {
          this.clearRefreshTimmer();
        }
      }, 1000 * 60);
    },
    clearRefreshTimmer() {
      clearInterval(this.refreshTimmer);
      this.refreshTimmer = null;
    },
    onSliding() {
      // 如果开始拖动了，要把定时器给停掉
      this.clearRefreshTimmer();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.register-container {
  background: #f4f8fa;
  min-height: 100vh;
  .content-wrap {
    min-height: calc(100vh - 100px);
    .content {
      width: 1200px;
      background: #fff;
      margin: 0 auto;
      padding-bottom: 20px;
      margin-top: 20px;
      overflow: hidden;
    }
    .content-empty {
      height: calc(100vh - 140px);
      position: relative;
      .empty-tooltip {
        position: absolute;
        left: 50%;
        margin-left: -208px;
        top: 50%;
        margin-top: -208px;
        width: 416px;
        height: 416px;
        .tooltip-txt {
          position: absolute;
          bottom: 96px;
          text-align: center;
          width: 100%;
          font-weight: 600;
          color: #121f2c;
          font-size: 18px;
        }
      }
    }
  }
}
.main-content {
  width: 480px;
  margin: 0 auto;
  margin-top: 64px;
  margin-bottom: 48px;
  .title {
    font-size: 34px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    margin: 0 auto;
    margin-bottom: 20px;
    text-align: center;
    width: 384px;
  }
  .prompt {
    font-size: 14px;
    font-weight: 400;
    color: #606972;
    margin: 0 auto;
    margin-bottom: 40px;
    text-align: center;
    width: 384px;
  }
  .form-wrap {
    position: relative;
    left: -60px;
  }
}

.count {
  color: #ff8c00;
}
.inviter-name {
  font-size: 14px;
  font-weight: 400;
  color: #606972;
  line-height: 20px;
}
.divider {
  min-width: 1080px;
  width: 1080px;
  margin-left: auto;
  margin-right: auto;
  border-color: #cbcfd2;
}

:deep(.verify-dialog .ant-modal-content) {
  border-radius: 12px;
}

:deep(.ant-spin-nested-loading > div > .ant-spin) {
  max-height: inherit;
}
</style>
