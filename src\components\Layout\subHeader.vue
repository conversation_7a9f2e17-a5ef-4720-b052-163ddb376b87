<template>
  <div class="sub-header-container">
    <div class="sub">
      <span class="title">{{ title }}</span>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
  },
};
</script>

<style lang="less" scoped>
div {
  display: flex;
}
.sub-header-container {
  flex-direction: column;
  align-items: center;
  width: 100%;
  background: #fff;
  .sub {
    width: 1200px;
    height: 56px;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 24px;
    }
  }
}
</style>
