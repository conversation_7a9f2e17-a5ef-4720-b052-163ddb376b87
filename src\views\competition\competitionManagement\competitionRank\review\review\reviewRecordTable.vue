<script setup>
import { FilterFilled } from '@ant-design/icons-vue';
</script>
<template>
  <section>
    <headTitle title="提交记录">
      <div class="subtitle">
        <search v-model="formData.searchValue" placeholder="团队名称/提交人用户名/姓名" class="search-input" @handSearch="handSearch" />
        <a-button class="button" type="primary" ghost :disabled="tableData.length == 0" @click="downloadFileWithToken({ url: `/competiton/web/manage/edit/replyList/export?cid=${cid}` })">
          <jt-icon type="icondaochu2"></jt-icon>
          导出记录
        </a-button>
      </div>
    </headTitle>
    <a-config-provider>
      <template #renderEmpty>
        <jt-common-content :empty="true" :loading="loading" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text"> </jt-common-content>
      </template>
      <a-table class="table" size="middle" :loading="tableData.length > 0 ? loading : undefined" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" row-key="id" :columns="columns" :data-source="tableData" :pagination="false" @change="tableChange">
        <!-- 根据接口返回自动生成的title -->
        <template v-for="x in columns.filter((z) => z.isAjaxRow)" :key="x.dataIndex" #[x.slots.title]>
          <p :title="x.dataIndex" :style="ajaxTitleStyle">{{ x.titleName }}</p>
        </template>

        <!-- 提交状态 -->
        <template #filterDropdownIcon>
          <FilterFilled :style="{ color: formData.submitStatus != '' ? '#108ee9' : undefined, right: 'auto' }" />
        </template>
        <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm }">
          <div class="filter-status-select">
            <div class="all" :class="{ select: formData.submitStatus == '' }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, '')">全部类型</div>
            <div class="already-study" :class="{ select: formData.submitStatus == 1 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 1)">{{ getReviewRecordStatusObj(1) }}</div>
            <div class="old-study" :class="{ select: formData.submitStatus == 4 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 4)">{{ getReviewRecordStatusObj(4) }}</div>
            <div class="un-study" :class="{ select: formData.submitStatus == 2 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 2)">{{ getReviewRecordStatusObj(2) }}</div>
          </div>
        </template>
        <template #submitstatus-slot="{ status }">
          <a-space>
            <div class="status-icon" :class="status == 1 ? 'green' : status == 2 || status == null ? 'orange' : 'blue'"></div>
            {{ getReviewRecordStatusObj(status) }}
          </a-space>
        </template>
        <!-- 提交状态 结束 -->
        <!-- 操作 -->
        <template #options-slot="{ record }">
          <a-button v-if="record.submitstatus == 1" type="link" class="single-download-button" @click="getCptReplyGetUrl(record.id, record.submitstatus)"> 下载文件 </a-button>
          <span v-else>--</span>
        </template>
        <!-- 操作 结束 -->
      </a-table>
    </a-config-provider>
    <div class="jt-pagination">
      <a-space size="large">
        <a-button class="button download-button" :disabled="selectedRowKeys.length == 0" @click="dowload"> 批量下载 </a-button>
        <span>共{{ page.total }}条记录</span>
        <span>
          每页显示
          <a-select default-value="10" style="width: 65px" @change="changePageSize">
            <a-select-option value="5"> 5 </a-select-option>
            <a-select-option value="10"> 10 </a-select-option>
            <a-select-option value="15"> 15 </a-select-option>
            <a-select-option value="20"> 20 </a-select-option>
          </a-select>
          条
        </span>
      </a-space>
      <a-pagination :page-size="page.pageSize" show-quick-jumper :default-current="1" :total="page.total" @change="changePageNum" />
    </div>
  </section>
</template>

<script>
const columns = [
  {
    dataIndex: 'teamName',
    key: 'teamName',
    title: '团队名称',
    ellipsis: true,
    customRender: ({ text, record, index }) => {
      return text || '--';
    },
  },
  {
    dataIndex: 'userName',
    key: 'userName',
    title: '提交人用户名',
    ellipsis: true,
    customRender: ({ text, record, index }) => {
      return text || '--';
    },
  },
  {
    dataIndex: 'fullName',
    key: 'fullName',
    title: '提交人姓名',
    ellipsis: true,
    customRender: ({ text, record, index }) => {
      return text || '--';
    },
  },
  {
    dataIndex: 'fileName',
    key: 'fileName',
    title: '提交目录 /文件名称',
    width: 200,
    customRender: ({ text, record, index }) => {
      return text || '--';
    },
  },

  {
    title: '提交状态',
    dataIndex: 'submitstatus',
    key: 'submitstatus',
    slots: {
      filterIcon: 'filterDropdownIcon',
      filterDropdown: 'filterDropdown',
      customRender: 'submitstatus-slot',
    },
  },
  {
    dataIndex: 'createtime',
    key: 'createtime',
    title: '提交时间',
    sorter: true,
    customRender: ({ text, record, index }) => {
      return text || '--';
    },
  },
  {
    title: '操作',
    key: 'options',
    dataIndex: 'options',
    slots: { customRender: 'options-slot' },
  },
];

import headTitle from '@/components/headTitle';
import search from '@/components/search';
import API from '@/constants/api/API';
import { setTableDataByRow } from '@/views/competition/competitionManagement/competitionRank/utils';
import { getReviewRecordStatusObj, ajaxTitleStyle } from '@/views/competition/competitionManagement/competitionRank/constants';
import { downloadFileWithToken } from '@/utils/file';

export default {
  components: {
    headTitle,
    search,
  },
  data() {
    const cid = this.$route.params.competitionId;
    return {
      cid,
      ajaxTitleStyle,
      noSearchResultEmptyImage: require('@/assets/image/empty2x.png'),
      tableData: [],
      columns,
      filter: false,
      loading: false,
      selectedRowKeys: [],
      formData: {
        keyWord: '',
        submitStatus: '',
        createtimeFlag: '',
      },
      page: {
        pageNum: 1,
        total: 0,
        pageSize: 10,
      },
    };
  },
  computed: {
    tableEmpty() {
      return this.tableData.length === 0;
    },
    emptyStatus() {
      if (this.formData.keyWord || this.formData.submitStatus) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关提交记录',
          text: '您可以换一个关键词试试哦～',
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '暂无提交记录',
          text: '',
        };
      }
    },
  },
  created() {
    this.getTableData();
  },
  methods: {
    downloadFileWithToken,
    getReviewRecordStatusObj,
    getTableData() {
      const params = { cid: this.cid, ...this.formData, ...this.page };
      this.loading = true;
      return new Promise((resolve) => {
        API.competition_model.getFileReplyList(params).then((res) => {
          this.loading = false;
          if (res.state === 'OK') {
            this.tableData = res.body?.list || [];
            setTableDataByRow(this.tableData);
            this.page.total = res.body?.total || 0;
            resolve(res.body);
          }
        });
      });
    },
    handSearch(val) {
      this.formData.keyWord = val;
      this.getTableData();
    },
    handleSelectClick(setSelectedKeys, selectedKeys, confirmCallBack, index) {
      confirmCallBack();
      this.formData.submitStatus = index;
      this.page.pageNum = 1;
      this.getTableData();
    },
    changePageSize(size) {
      this.page.pageSize = Number(size);
      this.page.pageNum = 1;
      this.getTableData();
    },
    changePageNum(num) {
      this.page.pageNum = num;
      this.getTableData();
    },
    tableChange(pagination, filters, sorter) {
      this.formData[sorter.columnKey + 'Flag'] = sorter.order === 'ascend' ? '0' : '1';
      this.getTableData();
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    // 获取答辩材料下载地址接口
    getCptReplyGetUrl(id, submitstatus) {
      if (submitstatus != 1) return;
      API.competition_model.getManageReplyUrl({ id }).then((res) => {
        if (res.state === 'OK' && res.body) {
          window.location = res.body.downloadUrl;
        }
      });
    },
    dowload() {
      downloadFileWithToken({
        url: '/competiton/web/manage/edit/reply/file/export',
        method: 'post',
        data: {
          cid: this.cid,
          ids: this.selectedRowKeys,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import '~@/views/competition/competitionManagement/competitionRank/table.less';
section {
  margin-top: 43px;
  padding-bottom: 48px;
}
.subtitle {
  display: flex;
}
.search-input {
  width: 240px;
  margin-right: 9px;
}
.button {
  width: 108px;
}
.download-button {
  width: 88px;
}
.table {
  margin-top: 16px;
}
.single-download-button {
  padding: 0;
}
</style>
