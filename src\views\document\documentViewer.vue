<template>
  <div class="document-viewer-container">
    <div class="content">
      <h1 class="title">{{ document.pageName }}</h1>
      <p class="update-time">更新时间：{{ document.updateTime }}</p>
      <div class="w-e-text">
        <!-- eslint-disable vue/no-v-html -->
        <div class="document-content" v-html="document.pageContent"></div>
      </div>
    </div>
    <div class="anchor-wrap">
      <a-anchor class="anchor" :items="items" @click="handleClick"></a-anchor>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DocumentViewer',
  props: {
    document: {
      type: Object,
      required: true,
    },
    titles: {
      type: Array,
      required: true,
    },
  },
  computed: {
    items() {
      return this.titles.map((item) => ({
        key: item.id,
        href: `#${item.id}`,
        title: item.text,
      }));
    },
  },
  methods: {
    handleClick(event) {
      event.preventDefault();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.document-viewer-container {
  display: flex;
}
.content {
  flex: 1;
}
.title {
  margin: 24px 0 16px 0;
  font-size: 30px;
  font-weight: @jt-font-weight-medium;
  color: #121f2c;
}
.update-time {
  margin-bottom: 40px;
  font-size: 14px;
  font-weight: 400;
  color: #a0a6ab;
}
.document-content {
  min-height: 520px;
}
.anchor-wrap {
  .anchor {
    position: fixed;
  }
}
:deep(.ant-anchor-wrapper) {
  background: transparent;
}
</style>
