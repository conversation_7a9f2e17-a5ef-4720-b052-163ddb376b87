<template>
  <div class="competition-detail-box">
    <div class="competition-detail-left">
      <img class="competition-img" :src="item.imageUrl" alt />
      <div class="competition-detail-content">
        <p class="competition-name">
          <span class="title">{{ item.typeName }} </span>
          <jt-tag class="competition-tag" :type="getTagClass(item.flag)">
            {{ runningStatusMaps[item.flag] }}
          </jt-tag>
          <jt-tag type="normal" class="competition-tag tag-s0">{{ competitionTypeMaps[item.typeId] }}</jt-tag>
        </p>
        <p class="competition-introduce">{{ item.summary }}</p>
        <div class="competition-label-box">
          <span class="tags-label">标签 : </span>
          <span class="tags-label-text">{{ item.tag }}</span>
          <span class="tags-label">时间 : </span>
          <span class="tags-label-text">{{ dateConvert(item.startTime) }} - {{ dateConvert(item.endTime) }}</span>
          <span>举办方 : &nbsp;</span>
          <div v-cloak v-for="(items, indexs) in item.leader" :key="indexs" class="organizers-box">
            <img class="organizers-img" :src="items.imageUrl" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div class="competition-detail-right">
      <div class="competition-reward-pool" v-if="item.amount !== '特别礼包'">
        奖池<br />
        ¥{{ toThousandFilter(item.amount) }}
      </div>
      <div v-else class="competition-reward-pool">奖池 特别礼包</div>
      <div class="competition-join-num">{{ item.number }}人参赛</div>
    </div>
  </div>
</template>

<script>
import JtTag from '@/components/tag/index.vue';
import { dateConvert, toThousandFilter } from '@/utils/utils.js';
import { runningStatusMaps, competitionTypeMaps } from '@/views/competition/competitionConfig/index';
export default {
  name: 'competition-list',
  components: {
    JtTag,
  },
  props: {
    item: {
      type: Object,
    },
  },
  data() {
    return {
      runningStatusMaps,
      competitionTypeMaps,
    };
  },
  methods: {
    dateConvert,
    toThousandFilter,
    getTagClass(flag) {
      return flag == 1 ? 'tobegin' : flag == 2 ? 'running' : 'end';
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.competition-detail-box {
  display: flex;
  justify-content: space-between;
  // max-width: 1200px;
  background: @jt-color-white;
  border-bottom: 1px solid @jt-line-color;
  border-radius: @jt-border-radius;
  padding: 32px;
  cursor: pointer;

  &:hover {
    background: #f8f9fa;
    transition: 0.3s;
  }
}

.competition-detail-left {
  display: flex;

  .competition-img {
    width: 128px;
    height: 128px;
    flex-shrink: 0;
    margin-right: 24px;
  }

  .competition-detail-content {
    .competition-name {
      display: flex;
      margin-bottom: 14px;
      margin-right: 8px;
      font-size: 20px;
      font-weight: @jt-font-weight-medium;
      color: @jt-text-color-primary;
      cursor: pointer;
      &:hover {
        color: @jt-primary-color;
        transition: 0.3s;
      }
      .title {
        padding-right: 8px;
        word-break: break-all;
      }
      .competition-tag {
        margin-top: 2px;
      }
    }

    .competition-introduce {
      height: 22px;
      font-size: @jt-font-size-base;
      font-weight: @jt-font-weight;
      color: @jt-text-color;
      line-height: 22px;
      margin-bottom: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 786px;
    }

    .competition-label-box {
      font-size: @jt-font-size-base;
      color: @jt-text-color-secondary;
      line-height: 20px;
      word-break: break-all;
      white-space: normal;
    }
  }
}

.competition-detail-right {
  width: 177px;
  min-width: 177px;

  .competition-reward-pool {
    text-align: right;
    font-size: 24px;
    font-weight: @jt-font-weight-medium;
    color: #ff4945;
    line-height: 33px;
    margin-bottom: 8px;
  }

  .competition-join-num {
    height: 22px;
    font-size: @jt-font-size-lg;
    color: @jt-text-color;
    line-height: 18px;
    text-align: right;
  }
}

.tags-label {
  padding-right: 5px;
}

.tags-label-text {
  color: @jt-text-color;
  padding-right: 15px;
}

.organizers-box {
  margin-left: 5px;
  display: inline-block;

  .organizers-img {
    height: 23px;
    margin-right: 13px;
    object-fit: contain;
  }
}
.tag-s0 {
  margin: 0 8px 0;
}
</style>
