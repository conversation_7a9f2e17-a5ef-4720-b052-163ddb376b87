import store from '../store';

export function getSessionKey(forceUpdate) {
  const sessionKey = store.state.systemNotice.sessionKey;
  if (sessionKey && !forceUpdate) {
    return new Promise((resolve) => {
      resolve(sessionKey);
    });
  } else {
    return store.dispatch('systemNotice/getPopUpMessage').then((res) => {
      return res ? res.sessionKey : '';
    });
  }
}
export function getMessageObj(forceUpdate) {
  const messageObj = store.state.systemNotice.messageObj;
  if (messageObj && !forceUpdate) {
    return new Promise((resolve) => {
      resolve(messageObj);
    });
  } else {
    return store.dispatch('systemNotice/getPopUpMessage').then((res) => {
      return res ? res.messageObj : {};
    });
  }
}
