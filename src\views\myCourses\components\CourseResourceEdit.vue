<template>
  <div class="course-resource-edit">
    <h3 class="resource-title">编辑课程资源</h3>
    <div style="padding: 0px 32px">
      <div class="edit-item">
        <div class="item-title">资源基本信息</div>
        <a-form ref="resourceEditForm" :model="formData" :colon="false">
          <a-form-item
            label="算力需求"
            name="flag"
            :rules="{
              required: true,
              message: '请选择算力需求',
            }"
            :label-col="{ span: 3 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-checkbox-group v-model="formData.flag" @change="handleComputeChange">
              <a-checkbox value="cpu" name="type"> CPU </a-checkbox>
              <a-checkbox value="vGpu" name="type"> vGPU </a-checkbox>
              <a-checkbox value="" name="type"> 无 </a-checkbox>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item
            label="代码量"
            name="codeNum"
            :rules="{
              required: true,
              message: '100个字符以内',
              trigger: 'change',
              max: 100,
            }"
            :label-col="{ span: 3 }"
            :wrapper-col="{ span: 16 }"
            extra="100个字符以内，请输入代码行数，ipynb/py等代码文件的数量和大小等"
          >
            <a-textarea v-model:value="formData.codeNum" placeholder="请输入代码量" :rows="4" />
          </a-form-item>

          <a-form-item label="数据量" name="dataNum" :label-col="{ span: 3 }" :wrapper-col="{ span: 6 }" extra="各个课节的项目实例中的data文件夹大小之和">
            <a-input v-model:value="formData.dataNum" :disabled="true" />
          </a-form-item>

          <a-form-item label="九天能力支持" name="jiuFlag" :label-col="{ span: 3 }" :wrapper-col="{ span: 16 }">
            <a-checkbox-group v-model="formData.jiuFlag">
              <a-checkbox value="1" name="type"> 九天可视化建模平台</a-checkbox>
              <a-checkbox value="2" name="type"> 九天智能交互平台</a-checkbox>
              <a-checkbox value="3" name="type"> 九天深度学习平台</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </div>
      <div class="edit-item">
        <div class="item-title">参考资料</div>
        <div id="reference-editor"></div>
      </div>

      <div class="edit-item">
        <div class="item-title">版权声明</div>
        <div id="copyrightClaim-editor"></div>
      </div>
    </div>
    <a-space class="btn-group">
      <a-button class="save" type="primary" :disabled="loading" @click="handleSaveClick">保存</a-button>
      <a-button class="cancel" :disabled="loading" @click="handleCancelClick">取消</a-button>
    </a-space>

    <upload-modal :visible="referenceUploadModalVisible" @cancel="referenceUploadModalCancel" @ok="referenceUploadModalOk"></upload-modal>
    <upload-modal :visible="copyrightClaimUploadModalVisible" @cancel="copyrightClaimUploadModalCancel" @ok="copyrightClaimUploadModalOk"></upload-modal>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import wangEditor from 'wangeditor';
import UploadModal from '@/components/chunkUploadModal';
import UploadMenu from '@/utils/wangEditorUpload.js';
import { uploadMenuKey } from '@/utils/wangEditorUpload.js';
import { textUpload, updateOrSaveCourseResource } from '@/apis/teaching.js';
wangEditor.registerMenu(uploadMenuKey, UploadMenu);
import _ from 'lodash';
let initFlag = []; // 用来存储选择计算资源的flag 因为radio-group只有一个change事件

export default {
  name: 'CourseResourceEdit',
  components: {
    UploadModal,
  },
  data() {
    return {
      courseId: this.$route.params.courseId,
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      referenceEditor: null,
      referenceUploadModalVisible: false,
      copyrightClaimEditor: null,
      copyrightClaimUploadModalVisible: false,
      formData: {
        flag: [],
        codeNum: '',
        dataNum: '',
        jiuFlag: [],
        courseDataData: '',
        courseCopyrightData: '',
      },
      loading: false,
    };
  },
  computed: {
    ...mapState(['refreshToken']),
    ...mapState('course', ['courseResourceData']),
  },
  watch: {
    refreshToken() {
      if (this.referenceEditor) {
        this.referenceEditor.config.uploadImgHeaders = {
          Authorization: 'Bearer ' + this.$keycloak.token,
        };
      }
      if (this.copyrightClaimEditor) {
        this.copyrightClaimEditor.config.uploadImgHeaders = {
          Authorization: 'Bearer ' + this.$keycloak.token,
        };
      }
    },
  },
  methods: {
    ...mapMutations('course', ['SET_COURSERESOURCE_DATA']),
    handleComputeChange(checkedflag) {
      let add = checkedflag.length > initFlag.length;
      if (add) {
        let diff = _.difference(checkedflag, initFlag);
        if (diff[0] == '') {
          this.formData.flag = [''];
          initFlag = [''];
        } else {
          if (checkedflag.includes('')) {
            this.formData.flag = diff;
            initFlag = [...diff];
          } else {
            initFlag = [...checkedflag];
          }
        }
      } else {
        initFlag = [...checkedflag];
      }
    },
    initEditors() {
      const referenceEditor = new wangEditor(`#reference-editor`);
      referenceEditor.uploadBtnClick = this.handleReferenceUploadBtnClick;
      referenceEditor.config.onchange = (newHtml) => {
        this.formData.courseDataData = newHtml;
      };
      referenceEditor.config.uploadImgShowBase64 = false;
      referenceEditor.config.uploadFileName = 'file';
      referenceEditor.config.uploadImgServer = './object/web/storage/image/upload';
      referenceEditor.config.uploadImgHeaders = {
        Authorization: 'Bearer ' + this.$keycloak.token,
      };
      referenceEditor.config.uploadImgHooks = {
        customInsert: function (insertImgFn, result) {
          insertImgFn(result.body.url);
        },
      };
      referenceEditor.config.zIndex = 99;
      referenceEditor.create();
      this.referenceEditor = referenceEditor;

      const copyrightClaimEditor = new wangEditor(`#copyrightClaim-editor`);
      copyrightClaimEditor.uploadBtnClick = this.handleCopyrightClaimUploadBtnClick;
      copyrightClaimEditor.config.onchange = (newHtml) => {
        this.formData.courseCopyrightData = newHtml;
      };
      copyrightClaimEditor.config.uploadImgShowBase64 = false;
      copyrightClaimEditor.config.uploadFileName = 'file';
      copyrightClaimEditor.config.uploadImgServer = './object/web/storage/image/upload';
      copyrightClaimEditor.config.uploadImgHeaders = {
        Authorization: 'Bearer ' + this.$keycloak.token,
      };
      copyrightClaimEditor.config.uploadImgHooks = {
        customInsert: function (insertImgFn, result) {
          insertImgFn(result.body.url);
        },
      };
      copyrightClaimEditor.config.zIndex = 99;
      copyrightClaimEditor.create();
      this.copyrightClaimEditor = copyrightClaimEditor;
    },
    // 调用保存接口
    handleSaveClick() {
      // 验证表单后直接提交
      let self = this;
      this.$refs.resourceEditForm
        .validate()
        .then(() => {
          // 这块为了防止后台报错当为null时传一个空字符串
          if (!self.formData.courseDataData) {
            self.formData.courseDataData = '';
          }
          if (!self.formData.courseCopyrightData) {
            self.formData.courseCopyrightData = '';
          }
          this.loading = true;
          const loadingCallback = this.$message.loading('课程资源保存中...');
          Promise.all([textUpload({ baseString: self.formData.courseDataData }), textUpload({ baseString: self.formData.courseCopyrightData })])
            .then((res) => {
              if (res[0].state === 'OK' && res[1].state === 'OK') {
                updateOrSaveCourseResource({
                  codeNum: self.formData.codeNum,
                  courseData: res[0].body.key,
                  courseCopyright: res[1].body.key,
                  courseId: self.courseId,
                  flag: self.formData.flag.join(','),
                  jiuFlag: self.formData.jiuFlag ? self.formData.jiuFlag.join(',') : '',
                })
                  .then((result) => {
                    if (result.state === 'OK') {
                      const courseResourceData = Object.assign({}, self.formData, {
                        courseData: res[0].body.key,
                        courseCopyright: res[1].body.key,
                        status: '0',
                        pubTime: self.courseResourceData.pubTime,
                      });
                      self.SET_COURSERESOURCE_DATA(courseResourceData);
                      this.backToCourseManage(true);
                      self.$message.success('课程资源保存成功');
                    } else {
                      this.$message.error('课程资源保存失败');
                    }
                  })
                  .finally(() => {
                    this.loading = false;
                    loadingCallback();
                  });
              } else {
                this.loading = false;
                loadingCallback();
                this.$message.error('课程资源保存失败');
              }
            })
            .catch(() => {
              this.loading = false;
              loadingCallback();
            });
        })
        .catch((err) => {
          throw new Error(err);
        });
    },

    handleCancelClick() {
      this.backToCourseManage();
    },
    backToCourseManage(noLeaveConfirm) {
      // eslint-disable-next-line no-unused-vars
      const { subPage, ...params } = this.$route.params;
      this.$router.replace({
        name: '课程管理',
        params,
        query: {
          noLeaveConfirm,
        },
      });
    },
    referenceUploadModalCancel() {
      this.referenceUploadModalVisible = false;
    },
    referenceUploadModalOk(uploadParams) {
      this.referenceUploadModalVisible = false;
      const { downloadUrl, fileName } = uploadParams;
      this.referenceEditor.cmd.do('insertHTML', `<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
    },
    handleReferenceUploadBtnClick() {
      this.referenceUploadModalVisible = true;
    },
    copyrightClaimUploadModalCancel() {
      this.copyrightClaimUploadModalVisible = false;
    },
    copyrightClaimUploadModalOk(uploadParams) {
      this.copyrightClaimUploadModalVisible = false;
      const { downloadUrl, fileName } = uploadParams;
      this.copyrightClaimEditor.cmd.do('insertHTML', `<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
      // this.copyrightClaimEditor.txt.append(`<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
    },
    handleCopyrightClaimUploadBtnClick() {
      this.copyrightClaimUploadModalVisible = true;
    },
  },
  mounted() {
    this.initEditors();
    this.formData = Object.assign({}, this.courseResourceData);
    initFlag = this.courseResourceData.flag || [];
    this.referenceEditor.txt.html(this.courseResourceData.courseDataData);
    this.copyrightClaimEditor.txt.html(this.courseResourceData.courseCopyrightData);
  },
  beforeUnmount() {
    this.referenceEditor.destroy();
    this.referenceEditor = null;
    this.copyrightClaimEditor.destroy();
    this.copyrightClaimEditor = null;
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.course-resource-edit {
  .resource-title {
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    border-bottom: 1px solid #e0e1e1;
    height: 64px;
    line-height: 64px;
    padding-left: 32px;
    margin-bottom: 32px;
  }
  .edit-item {
    position: relative;
    margin-bottom: 48px;
    .item-title {
      margin-bottom: 20px;
      padding-left: 16px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      font-size: 14px;
      &:before {
        position: absolute;
        content: '';
        top: 3px;
        left: 0px;
        display: block;
        width: 4px;
        height: 16px;
        background-color: #0082ff;
      }
      // &:after {
      //   content: ' *';
      //   color: red;
      // }
    }
    :deep(.w-e-text-container) {
      ul li {
        list-style: disc;
      }
      ol li {
        list-style: decimal;
      }
    }
    :deep(.w-e-droplist) {
      max-height: 300px;
      overflow-y: auto;
    }
  }
  .btn-group {
    margin-bottom: 64px;
    padding-left: 32px;
    .save {
      width: 120px;
    }
    .cancel {
      width: 88px;
    }
  }
}
</style>
