<template>
  <div>
    <a-spin :spinning="spinning">
      <div class="edit-header">编辑数据及实例设置</div>
      <div class="edit-data-case">
        <head-title title="数据设置" style="margin-bottom: 24px" />
        <a-form ref="ruleForm" :model="dateCaseObj" :rules="rules">
          <a-form-item label="是否开放数据" name="allowOpenData" v-bind="formItemLayout">
            <a-radio-group v-model:value="dateCaseObj.allowOpenData">
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="isOpenData && isBishengCompetition" label="开放数据集" name="dataSetName" v-bind="formItemLayout" help="选择控制台-数据管理模块中的个人数据集">
            <a-select v-model:value="dateCaseObj.dataSetName" style="width: 240px">
              <a-select-option v-for="item in dateCaseObj.dataSetNames" :key="item" :value="item">{{ item }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="isOpenData" label="是否允许下载" name="allowDownloadData" v-bind="formItemLayout">
            <a-radio-group v-model:value="dateCaseObj.allowDownloadData">
              <a-radio :value="1"> 是 </a-radio>
              <a-radio :value="0"> 否 </a-radio>
            </a-radio-group>
          </a-form-item>
          <head-title v-if="isBishengCompetition" title="实例设置" style="margin-bottom: 24px" />
          <a-form-item v-if="isBishengCompetition" label="单实例最高可用资源" name="instanceResource" v-bind="formItemLayout">
            <a-select v-model:value="dateCaseObj.instanceResource" placeholder="请选择单实例最高可用资源" style="width: 240px">
              <a-select-option v-for="item in dateCaseObj.instanceResources" :key="item.id" :value="item.instanceName">{{ item.instanceName }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="" :wrapper-col="{ offset: 4 }" v-bind="formItemLayout">
            <a-space>
              <a-button type="primary" style="width: 120px" @click="saveAndPublish">{{ currentManageCompetition.releaseSta === publishStatusKeys.PUBLISHED ? '保存并发布' : '保存' }}</a-button>
              <a-button style="width: 88px" @click="handleCancelEdit('noChange')">取消</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import headTitle from '@/components/headTitle';
import { publishStatusKeys } from '../../../competitionConfig/index';
import { GET, POST } from '@/request';

const store = useStore();
const route = useRoute();
const router = useRouter();

const ruleForm = ref();
const spinning = ref(false);
const formItemLayout = reactive({
  labelCol: {
    xs: { span: 6 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 18 },
    sm: { span: 12 },
  },
});

const dateCaseObj = reactive({
  allowOpenData: 0,
  dataSetName: undefined,
  dataSetNames: undefined,
  allowDownloadData: 0,
  instanceResource: undefined,
  instanceResources: undefined,
});
const dateCaseCopy = ref({});
const changeDataSetName = ref('default'); // default 一进页面默认为空，然后修改 change 修改过数据集 noChange 未修改过数据集

const currentManageCompetition = computed(() => store.state.competition.currentManageCompetition);
const isBishengCompetition = computed(() => currentManageCompetition.value.competitionTraining === 1);
const isOpenData = computed(() => Boolean(Number(dateCaseObj.allowOpenData)));

const rules = computed(() => ({
  allowOpenData: [{ required: true, message: '请选择是否开放数据', trigger: 'change' }],
  dataSetName: [{ required: true, message: '请选择开放数据集', trigger: 'change' }],
  allowDownloadData: [{ required: true, message: '请选择是否允许数据下载：', trigger: 'change' }],
  instanceResource: [{ required: true, message: '请选择单实例最高可用资源', trigger: 'change' }],
}));

watch(
  () => dateCaseCopy.value.dataSetName,
  (val, oldVal) => {
    if (oldVal !== val && oldVal) {
      changeDataSetName.value = 'change';
    }
  }
);

function getDataset() {
  spinning.value = true;
  const { competitionId } = route.params;
  GET('/competiton/web/manage/dataset/get', { cid: competitionId }).then((res) => {
    if (res.state === 'OK') {
      Object.assign(dateCaseObj, res.body);
      dateCaseObj.instanceResource = res.body.instanceResource || '1算力卡';
      dateCaseCopy.value = { ...res.body };
    } else {
      Object.keys(dateCaseObj).forEach((key) => (dateCaseObj[key] = undefined));
    }
    spinning.value = false;
  });
}

async function saveAndPublish() {
  ruleForm.value
    .validate()
    .then(async () => {
      spinning.value = true;
      const { competitionId } = route.params;
      const { allowOpenData, dataSetName, allowDownloadData, instanceResource, instanceResources } = dateCaseObj;
      let instanceValue = (instanceResources || []).find((item) => {
        return item.instanceName === instanceResource;
      });
      let dataSetCreateRequest = {
        allowOpenData,
        dataSetName,
        allowDownloadData,
        instanceResource: instanceValue?.id || 0,
        cid: competitionId,
      };
      const res = await POST('/competiton/web/manage/dataset/create', dataSetCreateRequest);
      if (res.state === 'OK') {
        if (changeDataSetName.value === 'noChange' || changeDataSetName.value === 'default') {
          message?.success?.('编辑数据及实例设置成功') || window.message?.success?.('编辑数据及实例设置成功');
        } else {
          message?.success?.('编辑数据及实例设置成功，数据同步中') || window.message?.success?.('编辑数据及实例设置成功，数据同步中');
        }
        handleCancelEdit(null, true);
      } else {
        message?.error?.('编辑数据及实例设置失败') || window.message?.error?.('编辑数据及实例设置失败');
      }
      spinning.value = false;
    })
    .catch((err) => {
      throw new Error(err);
    });
}

function handleCancelEdit(label, noLeaveConfirm) {
  const { competitionId } = route.params;
  router.push({
    path: `/competition/competition-management/${competitionId}`,
    query: { tabId: '4', subtabId: '1', changeDataSetName: label === 'noChange' ? undefined : changeDataSetName.value, noLeaveConfirm },
  });
}

onMounted(() => {
  getDataset();
});
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.edit-header {
  .competition-edit-header();
}
.edit-data-case {
  padding: 32px;
  color: #121f2c;

  :deep(.ant-col) {
    padding: 12px !important;
  }
  .ant-form-item {
    margin-bottom: 8px;
    :deep(.ant-col-sm-3) {
      width: 15.5%;
    }
  }
  :deep(.ant-form-item-label > label) {
    color: #121f2c;
  }
  :deep(.ant-form-explain) {
    font-size: 12px;
  }
}
</style>
