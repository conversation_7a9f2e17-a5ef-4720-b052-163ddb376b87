<template>
  <div>
    <a-form ref="form" :colon="false" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item label="用户名" name="userName">
        <a-input v-model:value="form.userName" size="large" />
      </a-form-item>
      <a-form-item label="密码" name="password">
        <a-input-password v-model:value="form.password" autocomplete="new-password" size="large" />
      </a-form-item>
      <a-form-item label="确认密码" name="confirmPassword">
        <a-input-password v-model:value="form.confirmPassword" autocomplete="new-password" size="large" />
      </a-form-item>
      <a-form-item ref="phone" label="手机号" name="phone">
        <a-input v-model:value="form.phone" size="large" />
      </a-form-item>
      <a-form-item label="验证码" name="code">
        <a-space style="line-height: 32px">
          <a-input v-model:value="form.code" size="large" style="width: 224px" />
          <a-button :disabled="timmer > 0" size="large" style="width: 128px" @click="handleSendCode">{{ timmer > 0 ? `重新获取(${timmer})` : '获取验证码' }}</a-button>
        </a-space>
        <a-form-item v-if="activityAvailable" ref="registEcloud" class="no-margin-bottom" label="" name="registEcloud">
          <a-checkbox v-model="form.registEcloud">
            {{ `同时注册移动云账号，算力豆+${ecloudRegisterBeanBount}` }}
          </a-checkbox>
        </a-form-item>
      </a-form-item>
      <a-form-item required label="学校" class="school-item">
        <a-space>
          <a-form-item style="width: 120px">
            <a-select size="large" show-search :options="areaOptions" placeholder="地区" @change="areaChange" />
          </a-form-item>
          <a-form-item style="width: 232px" name="school">
            <a-input v-if="form.area === '其他'" v-model:value="form.school" size="large" placeholder="在读学校/就职学校/毕业学校" />
            <a-select v-else v-model="form.school" size="large" placeholder="在读学校/就职学校/毕业学校" show-search :options="schoolOptions"></a-select>
          </a-form-item>
        </a-space>
      </a-form-item>
      <a-form-item name="terms" label=" " class="terms-row">
        <a-checkbox-group v-model="form.terms">
          <a-checkbox :value="1" name="type"> 我已阅读并同意九天人工智能平台<a :href="'/helpcenter#/document/23'" rel="noopenner noreferrer" target="_blank">服务条款</a> </a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item label=" ">
        <a-button size="large" type="primary" style="width: 100%" @click="handleRegister">注册</a-button>
      </a-form-item>
      <a-form-item v-if="showLoginBtn" label=" ">
        <div>
          已有帐号？
          <a class="login-btn" @click="handleLogin">登录</a>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import { axiosWithNoToken } from '@/request';
import { login } from '@/keycloak';
import { passwordRegex, telephoneNumberRegex } from '@/utils/regex';
// const zxcvbn = require('zxcvbn');

export default {
  name: 'RegisterForm',
  props: {
    activityAvailable: Boolean,
    showLoginBtn: Boolean,
    ecloudRegisterBeanBount: Number,
  },
  emits: ['onSubmit'],
  data() {
    function userNameValidator(rule, value, callback) {
      const pattern = /^([A-Za-z0-9_]){6,20}$/;
      const numberPattern = /^\d+$/;
      if (pattern.test(value) && !numberPattern.test(value)) {
        callback();
      } else {
        callback(new Error('6-20个字符，只能包含字母、下划线和数字，不支持全数字'));
      }
    }
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      areaOptions: [],
      schoolOptions: [],
      timmer: 0,
      form: {
        userName: '',
        password: '',
        confirmPassword: '',
        phone: '',
        code: '',
        area: undefined,
        school: undefined,
        registEcloud: false,
        terms: false,
      },
      rules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: ['change', 'blur'] },
          { validator: userNameValidator, trigger: ['change', 'blur'] },
        ],
        password: [
          { required: true, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          { pattern: passwordRegex, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          {
            validator: (rule, value, callback) => {
              if (value) {
                // if (zxcvbn(value).score >= 2) {
                //   callback();
                // } else {
                callback('密码过于简单或存在安全风险，请修改');
                // }
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        confirmPassword: [
          { required: true, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          { validator: this.validateConfirmPassword, trigger: ['blur', 'change'] },
        ],
        phone: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { pattern: telephoneNumberRegex, message: '请输入正确格式手机号', trigger: ['blur', 'change'] },
        ],
        code: [{ required: true, message: '请输入验证码', trigger: ['change', 'blur'] }],
        area: [{ required: true, message: '请选择地区', trigger: 'change' }],
        school: [{ required: true, message: '请选择/输入学校', trigger: ['blur', 'change'] }],
        terms: [{ required: true, message: '请勾选同意协议', type: 'array', trigger: 'change' }],
      },
      eCloudCodeAvailable: false,
      eCloudRegistered: false,
      codeSended: false,
    };
  },
  watch: {
    'form.password'() {
      this.$refs[`form`].validateField('confirmPassword');
    },
    activityAvailable() {
      this.form.registEcloud = this.activityAvailable;
    },
  },
  mounted() {
    this.getAreaOptions();
  },
  methods: {
    // zxcvbn,
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    },
    getAreaOptions() {
      axiosWithNoToken.get('/marketing/web/getProvince', {}).then((res) => {
        if (res.data.state === 'OK') {
          this.areaOptions = res.data.body.map((item) => {
            return {
              value: item,
              label: item,
            };
          });
        }
        this.areaOptions.push({
          value: '其他',
          label: '其他',
        });
      });
    },
    areaChange(value) {
      this.form.area = value;
      this.form.school = undefined;
      if (value !== '其他') {
        this.getSchoolOptions();
      }
    },
    getSchoolOptions() {
      axiosWithNoToken.get('/marketing/web/getSchool', { params: { province: this.form.area } }).then((res) => {
        if (res.data.state === 'OK') {
          this.schoolOptions = res.data.body.map((item) => {
            return {
              value: item,
              label: item,
            };
          });
        }
      });
    },
    handleSendCode() {
      if (this.timmer > 0) {
        return;
      }
      this.$refs.form.validateFields(['phone']);
      if (!this.form.phone) {
        this.$message.error('请输入手机号');
        return;
      }
      this.codeSended = true;
      this.eCloudCodeAvailable = this.form.registEcloud;
      if (this.form.registEcloud) {
        this.sendEcloudCode();
      } else {
        this.sendBiShengCode();
      }
    },

    setTimmer() {
      this.timmer = 60;
      const timer = setInterval(() => {
        this.timmer -= 1;
        if (this.timmer === 0) {
          clearInterval(timer);
        }
      }, 1000);
    },

    sendEcloudCode() {
      const url = '/marketing/web/ecloud/sendSmsCode';
      axiosWithNoToken(url, { params: { phoneNum: this.form.phone } }).then((res) => {
        if (res.data.state === 'OK') {
          this.setTimmer();
          this.$message.success('发送成功');
          this.eCloudRegistered = false;
        } else {
          if (res.data.errorCode === '1109') {
            this.eCloudRegistered = true;
            this.sendBiShengCode();
          } else {
            this.$message.error(res.data.errorMessage || '发送验证码错误');
          }
        }
      });
    },

    sendBiShengCode() {
      const url = '/marketing/web/sendSmsCode';
      axiosWithNoToken(url, { params: { phoneNum: this.form.phone } }).then((res) => {
        if (res.data.state === 'OK') {
          this.setTimmer();
          this.$message.success('发送成功');
        } else {
          this.$message.error(res.data.errorMessage || '发送验证码错误');
        }
      });
    },

    handleRegister() {
      this.$refs.form
        .validate()
        .then(() => {
          this.submit();
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
    checkEcloudCodeAvailable() {
      if (this.codeSended && this.eCloudCodeAvailable !== this.form.registEcloud) {
        if (this.form.registEcloud) {
          this.$message.error('您已勾选同时注册移动云，请重新获取移动云验证码');
        } else {
          this.$message.error('您已取消注册移动云，移动云验证码失效，请重新获取验证码');
        }
        return;
      }
      return true;
    },
    submit() {
      if (!this.checkEcloudCodeAvailable()) {
        return;
      }
      const obj = {
        username: this.form.userName,
        password: this.form.password,
        phonenum: this.form.phone,
        code: this.form.code,
        school: this.form.school,
        channel: '1', // 标识是pc注册渠道
        registEcloud: this.form.registEcloud,
      };
      this.$emit('onSubmit', obj, this.eCloudRegistered);
    },
    handleLogin() {
      login();
    },
  },
};
</script>

<style lang="less" scoped>
.school-item {
  :deep(.ant-form-item-children) {
    display: flex;
  }
}
.no-margin-bottom {
  margin-bottom: 0;
}
.login-btn {
  color: #1890ff;
}
.terms-row {
  :deep(.ant-form-item-required::before) {
    content: '';
  }
}
</style>
