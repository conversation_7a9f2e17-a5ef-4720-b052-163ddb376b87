<template>
  <div class="video-viewer-container">
    <div class="title-bar">
      <span class="title">{{ current.resourseName }}</span>
      <a-dropdown placement="bottomRight" :overlay-style="{ 'z-index': '10000' }">
        <jt-icon class="menu-icon" type="iconlist"></jt-icon>
        <template #overlay>
          <a-menu>
            <a-menu-item v-for="(item, i) of videoList" :key="i" @click="handleClick(i)">
              <a>{{ item.resourseName }}</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <!-- eslint-disable vue/no-v-html -->
    <div id="frame-container" class="frame" @load.capture="onFrameLoaded" v-html="current.resourseUrl"></div>
  </div>
</template>

<script>
export default {
  props: {
    videoList: { type: Array, default: () => [] },
  },
  data() {
    return {
      current: {},
      currentIndex: 0,
    };
  },
  watch: {
    videoList() {
      this.current = this.videoList[0] || {};
    },
    currentIndex() {
      this.current = this.videoList[this.currentIndex] || {};
    },
  },
  mounted() {
    this.current = this.videoList[0] || {};
    const self = this;
    window.addEventListener(
      'message',
      function (event) {
        if (event.origin !== 'https://www.miguvideo.com') {
          return;
        }
        if (event.data.type === 'VIDEO_END') {
          self.currentIndex++;
        }
      },
      false
    );
  },
  unmounted() {
    window.removeEventListener('message');
  },
  methods: {
    handleClick(index) {
      this.currentIndex = index;
    },
    onFrameLoaded(e) {
      const frame = e.target;
      frame.style.width = '100%';
      frame.style.height = '100%';
      // 对咪咕视频做的配置
      const content = frame.contentWindow; // 绕过solar检查
      content.postMessage(
        {
          type: 'skin_cfg',
          data: {
            value: {
              isvrbtn: false,
              liveTimeUI: false,
              loopBtn: false,
              // speedBtn: false,
              ratetypeBox: false,
              nextBtn: false,
            },
          },
        },
        'https://www.miguvideo.com'
      );
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.frame {
  width: 100%;
  height: 100%;
  word-break: break-all;
  word-wrap: break-word;
}
.video-viewer-container {
  flex-direction: column;
  padding: 24px;
  background: #fff;
}
.title-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
  }
  .menu-icon {
    font-size: 24px;
    cursor: pointer;
    &:hover {
      color: #0082ff;
    }
  }
}
</style>
