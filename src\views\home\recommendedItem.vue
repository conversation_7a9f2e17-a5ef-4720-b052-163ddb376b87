<template>
  <div class="recommended-item">
    <div class="item-title" :style="{ background: bgColor }"><img style="width: 32px" :src="iconImg" alt />{{ title }}</div>
    <jt-skeleton :loading="itemList.length === 0" :row-style="{ height: '38px', margin: '24px 16px 0px', 'border-radius': '4px' }" :rows="3">
      <div class="sub-item-list-container">
        <div v-for="x in itemList" :key="x.id" class="sub-item-list" @click="clickItem(x)">
          <div class="item-tag">
            <strong>{{ x.resourceName }}</strong>
            <span :style="{ background: tagColor }">{{ getResourceTypeText(x) }}</span>
          </div>
          <p>
            <span>{{ x.studyNum }}人</span>
            <span v-if="x.resourceType == 'course'">学习</span>
            <span v-else-if="x.resourceType == 'competition'">参赛</span>
            <span v-else>使用</span>
          </p>
        </div>
      </div>
    </jt-skeleton>
  </div>
</template>

<script>
import JtSkeleton from '@/components/skeleton';
export default {
  name: 'RecommendedItem',
  components: { JtSkeleton },
  props: {
    iconImg: String,
    bgColor: String,
    tagColor: String,
    itemList: Array,
    title: String,
  },
  emits: ['onClickItem'],
  methods: {
    clickItem(item) {
      this.$emit('onClickItem', item);
    },
    getResourceTypeText(item) {
      const TYPE_MAP = {
        course: '课程',
        competition: '比赛',
      };
      return TYPE_MAP[item.resourceType] || '数据';
    },
  },
};
</script>

<style lang="less" scoped>
.recommended-item {
  width: 387px;
  height: 311px;
  background: #ffffff;
  box-shadow: 0px 12px 48px 16px rgba(0, 0, 0, 0.03), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 6px 16px -8px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  &:nth-of-type(2) {
    margin: 0 20px;
  }
  .item-title {
    height: 65px;
    line-height: 65px;
    border-radius: 2px;
    font-size: 20px;
    font-weight: bold;
    // text-indent: 68px;
    img {
      width: 32px;
      margin-left: 24px;
      margin-right: 12px;
      vertical-align: text-top;
    }
  }
}
.sub-item-list {
  height: 82px;
  padding: 16px 24px;
  border-bottom: 1px solid #ecedee;
  cursor: pointer;
  &:hover {
    background: #f8f9fa;
    .item-tag {
      > strong {
        color: #0082ff;
        transition: 0.5s;
      }
    }
  }

  p {
    color: #a0a6ab;
  }
  &:nth-of-type(2) {
    // border-top: 1px solid #ecedee;
    border-bottom: 1px solid #ecedee;
  }
}
.item-tag {
  line-height: 14px;
  span {
    display: inline-block;
    width: 48px;
    height: 24px;
    line-height: 24px;
    background: #389bff;
    border-radius: 2px;
    color: #ffffff;
    margin-left: 16px;
    text-align: center;
    cursor: default;
    user-select: none;
    vertical-align: top;
  }
  strong {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 273px;
    display: inline-block;
    font-weight: normal;
    height: 24px;
    line-height: 24px;
  }
}
</style>
