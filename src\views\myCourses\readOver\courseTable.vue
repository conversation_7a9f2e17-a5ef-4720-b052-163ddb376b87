<template>
  <div>
    <div class="table-title">
      <a-space :size="40" class="title-text">
        <p>
          已评阅： <span>{{ correctDetails.correctNum }}</span>
        </p>
        <p>
          已提交： <span>{{ correctDetails.commitNum }}</span>
        </p>
        <p>
          总人数： <span>{{ correctDetails.classNum }}</span>
        </p>
        <p>上次发布成绩： {{ correctDetails.pubTime ? correctDetails.pubTime : '- -' }}</p>
      </a-space>
      <div class="title-ctrl">
        <a-input v-model:value="getTableFormData.studentName" placeholder="搜索姓名/学号" style="width: 240px; height: 32px">
          <template #prefix>
            <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
          </template>
        </a-input>
        <div class="button-group">
          <a-button class="button" :disabled="tableData.length < 1" :target="tableData.length && tableData.length < 1 ? '' : '_blank'" download @click="downScore">导出成绩</a-button>
          <a-button class="button" type="primary" :disabled="correctDetails.correctNum <= 0" @click="publish">发布成绩</a-button>
        </div>
      </div>
    </div>
    <a-config-provider>
      <a-table class="course-table" row-key="studentNum" :data-source="tableData" :columns="columns" :pagination="false" size="middle">
        <template #studentName-slot="{ text }">
          <span>{{ text }}</span>
        </template>
        <template #commitStatus-title-slot>
          <div class="status-title-slot">
            提交状态
            <table-filter :filter-list="statusFilterList" :status-classnamemap="statusClassnamemap" @changeStatus="getCommitStatus"></table-filter>
          </div>
        </template>
        <template #commitStatus-slot="{ text }">
          <div class="status-common" :class="{ active: text === '1' }">
            {{ text === '0' ? '未提交' : '已提交' }}
          </div>
        </template>
        <template #commitTime-slot="{ text }">
          {{ text ? text : '-' }}
        </template>
        <template #correctStatus-title-slot>
          <div class="status-title-slot">
            评阅状态
            <table-filter :filter-list="typeFilterList" :status-classnamemap="typeClassnamemap" @changeStatus="getCorrectStatus"></table-filter>
          </div>
        </template>
        <template #correctStatus-slot="{ text }">
          <a-space :size="10">
            <MinusCircleOutlined v-if="text === '0'" style="color: #c2c5cf" />
            <CheckCircleOutlined v-else style="color: #1dca94" />
            {{ text === '0' ? '未评阅' : '已评阅' }}
          </a-space>
        </template>
        <template #score-slot="{ text }">
          <span :class="{ 'score-slot': text }">
            {{ text ? ((text + '').includes('.') ? (+text).toFixed(1) : text) : '-' }}
          </span>
        </template>
        <template #operation-slot="{ record }">
          <a-button type="link" size="small" :disabled="record.commitStatus === '0'" style="padding: 0" @click="gotoReview(record)"> 评阅 </a-button>
        </template>
      </a-table>
    </a-config-provider>
    <jt-pagination v-if="total !== 0" :page-size="pageSize" :page-num="pageNum" :total="total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"></jt-pagination>
  </div>
</template>

<script lang="jsx">
import tableFilter from '../components/table-filter.vue';
import { homeworkApi } from '@/apis';
import _ from 'lodash';
import { axios } from '@/request';
import { checkAuth } from '@/utils/utils';
import { ExclamationCircleFilled, MinusCircleOutlined, CheckCircleOutlined } from '@ant-design/icons-vue';

export default {
  name: 'Homework',
  components: {
    tableFilter,
    MinusCircleOutlined,
    CheckCircleOutlined,
  },
  data() {
    return {
      homeworkApi,
      pageSize: 10,
      pageNum: 1,
      total: 0,
      statusFilterList: [
        { value: '', text: '全部状态' },
        { value: 0, text: '未提交' },
        { value: 1, text: '已提交' },
      ],
      statusClassnamemap: new Map([
        [0, 'status-lockup'],
        [1, 'status-complete'],
      ]),
      typeFilterList: [
        { value: '', text: '全部状态' },
        { value: 0, text: '未评阅' },
        { value: 1, text: '已评阅' },
      ],
      typeClassnamemap: new Map([
        [0, 'status-lockup'],
        [1, 'status-complete'],
      ]),
      tableData: [],
      columns: [
        {
          key: 'studentName',
          title: '姓名',
          dataIndex: 'studentName',
          slots: { customRender: 'studentName-slot' },
        },
        {
          key: 'studentNum',
          title: '学号',
          dataIndex: 'studentNum',
        },
        {
          key: 'commitStatus',
          dataIndex: 'commitStatus',
          slots: { customRender: 'commitStatus-slot' },
        },
        {
          key: 'commitTime',
          title: '提交时间',
          dataIndex: 'commitTime',
          slots: { customRender: 'commitTime-slot' },
        },
        {
          key: 'correctStatus',
          dataIndex: 'correctStatus',
          slots: { customRender: 'correctStatus-slot' },
        },
        {
          key: 'score',
          title: '分数',
          dataIndex: 'score',
          slots: { customRender: 'score-slot' },
        },
        {
          title: '操作',
          slots: { customRender: 'operation-slot' },
        },
      ],
      getTableFormData: {
        assignmentId: '', // 作业id
        courseId: '', // 课程id
        commitStatus: '', // 提交状态
        correctStatus: '', // 评阅状态
        studentName: '', // 学生姓名
        studentNum: '', //学生学号
      },
      correctDetails: {
        assignmentName: '123',
        classNum: 2,
        commitNum: 1,
        correctNum: 1,
        pubTime: null,
      },
    };
  },
  computed: {
    scoreExportUrl() {
      // return this.homeworkApi.scoreExport({ assignmentId: this.getTableFormData.assignmentId });
      return `/course_model/web/teaching/assignment/score/export?assignmentId=${this.getTableFormData.assignmentId}`;
    },
  },
  created() {
    this.getTableFormData.assignmentId = +this.$route.params.assignmentId;
    this.getTableFormData.courseId = +this.$route.params.courseId;
    this.getPageData();
  },
  watch: {
    'getTableFormData.studentName': _.debounce(function () {
      this.pageNumChange(1);
    }, 500),
    pageNum: _.debounce(function () {
      this.getTable();
    }, 500),
  },
  methods: {
    async getPageData() {
      this.getTable();
      this.getDetails();
    },
    async getTable() {
      const params = {
        ...this.getTableFormData,
        pageSize: this.pageSize,
        pageNum: this.pageNum,
      };
      const res = await homeworkApi.getCorrectList(params);
      if (!checkAuth(res.errorCode, '-802', '/course')) {
        return;
      }
      if (res.state === 'OK') {
        this.tableData = res.body.data;
        this.total = res.body.total;
      }
    },
    async getDetails() {
      const params = {
        assignmentId: this.getTableFormData.assignmentId, // 作业id
        courseId: this.getTableFormData.courseId, // 课程id
      };
      const res = await homeworkApi.getCorrectDetails(params);
      if (res.state === 'OK') {
        this.correctDetails = res.body;
      }
    },
    downScore() {
      let url = this.scoreExportUrl;
      axios.get(url, { responseType: 'blob' }).then((res) => {
        let blob = res.data;
        var reader = new FileReader();
        reader.readAsDataURL(blob); // 转换为base64，可以直接放入a标签href
        reader.onload = function (e) {
          var a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
          a.href = e.target.result;
          a.download = decodeURIComponent(res.headers['content-disposition'].split(';')[1].split('filename=')[1]);
          document.body.appendChild(a); // 火狐浏览器 必须把元素插入body中
          a.click();
          document.body.removeChild(a);
        };
      });
    },
    publish() {
      this.$confirm({
        title: '发布成绩提示',
        content: '发布后，已评阅学生将可查看分数和评语，请确认是否发布',
        icon: () => <ExclamationCircleFilled />,
        cancelText: '取消',
        okText: '发布',
        onOk: async () => {
          const params = {
            assignmentId: +this.$route.params.assignmentId,
          };
          const res = await homeworkApi.scorePublish(params);
          if (res.state === 'OK') {
            this.$message.success(`发布成功`);
            this.getPageData();
          }
        },
      });
    },
    gotoReview(record) {
      this.$router.push(`/course/teaching/homework-review/${record.courseId}/${record.stuAssignmentId}`);
    },
    getCommitStatus(status) {
      this.getTableFormData.commitStatus = status;
      this.getTable();
    },
    getCorrectStatus(status) {
      this.getTableFormData.correctStatus = status;
      this.getTable();
    },
    pageSizeChange(size) {
      this.pageSize = size;
      this.pageNumChange(1);
    },
    pageNumChange(num) {
      this.pageNum = num;
      this.getTable();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.sort-btns {
  display: inline-block;
}
.table-title {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  .title-text {
    font-size: @jt-font-size-base;
    span {
      color: @jt-primary-color;
    }
  }
}
.title-ctrl {
  display: flex;
  .button-group {
    margin-left: 16px;
    .button {
      margin-right: 8px;
      &:nth-child(1):not([disabled]) {
        border-color: @jt-primary-color;
        color: @jt-primary-color;
      }
    }
  }
}
.course-table {
  margin-top: 16px;
}
.score-slot {
  color: #ff9d00;
}
.status-common {
  width: 52px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid @jt-text-color-secondary;
  font-size: @jt-font-size-sm;
  color: @jt-text-color;
  text-align: center;
  &.active {
    color: #17bb85;
    border-color: #17bb85;
  }
}
.iconchenggong-miaobian {
  color: red;
}
.status-title-slot {
  display: flex;
}
.empty {
  h1 {
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: @jt-text-color-primary;
    line-height: 20px;
    margin-bottom: 9px;
  }
  p {
    color: @jt-text-color;
    a {
      color: @jt-primary-color;
    }
  }
}
</style>
