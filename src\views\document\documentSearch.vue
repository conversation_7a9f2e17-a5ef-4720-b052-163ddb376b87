<template>
  <div class="document-search-container">
    <div class="search-wrap">
      <a-input-search placeholder="请输入" enter-button="搜索" size="large" @search="onSearch" />
    </div>
    <div class="result-content-wrap">
      <jt-common-content :empty="total === 0" :loading="loading" :empty-title="emptyTitle" :empty-text="emptyText" :empty-image="require('@/assets/image/empty2x.png')">
        <div class="result-content">
          <div class="result-content-count">
            <span>共{{ total }}条结果</span>
          </div>
          <ul class="result-content-list">
            <li v-for="item of data" :key="item.id" class="result-content-list-item">
              <div class="result-content-list-item-title">
                <span>{{ item.title }}</span>
              </div>
              <div class="result-content-list-item-content">
                <span>{{ item.content }}</span>
              </div>
              <div class="result-content-list-item-source">
                <span>来源: {{ item.source }}</span>
              </div>
            </li>
          </ul>
        </div>
      </jt-common-content>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DocumentSearch',
  data() {
    return {
      data: [
        {
          id: 1,
          title: '标题1',
          content: '内容1',
          source: '来源1',
        },
        {
          id: 2,
          title: '标题2',
          content: '内容2',
          source: '来源2',
        },
        {
          id: 3,
          title: '标题3',
          content: '内容3',
          source: '来源3',
        },
      ],
      total: 3,
      loading: false,
      emptyTitle: '抱歉，没有找到相关结果',
      emptyText: '您可以换一个关键词试试哦~',
    };
  },
  methods: {
    onSearch() {
      this.data = [];
      this.total = 0;
    },
  },
};
</script>

<style lang="less" scoped>
.document-search-container {
  overflow: auto;
  background: #f4f8fa;
}
.search-wrap {
  width: 800px;
  margin: 38px auto;
}
.result-content-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  .result-content {
    width: 1200px;
    .result-content-count {
      margin: 32px 0;
      font-size: 14px;
      font-weight: 400;
      color: #a0a6ab;
    }
    .result-content-list-item {
      margin: 0 0 32px 0;
      .result-content-list-item-title {
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 400;
        color: #121f2c;
      }
      .result-content-list-item-content {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 400;
        color: #5e6974;
      }
      .result-content-list-item-source {
        font-size: 14px;
        font-weight: 400;
        color: #a0a6ab;
      }
    }
  }
}
</style>
