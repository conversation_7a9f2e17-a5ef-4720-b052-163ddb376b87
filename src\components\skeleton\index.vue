<template>
  <span>
    <ul :style="skeletonStyle" v-if="loading">
      <li class="jt-skeleton-li" :style="rowStyle" v-for="(item, i) in liItems" :key="i"></li>
    </ul>
    <slot v-else />
  </span>
</template>

<script>
export default {
  name: 'jt-skeleton',
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    rows: {
      type: Number,
      default: 1,
    },
    rowStyle: {
      type: Object,
      default() {
        return {
          width: '100%',
          height: '20px',
        };
      },
    },
    skeletonStyle: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    liItems() {
      return new Array(this.rows);
    },
  },
};
</script>

<style lang="less" scoped>
.jt-skeleton-li {
  background-image: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%) !important;
  background-color: transparent !important;
  background-size: 400% 100%;
  animation: skeleton-loading 1.5s ease-in infinite;
  min-height: 0.3rem;
  min-width: 1rem;
}

@keyframes skeleton-loading {
  from {
    background-position: 100% 50%;
  }
  to {
    background-position: 0 50%;
  }
}
</style>
