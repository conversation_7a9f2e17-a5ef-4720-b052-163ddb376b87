<template>
  <div class="pdfviewer-container">
    <div class="title-bar">
      <span class="title"> {{ current.resourseName }}</span>
      <a-dropdown placement="bottomRight" :overlay-style="{ 'z-index': '10000' }">
        <jt-icon class="menu-icon" type="iconlist"></jt-icon>
        <template #overlay>
          <a-menu>
            <a-menu-item v-for="item of docList" :key="item.id" @click="current = item">
              <a>{{ item.resourseName }}</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <vue-pdf-app :file-name="current.resourseName" :config="config" class="pdf-viewer" :pdf="current.resourseUrl"></vue-pdf-app>
  </div>
</template>

<script>
// import VuePdfApp from 'vue3-pdf-app';
// import this to use default icons for buttons
import 'vue3-pdf-app/dist/icons/main.css';
const config = {
  sidebar: {
    viewThumbnail: true,
    viewOutline: true,
    viewAttachments: true,
  },
  secondaryToolbar: {
    secondaryPresentationMode: true,
    secondaryOpenFile: true,
    secondaryPrint: true,
    secondaryDownload: true,
    secondaryViewBookmark: true,
    firstPage: false,
    lastPage: false,
    pageRotateCw: true,
    pageRotateCcw: true,
    cursorSelectTool: true,
    cursorHandTool: true,
    scrollVertical: false,
    scrollHorizontal: false,
    scrollWrapped: false,
    spreadNone: false,
    spreadOdd: false,
    spreadEven: false,
    documentProperties: false,
  },
  toolbar: {
    toolbarViewerLeft: {
      findbar: false,
      previous: true,
      next: true,
      pageNumber: true,
    },
    toolbarViewerRight: {
      presentationMode: true,
      openFile: false,
      print: false,
      download: true,
      viewBookmark: false,
    },
    toolbarViewerMiddle: {
      zoomOut: true,
      zoomIn: true,
      scaleSelectContainer: false,
    },
  },
  errorWrapper: true,
};

export default {
  components: {
    VuePdfApp: () => import('vue3-pdf-app'),
  },
  props: {
    docList: { type: Array, default: () => [] },
  },
  data() {
    return {
      config,
      current: {},
    };
  },
  watch: {
    docList() {
      this.current = this.docList[0] || {};
    },
  },
  mounted() {
    this.current = this.docList[0] || {};
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.pdf-viewer {
  width: 100%;
  height: 100%;
}
.pdfviewer-container {
  flex-direction: column;
  padding: 24px;
  background: #fff;
  :deep(.pdf-app .toolbar) {
    z-index: 1999;
  }
}

.title-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  .title {
    font-size: 16px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
  }
  .menu-icon {
    font-size: 24px;
    cursor: pointer;
    &:hover {
      color: #0082ff;
    }
  }
}
:deep(.pdf-app[class] #toolbarContainer) {
  background-color: #000 !important;
}
</style>
