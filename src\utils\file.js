import md5 from 'js-md5';
import { keycloak } from '@/keycloak.js';
import { axiosWithNoToken, axios } from '../request';

// 获取文件md5
export function getFileMd5(file, chunkType = true) {
  if (Object.prototype.toString.call(file) !== '[object File]') {
    return new Promise((resolve) => {
      resolve(md5(file));
    });
  }
  const fileReader = new FileReader();
  // 防止浏览器崩溃，只读取文件的前10mb的md5和后10mb的md5拼接起来
  const chunk = chunkType ? file.slice(0, 10 * 1024 * 1024) : file.slice(file.size - 10 * 1024 * 1024 < 0 ? 0 : file.size - 10 * 1024 * 1024, file.size);
  fileReader.readAsBinaryString(chunk);
  return new Promise((resolve, reject) => {
    fileReader.onload = (e) => {
      resolve(md5(e.target.result));
    };
    fileReader.onerror = () => {
      reject('uploadFail');
    };
  });
}

// 获取文件的唯一标识
export async function getFilePsw(file) {
  const [headMd5, FootMd5] = [await getFileMd5(file), await getFileMd5(file, false)];
  return await getFileMd5(headMd5 + FootMd5 + file.name + file.lastModified + keycloak.subject);
}

// 通过url下载文件（无token）
export function downloadFile(url, fileName = 'New_File') {
  if (!url) return;
  if (!fileName) return;
  axiosWithNoToken(url, { responseType: 'blob' })
    .then((res) => {
      return res.data;
    })
    .then((res) => {
      const blob = new Blob([res], { type: 'application/octet-stream' });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      link.click();
    });
}

// 通过url下载文件（带token）
export function downloadFileWithToken({ url, fileName = 'new_File', needToken = true, autoGetFileName = true, method = 'get', data = {} }) {
  if (!url) return;
  if (!fileName) return;
  const request = needToken ? axios : axiosWithNoToken;
  let innerFileName = fileName;
  let formData = {
    url,
    method,
    responseType: 'blob',
  };
  if (method === 'post') {
    formData.data = data;
  }
  request(formData)
    .then((res) => {
      if (autoGetFileName) {
        innerFileName = res.headers['content-disposition'].replace(/\w+;filename=(.*)/, '$1');
        innerFileName = decodeURIComponent(innerFileName);
      }
      return res.data;
    })
    .then((res) => {
      const blob = new Blob([res], { type: 'application/octet-stream' });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = innerFileName;
      link.click();
    });
}
