<script lang="jsx" setup>
import { DownloadOutlined, CaretDownOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="student">
    <div class="buttons">
      <a-input allow-clear placeholder="搜索姓名/学号/手机号" style="width: 240px; height: 32px" @change="handleCourseSearch">
        <template #prefix>
          <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
        </template>
      </a-input>
      <a-button class="single-add" @click="addStudent">
        <PlusOutlined />
        单独新增
      </a-button>
      <a-button type="primary" @click="handleGroupImport">
        <UploadOutlined />
        批量导入
      </a-button>
    </div>
    <div v-if="!(studentsTableData.length == 0 && searchValue == '')">
      <div class="toolbar">
        <span class="title"
          >学生名单<span class="count">{{ studentTotal }}</span></span
        >
        <a class="download-students" :href="`/course_model/web/teaching/course/student/export/?courseId=${courseId}`" target="_blank" rel="noopenner noreferrer"><DownloadOutlined />下载名单</a>
      </div>
      <div class="table">
        <a-table :columns="studentsColumns" :data-source="studentsTableData" row-key="id" :pagination="false" :loading="loading">
          <template #status="{ text }">
            <span>
              <a-tag :color="text === '1' ? 'geekblue' : 'volcano'">
                {{ text === '1' ? '成功' : '失败' }}
              </a-tag>
            </span>
          </template>
          <template #action="{ record }">
            <span class="action">
              <span style="color: #0082ff" @click="handleDeleteStudent(record)">删除</span>
            </span>
          </template>
        </a-table>
        <a-row v-if="total != 0" class="pagination">
          <a-col :span="6" class="leftpage">
            共 {{ total }} 条
            <span class="pageOption" style="margin-left: 15px">每页显示</span>
            <a-select :default-value="10" style="min-width: 50px; margin: 0 5px" @change="handlePageSize">
              <template #suffixIcon>
                <CaretDownOutlined :style="{ color: '#606266', marginRight: '-7px' }" />
              </template>
              <a-select-option :value="5">5</a-select-option>
              <a-select-option :value="10">10</a-select-option>
              <a-select-option :value="20">20</a-select-option>
            </a-select>
            <span>条</span>
          </a-col>
          <a-col :span="18" class="rightpage">
            <a-pagination show-quick-jumper :default-current="1" :total="total" :page-size="pageSize" @change="handlePage" />
          </a-col>
        </a-row>
      </div>
    </div>

    <div v-else class="empty-tooltip">
      <div style="position: relative">
        <img src="@/assets/image/emptys2x.png" alt="" />
        <div class="tootip-txt">
          <p class="intro">您暂未添加学生名单</p>
          <p class="route">请立即 <span @click="addStudent">单独新增</span><span> | </span><span @click="handleGroupImport">批量导入</span></p>
        </div>
      </div>
    </div>

    <a-modal v-model:open="showGroupImport" class="model" title="批量导入学生" :mask-closable="false">
      <a-spin :spinning="uploading" tip="上传中">
        <a-upload-dragger :file-list="fileList" :before-upload="beforeUpload" accept=".xls,.xlsx" :remove="handleRemove">
          <p class="ant-upload-drag-icon">
            <jt-icon type="iconshangchuanwenjian" />
          </p>
          <p class="ant-upload-text" style="font-size: 12px">请拖拽.xls或.xlsx文件到框内，或<a style="color: #0082ff"> 点击上传</a></p>
        </a-upload-dragger>
        <div class="model-tips">
          <span>请您参考表格样例，填写并上传表格。</span>
          <a @click="downloadModel">下载模板</a>
        </div>
      </a-spin>
      <template #footer>
        <a-space style="margin: 6px 16px">
          <a-button class="w-64" @click="hideUploadModal"> 取消 </a-button>
          <a-button type="primary" class="w-64" :disabled="uploadingBtnDisable" @click="handleUploadConfirm"> 确定 </a-button>
        </a-space>
      </template>
    </a-modal>

    <a-modal v-model:open="addModalVisible" title="单独新增学生" destroy-on-close :mask-closable="false">
      <a-form ref="addStudentModel" :model="addModalData" :colon="false" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item
          label="姓名"
          name="name"
          :rules="[
            { required: true, pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
            { max: 30, min: 1, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          ]"
        >
          <a-input v-model:value="addModalData.name" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item
          label="学号"
          name="studentID"
          :rules="[
            { required: true, pattern: numberOrLetterOrLineRegex, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
            { max: 20, min: 1, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
          ]"
        >
          <a-input v-model:value="addModalData.studentID" placeholder="请输入学号" />
        </a-form-item>
        <a-form-item
          label="手机号"
          name="phoneNumber"
          :rules="{
            required: true,
            message: '请输入正确的手机号码',
            trigger: 'blur',
            pattern: telephoneNumberRegex,
          }"
        >
          <a-input v-model:value="addModalData.phoneNumber" placeholder="请输入手机号码" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-space style="margin: 6px 16px">
          <a-button class="w-64" @click="hideAddModal"> 取消 </a-button>
          <a-button type="primary" class="w-64" :disabled="addStudentBtnDisable" @click="handleAddConfirm"> 确定 </a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script lang="jsx">
import { studentsCountByCourse, addStudentByCourse, getStudentsByCourse, deleteStudentByCourseId, downloadModel } from '@/apis/teaching.js';
import _ from 'lodash';
import { axios } from '@/request/index.js';
import { chineseOrLetterOrBlankRegex, telephoneNumberRegex, numberOrLetterOrLineRegex } from '@/utils/regex';
let url = null;

export default {
  name: 'StudentManage',

  data() {
    return {
      chineseOrLetterOrBlankRegex,
      telephoneNumberRegex,
      numberOrLetterOrLineRegex,
      loading: true,
      pageSize: 10, // 每页的条数
      pageNum: 1, // 第几页
      total: 0, // 总条数
      searchValue: '',
      courseId: this.$route.params.courseId,
      fileList: [],
      uploading: false,
      uploadingBtnDisable: true,
      studentsColumns: [
        {
          title: '姓名',
          dataIndex: 'studentName',
          key: 'studentName',
          slots: { title: 'customTitle', customRender: 'name' },
        },
        {
          title: '学号',
          dataIndex: 'studentNum',
          key: 'studentNum',
        },
        {
          title: '手机号',
          dataIndex: 'phoneNum',
          key: 'phoneNum',
        },
        {
          title: '加入时间',
          key: 'joinTime',
          dataIndex: 'joinTime',
          slots: { customRender: 'time' },
        },

        {
          title: '操作',
          width: 100,
          key: 'studentId',
          slots: { customRender: 'action' },
        },
      ],
      studentsTableData: [],
      showGroupImport: false,
      disUpload: true,
      uploadStatus: '',
      studentTotal: 0,
      addModalVisible: false,
      addModalData: {
        name: '',
        studentID: '',
        phoneNumber: '',
      },
    };
  },
  computed: {
    addStudentBtnDisable() {
      return !(this.addModalData.name && this.addModalData.studentID && this.addModalData.phoneNumber);
    },
  },
  watch: {
    uploadStatus(newValue) {
      if (newValue === 'done') {
        this.disUpload = false;
      } else {
        this.disUpload = true;
      }
    },
    addModalVisible(newValue) {
      if (!newValue) {
        this.addModalData = {
          name: '',
          studentID: '',
          phoneNumber: '',
        };
      }
    },
    showGroupImport(newValue) {
      if (!newValue) {
        this.fileList = [];
      }
    },
    fileList(newValue) {
      if (newValue.length > 0) {
        this.uploadingBtnDisable = false;
      } else {
        this.uploadingBtnDisable = true;
      }
    },
  },
  mounted() {
    this.getStudentNumber();
    this.getTableData();
  },
  methods: {
    async downloadModel() {
      if (url) {
        window.open(url);
      } else {
        const res = await downloadModel();
        if (res.state === 'OK') {
          url = res.body;
          window.open(url);
        }
      }
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.fileList = newFileList;
    },
    // 确定导入手动上传
    handleUploadConfirm() {
      if (this.fileList.length === 0) {
        return this.$message.warning('请导入文件');
      }
      const { fileList } = this;
      const formData = new FormData();
      formData.append('file', fileList[0]);
      this.uploading = true;

      axios
        .post(`/course_model/web/teaching/course/student/import?courseId=${this.courseId}`, formData, { useError: false })
        .then((res) => {
          if (res.status == 200 && res.data.state == 'OK') {
            let { group, totalNum, sucessNum, repeatNum, failNum } = res.data.body;

            let msg = `您当前导入文件中包含${totalNum}个学生，其中${sucessNum}个导入成功，${failNum}个导入失败`;
            if (repeatNum > 0) {
              msg += `（其中包含${repeatNum}个重复用户）`;
            }
            this.uploading = false;
            this.fileList = [];
            this.hideUploadModal();

            this.getStudentNumber();
            this.getTableData();
            if (totalNum == sucessNum) {
              this.$message.success(`批量导入学生成功，包含${sucessNum}个学生`);
            } else {
              this.handleUploadMsg(msg, group);
            }
          } else {
            this.uploading = false;
            this.$message.error(res.data.errorMessage || '批量导入失败');
          }
        })
        .catch(() => {
          //this.$message.error('导入失败');
          this.uploading = false;
        });
    },

    handleUploadMsg(msg, group) {
      this.$warning({
        title: '导入结果提示',
        content: (
          <div style="font-size:12px;">
            <p style="margin-bottom:8px;color:#606972;">{msg}</p>
            <a style="cursor:pointer;color:#0082FF;" href={`/course_model/web/teaching/fail/student/export?group=${group}`}>
              下载导入失败列表
            </a>
          </div>
        ),
        okText: '确定',
      });
    },

    hideUploadModal() {
      this.showGroupImport = false;
    },
    handleCourseSearch: _.debounce(function (e) {
      this.searchValue = e.target.value;
      this.getTableData();
    }, 500),
    // 获取学生总数
    async getStudentNumber() {
      const res = await studentsCountByCourse({ courseId: this.courseId });
      if (res.state === 'OK') {
        this.studentTotal = res.body;
      } else {
        this.studentTotal = 0;
      }
    },
    async getTableData(data = {}) {
      this.loading = true;
      data = Object.assign(data, {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        courseId: this.courseId,
        studentName: this.searchValue,
      });
      const res = await getStudentsByCourse(data);
      if (res.state === 'OK') {
        this.studentsTableData = res.body.data;
        this.total = res.body.total;
      } else {
        this.studentsTableData = [];
        this.total = 0;
      }
      this.loading = false;
    },
    // 提交新增数据
    handleAddConfirm() {
      let self = this;
      this.$refs.addStudentModel.validate().then(() => {
        addStudentByCourse({
          courseId: self.courseId,
          studentName: self.addModalData.name,
          studentNum: self.addModalData.studentID,
          phoneNum: self.addModalData.phoneNumber,
        }).then((res) => {
          if (res.state === 'OK') {
            self.$message.success('新增学生成功');
            self.addModalVisible = false;
            self.getTableData();
            self.getStudentNumber();
          } else {
            self.$message.error(res.errorMessage || '新增学生失败');
          }
        });
      });
    },
    hideAddModal() {
      this.addModalVisible = false;
    },
    handleDeleteStudent(record) {
      const self = this;
      let confirmModal = this.$confirm({
        icon: () => {
          return <jt-icon style="color:rgba(255, 69, 77, 1)" type="iconwarning-circle-fill"></jt-icon>;
        },
        title: <div style="font-weight:500">确定删除该学生吗？</div>,
        content: (
          <div>
            <p style="margin-bottom:6px">
              删除学生：<span style="color:#0082ff">{record.studentName}</span>
            </p>
            <p>删除后学生无法在我学习的课程里查看，相关项目实例均被删除，请谨慎操作</p>
          </div>
        ),
        okText: '删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          deleteStudentByCourseId({
            courseId: record.courseId,
            studentId: record.studentId,
          }).then((res) => {
            if (res.state === 'OK') {
              self.$message.success('删除学生成功');
              confirmModal.destroy();
              self.getTableData();
              self.getStudentNumber();
            }
          });
        },
        onCancel: () => {
          confirmModal.destroy();
        },
      });
    },
    beforeUpload(file) {
      this.fileList = [file];
      return this.fileList;
    },

    // 单独新增学生
    addStudent() {
      this.addModalVisible = true;
    },
    handleGroupImport() {
      this.showGroupImport = true;
    },
    handleUploadStudents(info) {
      this.uploadStatus = info.file.status;
    },
    handlePage(num) {
      this.pageNum = num;
      this.getTableData();
    },
    handlePageSize(size) {
      this.pageSize = size;
      this.pageNum = 1;
      this.getTableData();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.student {
  .buttons {
    position: absolute;
    top: 0px;
    right: 32px;
    .single-add {
      color: #0082ff;
      border-color: #0082ff;
      margin: 0px 9px;
    }
  }
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 24px 0 16px;
    .title {
      font-size: 18px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
    }
    .count {
      color: #606972;
      font-size: 18px;
      padding-left: 8px;
    }
    .download-students {
      i {
        margin-right: 8px;
      }
      color: #0082ff;
      cursor: pointer;
    }
  }
  .table {
    .action {
      cursor: pointer;
    }
  }
  .pagination {
    margin: 20px 0 40px;
    .leftpage {
      display: flex;
      align-items: center;
      .pagesize {
        margin-left: 20px;
      }
    }
    .rightpage {
      text-align: right;
    }
  }
}
.empty-tooltip {
  display: flex;
  justify-content: center;
  .tootip-txt {
    position: absolute;
    bottom: 90px;
    width: 100%;
    text-align: center;
    .intro {
      color: #121f2c;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .route {
      font-size: 14px;
      color: #606972;
      span {
        color: #0082ff;
        cursor: pointer;
      }
    }
  }
}
.ant-modal {
  .model-tips {
    font-size: 12px;
    margin-top: 8px;
    color: #a0a6ab;
    a {
      color: #0082ff;
    }
  }
}
</style>
