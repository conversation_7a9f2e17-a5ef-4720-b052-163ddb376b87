<template>
  <div class="course-cover">
    <div class="header">
      <div v-for="btn in btns" :key="btn.key" :class="coverType == btn.key ? `active-btn ${btn.class}` : btn.class" @click="handleCoverchange(btn.key)">{{ btn.name }}</div>
    </div>

    <a-row>
      <a-col v-show="coverType === '1'" :span="12">
        <span class="select-title"> 选择封面 </span>
        <div class="select">
          <div v-for="(cover, i) in existedCovers" :key="i" :class="{ selectCover: coverPreviewUrl === cover }" class="select-img" @click="handleChangeImage(cover)">
            <img :src="cover" alt="cover" style="width: 90px" />
          </div>
        </div>
      </a-col>

      <a-col v-show="coverType === '2'" :span="12">
        <span class="select-title">上传图片</span>
        <div class="upload">
          <a-upload-dragger
            :file-list="fileList"
            :multiple="false"
            accept=".jpg,.jpeg,.gif,.png,.bmp"
            action="./object/web/storage/image/upload"
            :headers="{
              Authorization: 'Bearer ' + refreshToken,
            }"
            :remove="handleRemove"
            @change="uploadCover"
          >
            <p class="ant-upload-drag-icon">
              <jt-icon type="iconshangchuanwenjian" />
            </p>
            <p class="ant-upload-text">请拖拽图片到框内，或<span style="color: #337dff"> 点击上传</span></p>
            <p class="ant-upload-hint">支持jpg、jpeg、gif、png、bmp格式，大小不超过10MB，建议图片比例为4:3</p>
          </a-upload-dragger>
        </div>
      </a-col>

      <a-col :span="12" style="padding-left: 120px">
        <span>封面预览</span>
        <div class="preview">
          <div class="preview-img">
            <img v-if="coverPreviewUrl" alt="" :src="coverPreviewUrl" @click="previewImg" />
            <div v-else></div>
          </div>
        </div>
      </a-col>
    </a-row>

    <a-modal v-model:open="previewVisible" :footer="null" @cancel="hidePreviewImg">
      <img alt="preview" style="width: 100%" :src="coverPreviewUrl" />
    </a-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { defaultPictures } from '@/apis/teaching.js';
const picType = ['jpg', 'jpeg', 'gif', 'png', 'bmp'];
export default {
  name: 'CourseCover',
  props: {
    imageUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      btns: [
        { name: '封面推荐', key: '1', class: 'cover-recommand-btn' },
        { name: '自定义封面', key: '2', class: 'cover-selfupload-btn' },
      ],
      coverType: '1',
      previewVisible: false,
      // 预览的地址
      coverPreviewUrl: '',
      defaultSelectUrl: '',
      uploadSelectUrl: '',
      existedCovers: [],
      fileList: [],
      editUrl: '',
    };
  },
  computed: {
    ...mapState(['refreshToken']),
  },
  watch: {
    // 如果是编辑的情况已经带了图片信息
    imageUrl(newValue) {
      if (newValue) {
        this.editUrl = newValue;
      }
    },
  },
  mounted() {
    this.getDefaultPictures();
  },
  methods: {
    handleRemove() {
      this.uploadSelectUrl = '';
      this.coverPreviewUrl = '';
    },
    // 获取默认图片列表，需要判断是否是编辑的图片，且编辑的图片是否在默认列表中
    async getDefaultPictures() {
      const res = await defaultPictures();
      if (res.state === 'OK' && res.body.length > 0) {
        this.existedCovers = res.body;
        if (this.editUrl) {
          if (this.existedCovers.includes(this.editUrl)) {
            this.coverPreviewUrl = this.editUrl;
            this.defaultSelectUrl = this.editUrl;
          } else {
            this.coverType = '2';
            this.uploadSelectUrl = this.editUrl;
            this.coverPreviewUrl = this.editUrl;
          }
        } else {
          this.defaultSelectUrl = this.existedCovers[0];
          this.coverPreviewUrl = this.defaultSelectUrl;
        }
      } else {
        // 没返回数据
        this.existedCovers = [];
        if (this.editUrl) {
          this.coverType = '2';
          this.uploadSelectUrl = this.editUrl;
          this.defaultSelectUrl = this.editUrl;
        }
      }
    },

    handleCoverchange(type) {
      this.coverType = type;
      if (type === '2') {
        this.coverPreviewUrl = this.uploadSelectUrl;
      } else {
        this.coverPreviewUrl = this.defaultSelectUrl;
      }
    },
    handleChangeImage(cover) {
      this.coverPreviewUrl = cover;
      this.defaultSelectUrl = cover;
    },
    previewImg() {
      this.previewVisible = true;
    },
    hidePreviewImg() {
      this.previewVisible = false;
    },
    uploadCover(info) {
      const file = info.file;
      const splitArr = file.name.split('.');
      // 格式校验
      if (splitArr.length > 0) {
        const type = splitArr[splitArr.length - 1];
        if (!picType.includes(type.toLocaleLowerCase())) {
          this.$message.error('上传文件格式有误');
          return false;
        }
      }
      if (file.size / 1024 > 1024 * 10) {
        this.$message.error('上传文件超过10M');
        return false;
      }

      let fileList = [...info.fileList];
      fileList = fileList.slice(-1);
      if (file.status === 'done') {
        if (file.response && file.response.state === 'OK') {
          this.uploadSelectUrl = file.response.body.url;
          this.coverPreviewUrl = this.uploadSelectUrl;
          this.$message.success(`文件上传成功`);
        }
      } else if (file.status === 'error') {
        this.$message.error(`文件上传失败`);
      }
      this.fileList = fileList;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.course-cover {
  text-align: left;
  .select-title:before {
    content: '* ';
    color: red;
  }
  .header {
    display: flex;
    margin: 20px 0px;
    cursor: pointer;
    &:nth-child(1) {
      border-radius: 2px 0px 0px 2px;
    }
    &:last-child {
      border-radius: 0px 2px 2px 0px;
    }
    div {
      border: 1px solid rgba(212, 212, 212, 1);
      text-align: center;
      width: 180px;
      height: 32px;
      line-height: 32px;
    }
    .active-btn {
      border-color: #0082ff;
      background-color: #f0f8ff;
      color: #0082ff;
    }
    .cover-recommand-btn:not(.active-btn) {
      border-right-width: 0;
    }
    .cover-selfupload-btn:not(.active-btn) {
      border-left-width: 0;
    }
  }
  .preview {
    display: flex;
    margin-top: 20px;
    .preview-img {
      width: 255px;
      height: 196px;
      border: 6px solid #f9fafb;
      border-radius: 2px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
      div {
        height: 100%;
        background-color: #f9fafb;
      }
    }
  }
  .select {
    display: flex;
    margin-top: 10px;
    flex-wrap: wrap;
    .select-img {
      padding: 10px;
      border: 4px solid transparent;
      img {
        width: 100%;
        cursor: pointer;
      }
    }
    // .select-img:active {
    //   border: 1px solid rgba(51, 125, 255, 1);
    // }
  }
  .upload {
    display: flex;
    margin-top: 20px;
    :deep(.ant-upload-list) {
      max-width: 500px;
    }
  }

  .selectCover {
    border: 4px solid #0082ff !important;
    border-radius: 6px;
  }
}
</style>
