<template>
  <div class="content">
    <div class="content-box">
      <img src="@/assets/image/4042x.png" alt="" />
      <div class="img-text">
        <p>抱歉，找不到页面</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    reload() {
      window.location.reload();
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  // margin-top: 211px;
  flex-wrap: wrap;
  position: relative;
  height: calc(100vh - 211px);
  background: #fff;
}
.content-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
img {
  width: 416px;
}
// .img-text {
//   position: absolute;
//   top: 300px;
// }
p {
  width: 100%;
  text-align: center;
  &:nth-of-type(1) {
    font-size: 28px;
    line-height: 34px;
    font-weight: 600;
  }
  &:nth-of-type(2) {
    margin-top: 16px;
    font-size: 18px;
    color: #606972;
    line-height: 20px;
    span {
      color: #178cf9;
      cursor: pointer;
    }
  }
}
</style>
