<template>
  <div class="competition-list">
    <div class="competition-list-banner">
      <div class="competition-list-banner-inner inner">
        <div class="banner-text">
          <h1>汇集国内外顶尖AI大赛</h1>
          <h2>挑战性难题、同场竞技、一较高下</h2>
        </div>
        <competition-banner></competition-banner>
      </div>
    </div>
    <div class="competition-list-tabs-contont">
      <div class="competition-list-tabs">
        <a-tabs default-active-key="2" @change="changeTabs">
          <a-tab-pane v-for="item in tabList" :key="item.key" :tab="item.label"></a-tab-pane>
          <template #rightExtra>
            <div class="tab-extra-container">
              <a-tooltip>
                <template #title>敬请期待</template>
                <div class="match">我要办赛</div>
              </a-tooltip>
            </div>
          </template>
        </a-tabs>
      </div>
    </div>
    <div class="competition-list-content">
      <jt-common-content :loading="tabMainLoading" :empty="list.length === 0" empty-title="暂无比赛" class="inner">
        <competition-list v-for="(item, index) in list" :key="index" :item="item" @click="goCompetitionDetail(item)"></competition-list>
      </jt-common-content>
      <jt-pagination v-if="list.length !== 0" class="pagination-box" :page-size="listform.pageSize" :page-num="listform.pageNum" :total="total" @changePageSize="pageSizeChange" @changePageNum="pageNumChange"> </jt-pagination>
    </div>
  </div>
</template>

<script>
import API from '@/constants/api/API.js';
import CompetitionBanner from './components/CompetitionBanner';
import CompetitionList from './components/CompetitionList';
export default {
  components: {
    CompetitionBanner,
    CompetitionList,
  },
  data() {
    return {
      list: [],
      listform: {
        TypeId: 0, // 比赛类型 正式赛、算法赛等等
        flag: 2, // 2 进行中；1 即将开始；3 已结束
        pageNum: 1,
        pageSize: 5,
      },
      total: 0,
      tabMainLoading: false,
      tabList: [
        {
          key: 2,
          label: '进行中',
        },
        {
          key: 1,
          label: '即将开始',
        },
        {
          key: 3,
          label: '已结束',
        },
      ],
    };
  },
  created() {
    if (this.$route.query.typeId) {
      this.listform.TypeId = this.$route.query.typeId;
    }
    this.getCompetitionList();
  },
  methods: {
    changeTabs(value) {
      this.listform.flag = value;
      this.listform.pageNum = 1;
      this.listform.pageSize = 5;
      this.getCompetitionList();
    },
    getCompetitionList() {
      let reqData = this.listform;
      this.list = [];
      this.tabMainLoading = true;
      API.competition_model.getCompetitionList(reqData).then((res) => {
        if (res.state === 'OK') {
          this.total = res.body.total;
          res.body.data.map((item, index) => {
            res.body.data[index].leader = JSON.parse(item.leader);
          });
          this.list = res.body.data;
        }
        this.tabMainLoading = false;
      });
    },
    goCompetitionDetail(item) {
      this.$router.push({
        path: '/competition/competition-detail',
        query: {
          id: item.cid,
          name: item.typeName,
        },
      });
    },
    pageSizeChange(pageSize) {
      this.listform.pageSize = pageSize;
      this.listform.pageNum = 1;
      this.getCompetitionList();
      this.toTop();
    },
    pageNumChange(pageNum) {
      this.listform.pageNum = pageNum;
      this.getCompetitionList();
      this.toTop();
    },
    toTop() {
      // 处理Safari
      if (document.documentElement.scrollTop || document.body.scrollTop) {
        document.documentElement.scrollTop = 460;
      } else {
        window.pageYOffset = 460;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.competition-list {
  background-color: #f4f8fa;
}

.competition-list-banner {
  height: 460px;
  background-color: #d6e7ff;
  background-image: url('~@/assets/image/home/<USER>');
  background-repeat: no-repeat;
  background-size: 498px;
  background-position: 62% 73%;
  min-width: 1200px;

  .competition-list-banner-inner {
    position: relative;
    display: flex;
    align-items: center;
  }
}

.banner-text {
  h1 {
    font-size: 48px;
    font-weight: @jt-font-weight-medium;
    color: @jt-text-color-primary;
    line-height: 67px;
    margin-bottom: 16px;
  }

  h2 {
    font-size: @jt-font-size-lg;
    font-weight: @jt-font-weight-medium;
    color: @jt-text-color-primary;
    line-height: 26px;
  }
}

.competition-list-tabs-contont {
  width: 100%;
  background: @jt-color-white;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);

  .competition-list-tabs {
    width: 1200px;
    margin: auto;
    display: flex;
    flex-direction: column;

    :deep(.ant-tabs-bar) {
      margin: 0px;
      border-bottom: 0px;
    }

    :deep(.ant-tabs-tab) {
      height: 67px;
      line-height: 43px;
    }

    :deep(.ant-tabs-nav) {
      font-size: 18px;
      margin: 1px;
    }

    // 我要办赛
    .match {
      font-size: @jt-font-size-lg;
      font-weight: @jt-font-weight;
      color: @jt-primary-color;
      border-radius: @jt-border-radius;
      border: 1px solid @jt-primary-color;
      cursor: no-drop;
      width: 104px;
      height: @jt-btn-height-base;
      text-align: center;
      line-height: 31px;
      margin-top: 17px; // 居中
    }
  }
}
.competition-list-content {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-bottom: 20px;
  background: #f4f8fa;
  flex-direction: column;
  align-items: center;
}

.pagination-box {
  display: flex;
  align-items: center;
  padding: 24px 32px;
  box-sizing: border-box;
  z-index: 1;
  background: @jt-color-white;
  width: 1200px;
  margin-top: 0;
  height: 92px;
}
</style>
