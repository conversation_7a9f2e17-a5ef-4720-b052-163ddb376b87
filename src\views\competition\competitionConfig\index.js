/**
 * 统一管理，比赛相关map集合
 */
export const competitionInfoMaps = {
  typeName: '比赛名称',
  time: '起止时间',
  competitionType: '比赛类型',
  competitionTraining: '训练平台',
  summary: '一句话简介',
  tag: '标签',
  amount: '奖池',
  imageUrl: '比赛封面',
  banner: '宣传图',
  leader: '主办方logo',
};

export const teamSettingMaps = {
  teamEditEndTime: '团队编辑截止时间',
  teamMax: '团队上限人数',
};

export const teamSignSettingMaps = {
  signupEndTime: '报名截止时间',
  rejectNames: '不允许同时报名的比赛',
  beanNum: '发放算力豆数量',
  daynum: '发放算力豆有效期',
  protocolvalue: '参赛协议',
};

// 比赛typeId类型
export const competitionTypeMaps = {
  1: '正式赛',
  2: '练习赛',
  3: '正式赛',
  4: '正式赛',
  5: '正式赛',
  6: '正式赛',
  7: '正式赛',
};

export const competitionRealTypeMaps = {
  1: '正式赛-算法赛',
  2: '练习赛',
  3: '正式赛-创意赛',
  4: '正式赛-算法赛',
  5: '正式赛-创意赛',
  6: '正式赛-算法赛', // 仅信息展示
  7: '正式赛-创意赛', // 仅信息展示
};

export const competitionTypeTextGroup = {
  1: '正式赛',
  2: '练习赛',
  3: '正式赛',
  4: '正式赛&移动云比赛',
  5: '正式赛&移动云比赛',
  6: '正式算法赛', //只展示比赛信息
  7: '正式创意赛', //只展示比赛信息
};

// 仅信息展示比赛的typeId
export const onlyInfoDiplayCompetitionTypeIds = [6, 7];

export const competitionTrainningMaps = {
  1: '毕昇平台',
  2: '移动云深度学习平台',
  3: '仅信息展示，不提供训练平台',
};

export const signStatusMaps = {
  0: '报名流程中',
  1: '已加入团队',
  2: '待加入团队',
};

export const publishStatusMaps = {
  1: '已发布',
  0: '未发布',
};

export const publishStatusKeys = {
  PUBLISHED: 1,
  UNPUBLISH: 0,
  ALL: 2,
};

export const runningStatusMaps = {
  1: '即将开始',
  2: '进行中',
  3: '已结束',
};

export const runningStatusKeys = {
  ALL: '0',
  STARTING: '1',
  RUNNING: '2',
  ENDED: '3',
};
// 移动云比赛报名当前状态（比赛详情页btn显示的文案）
export const ECLOUD_COMPETITION_SIGN_STATUS = {
  1: '立即报名',
  2: '继续报名',
  3: '继续报名',
  4: '完成报名',
};
// 普通比赛报名当前状态（比赛详情页btn显示的文案）
export const COMPETITION_SIGN_STATUS = {
  1: '已报名',
  2: '未报名',
  3: '未报名', //报名了互斥比赛
};

export const openDataCaseMaps = {
  1: '同步成功',
  2: '同步中',
  3: '同步失败',
  4: '同步中',
  5: '同步失败',
  6: '同步失败',
};
export const openDataCaseClassMaps = [
  { key: 'published', value: 1 },
  { key: 'verifying', value: 2 },
  { key: 'error', value: 3 },
  { key: 'verifying', value: 4 },
  { key: 'error', value: 5 },
  { key: 'error', value: 6 },
];

export const allowDownloadDataCase = {
  1: '是',
  0: '否',
};

export const allowOpenDataCase = {
  1: '是',
  0: '否',
};

/**
 * 我参加的比赛 有6种状态
 * 正式赛或练习赛 全部都为空，显示‘您暂无xxx’ NO_OFFICIAL_COMPETITION/NO_PRACTICE_COMPETITION
 * 正式赛或练习赛 全部不为空，进行中有比赛，已结束无比赛，显示 ‘您暂无进行中的xxx’  NO_RUNNING_OFFICIAL_COMPETITION/NO_RUNNING_PRACTICE_COMPETITION
 * 正式赛或练习赛 全部不为空，已结束有比赛，进行中无比赛，显示 ‘您暂无已结束的xxx’  NO_ENDED_OFFICIAL_COMPETITION/NO_ENDED_PRACTICE_COMPETITION
 */
export const EMPTY_STATE_TEXT_TYPE_MAPS = {
  NO_OFFICIAL_COMPETITION: 'NO_OFFICIAL_COMPETITION',
  NO_PRACTICE_COMPETITION: 'NO_PRACTICE_COMPETITION',
  NO_RUNNING_OFFICIAL_COMPETITION: 'NO_RUNNING_OFFICIAL_COMPETITION',
  NO_ENDED_OFFICIAL_COMPETITION: 'NO_ENDED_OFFICIAL_COMPETITION',
  NO_RUNNING_PRACTICE_COMPETITION: 'NO_RUNNING_PRACTICE_COMPETITION',
  NO_ENDED_PRACTICE_COMPETITION: 'NO_ENDED_PRACTICE_COMPETITION',
};
export const EMPTY_STATE_TEXT_MAPS = {
  NO_OFFICIAL_COMPETITION: {
    title: '您暂无正式赛',
    text: '快去报名吧',
  },
  NO_PRACTICE_COMPETITION: {
    title: '您暂无练习赛',
    text: '快去看看吧',
  },
  NO_RUNNING_OFFICIAL_COMPETITION: {
    title: '您暂无进行中的正式赛',
    text: '快去报名吧',
  },
  NO_ENDED_OFFICIAL_COMPETITION: {
    title: '您暂无已结束的正式赛',
    text: '快去报名吧',
  },
  NO_RUNNING_PRACTICE_COMPETITION: {
    title: '您暂无进行中的练习赛',
    text: '快去看看吧',
  },
  NO_ENDED_PRACTICE_COMPETITION: {
    title: '您暂无已结束的练习赛',
    text: '快去看看吧',
  },
};

/**
 * 我参加的比赛 1正式赛 2练习赛
 */
export const competitionTypeIdMaps = {
  officialCompetition: '1',
  practiceCompetition: '2',
};
export const competitionStatusMaps = {
  ALL: '',
  RUNNING: '2',
  ENDED: '3',
};

// 移动云比赛 报名steps步骤条下标
export const ECLOUD_COMPETITION_STEPS_TYPE = {
  REGISTER: 0,
  ACCOUNT_OPEN: 1,
  DEEPLEARN_SUBSCRIBED: 2,
  FINISH: 3,
};

// 普通比赛 报名steps步骤条下标
export const COMPETITION_STEPS_TYPE = {
  REGISTER: 0,
  FINISH: 1,
};

// 普通比赛步骤条title
export const COMPETITION_STEPS_TITLE = ['基本信息', '完成'];

// 移动云比赛步骤条title
export const ECLOUD_COMPETITION_STEPS_TITLE = ['基本信息', '移动云账号开通', '深度学习平台订购', '完成'];

// 移动云比赛 向后端发送当前第几步
export const ECLOUD_COMPETITION_JOIN_TYPE = {
  REGISTER: 1,
  ACCOUNT_OPEN: 2,
  DEEPLEARN_SUBSCRIBED: 3,
  FINISH: 4,
};

// 移动云比赛接口返回当前步骤，对应步骤条的下标
export const ECLOUD_COMPETITION_JOIN_STEP_TYPE = {
  [ECLOUD_COMPETITION_JOIN_TYPE.REGISTER]: ECLOUD_COMPETITION_STEPS_TYPE.REGISTER,
  [ECLOUD_COMPETITION_JOIN_TYPE.ACCOUNT_OPEN]: ECLOUD_COMPETITION_STEPS_TYPE.ACCOUNT_OPEN,
  [ECLOUD_COMPETITION_JOIN_TYPE.DEEPLEARN_SUBSCRIBED]: ECLOUD_COMPETITION_STEPS_TYPE.DEEPLEARN_SUBSCRIBED,
  [ECLOUD_COMPETITION_JOIN_TYPE.FINISH]: ECLOUD_COMPETITION_STEPS_TYPE.FINISH,
};

// 比赛详情tabs
export const COMPETITION_DETAIL_TABS = {
  INTRODUCE: 'introduce',
  DESCRIPTION: 'description',
  SUBMIT_RESULT: 'submitResult',
  MY_TEAM: 'myTeam',
  RANKINGS: 'ranking',
  COMMON_QUESTION: 'commonQuestion',
};

// 我的团队 -- 加入团队显示modal的状态码
export const MYTEAM_JOIN_TEAM_MODAL_TYPE = {
  NOT_OPEN: '-801', // 未开放
  REGISTERED: '-802', // 已注册
  USER_JOINED_EXPIRED: '-803', // 其他用户加入，该链接已失效
  NOT_REGISTERED: '-804', // 未注册
  JOINED_NOT_LEADER: '-805', // 已加入团队，不是队长
  JOINED_LEADER: '-806', // 已加入团队，是队长
  URL_EXPIRED: '-807', // 该链接已失效
  INVITE_JOIN: 200, // 成功邀请
  TO_COMPETITION_LIST: '-529', // 跳转到比赛列表页
  SUBMIT_RESULT_FILES: '-708', // 提交完结果文件，不允许解散团队
};

// 我的团队 -- 加入团队显示modal的文案
export const MYTEAM_JOIN_TEAM_MODAL_TEXT = {
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.NOT_OPEN]: {
    // 比赛当前未开放团队编辑操作，邀请链接已失效，无法加入团队
    title: '无法加入团队',
    okText: '知道了',
    cancelText: '',
    content: '比赛当前未开放团队编辑操作，邀请链接已失效，无法加入团队',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.REGISTERED]: {
    // 您已报名XXX，不可同时报名本比赛，无法加入团队
    title: '无法加入团队',
    okText: '知道了',
    cancelText: '',
    content: '',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.USER_JOINED_EXPIRED]: {
    // 已有其他用户通过该邀请加入团队，邀请链接已失效，无法加入团队
    title: '无法加入团队',
    okText: '知道了',
    cancelText: '',
    content: '已有其他用户通过该邀请加入团队，邀请链接已失效，无法加入团队',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.NOT_REGISTERED]: {
    //您尚未报名本比赛，请先完成报名后再加入团队
    title: '无法加入团队',
    okText: '前往报名',
    cancelText: '再想想',
    content: '您尚未报名本比赛，请先完成报名后再加入团队',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.JOINED_NOT_LEADER]: {
    //您已在团队中，无法加入新的团队，您可联系当前团队队长，将您移除团队后，再次尝试加入
    title: '无法加入团队',
    okText: '知道了',
    cancelText: '',
    content: '您已在团队中，无法加入新的团队, 您可联系当前团队队长，将您移除团队后，再次尝试加入',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.JOINED_LEADER]: {
    //您已在团队中，无法加入新的团队。您可解散当前团队，或从当前团队退出后，再次尝试加入
    title: '无法加入团队',
    okText: '知道了',
    cancelText: '',
    content: '您已在团队中，无法加入新的团队。您可解散当前团队，或从当前团队退出后，再次尝试加入',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.URL_EXPIRED]: {
    title: '无法加入团队',
    okText: '知道了',
    cancelText: '',
    content: '邀请链接已失效，无法加入团队',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.INVITE_JOIN]: {
    title: '团队邀请',
    okText: '确定加入',
    cancelText: '再想想',
    content: '',
  },
  [MYTEAM_JOIN_TEAM_MODAL_TYPE.SUBMIT_RESULT_FILES]: {
    // 提交完结果文件 不可解散团队
    title: '解散团队失败',
    okText: '知道了',
    cancelText: '',
    content: '结果文件提交已截止，无法解散团队',
  },
};

// 比赛详情tab -- 提交结果
export const SUBMIT_RESULT_MAPS = {
  SUBMIT_FILES: 1,
  SUBMIT_RECORD: 2,
};

// 提交结果文件提示状态
export const SUBMIT_RESULT_FILES_STATE = {
  NOT_OPEN: '1',
  MAX_LIMIT: '2',
  SUBMIT_SEVERAL_TIMES: '3',
  CREATE_TEAM_SUBMIT: '4',
  LEADER_SUBMIT: '5',
};

// 提交记录class
export const SUBMIT_STATUS_CLASS = {
  1: 'succeed-state',
  2: 'fail-state',
  3: 'submit-state',
  4: 'submit-state',
  5: 'submit-state',
};

// 结果文件状态
export const RESULT_FILES_STATE = {
  SUCCESS: 1,
  FAIL: 2,
  SUBMITTING: 3,
  SCORING: 4,
};

// 答辩记录状态
export const REPLY_RECORD_STATE = {
  SUCCESS: 1,
  FAIL: 2,
  SUBMITTING: 3,
};
