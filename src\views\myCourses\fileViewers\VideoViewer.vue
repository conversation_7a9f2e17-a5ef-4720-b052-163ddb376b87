<template>
  <div id="video-frame">
    <div id="iframe-video-container" @load.capture="onFrameLoaded" style="height: 100%" v-html="decodedUrl"></div>
  </div>
</template>

<script>
import { GET } from '@/request';
export default {
  name: 'ViedeoViewer',
  data() {
    return {
      videoUrl: '',
    };
  },
  computed: {
    decodedUrl() {
      if (this.videoUrl.indexOf('<iframe') > -1) {
        return this.videoUrl;
      }
      return decodeURIComponent(this.videoUrl);
    },
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getResourceUrl();
  },
  methods: {
    getResourceUrl() {
      GET(`/course_model/web/teaching/catalog/resource/list/id`, { id: this.id }).then((res) => {
        this.videoUrl = res.body.resourseUrl;
      });
    },
    /* 这里需要强制设置嵌入iframe的样式 */
    onFrameLoaded(e) {
      const frame = e.target;
      frame.style.width = '100%';
      frame.style.height = '100%';
      // 对咪咕视频做的配置
      const content = frame.contentWindow; // 绕过solar检查
      content.postMessage(
        {
          type: 'skin_cfg',
          data: {
            value: {
              isvrbtn: false,
              liveTimeUI: false,
              loopBtn: false,
              // speedBtn: false,
              ratetypeBox: false,
              nextBtn: false,
            },
          },
        },
        'https://www.miguvideo.com'
      );
    },
  },
};
</script>

<style lang="less" scoped>
#video-frame {
  height: 100vh;
}
#iframe-video-containe {
  width: 100%;
  height: 100vh;
}
</style>
