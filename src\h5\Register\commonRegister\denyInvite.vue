<template>
  <div class="notinvite">
    <van-popup v-model="showModel" class="invitePop" :close-on-click-overlay="false">
      <div class="popup-header"></div>
      <div class="popup-content-fail">
        <img :src="failicon" alt="" />
        <p class="fail-title">抱歉，活动暂未开放</p>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'denyInvite',
  data() {
    return { showModel: false, failicon: require('@/assets/image/h5/fail.png') };
  },
};
</script>

<style lang="less">
.notinvite {
  .invitePop {
    width: 80%;
    background: #ffffff;
    box-shadow: 0px 13px 14px 0px rgba(10, 31, 149, 0.08);
    border-radius: 6px;
    border: 2px solid #ffffff;
    text-align: center;
    padding: 15px 15px 30px;
    .popup-header {
      height: 20px;
    }
    .popup-content-success {
      color: #41497a;
      font-size: 17px;
      text-align: center;
      font-family: PingFangSC-Regular, PingFang SC, sans-serif;
      img {
        width: 80px;
        margin-bottom: 20px;
      }
      .success-title {
        margin-bottom: 12px;
        font-size: 18px;
        font-weight: 400;
        color: #121f2c;
      }

      .success-text {
        line-height: 1.8;
        font-size: 12px;
        color: #606972;
      }
      .success-link {
        color: #0082ff;
      }
      .success-info {
        margin-bottom: 15px;
        font-weight: 600;
      }
    }
    .popup-content-fail {
      font-family: PingFang-SC-Semibold, PingFang-SC, sans-serif;
      font-weight: 600;
      img {
        width: 70px;
        margin: 16px;
      }
      .fail-title {
        font-size: 16px;
        color: #f63f48;
        margin-bottom: 7px;
      }
      .fail-info {
        font-size: 14px;
        color: #41497a;
        margin-bottom: 25px;
      }
    }
  }
}
</style>
