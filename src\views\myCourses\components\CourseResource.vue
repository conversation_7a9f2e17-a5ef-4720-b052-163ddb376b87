<template>
  <div>
    <a-space v-if="isNotOpenAndPublishedCourse" class="operation-group">
      <a-button v-if="currentActiveCourse.courseFlag != '1'" class="publish-btn" :disabled="courseResourceData.status === null || courseResourceData.status == '1'" @click="handlePublish"><jt-icon type="iconfasong"></jt-icon>发布</a-button>
      <a-button type="primary" @click="handleEditClick">
        <EditOutlined />
        编辑
      </a-button>
    </a-space>
    <!-- 代码量为必填，有代码量时则显示内容 -->
    <div v-if="courseResourceData.codeNum">
      <a-space class="course-resource-header">
        <a-tag :color="courseResourceData.status == '1' ? 'green' : ''">{{ courseResourceData.status == '1' ? '已发布' : '未发布' }}</a-tag>
        <span>上次发布时间： {{ courseResourceData.pubTime || '————' }}</span>
      </a-space>
      <div class="title">资源基本信息</div>
      <div class="data-items">
        <p>算力需求：{{ foramtCompute(courseResourceData.flag) }}</p>
        <p>代码量：{{ courseResourceData.codeNum }}</p>
        <p>数据量：{{ courseResourceData.dataNum }}</p>
        <p>九天能力支持：{{ formateJtFlag(courseResourceData.jiuFlag) }}</p>
      </div>

      <div class="title">参考资料</div>
      <div>
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div class="html-area markdown-body" v-html="formatMarkDown(courseResourceData.courseDataData)" />
      </div>

      <div class="title">版权声明</div>
      <div>
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div class="html-area markdown-body" v-html="formatMarkDown(courseResourceData.courseCopyrightData)" />
      </div>
    </div>

    <div v-else class="empty-tooltip">
      <div style="position: relative">
        <img src="@/assets/image/emptys2x.png" alt="" />
        <div class="tootip-txt">
          <p class="intro">您暂未添加课程资源</p>
          <p v-if="isNotOpenAndPublishedCourse" class="route">请立即 <span @click="handleEditClick">编辑课程资源</span></p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { EditOutlined } from '@ant-design/icons-vue';
</script>

<script>
import { mapState, mapMutations } from 'vuex';
import { getCourseResource, textFind, publishIntroOrResource } from '@/apis/teaching.js';

export default {
  name: 'CourseResource',
  data() {
    return {
      courseId: this.$route.params.courseId,
    };
  },
  computed: {
    ...mapState('course', ['courseResourceData', 'currentActiveCourse']),
    isNotOpenAndPublishedCourse() {
      return !(this.currentActiveCourse.courseFlag == '1' && this.currentActiveCourse.coursePublish == '1');
    },
    subTab() {
      return this.$route.params.subTab;
    },
  },
  watch: {
    subTab() {
      if (this.subTab === 'course-resource') {
        this.initData();
      }
    },
  },
  mounted() {
    if (this.subTab === 'course-resource') {
      this.initData();
    }
  },
  methods: {
    ...mapMutations('course', ['SET_COURSERESOURCE_DATA']),
    foramtCompute: function (value) {
      if (!value) {
        return '无';
      } else {
        return value.map((item) => (item == 'cpu' ? 'CPU' : 'vGPU')).join('，');
      }
    },
    async initData() {
      const res = await getCourseResource({ courseId: this.courseId });
      if (res.state === 'OK') {
        const courseResourceData = Object.assign({}, res.body);
        if (courseResourceData.flag) {
          courseResourceData.flag = courseResourceData.flag.split(',');
        }
        if (courseResourceData.jiuFlag) {
          courseResourceData.jiuFlag = courseResourceData.jiuFlag.split(',');
        }
        // 这块比较麻烦需要判断参考资料和版本信息到底有没有存数据，分四种情况
        if (courseResourceData.courseData && courseResourceData.courseCopyright) {
          Promise.all([textFind({ id: courseResourceData.courseData }), textFind({ id: courseResourceData.courseCopyright })]).then((res) => {
            if (res[0].state === 'OK' && res[1].state === 'OK') {
              courseResourceData.courseDataData = res[0].body.content;
              courseResourceData.courseCopyrightData = res[1].body.content;
              this.SET_COURSERESOURCE_DATA(courseResourceData);
            }
          });
        } else if (courseResourceData.courseData) {
          textFind({ id: courseResourceData.courseData }).then((res) => {
            if (res.state === 'OK') {
              courseResourceData.courseDataData = res.body.content;
              this.SET_COURSERESOURCE_DATA(courseResourceData);
            }
          });
        } else if (courseResourceData.courseCopyright) {
          textFind({ id: courseResourceData.courseCopyright }).then((res) => {
            if (res.state === 'OK') {
              courseResourceData.courseCopyrightData = res.body.content;
              this.SET_COURSERESOURCE_DATA(courseResourceData);
            }
          });
        } else {
          this.SET_COURSERESOURCE_DATA(courseResourceData);
        }
      }
      // const item = {
      //   flag: 'cpu,vGpu',
      //   codeNum: 'int',
      //   dataNum: '',
      //   jiuFlag: '1,2,3', //1：九天可视化建模平台  2：九天智能交互平台、3：九天深度学习平台
      //   courseCopyright: '', // 版权声明
      //   specDes: '算力资源描述',
      //   courseData: '',
      //   status: ''
      // }
    },
    formatMarkDown: function (value) {
      if (value && value != ' ') {
        return value;
      } else {
        return '<span>无</span>';
      }
    },
    async handlePublish() {
      const res = await publishIntroOrResource({
        courseId: this.courseId,
        type: 'resource',
      });
      if (res.state === 'OK') {
        this.$message.success('课程资源发布成功');
        this.initData();
      }
    },

    handleEditClick() {
      const params = { ...this.$route.params, subPage: 'course-resource-edit' };
      this.$router.push({
        name: '课程管理编辑',
        params,
      });
    },
    formateJtFlag(jtFlag) {
      const jtMaps = {
        1: '九天可视化建模平台',
        2: '九天智能交互平台',
        3: '九天深度学习平台',
      };
      let res = '无';
      if (jtFlag) {
        if (jtFlag.length > 0) {
          res = '';
        }
        for (let i = 0; i < jtFlag.length; i++) {
          if (i !== jtFlag.length - 1) {
            res += jtMaps[jtFlag[i]] + '，';
          } else {
            res += jtMaps[jtFlag[i]];
          }
        }
      }
      return res;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.operation-group {
  position: absolute;
  top: 0px;
  right: 32px;
}
.course-resource-header {
  height: 40px;
  width: 100%;
  background-color: #f8f9fa;
  padding-left: 16px;
  margin-bottom: 24px;
  font-size: 12px;
  color: #606972;
}

.data-items {
  margin-bottom: 48px;
  p {
    margin-bottom: 24px;
    color: #606972;
  }
}

.title {
  font-weight: @jt-font-weight-medium;
  color: #121f2c;
  margin-bottom: 16px;
}

.html-area {
  color: #606972;
  min-height: 60px;
  white-space: pre-line;
  margin-bottom: 48px;
}
.empty-tooltip {
  display: flex;
  justify-content: center;
  .tootip-txt {
    position: absolute;
    bottom: 90px;
    width: 100%;
    text-align: center;
    .intro {
      color: #121f2c;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .route {
      font-size: 14px;
      color: #606972;
      span {
        color: #0082ff;
        cursor: pointer;
      }
    }
  }
}
</style>
