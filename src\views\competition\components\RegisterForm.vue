<template>
  <jt-common-content :loading="loading">
    <div class="section-form">
      <a-form ref="form" :colon="false" class="form-content" :rules="rules" :label-col="{ span: 6, offset: 1 }" :wrapper-col="wrapperCol" :model="formData">
        <a-form-item label="姓名" name="fullName">
          <a-input v-model:value="formData.fullName" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item label="身份" name="identity" :style="{ 'margin-bottom': '16px' }">
          <a-radio-group v-model:value="formData.identity" :style="{ 'padding-top': '10px' }" @change="changeIdentity">
            <a-radio value="教师"> 教师 </a-radio>
            <a-radio value="学生"> 学生 </a-radio>
            <a-radio value="开发者"> 开发者 </a-radio>
          </a-radio-group>
        </a-form-item>
        <div class="extra-info-wrap">
          <div class="extra-info">
            <a-form-item class="sub-item school-item" required :label-col="labelCol" :wrapper-col="subWrapperCol" label="学校" :style="{ 'margin-bottom': '24px' }">
              <a-space>
                <a-form-item class="area-item">
                  <a-select v-model:value="formData.schoolArea" :options="areaOptions" show-search placeholder="地区" @change="areaChange" />
                </a-form-item>
                <a-form-item v-if="formData.schoolArea === '其他'" class="area-school-item" name="schoolInput">
                  <a-input v-model:value="formData.schoolInput" :placeholder="schoolePlaceholder" />
                </a-form-item>
                <a-form-item v-else class="area-school-item" name="school">
                  <a-select v-model:value="formData.school" show-search :placeholder="schoolePlaceholder" :options="schoolOptions" />
                </a-form-item>
              </a-space>
            </a-form-item>
            <div v-if="formData.identity === '开发者'">
              <a-form-item class="sub-item school-item" :label-col="labelCol" :wrapper-col="subWrapperCol" label="工作单位" :style="{ 'margin-bottom': '24px' }" name="company">
                <a-space>
                  <a-form-item class="area-item">
                    <a-select v-model:value="formData.companyArea" :options="workAreaOptions" show-search placeholder="地区" />
                  </a-form-item>
                  <a-form-item class="area-school-item">
                    <a-input v-model:value="formData.company" style="width: 183px" :disabled="formData.companyArea == undefined" placeholder="工作单位" />
                  </a-form-item>
                </a-space>
              </a-form-item>
            </div>
            <a-form-item v-if="formData.identity === '教师' || formData.identity === '学生'" :label-col="labelCol" :wrapper-col="wrapperCol" class="sub-item mrb24" label="院系" name="faculty">
              <a-input v-model:value="formData.faculty" placeholder="请输入" />
            </a-form-item>
            <a-form-item v-if="formData.identity === '学生'" :label-col="labelCol" :wrapper-col="wrapperCol" class="sub-item mrb24" label="专业" name="major">
              <a-input v-model:value="formData.major" placeholder="请输入" />
            </a-form-item>
            <a-form-item v-if="formData.identity === '学生'" :label-col="labelCol" :wrapper-col="wrapperCol" class="sub-item mrb24" label="学号" name="stuNum">
              <a-input v-model:value="formData.stuNum" placeholder="请输入" />
            </a-form-item>
          </div>
        </div>
        <a-form-item
          label="联系方式"
          class="tel-item"
          :rules="[
            {
              required: true,
              message: '',
            },
          ]"
        >
          <div class="extra-info1">
            <a-form-item class="sub-item phoneNum-item" :label-col="labelCol" :wrapper-col="{ span: 20 }" label="手机号" :style="{ 'margin-bottom': '24px' }">
              <a-space>
                <a-form-item name="phoneNum"> <a-input v-model:value="formData.phoneNum" class="phoneNum-input" placeholder="请输入手机号" :disabled="true" /><span v-if="!formData.phoneNum" class="phone-extra-msg">请在账号管理页面绑定手机号</span></a-form-item>
                <a-form-item v-if="!checkPhoneNumInput" style="width: 119px" name="code">
                  <a-input v-model:value="formData.code" :disabled="disPhoneNum || checkPhoneNum" placeholder="请输入验证码" />
                </a-form-item>
                <a-form-item v-if="!checkPhoneNumInput">
                  <a-button :disabled="disPhoneNum || checkPhoneNum || timmer > 0" :class="{ 'input-disabled': disPhoneNum || checkPhoneNum || timmer > 0 }" type="primary" ghost @click="handleSendCode">{{ timmer > 0 ? `重新获取 ${timmer}` : `${sended ? '重新获取' : '获取验证码'}` }}</a-button>
                </a-form-item>
              </a-space>
            </a-form-item>

            <a-form-item class="sub-item email-item" :label-col="labelCol" :wrapper-col="{ span: 20 }" label="邮箱" name="email"> <a-input v-model:value="formData.email" class="email-input" placeholder="请输入邮箱" /> </a-form-item>
          </div>
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 17, offset: 7 }">
          <a-checkbox :checked="checkNick" @change="readAgreementChange"> 我已阅读并同意 </a-checkbox>
          <span class="agree-btn" @click="agreementModalForm.visible = true">《{{ cname }}参赛协议》</span>
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 17, offset: 7 }">
          <a-button type="primary" :disabled="submitBtnDisabled || !checkNick" @click="onSubmit"> {{ isEcloudCompetition ? '保存并下一步' : '报名' }} </a-button>
          <a-button style="margin-left: 10px" @click="goCompetitionDetail"> 取消 </a-button>
        </a-form-item>
      </a-form>
      <a-modal v-model:open="agreementModalForm.visible" title="比赛协议" :width="720" :footer="null">
        <h3 style="text-align: center; padding-bottom: 16px">《{{ cname }}参赛协议》</h3>
        <!-- eslint-disable vue/no-v-html -->
        <p class="protocolvalue" v-html="protocolvalue"></p>
      </a-modal>
    </div>
  </jt-common-content>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, getCurrentInstance } from 'vue';
import API from '@/constants/api/API.js';
import cloneDeep from 'lodash/cloneDeep';
import { sendCode } from '@/utils/utils';
import { chineseOrLetterOrBlankRegex, numberOrLetterOrLineRegex, emailRegex } from '@/utils/regex';
import { ECLOUD_COMPETITION_STEPS_TYPE, ECLOUD_COMPETITION_JOIN_TYPE, COMPETITION_STEPS_TYPE } from '@/views/competition/competitionConfig/index';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';

const props = defineProps({
  isEcloudCompetition: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['changeCurrent', 'changeCompleteSuccess']);

const route = useRoute();
const router = useRouter();
const store = useStore();
const form = ref();

const cid = ref(route.query.id);
const cname = ref(route.query.name);
const checkNick = ref(false);
const submitBtnDisabled = ref(false);
const formData = reactive({
  cid: '',
  fullName: '',
  identity: '',
  email: '',
  code: '',
  schoolArea: undefined,
  school: undefined,
  schoolInput: undefined,
  companyArea: undefined,
  company: undefined,
  faculty: '',
  major: '',
  stuNum: '',
  phoneNum: '',
  emailChecked: false,
});
const timmer = ref(0);
const sended = ref(false);
const labelCol = ref({ span: 4 });
const wrapperCol = ref({ span: 12 });
const subWrapperCol = ref({ span: 14 });
const areaOptions = ref([]);
const schoolOptions = ref([]);
const workAreaOptions = ref([]);
const originPhoneNum = ref('');
const protocolvalue = ref('');
const agreementModalForm = reactive({
  title: '比赛协议',
  visible: false,
});
const loading = ref(false);
const disPhoneNum = ref(true);
const getSchoolArea = ref(null);

const schoolePlaceholder = computed(() => {
  const placeholderMap = new Map([
    ['教师', '就职学校'],
    ['学生', '就读学校'],
    ['开发者', '毕业学校'],
  ]);
  return placeholderMap.get(formData.identity);
});

const checkPhoneNum = computed(() => {
  return !((formData.phoneNum && !originPhoneNum.value) || originPhoneNum.value !== formData.phoneNum);
});
const checkPhoneNumInput = computed(() => {
  return !!originPhoneNum.value;
});

const rules = computed(() => ({
  identity: [{ required: true, message: '请选择身份', trigger: ['blur', 'change'] }],
  fullName: [
    { required: true, message: '请输入', trigger: ['blur', 'change'] },
    { max: 30, min: 0, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
    { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
  ],
  school: [{ required: true, message: '请选择/输入学校', trigger: ['change'] }],
  schoolInput: [
    { required: true, message: '请选择/输入学校', trigger: ['blur', 'change'] },
    { min: 0, max: 50, message: '50字以内的中英文、空格', trigger: ['blur', 'change'] },
    { pattern: chineseOrLetterOrBlankRegex, message: '50字以内的中英文、空格', trigger: ['blur', 'change'] },
  ],
  company: [
    { required: true, message: '请选择/输入工作地点', trigger: ['blur', 'change'] },
    { min: 0, max: 30, message: '30字以内的中英文、空格', trigger: ['blur', 'change'] },
    { pattern: chineseOrLetterOrBlankRegex, message: '30字以内的中英文、空格', trigger: ['blur', 'change'] },
  ],
  faculty: [
    { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
    { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
  ],
  major: [
    { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
    { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
  ],
  stuNum: [
    { min: 0, max: 20, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
    { pattern: numberOrLetterOrLineRegex, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
  ],
  phoneNum: [{ required: true, message: ' ', trigger: ['blur', 'change'] }],
  email: [
    { required: true, message: '请输入', trigger: ['blur', 'change'] },
    { pattern: emailRegex, message: '请输入正确格式邮箱', trigger: ['blur', 'change'] },
  ],
}));

watch(
  () => formData.phoneNum,
  (val) => {
    const reg = /^1[3456789]\d{9}$/;
    disPhoneNum.value = !reg.test(val);
  }
);

watch(getSchoolArea, (val) => {
  if (val == '其他') {
    formData.schoolInput = formData.school;
  }
});

onMounted(() => {
  getBySchoolArea();
  getUserInfo();
  getAreaOptions();
  getProtocol();
});

function onSubmit() {
  form.value
    .validate()
    .then(() => {
      const formCopy = { ...formData };
      if (formCopy.identity === '开发者') {
        formCopy.faculty = '';
        formCopy.major = '';
        formCopy.stuNum = '';
      } else if (formCopy.identity === '教师') {
        formCopy.major = '';
        formCopy.stuNum = '';
      }
      formData.cid = route.query.id;
      submitBtnDisabled.value = true;
      if (props.isEcloudCompetition) {
        addEcloudInfo();
      } else {
        onFormOk({ ...formData, phoneNum: '' });
      }
    })
    .catch((err) => {
      throw new Error(err);
    });
}

function getEcloudInfo() {
  loading.value = true;
  API.competition_model.getEcloudInfo({ cid: cid.value }).then((res) => {
    if (res.state === 'OK') {
      Object.assign(formData, res.body.userInfo);
    } else if (res.state === 'ERROR' && res.errorCode == '-509') {
      this.$message.error('系统繁忙，请稍后重试');
    }
    loading.value = false;
  });
}

function addEcloudInfo() {
  let ecloudJoinRequest = {};
  ecloudJoinRequest.signUpInfo = formData;
  ecloudJoinRequest.cid = cid.value;
  ecloudJoinRequest.joinSta = ECLOUD_COMPETITION_JOIN_TYPE.REGISTER;
  loading.value = true;
  if (formData.schoolArea == '其他') {
    formData.school = formData.schoolInput;
  }
  API.competition_model.addEcloudInfo(ecloudJoinRequest).then((res) => {
    if (res.state === 'OK') {
      this.$message.success('基本信息保存成功');
      emit('changeCurrent', ECLOUD_COMPETITION_STEPS_TYPE.ACCOUNT_OPEN);
    } else if (res.state === 'ERROR' && res.errorCode == '-509') {
      this.$message.error('系统繁忙，请稍后重试');
    } else {
      this.$message.error(res.errorMessage || '报名失败，请稍后再试');
    }
    submitBtnDisabled.value = false;
    loading.value = false;
  });
}

function onFormOk(form = formData) {
  formData.cid = route.query.id;
  if (formData.schoolArea == '其他') {
    formData.school = formData.schoolInput;
  }
  API.competition_model.getCompleteInfo(form).then((res) => {
    if (res.state === 'OK') {
      getUserInfo();
      const redirectUrl = route.query.redirectUrl;
      redirectUrl && router.push(redirectUrl);
      emit('changeCurrent', COMPETITION_STEPS_TYPE.FINISH);
      emit('changeCompleteSuccess', res.body);
    } else {
      this.$message.error(res.errorMessage || '报名失败，请稍后再试');
    }
    submitBtnDisabled.value = false;
  });
}

function getUserInfo() {
  loading.value = true;
  API.competition_model.getUserInfo().then((res) => {
    Object.assign(formData, res.body);
    store.commit('SET_USERINFO_DATA', res.body);
    getBySchoolArea();
    originPhoneNum.value = formData.phoneNum;
    const { companyArea, school, schoolArea } = res.body;
    formData.companyArea = companyArea || undefined;
    formData.school = school || undefined;
    formData.schoolArea = schoolArea || undefined;
    if (formData.schoolArea == '其他') {
      formData.schoolInput = undefined;
    }
    loading.value = false;
  });
}

function handleSendCode() {
  sendCode(formData.phoneNum).then((res) => {
    if (!res.errorCode) {
      sended.value = true;
      timmer.value = 60;
      timmerDecrease();
      this.$message.success('发送成功');
    } else {
      this.$message.error(res.errorMessage || '发送失败');
    }
  });
}

function timmerDecrease() {
  if (timmer.value > 0) {
    timmer.value--;
    setTimeout(() => {
      timmerDecrease();
    }, 1000);
  }
}

function getAreaOptions() {
  API.keycloak_model.getProvince().then((res) => {
    if (res.state === 'OK') {
      const options = res.body.map((item) => ({
        value: item,
        label: item,
      }));
      options.push({
        value: '其他',
        label: '其他',
      });
      areaOptions.value = options;
      workAreaOptions.value = cloneDeep(options);
    }
  });
}

function getBySchoolArea() {
  if (!formData.school) {
    return;
  }
  API.keycloak_model.getProvinceBySchool({ school: formData.school }).then((res) => {
    formData.schoolArea = res.body && res.body.length > 0 ? res.body : '其他';
    if (formData.schoolArea !== '其他') {
      getSchoolOptions();
    }
    getSchoolArea.value = res.body;
  });
}

function getSchoolOptions() {
  API.keycloak_model.getSchool({ province: formData.schoolArea }).then((res) => {
    schoolOptions.value = res.body.map((item) => {
      return {
        value: item,
        label: item,
      };
    });
  });
}

function changeIdentity(e) {
  form.value.clearValidate();
  switch (e.target.value) {
    case '教师':
      formData.faculty = undefined;
      break;
    case '学生':
      formData.faculty = undefined;
      formData.major = undefined;
      formData.stuNum = undefined;
      break;
    case '开发者':
      formData.company = undefined;
      formData.companyArea = undefined;
      break;
    default:
      break;
  }
}

function areaChange(val) {
  formData.schoolArea = val;
  formData.school = undefined;
  if (val !== '其他') {
    getSchoolOptions();
  }
}

function workAreaChange(val) {
  formData.companyArea = val;
  formData.company = undefined;
}

function getProtocol() {
  API.competition_model.getProtocol({ cid: cid.value }).then((res) => {
    protocolvalue.value = res.body.protocolvalue;
    if (!res.body.protocolvalue?.includes('.txt')) return;
    API.competition_model.getCompetitionDescInfo(res.body.protocolvalue).then((includesTxt) => {
      protocolvalue.value = includesTxt;
    });
  });
}

function readAgreementChange(e) {
  checkNick.value = e.target.checked;
}

function goCompetitionDetail() {
  router.push({
    path: '/competition/competition-detail',
    query: {
      id: cid.value,
      name: cname.value,
    },
  });
}
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.mrb24 {
  margin-bottom: 24px;
}
.extra-info-wrap {
  display: flex;
  justify-content: center;
  .extra-info {
    flex-wrap: wrap;
    margin-left: 92px;
    margin-bottom: 24px;
    width: 580px;
    background: #f9fafb;
    border-radius: @jt-border-radius;
    padding-top: 24px;
    .sub-item {
      width: 580px;
    }
  }
}

.extra-info1 {
  display: flex;
  flex-wrap: wrap;
  width: 580px;
  background: #f9fafb;
  border-radius: @jt-border-radius;
  padding-top: 24px;
  .sub-item {
    width: 580px;
  }
}

.phoneNum-item,
.email-item {
  :deep(.ant-form-item-children) {
    display: flex;
  }
  :deep(.ant-form-item-label) {
    line-height: 32px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 0;
  }

  :deep(.ant-form-item-required::before) {
    display: none;
  }

  .phoneNum-input,
  .email-input {
    width: 232px;
  }

  :deep(.ant-space-item) {
    height: 33px;
  }

  .phone-extra-msg {
    position: absolute;
    top: 22px;
    color: #f5222d;
  }
}

.school-item {
  margin-bottom: 0;

  :deep(.ant-form-item) {
    margin-bottom: 0;
  }
  :deep(.ant-form-item-children) {
    display: flex;
  }
  :deep(.ant-form-item-label) {
    line-height: 32px;
  }
  :deep(.ant-space-item) {
    height: 33px;
  }
}

.area-item {
  :deep(.ant-select-selection) {
    width: 100px;
  }
}
.area-school-item {
  :deep(.ant-select-selection) {
    width: 172px;
  }
}
.agree-btn {
  color: @jt-primary-color;
  cursor: pointer;
  margin-left: -8px;
}

.input-disabled {
  background-color: #f5f5f5 !important;
}

// 比赛协议弹窗显示
.protocolvalue {
  height: 494px;
  overflow: auto;
  line-height: 35px;
}
</style>
