.notice-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.25);
    z-index: 999;
  }
  .notice-content {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 480px;
    height: fit-content;
    background: #fff;
    background-size: contain;
    border-radius: 4px;
    // padding: 40px 40px 32px 40px;
    color: #121f2c;
  }
  .notice-text {
    flex: 1;
    overflow-y: auto;
    padding-right: 15px;
  }
  h1 {
    width: 260px;
    font-size: 20px;
    font-weight: 600;
    color: #0082ff;
    line-height: 28px;
    text-align: left;
    margin-bottom: 32px;
    &::after {
      content: '';
      display: block;
      width: 32px;
      height: 4px;
      margin-top: 4px;
      background: #0082ff;
    }
  }
  :deep(.text) {
    p {
      &:not(:first-child) {
        text-indent: 2em;
      }
    }
  }
  :deep(p) {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    font-weight: 400;
    line-height: 24px;
  }
  :deep(.footer) {
    margin-top: 16px;
    text-align: right;
    p {
      margin-bottom: 0;
    }
  }
  
  .main-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 198px;
  }
  
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 24px;
  }

  .top{
    padding: 40px;
    padding-bottom: 0;
    background: url('~@/assets/image/system-notice/bg-top.png') no-repeat top;
    background-size: cover;
    height: 117px;
  }
  .middle{
    padding: 0 40px;
  }
  .bottom{
    padding: 32px 40px;
    padding-top: 0;
    background: url('~@/assets/image/system-notice/bg-down.png') no-repeat bottom;
    background-size: cover;
  }