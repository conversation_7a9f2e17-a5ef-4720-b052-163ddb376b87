<template>
  <a-config-provider :locale="zhCN" :theme="theme" :auto-insert-space-in-button="false">
    <div id="app">
      <top-header v-if="showHeader" />
      <Header v-if="showHeader" />
      <section :class="{ 'section-container': showHeader, 'no-sub-header': noSubHeader }">
        <a-spin :spinning="$store.state.globalLoading">
          <router-view />
        </a-spin>
      </section>
      <Footer v-if="showHeader && !isEntranceBySasac()" />
      <system-notice v-if="showHeader && !isEntranceBySasac()" />
      <agreement v-if="showHeader && !isEntranceBySasac()" :visible="agreementVisible" @ok="onDlgOk" @cancel="agreementVisible = false"></agreement>
      <jt-feedback v-if="feedbackShow && !isEntranceBySasac()" size="large"></jt-feedback>
      <!-- 问卷调查 -->
      <div class="portal-back-top">
        <back-top>
          <template #icon>
            <div class="back-top test-back-top">
              <em class="iconfont icontop"></em>
            </div>
          </template>
        </back-top>
      </div>
    </div>
  </a-config-provider>
</template>

<script>
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import Header from '@/components/Layout/Header';
import Footer from '@/components/Layout/Footer';
import agreement from './components/agreement.vue';
import JtFeedback from '@/components/feedback.vue';
import { userApi, storageApi } from '@/apis';
import systemNotice from './components/system-notice.vue';
import { POST, axios } from './request';
import { checkLogin } from '@/keycloak';
import { BackTop } from 'ant-design-vue';
import { mapActions } from 'vuex';
import { getEnvConfig } from '@/config';
import TopHeader from '@/components/Layout/TopHeader.vue';
import { isEntranceBySasac } from '@/utils/utils.js';
import { BrowserDetect } from './utils/browserdetect';
import theme from './assets/styles/theme/index.js';

const noHeaderPath = ['/job/online-exam', '/pdf-viewer', '/video-viewer', '/register', '/exam-feedback/giveup-feedback', '/invite-register'];

export default {
  components: {
    Header,
    Footer,
    agreement,
    systemNotice,
    JtFeedback,
    BackTop,
    TopHeader,
  },
  data() {
    return {
      isEntranceBySasac,
      zhCN,
      currentOs: BrowserDetect.init().OS,
      showHeader: true,
      agreementVisible: false,
      // 勿删 3.4要用
      feedbackData: {
        request: axios, // request请求 GET/POST
        showPrompt: true, // 是否显示 modal弹窗 黄色字体提示
        showTicketOption: true, // 是否显示 问题反馈option （帮助中心不显示，门户、控制台显示）
      },
      ifH5Show: window.location.href.indexOf('/h5') === -1,
      ifExamShow: window.location.href.indexOf('/online-exam') === -1,
      theme,
    };
  },
  computed: {
    feedbackShow() {
      if (getEnvConfig('SHOW_FEATURE_TICKET') !== '1') {
        return false;
      }
      if (this.$route.path === '/exam-feedback/giveup-feedback') {
        return false;
      }
      if (checkLogin() && this.ifH5Show && this.ifExamShow) {
        return true;
      }
      if (isEntranceBySasac()) {
        return false;
      }
      return false;
    },
    noSubHeader() {
      return this.$route.meta && this.$route.meta.noSubHeader;
    },
  },
  watch: {
    $route: function (val) {
      this.checkHeader();
      this.updateRouteWithQuery(val);
    },
  },

  mounted() {
    document.getElementById('jt-app-loading')?.remove();
    this.storageInitialize();
    this.checkHeader();
    this.checkAgreement();
    this.setGiveBeansByCourse();
    if (this.$keycloak.authenticated) {
      this.$store.dispatch('course/getExamUrl');
      this.autoRecharge();
    }
  },
  methods: {
    ...mapActions(['setGiveBeansByCourse']),
    autoRecharge() {
      POST('/autoRecharge/web/login/recharge').then((res) => {
        if (res.state === 'OK') {
          console.info('自动充值成功');
        }
      });
    },
    checkHeader() {
      if (this.currentOs === 'iPhone' || this.currentOs === 'Android') {
        this.showHeader = false;
      } else if (noHeaderPath.includes(this.$route.path)) {
        this.showHeader = false;
      } else {
        this.showHeader = true;
      }
    },
    onDlgOk() {
      userApi.postFirstLogin().then((res) => {
        if (res.body) {
          this.agreementVisible = false;
        }
      });
    },
    checkAgreement() {
      if (!this.$keycloak.authenticated) {
        return;
      }
    },
    // 储存初始化
    storageInitialize() {
      if (!this.$keycloak.authenticated) {
        return;
      }
      storageApi.initialize();
    },
    // 路由里可能会带noLeaveConfirm参数，需要把它自动删掉,noLeaveConfirm的作用看@/router/utils.js
    updateRouteWithQuery(val) {
      const { noLeaveConfirm, ...aQuery } = val.query;
      if (noLeaveConfirm) {
        this.$router.replace({ path: val.path, query: aQuery });
      }
    },
  },
};
</script>

<style lang="less">
@import './assets/font/iconfont.css';
@import './assets/styles/base.less';
@import './assets/styles/global.less';
@import './assets/styles/theme/reset.less';
</style>
<style lang="less" scoped>
@import './assets/styles/var.less';
// 内容区
section {
  // 动态计算高度，保证页面可以充满屏幕
  min-height: calc(~'100%  - @{jt-header-height} - @{jt-footer-height}');
  background: @jt-main-bg-color;
}
// 特殊情况：如果为无边框的header，则header需要定位在页面上，不占位
header.have-border + section {
  min-height: calc(~'100% - @{jt-header-height} - @{jt-footer-height}');
}
:deep(.ellipsis-text) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 内容区 end
.portal-back-top {
  :deep(.ant-float-btn-default) {
    border-radius: unset;
    background-color: transparent;
    box-shadow: unset;
    .ant-float-btn-body {
      border-radius: unset;
      background-color: transparent;
      .ant-float-btn-content {
        overflow: unset;
      }
    }
  }
}
// 回到顶部
.back-top {
  width: 48px;
  height: 48px;
  margin-left: -13px;
  background: #ffffff;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  color: #606972;
  display: flex;
  justify-content: center;
  align-items: center;
  .iconfont {
    font-size: 28px;
    display: block;
  }
}
.section-container {
  padding-top: calc(~' @{jt-header-height} + 60px');
  &.no-sub-header {
    padding-top: 60px;
  }
}
</style>
