<template>
  <div class="competition-question-intro">
    <jt-common-content :loading="loading" :empty="questionIntro.length === 0" empty-title="您暂未设置赛题说明">
      <template #empty-operation>
        <p v-if="showEditBtn" class="to-edit-page" @click="toEditPage">编辑赛题说明</p>
      </template>
      <html-container v-for="(item, index) in questionIntro" :key="item.title" :style="{ 'margin-top': index > 0 ? '40px' : '0px' }" :text-value="item.value" :title="item.title" :text-url="item.content" @updateRichLoading="updateRichLoading" />
    </jt-common-content>
  </div>
</template>

<script>
import htmlContainer from '../components/htmlContainer.vue';
import { competitionApi } from '@/apis/index';
export default {
  name: 'CompetitionQuestionIntro',
  components: {
    htmlContainer,
  },
  props: {
    dataList: {
      type: Array,
      default: () => [],
    },
    showEditBtn: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['toEdit'],
  data() {
    return {
      questionIntro: [],
      loading: false,
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      this.loading = true;
      if (this.dataList?.length > 0) {
        this.questionIntro = this.dataList.map((item) => {
          return {
            title: item.inputValue,
            value: item.editorValue,
          };
        });
        this.loading = false;
      } else {
        this.initQuestionIntro();
      }
    },
    async initQuestionIntro() {
      const cid = this.$route.params.competitionId;
      const res = await competitionApi.getCompetitionDesAndIntro({ cid });
      if (res.state === 'OK') {
        this.questionIntro = res.body.instrucationsPathJson || [];
      }
      this.loading = false;
    },
    toEditPage() {
      this.$emit('toEdit');
    },
    updateRichLoading(val) {
      this.loading = val;
    },
  },
};
</script>

<style lang="less" scoped>
.to-edit-page {
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  color: #0082ff;
}
</style>
