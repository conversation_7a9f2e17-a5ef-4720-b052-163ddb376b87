/* 全局样式的定义 */
@import '~@/assets/styles/var.less';


#app {
  height: 100%;
  position: relative;
  letter-spacing: 0px;
}

// 竞赛相关用到的统一容器样式
.inner {
  width: 1200px;
  height: 100%;
  margin: auto;
}

// markdown样式
.markdown-body {
  a {
    color: @jt-primary-color;
  }
  table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
  }
  table td,
  table th {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    padding: 3px 5px;
  }
  table th {
    border-bottom: 2px solid #ccc;
    text-align: center;
  }

  /* blockquote 样式 */
  blockquote {
    display: block;
    border-left: 8px solid #d0e5f2;
    padding: 5px 10px;
    margin: 10px 0;
    line-height: 1.4;
    font-size: 100%;
    background-color: #f1f1f1;
  }

  /* code 样式 */
  code {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    background-color: #f1f1f1;
    border-radius: 3px;
    padding: 3px 5px;
    margin: 0 3px;
  }
  pre code {
    display: block;
  }

  /* ul ol 样式 */
  ul,
  ol {
    margin: 10px 0 10px 20px;
  }

  ul li {
    list-style: disc;
  }
  ol li {
    list-style: decimal;
  }
}

// 按钮宽度64px
.w-64 {
  width: 64px;
}

// 默认盒子阴影
.jt-box-shadow {
  box-shadow: @jt-box-shadow;
}

.common-content-container {
  box-shadow: @jt-box-shadow;
  border-radius: @jt-border-radius;
}


.w-e-text{
  a {
    color: #0082ff;
  }
  img{
    width: 100%;
  }
}
