export const teachingManage = {
  title: '教学和管理  全面覆盖',
  teachManage: {
    title: '课程管理',
    subtitle: '多年一线教学经验和积累，系统功能场景覆盖全面，贴合教学场景',
    list: [
      {
        title: '全场景测评工具',
        data: ['在线作业', '在线考试', '纸卷评阅', '代码测评'],
      },
      {
        title: '便捷的管理沟通',
        data: ['学生管理', '成绩分析', '在线答疑', '数据统计'],
      },
      {
        title: '丰富的教学内容',
        data: ['在线实验', '慕课课堂', '课件资料', '题库管理'],
      },
    ],
  },
  systemManage: {
    title: '系统管理',
    subtitle: '管理服务，充分自治，数据直达',
    list: [
      {
        title: '线上实验室建设',
        data: ['环境管理', '测评机管理'],
      },
      {
        title: '教务管理小帮手',
        data: ['课程管理', '教师管理', '数据分析'],
      },
    ],
  },
};

export const developEnviroment = {
  title: '四大实验环境',
  list: [
    {
      icon: 'jupyterIcon',
      title: 'Jupyter实验环境',
      data: ['完美支持Jupytrer Notebook、JupyterLab、WebTerminal', '自定义Jupyter环境', '无缝切换实验手册和代码', '一键还原实验环境', '自动化测评'],
    },
    {
      icon: 'cloudIcon',
      title: '超级云桌面实验环境',
      data: ['支持共享桌面', '自动化测评', '一键还原实验环境', 'Linux和Windows双系统', '自定义桌面环境', '可搭载任意软件'],
    },
    {
      icon: 'commontestIcon',
      title: '通用评测实验环境',
      data: ['支持自动化部署、负载均衡、超时处理、内存限定、容错处理', '适用于任意课程和实验', '自定义评测kernel', '多台评测机集群'],
    },
    {
      icon: 'autotestIcon',
      title: '自动测评实验环境',
      data: ['支持自动化代码查重', '多种语言实时自动评判', '代码风格检查、性能剖析、动态测试及静态分析', '兼容TensorFlow、Keras等多种框架'],
    },
  ],
};

export const courseContent = {
  title: '丰富的课程内容',
  list: [
    {
      title: '人工智能',
      columns: 2,
      data: ['机器学习', '深度学习', '自然语言处理', '人工智能数学基础', '智能控制与机器人', '智能计算系统', '智能应用系统综合设计', '...'],
      data1: ['超级云桌面实验环境', 'Jupyter实验环境', '完善的课程管理与考试平台', '自动测评实验环境', '教育大数据沉淀'],
    },
    {
      title: '大数据',
      columns: 3,
      data: ['Hadoop大数据开发', 'Spark大数据开发', 'NoSQL数据库', '机器学习', '数据采集技术', '数据可视化分析', '数据挖掘', 'R语言实训', '...'],
      data1: ['超级云桌面实验环境', '完善的课程管理与考试平台', '自动测评实验环境', '教育大数据沉淀'],
    },
    {
      title: '信息安全',
      columns: 2,
      data: ['信息安全基础', '软件安全与可信系统', '安全管理', '互联网安全', '...'],
      data1: ['超级云桌面实验环境', '完善的课程管理与考试平台', '自动测评实验环境 ', '教育大数据沉淀'],
    },
    {
      title: '区块链',
      columns: 3,
      data: ['Go语言程序设计', '密码学基础原理', '分布式系统', '区块链原理', '区块链系统开发', 'Solidity程序设计', '智能合约', '区块链案例实践', '...'],
      data1: ['超级云桌面实验环境', '完善的课程管理与考试平台', '自动测评实验环境 ', '教育大数据沉淀'],
    },
    {
      title: '集成电路',
      columns: 3,
      data: ['Verilog硬件描述语言', '数字电路', 'VLSI数字通信原理与设计', '处理器设计', '智能处理器设计', '汇编语言程序设计', '电路分析', 'FPGA技术', 'VLSI数字信号处理', '计算机组成原理', '数字集成电路设计', 'CMOS模拟集成电路设计'],
      data1: ['超级云桌面实验环境', '完善的课程管理与考试平台', '自动测评实验环境', 'FPGA云计算平台', '教育大数据沉淀'],
    },
  ],
};

export const chrastic = {
  title: '云端服务，开箱即用',
  list: [
    {
      title: '安全至上，值得信赖',
    },
    {
      title: '弹性资源，伸缩灵活',
    },
  ],
};
