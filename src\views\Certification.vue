<template>
  <div class="home">
    <div class="banner">
      <jt-banner :sliders="list" class="banner-img">
        <div>
          <jt-tooltip>
            <template #title> 敬请期待 </template>
            <div class="button-box">
              <span class="a-button" disabled="disabled">立即申请</span>
            </div>
          </jt-tooltip>
        </div>
      </jt-banner>
    </div>
    <section class="content-box">
      <div class="left-box">
        <div class="tupian">
          <img src="@/assets/image/certification/file-protect.png" alt="" />
        </div>
        <div>
          <h2>实力检验</h2>
          <p>AI学习和实战成果质量的最佳检验，获得权威认可，助力职业发展</p>
        </div>
      </div>
      <div class="left-box">
        <div class="tupian">
          <img src="@/assets/image/certification/account-book.png" alt="" />
        </div>
        <div>
          <h2>技术变现</h2>
          <p>能力上架对外服务，根据调用情况获得收益，实现技术变现</p>
        </div>
      </div>
    </section>
    <div style="width: 100%; background-color: #f6f9fb">
      <div class="cma-box">
        <h1>认证流程</h1>
        <div class="apply-box">
          <div class="a-subbox">
            <div class="b-subbox">
              <img src="@/assets/image/certification/shop2.png" alt="" />
            </div>
            <div class="c-subbox">
              <h3 style="font-size: 18px; color: #121f2c; margin-bottom: 8px">Step1：在线申请</h3>
              <span style="font-size: 16px; color: #606972">提交AI能力</span>
            </div>
          </div>
          <div class="apply-subbox">
            <!-- 箭头标签位置 -->
            <img style="width: 123px; height: 9px" src="@/assets/image/certification/arrows.png" alt="" />
          </div>
          <div class="a-subbox">
            <div class="b-subbox">
              <img src="@/assets/image/certification/shop3.png" alt="" />
            </div>
            <div class="c-subbox">
              <h3 style="font-size: 18px; color: #121f2c; margin-bottom: 8px">Step2：在线参与评测</h3>
              <span style="font-size: 16px; color: #606972">获取证书</span>
            </div>
          </div>
          <div class="apply-subbox">
            <!-- 箭头标签位置 -->
            <img style="width: 123px; height: 9px" src="@/assets/image/certification/arrows.png" alt="" />
          </div>
          <div class="a-subbox">
            <div class="b-subbox">
              <img src="@/assets/image/certification/shop.png" alt="" />
            </div>
            <div class="c-subbox">
              <h3 style="font-size: 18px; color: #121f2c; margin-bottom: 8px">Step3：AI能力上架</h3>
              <span style="font-size: 16px; color: #606972">获得收益</span>
            </div>
          </div>
        </div>
        <jt-tooltip>
          <template #title> 敬请期待 </template>
          <div class="button-box">
            <span class="a-button">立即申请</span>
          </div>
        </jt-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import JtBanner from '@/components/banner.vue';
import { Tooltip as JtTooltip } from 'ant-design-vue';

export default {
  data() {
    return {
      list: [
        //banner图
        {
          h1: '',
          h2: '九天揽月计划AI能力认证',
          p: '直通中移智慧中台AaaS、移动云AI能力市场，开启技术变现之路',
          img: require('@/assets/image/certification/banner.png'),
        },
      ],
    };
  },
  methods: {},
  components: {
    JtBanner,
    JtTooltip,
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.text {
  margin-bottom: 25px;
}
.banner {
  height: 460px;
  .banner-img:deep(img) {
    width: 399px;
    height: 342px;
  }
}
/* banner文字 */
.banner-img:deep(.text) {
  margin-bottom: 35px;
}
.banner .button-box {
  /* text-align: center; */
  /* margin-left: -500px; */
  display: inline-block;
  /* width: 100%; */
  .a-button {
    display: inline-block;
    font-size: 16px;
    width: 128px;
    height: 40px;
    line-height: 40px;
    background: #0082ff;
    color: #ffffff;
    border: 1px solid #0082ff;
    /* cursor:pointer; */
    cursor: no-drop;
    text-align: center;
  }
}
section {
  width: 1200px;
  margin: auto;
  padding: 64px 0;
  display: flex;
  justify-content: space-between;
  .left-box {
    display: flex;
    justify-content: space-around;
    align-items: center;

    .tupian {
      margin-right: 24px;
      img {
        width: 72px;
      }
    }
    h2 {
      font-size: 20px;
      margin-bottom: 8px;
    }
    p {
      font-size: 14px;
      color: #606972;
    }
  }
}
.cma-box {
  width: 1200px;
  margin: auto;
  height: 474px;
  background: #f6f9fb;
  h1 {
    font-size: 32px;
    text-align: center;
    padding-top: 56px;
  }
  .apply-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 66px;
    .a-subbox {
      display: flex;
      flex-direction: column;
      align-items: center;
      .b-subbox {
        margin-bottom: 27px;
        img {
          width: 56px;
        }
      }
      .c-subbox {
        text-align: center;
      }
    }
    .apply-subbox {
      p {
        width: 25px;
        height: 2px;
        color: #ccc;
        background: #ccc;
      }
    }
  }
  .button-box {
    text-align: center;
    margin-top: 48px;
    .a-button {
      display: inline-block;
      font-size: 16px;
      width: 128px;
      height: 40px;
      line-height: 40px;
      background: #f6f9fb;
      color: #0082ff;
      border: 1px solid #0082ff;
      /* cursor:pointer; */
      cursor: no-drop;
    }
  }
}
.cer-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .text {
    p:nth-child(1) {
      height: 67px;
      font-size: 48px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 67px;
    }
    p:nth-child(2) {
      width: 597px;
      height: 26px;
      font-size: 16px;
      font-weight: 400;
      color: #121f2c;
      line-height: 26px;
      margin-top: 16px;
    }
    button {
      margin-top: 32px;
      width: 128px;
      height: 40px;
      background: #0082ff;
      border-radius: 2px;
      color: #ffffff;
      border: 0;
      outline: none;
      cursor: pointer;
    }
  }
}
</style>
