import { createRouter, createWebHashHistory } from 'vue-router';
import store from '@/store/index.js';
import routes from './routes';
import { checkLogin, keycloak } from '@/keycloak';
import { updateBeforeUnloadListener, confirmBeforeLeavingPage } from './utils';
import { BrowserDetect } from '@/utils/browserdetect';

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0, left: 0 };
    }
  },
});

router.beforeEach((to, from, next) => {
  // 解决路由出现乱码字段
  if (to.fullPath.search('&state') > -1 && to.fullPath.search(/\?/) === -1) {
    const path = to.fullPath.split('&')[0];
    next(path);
  } else {
    next();
  }
  updateBeforeUnloadListener(to, from, next);
  if (!confirmBeforeLeavingPage(to, from, next)) return;

  const { path, meta } = to;
  const currentOs = BrowserDetect.init().OS;
  const isH5Page = path.includes('/h5');
  const isRegisterPage = path.includes('/invite-register') || path.includes('/h5/inviteregister');
  const isH5device = ['iPhone', 'Android'].includes(currentOs);

  if (isH5device && !isH5Page && !isRegisterPage) {
    alert('当前页面不支持在移动端查看');
    return;
  } else if (!isH5device && isH5Page && !isRegisterPage) {
    alert('当前页面不支持在PC端查看');
    next('/home');
    return;
  }

  if (path !== '/dataset' && path !== '/dataset/dataset-detail') {
    store.commit('SET_DATASET_DATA', { pageNum: 1, pageSize: 10 });
  }

  if (meta.requiresLogin && checkLogin(true)) {
    const shouldNextToCourse = meta.role && meta.role.includes('teacher') && keycloak.idTokenParsed.DLP_USER_ALLOW_PUBLISH_OPEN_COURSE !== '1';
    next(shouldNextToCourse ? '/course' : undefined);
  } else {
    next();
  }
});

export default router;
