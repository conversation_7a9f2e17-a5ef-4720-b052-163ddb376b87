<script setup>
import { LogoutOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <ul class="link-list">
    <li><message-alert /></li>
    <li v-if="showRegisterBtn && !logined" style="display: flex; align-items: center" @click="routeToRegister">注册</li>
    <li v-if="logined" class="user-item">
      <a-dropdown placement="bottom">
        <div :class="!isEntranceBySasac() ? 'user-box' : 'user-box nopointer'">
          <img class="avatar" :src="userInfo.image || defaultAvatar" alt="" />
          <p :title="userInfo.userName">{{ userInfo.userName }}</p>
        </div>
        <template v-if="!isEntranceBySasac()" #overlay>
          <a-menu class="jt-login-menu">
            <a-menu-item @click="handleGoAdmin">
              <jt-icon type="iconsetting-outlined" />
              <span>账号管理</span>
            </a-menu-item>
            <a-menu-item @click="handleLogout">
              <LogoutOutlined />
              <span>退出登录</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </li>
    <li v-else class="login-btn" @click="gotoLogin">{{ isOAMethod ? 'OA登录' : '登录' }}</li>
  </ul>
</template>
<script>
import { mapState } from 'vuex';
import { login, logout, register, checkLogin } from '@/keycloak';
import { getEnvConfig } from '@/config';
import messageAlert from './Layout/messageAlert.vue';

import { openInNewTab, isEntranceBySasac } from '@/utils/utils';

import { GET } from '@/request';

export default {
  name: 'LinkItems',
  components: {
    messageAlert,
  },
  data() {
    return {
      isEntranceBySasac,
      defaultAvatar: require('@/assets/image/avatar_big.png'),
      showRegisterBtn: false,
      isOAMethod: getEnvConfig('LOGIN_METHOD') === '1',
    };
  },
  computed: {
    ...mapState(['userInfo']),
    logined() {
      return this.checkLogin();
    },
  },
  mounted() {
    this.initRegisterState();
  },
  methods: {
    openHelpCenter() {
      openInNewTab('./common-helpcenter');
    },
    async initRegisterState() {
      // 获取当前keycloak是否开启注册
      const res = await GET('/keycloak/web/user/registAllow');
      if (res.state === 'OK') {
        this.showRegisterBtn = res.body;
      }
    },
    handleLogout() {
      let redirectUrl = location.href;
      if (this.$route.path === '/404' || this.$route.path === '/401') {
        redirectUrl = window.location.origin + window.location.pathname;
      }
      logout(redirectUrl);
    },
    routeToRegister() {
      register();
    },
    gotoLogin() {
      login();
    },
    checkLogin() {
      return checkLogin(this.$route.meta.requiresLogin);
    },
    handleGoAdmin() {
      openInNewTab('/portal/#/admin');
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.link-list {
  display: flex;
  height: 60px;
  li {
    text-align: center;
    cursor: pointer;
    color: #121f2c;
    transition: 0.3s all ease;
  }
  li:not(:last-child) {
    margin-right: 32px;
  }
  .user-item {
    width: auto;
    max-width: 120px;
    margin-right: 32px;
  }
  .nopointer {
    cursor: auto !important;
  }
  li:hover:not(:last-child) {
    color: @jt-cyan-color;
  }
  .user-box {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 100%;
    .avatar {
      width: 28px;
      height: 28px;
      margin-right: 8px;
      border-radius: 50%;
    }
    p {
      line-height: 20px;
      max-width: 85px;
      text-overflow: ellipsis;
      overflow: hidden;
      color: #333333;
    }
  }
  .login-btn {
    transition: 0.3s all ease;
    width: 120px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    background: linear-gradient(90deg, #00c1dc 0%, #00aaff 100%);
    &:hover {
      background: linear-gradient(90deg, #01cedd 0%, #0188cb 100%);
    }
  }
}
</style>
<style lang="less">
@import '~@/assets/styles/index.less';
.jt-login-menu.ant-dropdown-menu .ant-dropdown-menu-item {
  color: #606972;
  background-color: transparent;
  &:hover {
    color: @jt-cyan-color;
  }
}
</style>
