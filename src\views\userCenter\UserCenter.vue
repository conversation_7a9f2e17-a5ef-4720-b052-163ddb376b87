<template>
  <div class="container">
    <sub-header title="个人中心"></sub-header>
    <div class="user-center">
      <a-tabs v-model:activeKey="activeTab" class="content-container">
        <!-- <a-tab-pane :key="USER_CENTER_TAB.PERSONAL_INFO" tab="个人信息">
          <user-information ref="UserInformation" @changeActiveTab="changeActiveTab(arguments)"></user-information>
        </a-tab-pane> -->
        <a-tab-pane :key="USER_CENTER_TAB.COMPUTATIONAL_INFO" tab="算力信息">
          <compute-information ref="ComputeInformation" v-model:active-tab="activeTab" @changeActiveTab="changeActiveTab(arguments)"></compute-information>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
// import UserInformation from './UserInformation.vue';
import ComputeInformation from './ComputeInformation.vue';
import subHeader from '@/components/Layout/subHeader';
import { checkLogin } from '@/keycloak';
import { mapState } from 'vuex';

const USER_CENTER_TAB = {
  PERSONAL_INFO: '1',
  COMPUTATIONAL_INFO: '2',
};
export default {
  name: 'UserCenter',
  components: {
    ComputeInformation,
    subHeader,
  },
  data() {
    return {
      USER_CENTER_TAB,
      activeTab: this.$route.query.activeTab || USER_CENTER_TAB.COMPUTATIONAL_INFO,
    };
  },
  computed: {
    ...mapState(['suanLiState']),
  },
  watch: {
    activeTab(val) {
      this.$router.replace({ query: { ...this.$route.query, activeTab: val } });
    },
  },
  mounted() {
    checkLogin(true);
  },
  methods: {
    changeActiveTab(value) {
      /**
       * if value[1] === 'ecloud'  未绑定手机号跳转到active 1，编辑的页面
       * else 从编辑页返回到active 2 可能会进入三个步骤的某一个，也会提示弹窗
       */
      if (value[1] === 'ecloud') {
        this.activeTab = value[0];
        this.$router.replace({ query: { ...this.$route.query, activeTab: value, ecloud: 'true' } });
        this.$nextTick(() => {
          this.$refs.UserInformation.editing = true;
        });
      } else {
        this.activeTab = value[0];
        this.$refs.UserInformation.editing = false;
        if (!this.$route.query.ecloud) return;
        if (this.suanLiState.joinSta == 4 || this.suanLiState.joinSta == 5) {
          this.$refs.ComputeInformation.unableJoinVisible = true;
        } else if (this.suanLiState.joinSta == 6) {
          this.$refs.ComputeInformation.notOpenVisible = true;
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f4f8fa;
}
.user-center {
  width: 1200px;
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 44px;
  background: #fff;
}
.content-container {
  width: 1200px;
  background: #fff;
  padding: 16px 32px 0 32px;
}
:deep(.ant-tabs-tab) {
  font-size: 18px;
}
</style>
