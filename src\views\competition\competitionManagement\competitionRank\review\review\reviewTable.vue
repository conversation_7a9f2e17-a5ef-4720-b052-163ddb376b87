<script setup>
import { FilterFilled } from '@ant-design/icons-vue';
</script>
<template>
  <section>
    <a-config-provider>
      <template #renderEmpty>
        <jt-common-content :empty="true" :loading="loading" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text"> </jt-common-content>
      </template>
      <a-table class="table" size="middle" :loading="tableData.length > 0 ? loading : undefined" :row-selection="ableSelect ? { selectedRowKeys: selectedRowKeys, onSelect: onSelectChange, onSelectAll: onSelectAll } : undefined" row-key="teamId" :columns="columns" :data-source="tableData" :scroll="{ x: columns.filter((x) => x.isAjaxRow).length * 200 + (ableSelect ? 300 : 0) }" :pagination="false" @change="tableChange">
        <!-- 提交状态 -->
        <template #filterDropdownIcon>
          <FilterFilled :style="{ color: teamNameFilter !== '' ? '#108ee9' : undefined, right: 'auto' }" />
        </template>
        <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm }">
          <div class="filter-status-select">
            <div class="all" :class="{ select: teamNameFilter === '' }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, '')">全部</div>
            <div class="already-study" :class="{ select: teamNameFilter === 1 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 1)">已选</div>
            <div class="un-study" :class="{ select: teamNameFilter === 2 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 2)">未选</div>
          </div>
        </template>
        <!-- 提交状态 结束 -->
        <!-- 根据接口返回自动生成的title -->
        <template v-for="x in columns.filter((z) => z.isAjaxRow)" :key="x.dataIndex" #[x.slots.title]>
          <p :title="x.dataIndex" :style="ajaxTitleStyle">{{ x.titleName }}</p>
        </template>
      </a-table>
    </a-config-provider>
    <div v-if="!tableEmpty" class="jt-pagination">
      <a-space size="large">
        <span>共{{ page.total }}条记录</span>
        <span>
          每页显示
          <a-select default-value="10" style="width: 65px" @change="changePageSize">
            <a-select-option value="5"> 5 </a-select-option>
            <a-select-option value="10"> 10 </a-select-option>
            <a-select-option value="15"> 15 </a-select-option>
            <a-select-option value="20"> 20 </a-select-option>
          </a-select>
          条
        </span>
      </a-space>
      <a-pagination :page-size="page.pageSize" show-quick-jumper :default-current="1" :total="page.total" @change="changePageNum" />
    </div>
  </section>
</template>

<script>
import API from '@/constants/api/API';
import { getTableRow, setTableDataByRow, setTableRowWidth } from '@/views/competition/competitionManagement/competitionRank/utils';
import { ajaxTitleStyle } from '@/views/competition/competitionManagement/competitionRank/constants';

export default {
  props: {
    // 搜索内容
    searchValue: {
      type: String,
      default: '',
    },
    // 提交开放范围
    replyCreateSta: {
      type: Boolean,
      default: false,
    },
    // 1==编辑中的table    2==预览中的table
    editMode: {
      type: [String, Number],
      default: null,
    },
    // 预览时的table列表
    previewTableList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  emits: ['getTableData', 'getTableSelectedRowKeys', 'loading'],
  data() {
    const cid = this.$route.params.competitionId;
    return {
      ajaxTitleStyle,
      cid,
      scoreName: {},
      tableData: [],
      allTableData: [], // 编辑和预览时需要获取所有内容然后前端分页
      selectedRowKeys: [],
      selectedRows: [],
      columns: [
        {
          dataIndex: 'teamName',
          key: 'teamName',
          title: '团队名称',
          ellipsis: true,
          fixed: 'left',
          width: 200,
          // slots: { filterDropdown: 'filterDropdown', filterIcon: 'filterDropdownIcon' },
          customRender: ({ text, record, index }) => {
            // text: 当前单元格内容
            // record: 当前行数据
            // index: 当前行索引
            return text || '--';
          },
        },
        {
          dataIndex: 'memberNum',
          key: 'memberNum',
          title: '团队人数',
          ellipsis: true,
          fixed: 'left',
          width: 100,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          dataIndex: 'leaderUserName',
          key: 'leaderUserName',
          title: '队长用户名',
          ellipsis: true,
          fixed: 'left',
          width: 100,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          dataIndex: 'leaderFullName',
          key: 'leaderFullName',
          title: '队长姓名',
          ellipsis: true,
          fixed: 'left',
          width: 100,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          dataIndex: 'teamNum',
          key: 'teamNum',
          title: '排名',
          ellipsis: true,
          fixed: 'left',
          width: 100,
          sorter: true,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
      ],
      filter: false,
      loading: false,
      teamNameFilter: '',
      formData: {
        keyWord: '',
      },
      page: {
        pageNum: 1,
        total: 0,
        pageSize: 10,
      },
    };
  },
  computed: {
    tableEmpty() {
      return this.tableData.length === 0;
    },
    emptyStatus() {
      if (this.formData.keyWord) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关团队',
          text: '您可以换一个关键词试试哦～',
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '暂无团队',
          text: '',
        };
      }
    },
    ableSelect() {
      return this.editMode === '1' && !this.replyCreateSta;
    },
  },
  watch: {
    searchValue(val) {
      this.page.pageNum = 1;
      this.formData.keyWord = val;
      this.getTableData();
    },
    replyCreateSta(val) {
      // 所有团队
      if (val) {
        delete this.columns.find((x) => x.dataIndex === 'teamName').slots;
        this.$emit('getTableData', this.allTableData);
        this.$emit('getTableSelectedRowKeys', { keys: this.allTableData.map((x) => x.teamId) });
      } else {
        // 指定团队
        this.columns.find((x) => x.dataIndex === 'teamName').slots = {
          filterIcon: 'filterDropdownIcon',
          filterDropdown: 'filterDropdown',
        };
        this.$emit('getTableData', this.selectedRows);
        this.$emit('getTableSelectedRowKeys', { keys: this.selectedRowKeys });
      }
    },
    selectedRowKeys: {
      handler() {
        this.$emit('getTableSelectedRowKeys', { keys: this.selectedRowKeys });
        this.$emit('getTableData', this.selectedRows);
      },
      deep: true,
    },
    loading(val) {
      this.$emit('loading', val);
    },
  },
  created() {
    this.loading = true;

    // 如果为编辑，则需要根据提交开放范围判断是否显示筛选
    // 同时获取之前选过的队伍列表，实现默认选中
    if (this.editMode === '1') {
      this.columns.find((x) => x.dataIndex === 'teamName').slots = {
        filterIcon: 'filterDropdownIcon',
        filterDropdown: 'filterDropdown',
      };
      API.competition_model.getFileReplyTeamIds({ cid: this.cid }).then((res) => {
        if (res.state === 'OK') {
          this.selectedRowKeys = res.body.map((x) => x.teamId);
          this.selectedRows = res.body;
        }
      });
      API.competition_model.getFileReplyTeamAllWithNoPage({ cid: this.cid }).then((res) => {
        if (res.state === 'OK') {
          this.allTableData = res.body;
          this.getTableData();
          // 如果提交开放范围为 所有团队 ，则将所有团队的信息传给父组件
          if (this.replyCreateSta) {
            this.$emit('getTableData', this.allTableData);
          }
        }
      });
    } else if (this.editMode === '2') {
      this.allTableData = [...this.previewTableList];
    }

    // 根据后端返回的动态列，格式化数据
    API.competition_model.getCompetitionGetScoreName({ cid: this.cid }).then((res) => {
      if (res.state === 'OK') {
        getTableRow({ scoreName: res.body }).then((_res) => {
          this.columns.splice(5, 0, ..._res);
          setTableRowWidth(this.columns);
          if (this.columns.filter((x) => x.isAjaxRow).length === 0) {
            this.columns
              .filter((x, i) => i > this.columns.length / 3)
              .map((x) => {
                delete x.width;
                delete x.fixed;
              });
          }
          this.getTableData();
        });
      }
    });
  },
  methods: {
    getTableData() {
      this.loading = true;
      if (!this.editMode) {
        const params = { cid: this.cid, ...this.formData, ...this.page };
        API.competition_model.getFileReplyTeamsList(params).then((res) => {
          this.loading = false;
          if (res.state === 'OK') {
            this.tableData = res.body.list;
            setTableDataByRow(this.tableData);
            this.page.total = res.body.total;
            this.$emit('getTableData', res.body);
          }
        });
      } else {
        this.getTableDataFromFrontend();
      }
    },
    // 前端过滤团队名称、队长用户名、队长姓名+分页+筛选是否选中
    getTableDataFromFrontend() {
      const searchFilter = (item) => {
        if (item.leaderFullName?.includes(this.searchValue) || item.leaderUserName?.includes(this.searchValue) || item.teamName?.includes(this.searchValue)) {
          if (this.teamNameFilter === 1) {
            return this.selectedRowKeys.find((x) => x === item.teamId);
          } else if (this.teamNameFilter === 2) {
            return !this.selectedRowKeys.find((x) => x === item.teamId);
          }
          return true;
        }
        return false;
      };
      const sorterFn = (a, b) => {
        if (this.formData.teamNumFlag === '0') {
          return a.teamNum - b.teamNum;
        }
        return b.teamNum - a.teamNum;
      };
      this.tableData = this.allTableData
        .filter(searchFilter)
        .sort(sorterFn)
        .slice((this.page.pageNum - 1) * this.page.pageSize, this.page.pageNum * this.page.pageSize);
      setTableDataByRow(this.tableData);
      this.loading = false;
      this.page.total = this.allTableData.filter(searchFilter).length;
    },
    handleSelectClick(setSelectedKeys, selectedKeys, confirmCallBack, index) {
      confirmCallBack();
      this.teamNameFilter = index;
      this.page.pageNum = 1;
      this.getTableData();
    },
    changePageSize(size) {
      this.page.pageSize = Number(size);
      this.page.pageNum = 1;
      this.getTableData();
    },
    changePageNum(num) {
      this.page.pageNum = num;
      this.getTableData();
    },
    tableChange(pagination, filters, sorter) {
      this.formData[sorter.columnKey + 'Flag'] = sorter.order === 'ascend' ? '0' : '1';
      this.getTableData();
    },
    onSelectChange(record, selected) {
      if (selected) {
        this.selectedRows.push(record);
      } else {
        this.selectedRows = this.selectedRows.filter((x) => x.teamId !== record.teamId);
      }
      this.selectedRowKeys = this.selectedRows.map((x) => x.teamId);
      // 如果是编辑状态，每次选中之后需要更新前端显示的列表
      if (this.editMode === '1') {
        this.getTableDataFromFrontend();
      }
    },
    onSelectAll(selected, selectedRows, changeRows) {
      if (selected) {
        this.selectedRows = [...this.selectedRows, ...changeRows];
      } else {
        this.selectedRows = this.selectedRows.filter((x) => !changeRows.find((y) => y.teamId === x.teamId));
      }
      this.selectedRowKeys = this.selectedRows.map((x) => x.teamId);
      if (this.editMode === '1') {
        this.getTableDataFromFrontend();
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import '~@/views/competition/competitionManagement/competitionRank/table.less';
.table {
  margin-top: 0;
}
</style>
