<template>
  <div class="register-container">
    <register-header :showBackToHome="true"></register-header>
    <div class="content-wrap">
      <div class="content">
        <div class="main-content">
          <h1 class="title">欢迎注册九天·毕昇</h1>
          <div class="form-wrap">
            <register-form :ecloudRegisterBeanBount="ecloudRegisterBeanBount" :showLoginBtn="true" :activityAvailable="activityAvailable" @onSubmit="submit"></register-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import registerHeader from './components/header.vue';
import registerForm from './components/form.vue';
import { axiosWithNoToken } from '@/request';
import { getKeycloakUrl } from '@/keycloak';

export default {
  name: 'register',
  components: {
    registerHeader,
    registerForm,
  },
  data() {
    return {
      inviterName: '',
      receiveCount: 0,
      assistCount: 0,
      period: 0,
      activityAvailable: false,
      ecloudRegisterBeanBount: 0,
    };
  },
  mounted() {
    this.getActivityStatus();
    this.getEcloudRegisterBeanMessage();
  },
  created() {
    this.checkRegisterAvailable();
    this.checkToken();
  },
  methods: {
    // 做一个兼容原先邀请注册路由的逻辑，如果带有token的话，跳转到邀请注册的新路由
    checkToken() {
      const query = this.$route.query;
      const token = query.token;
      if (token) {
        this.$router.replace({ path: '/invite-register', query });
      }
    },
    getEcloudRegisterBeanMessage() {
      axiosWithNoToken('/marketing/web/ecloud/getRegisterBeanMessage').then((res) => {
        if (res.data.state === 'OK') {
          this.ecloudRegisterBeanBount = (res.data.body && res.data.body.ecloudRegisterBeanBount) || 0;
        }
      });
    },
    checkRegisterAvailable() {
      const keycloakUrl = getKeycloakUrl();
      axiosWithNoToken('/keycloak/web/user/registAllow').then((res) => {
        if (res.data.state === 'OK' && !res.data.body) {
          window.location.replace(`${keycloakUrl}/realms/TechnicalMiddlePlatform/login-actions/registration`);
        }
      });
    },
    getActivityStatus() {
      axiosWithNoToken('/marketing/web/ecloud/activityStatus').then((res) => {
        if (res.data.state === 'OK') {
          this.activityAvailable = res.data.body;
        }
      });
    },
    submit(obj, eCloudRegistered) {
      const url = obj.registEcloud && !eCloudRegistered ? '/marketing/web/ecloud/pcUserRegistration' : '/marketing/web/userRegistration';
      const params = {
        ...obj,
        registEcloud: +obj.registEcloud,
      };
      axiosWithNoToken.post(url, params).then((res) => {
        if (res.data.state === 'OK') {
          const successMessage = obj.registEcloud ? '毕昇平台账号及移动云账号注册成功，算力豆已发放' : '注册成功';
          this.$message.success(successMessage);
          this.onSubmited();
        } else {
          if (res.data.errorCode === '1111') {
            this.$message.error('移动云账号注册成功，毕昇平台账号注册失败，算力豆未发放，请稍后重试');
            return;
          }
          this.$message.error(res.data.errorMessage || '注册失败');
        }
      });
    },
    onSubmited() {
      window.location.replace(`${window.location.origin}${window.location.pathname}#/home`);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.register-container {
  background: #f4f8fa;
  min-height: 100vh;
  .content-wrap {
    min-height: calc(100vh - 100px);
    .content {
      width: 1200px;
      background: #fff;
      margin: 0 auto;
      padding-bottom: 20px;
      margin-top: 20px;
      overflow: hidden;
    }
    .content-empty {
      height: calc(100vh - 140px);
      position: relative;
      .empty-tooltip {
        position: absolute;
        left: 50%;
        margin-left: -208px;
        top: 50%;
        margin-top: -208px;
        width: 416px;
        height: 416px;
        .tooltip-txt {
          position: absolute;
          bottom: 96px;
          text-align: center;
          width: 100%;
          font-weight: 600;
          color: #121f2c;
          font-size: 18px;
        }
      }
    }
  }
}
.main-content {
  width: 480px;
  height: calc(100vh - 300px);
  margin: 0 auto;
  margin-top: 64px;
  margin-bottom: 48px;
  .title {
    font-size: 34px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    margin: 0 auto;
    margin-bottom: 20px;
    text-align: center;
    width: 384px;
  }
  .prompt {
    font-size: 14px;
    font-weight: 400;
    color: #606972;
    margin: 0 auto;
    margin-bottom: 40px;
    text-align: center;
    width: 384px;
  }
  .form-wrap {
    position: relative;
    left: -60px;
  }
}

.count {
  color: #ff8c00;
}
.inviter-name {
  font-size: 14px;
  font-weight: 400;
  color: #606972;
  line-height: 20px;
}
.divider {
  min-width: 1080px;
  width: 1080px;
  margin-left: auto;
  margin-right: auto;
  border-color: #cbcfd2;
}
</style>
