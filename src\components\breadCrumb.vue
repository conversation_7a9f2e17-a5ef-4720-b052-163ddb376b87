<template>
  <div class="bread-crumb-container" :class="{ 'jt-box-shadow': shadow }">
    <div class="bread-crumb">
      <a-breadcrumb>
        <a-breadcrumb-item v-for="(item, i) in value" :key="i">
          <span v-if="!item.path">{{ item.name }}</span>
          <router-link v-else :to="item.path">
            {{ item.name }}
          </router-link>
        </a-breadcrumb-item>
      </a-breadcrumb>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    shadow: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="less" scoped>
div {
  display: flex;
}
.bread-crumb-container {
  justify-content: center;
  width: 100%;
  background: #fff;
}
.bread-crumb {
  justify-content: space-between;
  width: 1200px;
  height: 56px;
  align-items: center;
}
:deep(.ant-breadcrumb > span:last-child) {
  color: #121f2c;
}
</style>
