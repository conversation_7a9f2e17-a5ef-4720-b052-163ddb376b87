<template>
  <div class="add-homework jt-box-shadow">
    <h3 class="head-title">{{ title }}</h3>
    <a-form ref="createWorkForm" class="create-work-form" :model="formData" :rules="rules" :colon="false" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item label="作业名称" name="assignmentName" :wrapper-col="{ span: 12 }">
        <a-input v-model:value="formData.assignmentName" :disabled="isEditWork && isPublishedWork" placeholder="请输入作业名称" />
      </a-form-item>
      <a-form-item required label="作业类型" name="assignmentType">
        <a-radio-group v-model="formData.assignmentType">
          <a-radio key="item" value="item" style="margin-right: 75px">项目</a-radio>
          <!-- <a-radio key="test" value="test">测验</a-radio> -->
        </a-radio-group>
      </a-form-item>
      <a-form-item required label="选择实例" name="instanceName" class="select-instance-item">
        <template #extra>
          <span>仅可选择模型训练的实例<br />实例中挂载的data文件夹（含公开和个人数据）将自动挂载至学生实例，个人数据总大小不超过30GB；<br />除data文件夹以外的文件将复制至学生实例，需确保文件总数小于1000，总大小不超过1GB</span>
        </template>
        <a-input v-model:value="formData.instanceName" :disabled="isEditWork && isPublishedWork" placeholder="请选择实例">
          <template #suffix>
            <jt-icon type="iconshujushangdian"></jt-icon>
          </template>
        </a-input>
        <div v-if="!isPublishedWork" class="instance-input-mask" @click="handleOpenInstance"></div>
      </a-form-item>
      <a-form-item required label="算力资源" name="spec">
        <a-radio-group v-model="formData.spec" :disabled="isEditWork && isPublishedWork">
          <a-radio key="cpu" value="cpu" style="margin-right: 72px">CPU</a-radio>
          <a-radio key="vGpu" value="vGpu">vGPU</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item required label="实例模式" name="instanceModel">
        <a-radio-group v-model="formData.instanceModel" :disabled="isEditWork && isPublishedWork">
          <a-radio key="Jupyter" value="Jupyter" style="margin-right: 28px"><span class="jt-jupyter-icon" style="margin-right: 4px">J</span>Jupyter</a-radio>
          <a-radio key="VSCode" value="VSCode"><span class="jt-vscode-icon" style="margin-right: 4px">V</span>VSCode</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item required label="提交截止时间" name="endTime">
        <a-date-picker v-model="formData.endTime" style="width: 260px" :show-time="defaultTime" :show-today="false" format="YYYY.MM.DD HH:mm:ss" placeholder="请选择提交截止时间" :disabled-date="handleDisabledDate" :disabled-time="handleDisableDateTime" />
      </a-form-item>
      <a-form-item required label="是否提交附件" name="accessory">
        <template #extra>提交一个50M以内的文件，格式支持zip/rar/pdf/docx/pptx </template>
        <a-radio-group v-model="formData.accessory" :disabled="isEditWork && isPublishedWork">
          <a-radio key="1" value="1" style="margin-right: 30px">要求提交附件</a-radio>
          <a-radio key="0" value="0">不要求提交附件</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label=" ">
        <a-space :size="8">
          <a-button v-if="!isPublishedWork" type="primary" style="width: 120px" :disabled="btnDisable || saveBtnDisable" @click="saveAndpublish">保存并发布</a-button>
          <a-button type="primary" ghost style="width: 88px" :disabled="btnDisable || saveBtnDisable" @click="saveWork">保存</a-button>
          <a-button style="width: 88px" @click="routeToWorkList">取消</a-button>
        </a-space>
        <p class="add-work-tips">当实例中挂载的data文件夹，或实例其他存储内容发生变化时，需再次编辑项目并发布，方可将最新实例内容同步至学生</p>
      </a-form-item>
    </a-form>
    <confirm-modal v-model="publishModalVisible" title="发布作业提示" :spinning="publishBtnDisabled" :confirm-loading="publishBtnDisabled" ok-text="发布" @ok="confirmPublish">
      <template #icon>
        <jt-icon type="iconwarning-circle-fill" style="font-size: 18px; color: #fa8014" />
      </template>
      <div style="margin-bottom: 32px; line-height: 22px">
        <p>发布前请务必确保作业名称、类型、实例信息、提交附件等均已正确设置。</p>
        <p>发布后仅可修改提交截止日期，作业开始时间即为发布时间。</p>
      </div>
    </confirm-modal>
    <select-instance-modal v-model="instanceModelVisible" :instance-id="formData.instanceId" @ok="handleOk" />
  </div>
</template>

<script lang="jsx">
import SelectInstanceModal from '@/components/selectInstanceModal/index.vue';
import ConfirmModal from '@/components/confirmModal/index.vue';
import { getCourseDetail } from '@/apis/teaching.js';
import { homeworkApi } from '@/apis';
import { mapState } from 'vuex';
import moment from 'moment';

const HOMEWORKSTATUS = {
  UNPUBLISH: '0',
  PUBLISHING: '1',
  PUBLISHED: '2',
  ENDED: '3',
};
export default {
  name: 'AddHomework',
  components: {
    SelectInstanceModal,
    ConfirmModal,
  },
  data() {
    return {
      title: this.$route.params.workId ? '编辑作业' : '新增作业',
      publishModalVisible: false,
      publishBtnDisabled: false,
      formData: {
        id: undefined,
        assignmentName: undefined,
        assignmentType: 'item', // 默认是项目 测验本次不开放
        instanceName: undefined,
        instanceId: undefined,
        spec: 'cpu',
        instanceModel: 'Jupyter',
        endTime: undefined,
        accessory: '0', // 要求提交附件 1 不要求 0
      },
      defaultTime: {
        defaultValue: moment('23:59:59', 'HH:mm:ss'),
      },
      isPublishedWork: false, // 是否是已发布作业
      instanceModelVisible: false,
      prevEndtime: undefined, // 保存上一次的截止时间，新的时间必须在该时间之后
      saveBtnDisable: false, // 保存按钮是否禁用
      labelCol: { span: 3 },
      wrapperCol: { span: 16 },
      rules: {
        assignmentName: [
          { required: true, message: '请输入作业名称', trigger: 'change' },
          { min: 1, max: 20, message: '20个字符以内', trigger: 'change' },
        ],
        assignmentType: [{ required: true, message: '请选择作业模式', trigger: 'change' }],
        instanceName: [{ required: true, message: '请选择实例', trigger: 'change' }],
        spec: [{ required: true, message: '请选择资源', trigger: 'change' }],
        instanceModel: [{ required: true, message: '请选择实例模式', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择提交截止时间', trigger: 'change' }],
      },
    };
  },
  computed: {
    isEditWork() {
      return !!this.$route.params.workId;
    },
    btnDisable() {
      return !this.formData.assignmentName || !this.formData.instanceName || !this.formData.endTime;
    },
    ...mapState('course', ['currentActiveCourse']),
  },
  created() {
    // 刷新进入页面时需要初始化数据
    const { courseId } = this.$route.params;
    if (!this.currentActiveCourse || Object.keys(this.currentActiveCourse).length === 0) {
      this.initCourseDetail(courseId);
    }
  },
  mounted() {
    const { workId } = this.$route.params;
    if (workId) {
      this.initHomeworkData(workId);
    }
  },
  methods: {
    // 禁止选中的时间
    handleDisabledDate(date) {
      let splitTime = moment();
      // if(this.prevEndtime && splitTime.diff(this.prevEndtime) < 0) {
      //   splitTime = moment(this.prevEndtime);
      // }
      return date && date <= splitTime.endOf('day').subtract(1, 'day');
    },
    handleDisableDateTime(date) {
      // 如果选择的是当天的时候则需有对时分秒进行控制
      let splitTime = moment();
      // if(this.prevEndtime && splitTime.diff(this.prevEndtime) < 0) {
      //   splitTime = this.prevEndtime;
      // }
      if (moment(date).isSame(moment(splitTime).endOf('day'), 'day')) {
        let nowHours = splitTime.hours(); // 0~23
        let nowMinutes = splitTime.minutes(); // 0~59
        let nowSeconds = splitTime.seconds(); // 0~59
        return {
          disabledHours: () => {
            return this.range(0, nowHours);
          },
          disabledMinutes: () => {
            return date.hours() === splitTime.hours() ? this.range(0, nowMinutes) : [];
          },
          disabledSeconds: () => {
            return date.hours() === splitTime.hours() && date.minutes() === splitTime.minutes() ? this.range(0, nowSeconds) : [];
          },
        };
      } else {
        return {
          disabledHours: () => [],
          disabledMinutes: () => [],
          disabledSeconds: () => [],
        };
      }
    },
    range(start, end) {
      const result = [];
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    },
    handleOpenInstance() {
      this.instanceModelVisible = true;
    },
    handleOk(selectInstance) {
      this.formData.instanceName = selectInstance.instanceName;
      this.formData.instanceId = selectInstance.instanceId;
    },
    // 打开保存并发布弹框
    saveAndpublish() {
      this.$refs.createWorkForm
        .validate()
        .then(() => {
          if (moment().diff(this.formData.endTime) > 0) {
            this.$message.error('提交截止时间必须晚于当前时间');
            return;
          }
          this.publishModalVisible = true;
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
    // 确认发布
    confirmPublish() {
      this.$refs.createWorkForm.validate().then(() => {
        const submitData = {
          ...this.formData,
          courseId: this.$route.params.courseId,
          endTime: this.formData.endTime.format('YYYY.MM.DD HH:mm:ss'),
        };
        this.publishBtnDisabled = true;
        homeworkApi
          .saveAndPublishHomework(submitData)
          .then((res) => {
            if (res.state === 'OK') {
              this.$message.success({
                content: <span style="color: #666666;">{`作业“${submitData.assignmentName}”发布中，请稍后查看`}</span>,
                icon: <jt-icon style="color: #0082FF;" type="icondengdaizhong"></jt-icon>,
              });
              this.routeToWorkList();
            } else {
              this.$message.error(res.errorCode === '-001' ? res.errorMessage : `作业${submitData.assignmentName}发布失败，请勿在发布过程中更改模型训练实例内容`);
            }
          })
          .finally(() => {
            this.publishBtnDisabled = false;
          });
      });
    },
    // 仅保存
    saveWork() {
      this.$refs.createWorkForm.validate().then(() => {
        if (moment().diff(this.formData.endTime) > 0) {
          this.$message.error('提交截止时间必须晚于当前时间');
          return;
        }
        // 更新或创建新的作业
        const requestInterface = this.isEditWork ? homeworkApi.updateHomework : homeworkApi.addHomework;
        const submitData = {
          ...this.formData,
          courseId: this.$route.params.courseId,
          endTime: this.formData.endTime.format('YYYY.MM.DD HH:mm:ss'),
        };
        this.saveBtnDisable = true;
        requestInterface(submitData)
          .then((res) => {
            const { assignmentName } = submitData;
            if (res.state === 'OK') {
              this.$message.success(`作业"${assignmentName}"保存成功`);
              this.routeToWorkList();
            } else {
              this.$message.error(res.errorCode === '-001' ? res.errorMessage : `作业"${assignmentName}"保存失败`);
            }
          })
          .finally(() => {
            this.saveBtnDisable = false;
          });
      });
    },
    // 跳转到列表页
    routeToWorkList() {
      const courseId = this.$route.params.courseId;
      this.$router.push(`/course/teaching/mycourses/course-manage/${courseId}?tabId=4&evaluateSubId=1`);
    },
    async initHomeworkData(id) {
      const res = await homeworkApi.getHomeworkDetail(id);
      if (res.state === 'OK') {
        const item = res.body;
        if (item.status === HOMEWORKSTATUS.PUBLISHING || item.status === HOMEWORKSTATUS.ENDED) {
          this.$router.push('/404');
        }
        // formData.startTime === 'string' ? formData.startTime : formData.startTime.format('YYYY/MM/DD')
        this.isPublishedWork = item.status === HOMEWORKSTATUS.PUBLISHED;
        this.prevEndtime = item.endTime ? moment(item.endTime) : '';
        this.formData.id = item.id;
        this.formData.assignmentName = item.name;
        this.formData.assignmentType = item.assignmentType;
        this.formData.instanceName = item.instanceName;
        this.formData.instanceId = item.instanceId;
        this.formData.spec = item.spec;
        this.formData.instanceModel = item.instanceModel;
        this.formData.endTime = item.endTime ? moment(item.endTime) : '';
        this.formData.accessory = item.accessory;
      }
    },
    initCourseDetail(courseId) {
      getCourseDetail({ courseId }).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.$store.commit('course/SET_CURRENTACTIVECOURSE_DATA', res.body);
        } else {
          this.$router.push('/course');
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.add-homework {
  width: 1200px;
  padding: 32px;
  margin: 20px auto 0px;
  background-color: @jt-color-white;
  .head-title {
    color: @jt-title-color;
    font-size: @jt-font-size-lger;
  }
  .create-work-form {
    margin-top: 32px;
    .add-work-tips {
      font-size: @jt-font-size-sm;
      color: #f78500;
      line-height: normal;
      margin-top: 24px;
    }
    .select-instance-item {
      :deep(.ant-input-affix-wrapper) {
        input {
          width: 380px;
        }
        .ant-input-suffix {
          z-index: 1;
          right: auto;
          left: 360px;
        }
      }

      .instance-input-mask {
        position: absolute;
        top: -10px;
        left: 0px;
        width: 380px;
        z-index: 1;
        height: 36px;
        cursor: pointer;
      }
    }
    .jt-vscode-icon {
      .jt-vscode-icon();
    }
    .jt-jupyter-icon {
      .jt-jupyter-icon();
    }
  }
}
</style>
