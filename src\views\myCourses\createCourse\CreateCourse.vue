<template>
  <div class="create-course jt-box-shadow">
    <h3 class="create-title">{{ Object.keys(this.currentActiveCourse).length == 0 ? '创建课程' : '设置课程基本信息' }}</h3>
    <div class="course-container">
      <a-steps :current="current" class="step-graph">
        <a-step v-for="item in steps" :key="item.title" :title="item.title" />
      </a-steps>
      <div class="steps-content">
        <BasicForm ref="basicForm" @handleEditImageUrl="handleEditImageUrl" v-show="current === 0" @changePage="next" />
        <CourseCover ref="courseCover" :imageUrl="editImageUrl" v-show="current === 1" />
        <CoursePreview :submitData="submitData" v-show="current === 2" />
        <FinishCourse :submitData="submitData" v-show="current === 3" />
      </div>
      <a-space class="steps-action">
        <a-button v-show="current < steps.length - 1 && current !== 0" type="primary" @click="next" style="width: 120px" :disabled="nextBtnDisable"> 下一步 </a-button>
        <a-button v-show="current > 0 && current !== 3" @click="prev" style="width: 88px"> 上一步 </a-button>
      </a-space>
    </div>
  </div>
</template>

<script>
import BasicForm from './components/BasicForm.vue';
import CourseCover from './components/CourseCover.vue';
import CoursePreview from './components/CoursePreview.vue';
import FinishCourse from './components/FinishCourse.vue';
import { basicSteps } from './constants';
import { addCourse, updateCourse, getCourseDetail } from '@/apis/teaching.js';
import { mapState, mapMutations } from 'vuex';

export default {
  components: {
    BasicForm,
    CourseCover,
    CoursePreview,
    FinishCourse,
  },
  created() {
    if (Object.keys(this.currentActiveCourse).length == 0) {
      const courseId = this.$route.params.courseId;
      if (courseId) {
        // 请求数据
        getCourseDetail({ courseId }).then((res) => {
          if (res.state === 'OK') {
            if (res.body) {
              this.SET_CURRENTACTIVECOURSE_DATA(res.body);
            }
          }
        });
      }
    }
  },
  data() {
    return {
      editImageUrl: '',
      courseId: this.$route.params.courseId,
      steps: basicSteps,
      current: 0,
      submitData: {},
      nextBtnDisable: false,
    };
  },
  computed: {
    ...mapState('course', ['currentActiveCourse']),
  },
  methods: {
    ...mapMutations('course', ['SET_CURRENTACTIVECOURSE_DATA']),
    handleEditImageUrl(url) {
      this.editImageUrl = url;
    },
    next() {
      if (this.current === 1) {
        if (this.$refs.courseCover.coverPreviewUrl == '') {
          this.$message.warning('请选择封面');
          return;
        }
      }
      if (this.current === 2) {
        let loadingMsg = this.$message.loading(this.courseId ? '课程修改中' : '课程创建中', 0);
        this.nextBtnDisable = true;
        if (this.courseId) {
          // 更新课程信息
          updateCourse({ ...this.submitData, courseId: this.courseId })
            .then((res) => {
              loadingMsg();
              if (res.state === 'OK') {
                this.current++;
              } else {
                this.$message.error('修改课程信息失败');
              }
              this.nextBtnDisable = false;
            })
            .catch(() => {
              loadingMsg();
              this.nextBtnDisable = false;
              this.$notification.error({
                message: '错误',
                description: '系统出错，请联系相应人员',
              });
            });
        } else {
          addCourse(this.submitData)
            .then((res) => {
              loadingMsg();
              if (res.state === 'OK') {
                this.current++;
              } else {
                this.$message.error('创建课程失败');
              }
              this.nextBtnDisable = false;
            })
            .catch(() => {
              loadingMsg();
              this.nextBtnDisable = false;
              this.$notification.error({
                message: '错误',
                description: '系统出错，请联系相应人员',
              });
            });
        }
      } else {
        this.current++;
      }
    },
    prev() {
      this.current--;
    },
  },
  watch: {
    current(newValue) {
      // 进入到预览页
      if (newValue === 2) {
        const formData = this.$refs.basicForm.formData;
        const courseImage = this.$refs.courseCover.coverPreviewUrl;
        const startTime = typeof formData.startTime === 'string' ? formData.startTime : formData.startTime.format('YYYY/MM/DD');
        const endTime = typeof formData.endTime === 'string' ? formData.time[1] : formData.endTime.format('YYYY/MM/DD');
        const time = startTime + '-' + endTime;
        this.submitData = { ...formData, courseImage, time, startTime, endTime };
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.create-course {
  background: #fff;
  width: 1200px;
  margin: 20px auto 0px;
  border-radius: 2px;
  .create-title {
    height: 64px;
    line-height: 64px;
    padding-left: 32px;
    border-bottom: 1px solid #e0e1e1;
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
  }
  .course-container {
    margin: 20px 100px 0px;
    .step-graph {
      margin: 60px 0px;
      :deep(.ant-steps-item-wait .ant-steps-item-icon) {
        background-color: #d7dfe6;
        border-color: #d7dfe6;
        .ant-steps-icon {
          color: white;
        }
      }
    }
    .steps-content {
      min-height: 360px;
      text-align: center;
    }
    .steps-action {
      margin-top: 40px;
      margin-bottom: 64px;
    }
  }
}
</style>
