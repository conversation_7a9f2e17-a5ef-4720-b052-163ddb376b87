<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
</script>
<template>
  <jt-common-content :loading="loading">
    <div class="section-form">
      <div class="top-tips-text">
        <p>
          {{ COMPETITION_DEEPLEARN_SUBSCRIBE_TEXT[currentState].tips }}
          <template v-if="isUnSubscribe">
            <a rel="noopener noreferrer" style="color: #0082ff" @click="goDeeplearnOrder">“移动云深度学习平台”</a>
            <span>{{ COMPETITION_DEEPLEARN_SUBSCRIBE_TEXT[currentState].secondTips }}</span>
          </template>
        </p>
      </div>

      <div>
        <div class="reg-success">
          <ul class="user-information-list">
            <li><span class="title">移动云用户名:</span>{{ checkTextEmpty(deepLearnFormData.ecloudUserName) }}</li>
            <li><span class="title">手机号:</span>{{ checkTextEmpty(deepLearnFormData.phone) }}</li>
            <li>
              <span class="title">实名认证:</span><strong class="status status-complete">{{ checkTextEmpty(deepLearnFormData.realStaText) }}</strong>
            </li>
            <li>
              <span class="title">深度学习平台:</span><strong class="status" :class="getStatusClass">{{ checkTextEmpty(getStatusText) }}</strong>
            </li>
          </ul>
        </div>

        <div class="handle-btn">
          <a-button type="primary" @click="goDeeplearnOrder"> {{ COMPETITION_DEEPLEARN_SUBSCRIBE_TEXT[currentState].upStep }} </a-button>
          <a-button v-if="!isSubscribed" style="margin-left: 10px" @click="goCompetitionDetail"> {{ COMPETITION_DEEPLEARN_SUBSCRIBE_TEXT[currentState].nextStep }} </a-button>
        </div>
      </div>

      <confirm-modal v-model="deeplearnModalVisible" title="移动云深度学习平台订购" ok-text="已订购" cancel-text="稍后订购" @cancel="goCompetitionDetail" @ok="handleModalOk">
        <template #icon>
          <ExclamationCircleFilled class="invited-icon" style="color: #0082ff" />
        </template>
        <div>请确认您已成功订购深度学习平台</div>
        <div style="margin-bottom: 18px">您需要完成深度学习平台订购，才能参加本次比赛哟~~</div>
      </confirm-modal>
    </div>
  </jt-common-content>
</template>

<script>
import API from '@/constants/api/API.js';

import confirmModal from '@/components/confirmModal';
import { ECLOUD_URL_CONFIG, ECLOUD_URL, COMPETITION_DEEPLEARN_SUBSCRIBE_STATE, COMPETITION_DEEPLEARN_SUBSCRIBE_TEXT, COMPETITION_DEEPLEARN_SUBSCRIBE_STATE_CLASS, COMPETITION_DEEPLEARN_SUBSCRIBE_STATE_TEXT } from '@/common/ecloud';
import { ECLOUD_COMPETITION_STEPS_TYPE, ECLOUD_COMPETITION_JOIN_TYPE } from '../competitionConfig/index';
import { openInNewTab, addUrlParams, checkTextEmpty } from '@/utils/utils';

const VERIFIED_DEEP_LEARN_URL = `${ECLOUD_URL}/api/page/deepLearning/web#/order/on-order`;

export default {
  name: 'DeepLearnOrder',
  components: { confirmModal },
  emits: ['changeCurrent', 'changeCompleteSuccess'],
  data() {
    return {
      cid: this.$route.query.id,
      cname: this.$route.query.name,
      currentState: COMPETITION_DEEPLEARN_SUBSCRIBE_STATE.UNSUBSCRIBE,
      COMPETITION_DEEPLEARN_SUBSCRIBE_TEXT,
      deeplearnModalVisible: false,
      loading: false,
      deepLearnFormData: {
        phone: '',
        ecloudUserName: '',
        realStaText: '已认证',
      },
    };
  },
  computed: {
    isSubscribed() {
      return this.currentState === COMPETITION_DEEPLEARN_SUBSCRIBE_STATE.SUBSCRIBED;
    },
    isUnSubscribe() {
      return this.currentState === COMPETITION_DEEPLEARN_SUBSCRIBE_STATE.UNSUBSCRIBE;
    },
    getStatusClass() {
      return COMPETITION_DEEPLEARN_SUBSCRIBE_STATE_CLASS[this.currentState];
    },
    getStatusText() {
      return COMPETITION_DEEPLEARN_SUBSCRIBE_STATE_TEXT[this.currentState];
    },
  },
  mounted() {
    this.getEcloudInfo();
  },
  methods: {
    checkTextEmpty,
    // 移动云承接的比赛报名状态查询
    getEcloudInfo(isMessage = false) {
      this.loading = true;
      API.competition_model.getEcloudInfo({ cid: this.cid }).then((res) => {
        if (res.state === 'OK') {
          this.currentState = res.body.orderSta;
          const { phone, ecloudUserName, orderSta, realSta } = res.body;
          this.deepLearnFormData.phone = phone; //手机号
          this.deepLearnFormData.ecloudUserName = ecloudUserName; // 移动云用户名
          this.deepLearnFormData.orderSta = orderSta; // 实名状态
          this.deepLearnFormData.realSta = realSta; // 认证状态
          if (isMessage && this.isUnSubscribe) {
            this.$message.error('抱歉，我们检测到您尚未完成深度学习平台订购，请再次尝试订购');
          }
          this.deeplearnModalVisible = false;
        } else if (res.state === 'ERROR' && res.errorCode == '-509') {
          this.$message.error('系统繁忙，请稍后重试');
        }
        this.loading = false;
      });
    },
    // 移动云承接的比赛报名提交
    addEcloudInfo() {
      const ecloudJoinRequest = {};
      ecloudJoinRequest.ecloudUserInfo = this.deepLearnFormData;
      ecloudJoinRequest.cid = this.cid;
      ecloudJoinRequest.joinSta = ECLOUD_COMPETITION_JOIN_TYPE.DEEPLEARN_SUBSCRIBED;
      this.loading = true;
      API.competition_model.addEcloudInfo(ecloudJoinRequest).then((res) => {
        if (res.state === 'OK') {
          const { redirectUrl } = this.$route.query;
          redirectUrl && this.$router.push(redirectUrl);

          if (this.isSubscribed) {
            this.$emit('changeCurrent', ECLOUD_COMPETITION_STEPS_TYPE.FINISH);
            this.$emit('changeCompleteSuccess', res.body);
          }
        } else {
          // 有互斥提示互斥，返回的是数组需要加，来隔开
          if (res.errorCode == '-513') {
            let rejectTagStr = res.errorParams.join('，');
            this.$message.error(`您已报名${rejectTagStr}，不可同时报名本比赛`);
          } else if (res.errorCode == '-509') {
            this.$message.error('系统繁忙，请稍后重试');
          } else {
            this.$message.error(res.errorMessage);
          }
        }
        this.loading = false;
      });
    },
    goDeeplearnOrder() {
      if (this.isUnSubscribe) {
        // 前往移动云实名认证
        this.goEClound();
        this.deeplearnModalVisible = true;
      } else {
        this.addEcloudInfo();
      }
    },
    // 前往移动云 自动登录
    goEClound() {
      this.loading = true;
      API.competition_model.ecloudSSOCheck().then((res) => {
        this.loading = false;
        const url = `${ECLOUD_URL}${ECLOUD_URL_CONFIG.ECLOUD_SSO_CHECK_URL}`;
        const urlParams = {
          token: res.body,
          destUrl: window.escape(VERIFIED_DEEP_LEARN_URL),
          systemSource: 'BiSheng',
        };
        const deepLearnUrl = addUrlParams(url, urlParams);
        openInNewTab(deepLearnUrl);
      });
    },
    handleModalOk() {
      this.getEcloudInfo(true);
    },
    goCompetitionDetail() {
      this.$router.push({
        path: '/competition/competition-detail',
        query: {
          id: this.cid,
          name: this.cname,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.top-tips-text {
  font-size: @jt-font-size-base;
  text-align: center;
  color: @jt-text-color-primary;
  margin-bottom: 32px;
}
.reg-success {
  width: 434px;
  height: 170px;
  background: #f9fafb;
  border-radius: @jt-border-radius;
  margin: auto;
  padding: 24px 24px 0;

  .user-information-list {
    li {
      margin-bottom: 16px;
      color: @jt-text-color;
    }

    .title {
      display: inline-block;
      min-width: 88px;
      height: 18px;
      line-height: 18px;
      text-align: right;
      font-size: @jt-font-size-base;
      color: @jt-text-color-primary;
      margin-right: 16px;
    }

    .status {
      font-weight: @jt-font-weight;
    }
    .status-notcre {
      color: @jt-error-color;
    }
    .status-complete {
      color: #17bb85;
    }
    .status-auditmiddle {
      color: @jt-warn-color;
    }
  }
}
.handle-btn {
  width: 434px;
  margin: 32px auto 20px;
}
</style>
