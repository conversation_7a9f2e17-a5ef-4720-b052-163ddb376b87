<script setup>
import { LeftOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="course-manage jt-box-shadow">
    <div class="content-title">
      <div class="text">作业评阅：{{ $route.query.assignmentName }}</div>
      <span @click="$router.push(`/course/teaching/mycourses/course-manage/${courseId}?tabId=4&evaluateSubId=1`)" class="return-button">
        <a-space> <LeftOutlined />返回作业列表 </a-space>
      </span>
    </div>
    <course-table></course-table>
  </div>
</template>

<script>
import courseTable from './courseTable.vue';
export default {
  name: 'Homework',
  components: {
    courseTable,
  },
  data() {
    return {};
  },
  computed: {
    courseId() {
      return this.$route.params.courseId;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.course-manage {
  width: 1200px;
  margin: 20px auto 0px;
  background-color: @jt-color-white;
  padding: 24px 32px 64px;
  min-height: ~'calc(100vh - 390px)';
}
.content-title {
  display: flex;
  justify-content: space-between;
  .text {
    color: @jt-title-color;
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
  }
  .return-button {
    font-size: @jt-font-size-sm;
    color: @jt-primary-color;
    cursor: pointer;
  }
}
</style>
