<template>
  <div class="head-title-container">
    <h3 class="jt-head-title">{{ title }}</h3>
    <slot></slot>
  </div>
</template>
<!--
  标题效果组件（前有一个label横杠）
  <head-title :title="这是一个标题"/>
 -->
<script>
export default {
  name: 'HeadTitle',
  props: {
    title: {
      type: String,
      required: true,
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
.head-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .jt-head-title {
    color: @jt-title-color;
    font-size: @jt-font-size-lger;
    position: relative;
    margin-left: 12px;
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: @jt-primary-color;
      position: absolute;
      left: -12px;
      top: 6px;
    }
  }
}
</style>
