<template>
  <div class="competition-edit-form">
    <a-form ref="formRef" v-bind="formItemLayout" :model="formData" :colon="false" @submit="handleSubmit">
      <a-form-item
        label="比赛名称"
        name="typeName"
        :rules="[
          {
            required: true,
            validator: cptNameVerify,
            trigger: ['blur', 'change'],
          },
        ]"
      >
        <a-input v-model:value="formData.typeName" placeholder="请输入比赛名称" style="width: 740px" />
      </a-form-item>
      <a-form-item
        label="起止时间"
        class="start-end-time"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 21 }"
        :rules="{
          required: true,
        }"
      >
        <a-form-item
          name="startTime"
          :style="{ display: 'inline-block' }"
          :rules="{
            required: true,
            message: '请选择起始时间',
          }"
        >
          <a-date-picker v-model:value="formData.startTime" :disabled-date="disabledStartDate" :disabled="timeDisabled" :allow-clear="false" value-format="YYYY-MM-DD" placeholder="起始时间" />
        </a-form-item>
        <span> — </span>
        <a-form-item
          name="endTime"
          :style="{ display: 'inline-block' }"
          :rules="{
            required: true,
            message: '请选择结束时间',
          }"
        >
          <a-date-picker v-model:value="formData.endTime" :allow-clear="false" :disabled-date="disabledEndDate" value-format="YYYY-MM-DD" placeholder="结束时间" />
        </a-form-item>
        <span class="extra-msg">开始日期到期之后不可修改</span>
      </a-form-item>
      <a-form-item
        label="比赛类型"
        :rules="{
          required: true,
          message: '请选择比赛类型',
        }"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 21 }"
        name="competitionType"
      >
        <a-radio-group v-model:value="formData.competitionType" :disabled="currentManageCompetition.releaseSta ? true : false">
          <a-radio :key="1" :value="1"> 正式赛-算法赛</a-radio>
          <a-radio :key="3" :value="3"> 正式赛-创意赛</a-radio>
          <a-radio :key="2" :value="2" disabled> 练习赛</a-radio>
        </a-radio-group>
        <span v-if="isCreateCompetition" class="extra-msg">创建后不可修改，请谨慎选择</span>
      </a-form-item>
      <a-form-item
        label="训练平台"
        :rules="{
          required: true,
          message: '请选择训练平台',
        }"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 21 }"
        name="competitionTraining"
      >
        <a-radio-group v-model:value="formData.competitionTraining" :disabled="currentManageCompetition.releaseSta ? true : false">
          <a-radio :key="1" :value="1"> 毕昇平台</a-radio>
          <a-radio :key="2" :value="2"> 移动云深度学习平台</a-radio>
          <a-radio :key="3" :value="3"> 仅信息展示，不提供训练平台</a-radio>
        </a-radio-group>
        <span v-if="isCreateCompetition" class="extra-msg">创建后不可修改，请谨慎选择</span>
      </a-form-item>
      <a-form-item
        class="intro-paragraph"
        label="一句话简介"
        name="summary"
        :rules="{
          required: true,
          message: '50个字符以内',
          trigger: 'change',
          max: 50,
        }"
      >
        <a-textarea v-model:value="formData.summary" placeholder="请输入简介内容" style="height: 72px" />
        <span class="intro-size-total">{{ formData.summary ? formData.summary.length : 0 }}/50</span>
      </a-form-item>
      <a-form-item
        label="标签"
        name="tag"
        help="20个字符以内，最多不超过3个标签"
        :rules="[
          {
            required: true,
            validator: tagsVerify,
            message: '请添加标签',
            trigger: 'change',
          },
        ]"
      >
        <div style="width: 740px">
          <a-tag v-for="tag in formData.tag" :key="tag" :closable="true" size="large" @close="handleClose(tag)">
            {{ tag }}
          </a-tag>
          <a-input v-if="formData.inputVisible" ref="input" v-model:value="formData.inputValue" :max-length="20" type="text" :size="'large'" :style="{ width: '78px' }" @change="handleInputChange" @blur="handleInputConfirm" @keyup.enter="handleInputConfirm" />
          <a-tag v-else-if="formData.tag.length < 3" style="background: #fff" @click="showInput"> <PlusOutlined /> 添加标签 </a-tag>
        </div>
      </a-form-item>
      <a-form-item
        label="奖池"
        name="amount"
        :rules="[
          {
            required: true,
            message: '10个字符以内',
            trigger: 'change',
            max: 10,
          },
        ]"
      >
        <a-input v-model:value="formData.amount" placeholder="请输入" />
      </a-form-item>
    </a-form>
    <a-space class="form-btns">
      <a-button type="primary" :disabled="nextDisabled" style="width: 120px" @click="handleSubmit"> 下一步 </a-button>
      <a-button style="width: 88px" @click="handleCancelEdit">取消</a-button>
    </a-space>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, defineEmits } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { PlusOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
import { competitionApi } from '@/apis/index.js';
import debounce from 'lodash/debounce';
import { message } from 'ant-design-vue';

const competitionNameRege = /^([“|”|【|】|《|》|（|）|+|\-|：|——|·|*|a-zA-Z0-9|\u4e00-\u9fa5]){1,50}$/;

const route = useRoute();
const router = useRouter();
const store = useStore();

const formRef = ref();
const input = ref();
const isCreateCompetition = ref(false); // 可通过props传递
const formItemLayout = {
  labelCol: {
    xs: { span: 6 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 18 },
    sm: { span: 12 },
  },
};
const timeDisabled = ref(false);
const formData = reactive({
  typeName: '',
  startTime: null,
  endTime: null,
  competitionType: '',
  competitionTraining: '',
  summary: '',
  tag: [],
  amount: '',
  inputVisible: false,
  inputValue: '',
});
const currentManageCompetition = computed(() => store.state.competition.currentManageCompetition);

const emit = defineEmits(['changeCurrent']);

const nextDisabled = computed(() => {
  let { typeName, startTime, endTime, competitionType, competitionTraining, summary, tag, amount } = formData;
  typeName = competitionNameRege.test(typeName);
  if (!typeName || !startTime || !endTime || !competitionType || !competitionTraining || !(summary.length <= 50) || !(summary.length > 0) || !(tag.length > 0) || !(amount.length > 0) || !(amount.length <= 10)) {
    return true;
  }
  return false;
});

onMounted(() => {
  initFormData();
});

function initFormData() {
  if (isCreateCompetition.value) {
    return;
  }
  const { typeName, StartTime, EndTime, competitionType, competitionTraining, summary, amount, tag } = currentManageCompetition.value;
  formData.typeName = typeName;
  formData.startTime = StartTime ? dayjs(StartTime).format('YYYY-MM-DD HH:mm:ss') : null;
  formData.endTime = EndTime ? dayjs(EndTime).format('YYYY-MM-DD HH:mm:ss') : null;
  formData.competitionType = competitionType;
  formData.competitionTraining = competitionTraining;
  formData.summary = summary;
  formData.amount = amount;
  if (tag) {
    formData.tag = tag.split('；');
  }
  let nowTime = new Date();
  let cptStartTime = new Date(Date.parse(formData.startTime));
  if (nowTime > cptStartTime) timeDisabled.value = true;
}

function disabledStartDate(startTime) {
  if (startTime && dayjs(startTime).isBefore(dayjs().endOf('day').subtract(1, 'day'))) {
    return true;
  }
  if (formData.endTime) {
    const endTime = dayjs(formData.endTime);
    return dayjs(startTime).endOf('day').valueOf() >= endTime.endOf('day').valueOf();
  } else {
    return false;
  }
}
function disabledEndDate(endTime) {
  if (endTime && dayjs(endTime).isBefore(dayjs().endOf('day').subtract(1, 'day'))) {
    return true;
  }
  if (formData.startTime) {
    const startTime = dayjs(formData.startTime);
    return startTime.endOf('day').valueOf() >= dayjs(endTime).endOf('day').valueOf();
  } else {
    return false;
  }
}

function handleSubmit() {
  formRef.value
    .validate()
    .then(() => {
      emitChangeCurrent(1);
    })
    .catch((err) => {
      message.error('表单校验失败');
    });
}
function handleCancelEdit() {
  if (isCreateCompetition.value) {
    router.push({
      path: '/competition/held-competition',
    });
  } else {
    const { competitionId } = route.params;
    router.push({
      path: `/competition/competition-management/${competitionId}`,
      query: {
        tabId: '2',
        subtabId: '1',
      },
    });
  }
}
function cptNameVerify(rule, value, callback) {
  if (value === '') {
    callback(new Error('比赛名称不可为空'));
  } else {
    if (competitionNameRege.test(value)) {
      if (currentManageCompetition.value.typeName === value) {
        callback();
      } else {
        checkCompetitionRepeat(value, callback);
      }
    } else {
      callback(new Error('50个字符以内，支持中文、英文大小写、数字及部分中文符号，如"、《》、（）、【】、+、-、：、——、·、*'));
    }
  }
}
function tagsVerify(rule, value, callback) {
  if (value.length == 0 && formData.inputValue == '') {
    callback(new Error());
  } else {
    callback();
  }
}
const checkCompetitionRepeat = debounce(function (value, callback) {
  competitionApi.checkCompetitionByName({ competitionName: value }).then((res) => {
    if (formData.typeName !== value) {
      return;
    }
    if (res.state === 'OK' && res.body === true) {
      callback();
    } else {
      callback(new Error('已存在同名比赛，请重新输入'));
    }
  });
}, 400);

function handleClose(removedTag) {
  const tags = formData.tag.filter((tag) => tag !== removedTag);
  formData.tag = tags;
}
function showInput() {
  formData.inputVisible = true;
  nextTick(() => {
    input.value && input.value.focus();
  });
}
function handleInputChange(e) {
  formData.inputValue = e.target.value;
}
function handleInputConfirm() {
  const inputValue = formData.inputValue;
  let tag = formData.tag;
  if (inputValue && tag.indexOf(inputValue) === -1) {
    tag = [...tag, inputValue];
  } else if (inputValue && tag.indexOf(inputValue) !== -1) {
    message.error(`已有同名标签`);
  }
  Object.assign(formData, {
    tag,
    inputVisible: false,
    inputValue: '',
  });
}
function emitChangeCurrent(val) {
  if (typeof emit !== 'undefined') {
    emit('changeCurrent', val);
  }
}
const getFormData = () => {
  return formData;
};
// 显式暴露给父组件（必须！）
defineExpose({
  getFormData,
});
</script>

<style lang="less" scoped>
.competition-edit-form {
  :deep(.ant-form-item) {
    .ant-col {
      margin-bottom: 8px;
    }
  }
  .extra-msg {
    color: red;
    font-size: 12px;
  }
  :deep(.ant-form-item-label > label::after) {
    margin-right: 16px;
  }
  .ant-tag {
    padding: 5px;
  }
  .start-end-time {
    margin-bottom: 0;
    .extra-msg {
      padding-left: 40px;
    }
    :deep(.ant-form-explain) {
      width: 150px !important;
    }
  }
  .form-btns {
    margin-left: 124px;
  }
  .intro-paragraph {
    position: relative;
    .intro-size-total {
      position: absolute;
      bottom: -15px;
      right: 8px;
    }
  }
  :deep(.ant-form-explain) {
    width: 740px;
    font-size: 12px;
  }
  :deep(.ant-radio-wrapper) {
    margin-right: 32px;
  }
}
</style>
