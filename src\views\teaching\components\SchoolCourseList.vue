<template>
  <div class="course-list-container">
    <div class="list-header">
      <p class="header-title">我开设的课程</p>
      <!-- <a-button @click="handleCourseData">获取课程数据</a-button> -->
    </div>
    <a-spin :spinning="loading">
      <div class="list-content">
        <div class="list-item" v-for="item in courseList" :key="item.id" @click="() => openToCourse(item.ude.url)">
          <img :src="item.ude.coverImageURL" alt="" />
          <div class="item-text">
            <p class="item-title" :title="item.name">{{ item.name }}</p>
            <span class="item-type">管理课程<span v-if="isDueTime(item.ude.dueTimestamp)" style="padding-left: 12px" class="duetime-alert">已到期，请联系客户经理</span></span>
            <!-- <span>{{ item.ude.dueTimestamp }}</span> -->
          </div>
        </div>
      </div>
    </a-spin>
    <div class="course-pagination-container">
      <jt-pagination :pageSizeOptions="[4, 8, 16]" @changePageSize="pageSizeChange" @changePageNum="pageNumChange" :pageSize="pagination.pageSize" :pageNum="pagination.pageNum" :total="pagination.total"></jt-pagination>
    </div>
  </div>
</template>

<script>
import { getXJCourseList } from '../../../apis/teaching.js';
import { openInNewTab } from '@/utils/utils';
export default {
  name: 'SchoolCourseList',
  props: {
    courseData: {
      type: Array,
    },
    initCourseTotal: {
      type: Number,
    },
  },
  data() {
    return {
      loading: false,
      courseList: this.courseData,
      pagination: {
        pageSize: 8,
        pageNum: 1,
        total: this.initCourseTotal,
      },
    };
  },
  methods: {
    isDueTime(time) {
      if (time === '0') {
        return false;
      }
      const currentTime = new Date();
      if (time - currentTime.getTime() > 0) {
        return false;
      } else {
        return true;
      }
    },
    async getCourseData() {
      this.loading = true;
      const res = await getXJCourseList({
        page: this.pagination.pageNum,
        limitInPage: this.pagination.pageSize,
        userType: 1, // 1表示教师
      });
      if (res.state === 'OK') {
        this.courseList = res.body.course;
        this.pagination.total = res.body.total;
      }
      this.loading = false;
    },
    pageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.pagination.pageNum = 1;
      this.getCourseData();
    },
    pageNumChange(pageNum) {
      this.pagination.pageNum = pageNum;
      this.getCourseData();
    },
    openToCourse(url) {
      openInNewTab(url);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.course-list-container {
  width: 1212px;
  margin: 40px auto 0px;
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-title {
      padding-left: 6px;
      width: 245px;
      font-size: 24px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 33px;
    }
  }
  .list-content {
    display: flex;
    flex-wrap: wrap;
    .list-item {
      margin-right: 20px;
      margin-top: 20px;
      width: 288px;
      height: 263px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid;
      border-color: #ecedee;
      cursor: pointer;
      transition: 0.3s all;
      img {
        width: 100%;
        height: 170px;
      }
      .item-text {
        padding: 20px 20px 0px;
        .item-title {
          font-size: 18px;
          color: #121f2c;
          line-height: 25px;
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .item-type {
          font-size: 14px;
          color: #0885ff;
          line-height: 20px;
        }
        .duetime-alert {
          color: #e6231f;
          font-size: 12px;
        }
      }

      &:hover {
        border-color: transparent;
        box-shadow: 0px 9px 28px 8px rgba(5, 11, 23, 0.05), 0px 6px 16px 0px rgba(5, 11, 23, 0.08), 0px 3px 6px -4px rgba(5, 11, 23, 0.12);
        .item-title {
          color: #0082ff;
        }
      }
    }
  }
  .list-item:nth-child(4n) {
    margin-right: 0px;
  }
  .course-pagination-container {
    padding: 0px 6px 50px;
    .pagination {
      margin-top: 24px;
    }
  }
}
</style>
