<template>
  <div class="competition-data-case">
    <a-radio-group v-model:value="subtabId" class="competition-data-case-radios" @change="radioChange">
      <a-radio-button value="1">数据及实例设置</a-radio-button>
    </a-radio-group>
    <dataCaseSetting v-if="subtabId === '1'"></dataCaseSetting>
  </div>
</template>

<script>
import dataCaseSetting from './dataCaseSetting.vue';
export default {
  name: 'CompetitionDataCase',
  components: {
    dataCaseSetting,
  },
  data() {
    return {
      subtabId: this.$route.query.subtabId || '1',
    };
  },
  methods: {
    radioChange(event) {
      const subtabId = event.target.value;
      this.$router.replace({ query: { tabId: '4', subtabId } });
    },
  },
};
</script>

<style lang="less" scoped>
.competition-data-case {
  position: relative;
  padding: 0px 32px 64px;
  .competition-data-case-radios {
    margin-bottom: 32px;
  }
}
</style>
