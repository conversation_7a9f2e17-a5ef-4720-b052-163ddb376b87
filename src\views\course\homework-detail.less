.main-container {
  flex-direction: column;
  background: #f4f8fa;
}
.content {
  justify-content: center;
  padding: 20px 0;
  min-height: 640px;
  .item-container {
    display: flex;
    min-width: 1200px;
    margin: 0 20px;
    width: initial;
    min-height: 680px;
    background: #fff;
  }
}
.btn {
  margin-right: 8px;
}
.bread-crumb-container {
  :deep(.bread-crumb) {
    margin: 0 20px;
    min-width: 1200px;
    flex: 1;
  }
    :deep(.ant-space-item) {
    margin-right: 0px !important;
  }
  // 上一页
  .mr16 {
    margin-right: 16px;
    font-size: 12px;
    padding-left: 17px;
  }
  // 下一页
  .ml16 {
    padding-right: 17px;
    font-size: 12px;
  }
}
.learn-status-tag {
  background-color: #fff;

  &.unstarted {
    color: #bec2c5;
    border: 1px solid #bec2c5;
  }
  &.unfinished {
    color: #ff7b00;
    border: 1px solid #ff7b00;
  }
  &.finished {
    color: #17c189;
    border: 1px solid #17c189;
  }
}
.end-time {
  color: #f78500;
  margin-right: 16px;
  &-outdated {
    color: #a0a6ab
  }
}
