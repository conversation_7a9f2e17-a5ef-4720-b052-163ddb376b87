import { GET } from '@/request';

const actions = {
  // 获取签到赢算力活动开放状态
  setSignGainSuanliSwitch({ commit }) {
    GET('/marketing/web/checkin/getCampaignStatus', {}).then((res) => {
      if (res.state === 'OK') {
        commit('SET_SIGNGAINSUANLISWITCH', res.body);
      }
    });
  },
  // 获取今日签到情况
  getCheckinStatus({ commit }) {
    GET('/marketing/web/checkin/getCheckinStatus', {}).then((res) => {
      if (res.state === 'OK') {
        commit('SET_SIGNGAINSUANLITODAY', res.body);
      }
    });
  },
};

export default actions;
