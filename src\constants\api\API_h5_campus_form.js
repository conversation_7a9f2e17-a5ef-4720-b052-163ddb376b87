import { GET, POST } from '@/request';
const mockBaseUrl = '';
// 控制台接口
export default {
  //课程管理页面，获取课程基本信息
  sendSmsCode: (data) => GET(`${mockBaseUrl}/marketing/web/sendSmsCode`, data, { useError: false }),
  userRegistration: (data) => POST(`${mockBaseUrl}/marketing/web/userRegistration`, data),
  userDetails: (data) => POST(`${mockBaseUrl}/marketing/web/userDetails`, data),
  getProvince: () => GET(`${mockBaseUrl}/marketing/web/getProvince`),
  getSchool: (data) => GET(`${mockBaseUrl}/marketing/web/getSchool`, data),
  getSearchProvince: (data) => GET(`${mockBaseUrl}/marketing/web/getSearchProvince`, data),
  getSearchSchool: (data) => GET(`${mockBaseUrl}/marketing/web/getSearchSchool`, data),
  getInviterName: (data) => GET(`${mockBaseUrl}/marketing/web/getInviterName`, data),
  inviteeRegistration: (data) => POST(`${mockBaseUrl}/marketing/web/inviteeRegistration`, data),
  getInvitationFeature: () => GET(`${mockBaseUrl}/marketing/web/getInvitationFeature`),
  getInviteLimitFeature: (data) => GET(`${mockBaseUrl}/marketing/web/getInvitationUrlStatus`, data),
  getMarketingFeature: () => GET(`${mockBaseUrl}/marketing/web/getMarketingFeature`),
  getInviteeBeanMessage: () => GET(`${mockBaseUrl}/marketing/web/getInviteeBeanMessage`),
};
