<template>
  <a-modal v-model:open="sVisible" class="jt-confirm-modal" :get-container="getContainer" :destroy-on-close="true" :title="null" :width="width" :closable="false" :mask-closable="false" :footer="null">
    <a-spin :spinning="spinning">
      <div class="confirm-modal-header">
        <slot name="icon"></slot>
        <h3 class="header-title">{{ title }}</h3>
      </div>
      <div class="confirm-modal-content">
        <slot></slot>
      </div>
      <div class="confirm-modal-footer">
        <a-space :size="8">
          <a-button v-if="type === 'danger'" type="primary" danger :disabled="confirmLoading" @click="handleOk">{{ okText }}</a-button>
          <a-button v-if="showCancel" :disabled="cancelLoading" @click="handleCancel">{{ cancelText }}</a-button>
          <a-button v-if="type !== 'danger'" type="primary" :disabled="confirmLoading" @click="handleOk">{{ okText }}</a-button>
        </a-space>
      </div>
    </a-spin>
  </a-modal>
</template>
<!--
  确认弹框组件
  <confirm-modal 
      v-model="visible" // 或者visible：Boolean
      type="danger" // 默认或danger (控制按钮的样式)
      okText=“确定”
      cancelText="取消"
      title="确认删除吗？"
      :confirmLoading="false" // 确认按钮disable
      :cancelLoading="false" // 取消按钮disable
      :showCancel="true" // 是否显示取消按钮，默认显示
      @cancel="handlecancel"
      @ok="handleOk"
    >
    <template #icon>
        <jt-icon type="iconwarning-circle-fill" style="font-size: 18px" />
      </template>
    <div>默认内容</div>
  </confirm-modal>
 -->
<script>
export default {
  name: 'ComfirmModal',
  model: {
    prop: 'visible',
    event: 'change',
  },
  props: {
    type: {
      type: String, // danger 或默认，对显示按钮控制
      default: '',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    okText: {
      type: String,
      default: '确定',
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    cancelLoading: {
      type: Boolean,
      default: false,
    },
    spinning: {
      type: Boolean,
      default: false,
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    title: {
      type: String,
      required: true,
    },
    showCancel: {
      // 取消按钮是否显示
      type: Boolean,
      default: true,
    },
    width: {
      type: [String, Number],
      default: '440px',
    },
    getContainer: {
      type: [Function],
      default: () => document.body,
    },
  },
  emits: ['change', 'cancel', 'ok'],
  data() {
    return {
      sVisible: !!this.visible,
    };
  },
  watch: {
    visible(val) {
      this.sVisible = !!val;
    },
  },
  methods: {
    handleCancel(e) {
      this.$emit('change', false);
      this.$emit('cancel', e);
    },
    handleOk(e) {
      this.$emit('ok', e);
    },
  },
};
</script>

<style lang="less">
.jt-confirm-modal {
  .confirm-modal-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .header-title {
      color: #121f2c;
      font-size: 14px;
      margin-left: 8px;
    }
  }
  .confirm-modal-content {
    padding: 0px 26px;
    color: #606972;
    font-size: 12px;
  }
  .ant-modal-content .ant-modal-body {
    padding: 32px 32px 24px;
  }
  .confirm-modal-footer {
    text-align: right;
  }
}
</style>
