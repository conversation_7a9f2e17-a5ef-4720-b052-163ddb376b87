<template>
  <div class="competition-cover-upload">
    <div class="upload-rows">
      <a-form ref="createForm" :model="formData" :colon="false" @submit="handleSubmit">
        <a-form-item
          label="比赛封面"
          v-bind="formItemLayout"
          class="upload-cover"
          name="advertiseImageUrl"
          help="支持jpg、jpeg、gif、png、bmp格式，大小不超过10MB，建议图片比例为1:1"
          :rules="[
            {
              required: true,
              message: '请选择',
              trigger: 'change',
            },
          ]"
        >
          <a-upload
            name="file"
            :headers="{
              Authorization: 'Bearer ' + refreshToken,
            }"
            list-type="picture-card"
            accept=".jpg,.jpeg,.gif,.png,.bmp"
            action="./object/web/storage/image/upload"
            :file-list="formData.advertiseImageUrl"
            :before-upload="beforeUpload"
            @change="handleAdvertiseChange"
          >
            <div v-if="formData.advertiseImageUrl.length < 1" class="upload-image">
              <div>
                <PlusOutlined />
                <div>上传图片</div>
              </div>
            </div>
          </a-upload>
        </a-form-item>
        <a-form-item
          label="宣传图"
          v-bind="formItemLayout"
          class="upload-advertise"
          name="coverImageUrl"
          help="支持jpg、jpeg、gif、png、bmp格式，大小不超过10MB，宽度不小于1136px，建议图片尺寸为1136*320px"
          :rules="[
            {
              required: false,
              message: '请选择',
              trigger: 'change',
            },
          ]"
        >
          <a-upload
            name="file"
            :headers="{
              Authorization: 'Bearer ' + refreshToken,
            }"
            list-type="picture-card"
            accept=".jpg,.jpeg,.gif,.png,.bmp"
            action="./object/web/storage/image/upload"
            :file-list="formData.coverImageUrl"
            :before-upload="beforeUpload"
            @change="handleCoverChange"
          >
            <div v-if="formData.coverImageUrl.length < 1" class="upload-image">
              <div>
                <PlusOutlined />
                <div>上传图片</div>
              </div>
            </div>
          </a-upload>
        </a-form-item>
        <a-form-item
          label="主办方logo"
          v-bind="formItemLayout"
          class="upload-logo"
          style="margin-bottom: 32px"
          name="logoImages"
          help="支持jpg、jpeg、gif、png、bmp格式，大小不超过10MB，建议透明背景"
          :rules="[
            {
              required: true,
              message: '请选择',
              trigger: 'change',
            },
          ]"
        >
          <a-upload
            name="file"
            :headers="{
              Authorization: 'Bearer ' + refreshToken,
            }"
            list-type="picture-card"
            accept=".jpg,.jpeg,.gif,.png,.bmp"
            action="./object/web/storage/image/upload"
            :before-upload="beforeUpload"
            :file-list="formData.logoImages"
            @change="handleLogoChange"
          >
            <div v-if="formData.logoImages.length < 10" class="upload-image">
              <PlusOutlined />
              <div>上传图片</div>
            </div>
          </a-upload>
        </a-form-item>
      </a-form>
    </div>

    <a-space class="form-btns">
      <a-button :disabled="previewBtnDisable" type="primary" style="width: 120px" @click="handleSubmit"> 预览 </a-button>
      <a-button style="width: 88px" @click="handleBack">上一步</a-button>
    </a-space>
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';

// Props and Emits
const props = defineProps({
  isCreateCompetition: Boolean,
});

const emit = defineEmits(['changeCurrent']);

// Store
const store = useStore();

// Refs
const createForm = ref(null);

// Reactive data
const formData = ref({
  advertiseImageUrl: [], // 封面
  coverImageUrl: [], // 宣传页
  logoImages: [], // logo
});

const formItemLayout = {
  labelCol: {
    xs: { span: 3 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 21 },
    sm: { span: 21 },
  },
};

// Computed
const refreshToken = computed(() => store.state.refreshToken);
const currentManageCompetition = computed(() => store.state.competition.currentManageCompetition);

// 图片上传完成且必传的图片都存在，预览按钮才可以点击
const previewBtnDisable = computed(() => {
  const { advertiseImageUrl, coverImageUrl, logoImages } = formData.value;
  const requiredImageUploaded = advertiseImageUrl.length > 0 && logoImages.length > 0 && advertiseImageUrl.every((item) => item.status === 'done') && logoImages.every((item) => item.status === 'done');
  const coverImageUrlPrepared = coverImageUrl.length === 0 || coverImageUrl.every((item) => item.status === 'done');
  return !(requiredImageUploaded && coverImageUrlPrepared);
});

// Methods
// 初始化回填数据
const initImages = () => {
  const { imageUrl, banner, leader } = currentManageCompetition.value;
  if (banner) {
    formData.value.coverImageUrl.push({
      uid: banner,
      name: banner,
      status: 'done',
      url: banner,
    });
  }
  if (imageUrl) {
    formData.value.advertiseImageUrl.push({
      uid: imageUrl,
      name: imageUrl,
      status: 'done',
      url: imageUrl,
    });
  }
  if (leader && leader.length > 0) {
    formData.value.logoImages = leader
      .map((item) => {
        if (item) {
          return {
            uid: item,
            name: item,
            status: 'done',
            url: item,
          };
        }
      })
      .filter(Boolean);
  }
};

const handleSubmit = () => {
  createForm.value
    .validate()
    .then(() => {
      emit('changeCurrent', 1);
    })
    .catch((err) => {
      throw new Error(err);
    });
};

const beforeUpload = (file) => {
  let isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error(`上传文件超过10MB`);
    return false;
  }
};

// 上传封面图
const handleCoverChange = ({ file, fileList }) => {
  if (file.status !== undefined) {
    formData.value.coverImageUrl = fileList;
  }
  if (file.status === 'done') {
    if (file.response && file.response.state === 'OK') {
      formData.value.coverImageUrl = fileList.map((fileItem) => {
        return {
          uid: fileItem.uid,
          name: fileItem.name,
          status: fileItem.status,
          url: fileItem.response.body.url,
        };
      });
      message.success(`文件上传成功`);
    }
  } else if (file.status === 'error') {
    formData.value.logoImages = fileList.slice(0, -1);
    message.error(`文件上传失败`);
  }
};

// 上传宣传图
const handleAdvertiseChange = ({ file, fileList }) => {
  if (file.status !== undefined) {
    formData.value.advertiseImageUrl = fileList;
  }
  if (file.status === 'done') {
    if (file.response && file.response.state === 'OK') {
      formData.value.advertiseImageUrl = fileList.map((fileItem) => {
        return {
          uid: fileItem.uid,
          name: fileItem.name,
          status: fileItem.status,
          url: fileItem.response.body.url,
        };
      });
      message.success(`文件上传成功`);
    }
  } else if (file.status === 'error') {
    formData.value.logoImages = fileList.slice(0, -1);
    message.error(`文件上传失败`);
  }
};

// 上传logo图
const handleLogoChange = ({ file, fileList }) => {
  if (file.status !== undefined) {
    formData.value.logoImages = fileList;
  }
  if (file.status === 'done') {
    if (file.response && file.response.state === 'OK') {
      formData.value.logoImages = fileList.map((fileItem) => {
        return {
          uid: fileItem.uid,
          name: fileItem.name,
          status: fileItem.status,
          url: fileItem.response ? fileItem.response.body.url : fileItem.url,
        };
      });
      message.success(`文件上传成功`);
    }
  } else if (file.status === 'error') {
    formData.value.logoImages = fileList.slice(0, -1);
    message.error(`文件上传失败`);
  }
};

const handleBack = () => {
  emit('changeCurrent', -1);
};

const getFormData = () => {
  return formData.value;
};
// Lifecycle
onMounted(() => {
  initImages();
});
// 显式暴露给父组件（必须！）
defineExpose({
  getFormData,
});
</script>

<style lang="less" scoped>
.competition-cover-upload {
  .form-btns {
    margin-left: 124px;
  }
  .upload-rows {
    :deep(.ant-form-item-label > label::after) {
      margin-right: 16px;
    }
    :deep(.ant-form-item) {
      margin-bottom: 24px;
    }
    :deep(.ant-form-explain) {
      width: 666px;
      margin-top: -23px !important;
      font-size: 12px;
    }
    .upload-col {
      text-align: right;
    }
    .upload-public-style {
      .ant-upload-select-picture-card i {
        font-size: 16px;
        color: #7f828f;
      }
      .upload-image {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #606972;
      }
      :deep(.ant-upload-list-item-info::before) {
        left: 0;
      }
    }
    .upload-cover {
      .upload-public-style();
      .upload-image {
        width: 112px;
        height: 112px;
      }
      :deep(.ant-upload-list-item-info) {
        width: 112px;
        height: 112px;
        text-align: center;
        span {
          a {
            img {
              width: 112px;
              height: 112px;
              object-fit: fill;
            }
          }
        }
      }
      :deep(.ant-upload-list-picture-card-container) {
        width: 128px;
        height: 128px;
      }
      :deep(.ant-upload-list-item-list-type-picture-card) {
        width: 128px;
        height: 128px;
      }
    }
    .upload-advertise {
      .upload-public-style();
      .upload-image {
        width: 480px;
        height: 136px;
      }
      :deep(.ant-upload-list-item-info) {
        width: 464px;
        height: 120px;
        text-align: center;
        span {
          a {
            img {
              width: 464px;
              height: 120px;
              object-fit: fill;
            }
          }
        }
      }
      :deep(.ant-upload-list-picture-card-container) {
        width: 480px;
        height: 136px;
      }
      :deep(.ant-upload-list-item-list-type-picture-card) {
        width: 480px;
        height: 136px;
      }
    }
    .upload-logo {
      .upload-public-style();
      :deep(.ant-upload-select-picture-card) {
        width: 162px;
        height: 62px;
      }
      .upload-image {
        .anticon-plus {
          margin-right: 12px;
        }
      }
      :deep(.ant-upload-list-item-info) {
        width: 144px;
        height: 45px;
        text-align: center;
        span {
          a {
            img {
              width: 144px;
              height: 45px;
              object-fit: contain;
            }
          }
        }
      }
      :deep(.ant-upload-list-picture-card-container) {
        width: 160px;
        height: 60px;
        margin: 0 8px 8px 0;
      }
      :deep(.ant-upload-list-item-list-type-picture-card) {
        width: 160px;
        height: 60px;
      }
    }
  }
}
</style>
