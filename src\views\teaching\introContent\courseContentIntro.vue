<template>
  <div class="outer-container">
    <div class="course-content-intro">
      <h1 class="head-title">{{ courseContent.title }}</h1>
      <div class="content-tab">
        <ul class="tab-ul">
          <li @mouseover="selectIndex = index" class="tab-li" v-for="(item, index) in courseContent.list" :class="selectIndex === index && 'select-li'" :key="item.title">
            {{ item.title }}
          </li>
        </ul>
      </div>
      <div class="content-intro">
        <div class="professional-course" :class="courseContent.list[selectIndex].columns == 3 && 'course-columns3'">
          <div class="intro-title">
            <img :src="courseIcon" alt="" />
            <h3>专业课程</h3>
          </div>
          <ul class="intro-text">
            <li v-for="item in courseContent.list[selectIndex].data" style="" :key="item">
              {{ item }}
            </li>
          </ul>
        </div>
        <div class="func-recommand">
          <div class="intro-title">
            <img :src="recommendIcon" alt="" />
            <h3>功能推荐</h3>
          </div>
          <ul class="intro-text">
            <li v-for="item in courseContent.list[selectIndex].data1" :key="item">
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
      <div style="text-align: center; margin-top: 40px">
        <consult-button />
      </div>
    </div>
  </div>
</template>

<script>
import { courseContent } from './config.js';
import ConsultButton from './../components/ConsultButton.vue';
export default {
  name: 'courseContentIntro',
  components: {
    ConsultButton,
  },
  data() {
    return {
      courseContent,
      selectIndex: 0,
      courseIcon: require('@/assets/image/teaching/course-icon.png'),
      recommendIcon: require('@/assets/image/teaching/recommend-icon.png'),
    };
  },
};
</script>

<style lang="less" scoped>
.outer-container {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url('~@/assets/image/teaching/content-bac.png');
  .course-content-intro {
    width: 1200px;
    height: 571px;
    margin: 0px auto;
    .head-title {
      padding-top: 48px;
      padding-bottom: 30px;
      color: #121f2c;
      font-size: 32px;
      text-align: center;
    }
    .content-tab {
      .tab-ul {
        display: flex;
        justify-content: space-between;
        padding: 0px 100px;
        border-bottom: 1px solid #c3d0d0;
        .tab-li {
          font-size: 20px;
          color: #121f2c;
          line-height: 28px;
          padding-bottom: 20px;
          // transition: 0.3s all;
        }
        .select-li {
          font-weight: 600;
          border-bottom: 4px solid #0082ff;
        }
      }
    }
    .content-intro {
      display: flex;
      justify-content: space-between;
      padding-top: 36px;
      .professional-course,
      .func-recommand {
        width: 588px;
        height: 237px;
        padding: 30px 30px 30px 60px;
        background: linear-gradient(136deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.3) 100%);
        box-shadow: 0px 2px 6px 3px rgba(0, 151, 215, 0.07);
        border-radius: 4px;
        border: 1px solid #ffffff;
        backdrop-filter: blur(8px);
        .intro-title {
          padding-bottom: 7px;
          display: flex;
          align-items: center;
          img {
            margin-right: 12px;
            width: 28px;
            height: 28px;
          }
        }
        .intro-text {
          display: flex;
          flex-wrap: wrap;
          padding-left: 16px;
          li {
            width: 50%;
            color: #121f2c;
            line-height: 24px;
            font-size: 14px;
            padding-top: 12px;
            position: relative;
            &::before {
              content: '';
              display: inline-block;
              width: 4px;
              height: 4px;
              position: absolute;
              left: -12px;
              top: 22px;
            }
          }
        }
        &.course-columns3 {
          padding-left: 30px;
        }
        &.course-columns3 .intro-text {
          li {
            width: 33%;
          }
        }
      }
      .professional-course .intro-text li::before {
        background-color: #0082ff;
      }
      .func-recommand .intro-text li::before {
        background-color: #00bba2;
      }
    }
  }
}
</style>
