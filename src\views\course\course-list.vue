<template>
  <div class="page-box">
    <header>
      <div class="inner">
        <jt-breadcrumb class="jt-breadcrumb">
          <jt-breadcrumb-item>
            <router-link to="/course">{{ '学习' }}</router-link>
          </jt-breadcrumb-item>
          <jt-breadcrumb-item>{{ '全部公开课' }}</jt-breadcrumb-item>
        </jt-breadcrumb>
        <jt-input v-model:value="formdata.courseName" allow-clear placeholder="搜索课程" class="search-input">
          <template #prefix>
            <span style="cursor: pointer; color: #bec2c5" class="iconfont search-icon search-fn-btn" @click="getListData">&#xe622;</span>
          </template>
        </jt-input>
        <div class="filter-list">
          <jt-button-filter v-for="(x, key) in filterList" :key="key" v-model="formdata[key]" class="filter-row" :options="x.list" :label="x.title" @change="changeFilterActive(key, $event)"></jt-button-filter>
        </div>
      </div>
    </header>

    <section>
      <div class="inner">
        <new-course-message v-if="giveBeansByCourse.feature"></new-course-message>
        <jt-common-content :empty="listData.data.length === 0" :loading="loading" :empty-style="{ height: '576px' }" :empty-image="emptyImage" :empty-title="emptyTitle">
          <div class="course-list common-content-container">
            <div v-for="(item, index) in listData.data" :key="index" class="item" @click="gotoDetail(item)">
              <img :src="item.courseImage" alt="courseImage" />
              <div class="item-content">
                <div class="item-title">
                  <h6>
                    <span class="ellipsis-text" style="max-width: 300px">
                      {{ item.courseName }}
                    </span>
                    <span class="text" :class="getCourseStatus(item.courseStatus).className">{{ getCourseStatus(item.courseStatus).text }}</span>
                  </h6>
                  <p class="ellipsis-text" style="max-width: 460px">
                    <span class="ellipsis-text" style="max-width: 150px">
                      {{ item.instituteName }}
                    </span>
                    <jt-divider type="vertical" />
                    {{ item.catalogNum || 0 }}节
                    <jt-divider type="vertical" />
                    开课时间：{{ item.startTime }} - {{ item.endTime }}
                  </p>
                </div>
                <p>{{ item.courseIntroduce }}</p>
                <span>{{ item.courseStudyNum || 0 }}人学习</span>
              </div>
            </div>
            <!-- 分页 -->
            <jt-pagination class="pagination-box" :page-size="formdata.pageSize" :page-num="formdata.pageNum" :total="listData.total" @changePageSize="pageSizeChange" @changePageNum="changePage"></jt-pagination>
          </div>
        </jt-common-content>
      </div>
    </section>
  </div>
</template>

<script>
import { Breadcrumb as JtBreadcrumb, Input as JtInput, Divider as JtDivider } from 'ant-design-vue';
import NewCourseMessage from './course-component/new-course-message.vue';
import API from '@/constants/api/API.js';
import { mapState } from 'vuex';
import debounce from 'lodash/debounce';
export default {
  components: {
    JtBreadcrumb,
    JtBreadcrumbItem: JtBreadcrumb.Item,
    JtInput,
    JtDivider,
    NewCourseMessage,
  },
  data() {
    return {
      loading: false,
      filterList: {
        courseCateCode: {
          title: '课程分类：',
          list: [
            {
              value: '-1',
              label: '全部',
            },
            {
              value: '1',
              label: '人工智能理论',
            },
            {
              value: '2',
              label: '计算机视觉',
            },
            {
              value: '3',
              label: '自然语言处理',
            },
            {
              value: '4',
              label: '智能数据分析',
            },
            {
              value: '5',
              label: '智能语音',
            },
          ],
        },
        courseLevelCode: {
          title: '能力分级：',
          list: [
            {
              value: '-1',
              label: '全部',
            },
            {
              value: '1',
              label: '预备知识',
            },
            {
              value: '2',
              label: '进阶入门',
            },
            {
              value: '3',
              label: '工程实战',
            },
          ],
        },
        courseStatus: {
          title: '课程状态：',
          list: [
            {
              value: '',
              label: '全部',
            },
            {
              value: '1',
              label: '进行中',
            },
            {
              value: '0',
              label: '即将开始',
            },
            {
              value: '2',
              label: '已结束',
            },
          ],
        },
      },
      // 已有课程数量
      courseNumber: 0,
      // 列表请求所需参数
      formdata: {
        courseCateCode: '', // 课程分类码
        courseLevelCode: '', // 课程级别码
        courseStatus: '', // 课程状态 0：未开始；1：进行中； 2：结束
        courseName: '', // 课程名称
        pageNum: 1, // 页号
        pageSize: 5, // 一页条数
        requestId: this.$store.state.requestId, // 请求唯一id
      },
      listData: {
        total: 0,
        data: [],
      },
    };
  },
  computed: {
    ...mapState(['giveBeansByCourse']),
    emptyImage() {
      return this.formdata.courseName ? require('@/assets/image/empty2x.png') : require('@/assets/image/emptys2x.png');
    },
    emptyTitle() {
      let title = '';
      title = this.formdata.courseName ? '暂无结果' : '暂无课程';
      return title;
    },
  },
  watch: {
    'formdata.courseName': debounce(function () {
      this.formdata.pageNum = 1;
      this.getListData();
    }, 500),
  },
  created() {
    API.course_model.getCourseExistedNum({ requestId: this.formdata.requestId }).then((res) => (this.courseNumber = res.body));
    this.getTitleTypes().then(() => this.changeFilterActive(this.$route.query.key, this.$route.query.value));
  },
  methods: {
    /**
     * 改变分类处的高亮选择
     * @param {object} key 分类列表的key
     * @param {object} item 被点击的项
     */
    changeFilterActive(key, item) {
      this.formdata[key] = item;
      this.formdata.pageNum = 1;
      this.getListData();
    },
    /**
     * 跳转 课程详情 页面
     * @param {object} item 被点击的课程
     */
    gotoDetail(item) {
      this.$router.push({
        path: '/course/course-detail',
        query: {
          num: item.courseStudyNum,
          courseId: item.courseId,
        },
      });
    },
    /**
     * 根据状态获取对应文案和classname
     * @returns {string} text 状态对应文案
     * @returns {string} text 状态对应class
     */
    getCourseStatus(status) {
      let text = '',
        className;
      switch (status) {
        case '0':
          text = '即将开始';
          className = 'ready';
          break;
        case '1':
          text = '进行中';
          className = 'running';
          break;
        case '2':
          text = '已结束';
          className = 'over';
          break;
      }
      return { text, className };
    },
    /**
     * 获取页面头部分类列表
     */
    getTitleTypes() {
      return new Promise((resove, reject) => {
        if (this.$route.query.id) {
          delete this.filterList.courseCateCode;
          delete this.filterList.courseLevelCode;
          resove();
          reject();
          return;
        }

        const resultAll = Promise.all([
          API.course_model.getCourseCategoryList({
            requestId: this.formdata.requestId,
          }),
          API.course_model.getCourseLevelList({
            requestId: this.$store.state.requestId,
          }),
        ]);
        resultAll
          .then((posts) => {
            let arr = [
              {
                value: '',
                label: '全部',
              },
            ];
            posts[0].body.map((x) => arr.push({ value: x.categoryCode, label: x.categoryName }));
            this.filterList.courseCateCode.list = arr;
            arr = [
              {
                value: '',
                label: '全部',
              },
            ];
            posts[1].body.map((x) => arr.push({ value: x.levelCode, label: x.levelName }));
            this.filterList.courseLevelCode.list = arr;
            resove();
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    /**
     * 获取列表
     */
    getListData() {
      this.loading = true;
      API.course_model.getCourseList(this.formdata).then((res) => {
        if (res.state === 'OK') {
          this.listData = res.body;
        }
        this.loading = false;
      });
    },
    /**
     * 翻页
     */
    changePage(pageNum) {
      this.formdata.pageNum = pageNum;
      this.getListData();
    },
    pageSizeChange(pageSize) {
      this.formdata.pageNum = 1;
      this.formdata.pageSize = pageSize;
      this.getListData();
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';
header {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  padding-bottom: 32px;
  .inner {
    position: relative;
  }
}
.jt-breadcrumb {
  line-height: 56px;
  // border-bottom: 1px solid #e0e1e1;
}
.search-input {
  width: 240px;
  height: 32px;
  border-radius: 2px;
  position: absolute;
  right: 0;
  top: 18px;
}
.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  &:last-of-type {
    margin-bottom: 0;
  }
  > p {
    width: 76px;
    line-height: 20px;
    margin-right: 13px;
  }
}
.filter-item {
  margin-right: 12px;
  display: block;
  padding: 3px 11px;
  line-height: 20px;
  height: auto;
  font-size: 14px;
  &.ant-btn-default {
    border-color: transparent;
    box-shadow: none;
  }
}
// header end

// section
section {
  background: #f4f8fa;
  padding-bottom: 48px;
  margin-top: 20px;
}
.course-banner {
  width: 1200px;
  height: 180px;
  background: #178cf9;
  border-radius: 2px 2px 0px 0px;
}
.course-list {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  .item {
    height: 176px;
    padding: 32px;
    display: flex;
    box-shadow: 0px 1px 0px 0px #e0e1e1;
    margin-bottom: 1px;
    cursor: pointer;
    &:hover {
      background: #f8f9fa;
      h6 {
        color: @jt-primary-color;
      }
    }
    img {
      width: 148px;
      height: 112px;
      flex-shrink: 0;
      margin-right: 32px;
    }
    .item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      h6 {
        font-size: 20px;
        // line-height: 28px;
        display: flex;
        align-items: center;
        span.text {
          display: inline-flex;
          justify-content: center;
          align-items: center;
          height: 24px;
          padding: 0 8px;
          border-radius: 2px;
          border: 1px solid #0082ff;
          font-size: 12px;
          margin-left: 16px;
          &.running {
            color: #0082ff;
          }
          &.ready {
            border-color: #0cb0d4;
            color: #0cb0d4;
          }
          &.over {
            background: linear-gradient(270deg, #cbcfd2 0%, #cbcfd2 100%);
            border: none;
            color: #fff;
          }
        }
      }
      // 视觉走查
      p {
        color: #606972;
      }
    }
    .item-content {
      width: 100%;
      > p {
        color: #606972;
        line-height: 20px;
        margin-top: 16px;
      }
      > span {
        display: block;
        color: #a0a6ab;
        line-height: 20px;
        margin-top: 16px;
      }
    }
  }
}
.pagination-box {
  display: flex;
  align-items: center;
  height: 92px;
  padding: 0 32px;
  box-sizing: border-box;
  z-index: 1;
  margin-top: 0;
}
// section end

// input 文字
:deep(.ant-input) {
  text-indent: 8px;
}
// input icon
:deep(.ant-input-clear-icon) {
  font-size: 16px;
}
:deep(.ant-pagination-item:hover) {
  border-color: #0082ff;
  color: #0082ff;
}
.empty {
  text-align: center;
}
</style>
