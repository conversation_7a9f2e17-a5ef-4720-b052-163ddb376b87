<template>
  <div class="contents">
    <div class="header">教师团队</div>
    <jt-common-content :empty="courseTeacherList.length === 0" :loading="loading">
      <div class="main-contents">
        <div class="item" v-for="item in courseTeacherList" :key="item.id">
          <div class="img-container">
            <img :src="item.teacherImage || defaultUrl" alt="" />
          </div>
          <p class="teacher-name">{{ item.teacherName }}</p>
          <p class="description">{{ item.teacherDesc }}</p>
        </div>
      </div>
    </jt-common-content>
  </div>
</template>

<script>
import API from '@/constants/api/API.js';

export default {
  props: { courseId: String },
  data() {
    return {
      defaultUrl: require('@/assets/image/avatar_big.png'),
      courseTeacherList: [],
      loading: false,
    };
  },
  mounted() {
    const courseId = this.courseId;
    const obj = {
      courseId,
    };
    this.loading = true;
    API.course_model.getCourseDetailteachers(obj).then((res) => {
      this.courseTeacherList = res.body;
      this.loading = false;
    });
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.contents {
  background: #fff;
  flex: 1;
  flex-direction: column;
  .header {
    padding: 24px 0 16px 32px;
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    border-bottom: 1px solid #e0e1e1;
  }
  .main-contents {
    display: flex;
    flex-wrap: wrap;
    padding: 32px;
    .item {
      width: 232px;
      margin-bottom: 76px;
      margin-right: 70px;
      &:nth-child(3n) {
        margin-right: 0;
      }
      .img-container {
        text-align: center;
        margin-bottom: 20px;
        img {
          width: 120px;
          height: 120px;
          border-radius: 50%;
        }
      }
      p {
        margin: 0;
      }
      .teacher-name {
        padding: 12px 0;
        text-align: center;
        font-size: 18px;
        font-weight: 400;
        color: #121f2c;
      }
      .description {
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        color: #606972;
      }
    }
  }
}
</style>
