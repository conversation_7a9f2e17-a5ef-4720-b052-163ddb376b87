<script setup>
import { FilterFilled } from '@ant-design/icons-vue';
</script>
<template>
  <section>
    <headTitle title="提交记录">
      <div class="subtitle">
        <search v-model="formData.keyword" placeholder="团队名称/提交人用户名/姓名" class="search-input" @handSearch="handSearch" />
        <a-button class="button" type="primary" ghost :disabled="tableEmpty" @click="download">
          <jt-icon type="icondaochu2"></jt-icon>
          导出
        </a-button>
      </div>
    </headTitle>
    <a-config-provider>
      <template #renderEmpty>
        <jt-common-content :empty="true" :loading="loading" :empty-image="emptyStatus.image" :empty-title="emptyStatus.title" :empty-text="emptyStatus.text"> </jt-common-content>
      </template>
      <a-table class="table" size="middle" :loading="tableData.length > 0 ? loading : undefined" row-key="index" :columns="columns" :data-source="tableData" :scroll="{ x: columns.filter((x) => x.isAjaxRow).length * 250 + 300 }" :pagination="false" @change="tableChange">
        <!-- 根据接口返回自动生成的title -->
        <template v-for="x in columns.filter((z) => z.isAjaxRow)" :key="x.dataIndex" #[x.slots.title]>
          <p :title="x.dataIndex" :style="ajaxTitleStyle">{{ x.titleName }}</p>
        </template>
        <template #filterDropdownIcon>
          <FilterFilled :style="{ color: formData.submitStatus != '' ? '#108ee9' : undefined, right: 'auto' }" />
        </template>
        <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm }">
          <div class="filter-status-select">
            <div class="all" :class="{ select: formData.submitStatus == '' }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, '')">全部类型</div>
            <div class="already-study" :class="{ select: formData.submitStatus == 1 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 1)">{{ getResultStatusObj(1) }}</div>
            <div v-if="$store.state.competition.resultContentData.markType" class="old-study" :class="{ select: formData.submitStatus == 3 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 3)">{{ getResultStatusObj(3) }}</div>
            <div v-else class="old-study" :class="{ select: formData.submitStatus == 4 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 4)">{{ getResultStatusObj(4) }}</div>
            <div class="un-study" :class="{ select: formData.submitStatus == 2 }" @click="handleSelectClick(setSelectedKeys, selectedKeys, confirm, 2)">{{ getResultStatusObj(2) }}</div>
          </div>
        </template>
        <template #submitstatus-slot="{ status }">
          <a-space>
            <div class="status-icon" :class="status == 1 ? 'green' : status == 2 || status == null ? 'orange' : 'blue'"></div>
            {{ getResultStatusObj(status) }}
          </a-space>
        </template>
      </a-table>
    </a-config-provider>
    <div v-if="!tableEmpty" class="jt-pagination">
      <a-space size="large">
        <span>共{{ page.total }}条记录</span>
        <span>
          每页显示
          <a-select default-value="10" style="width: 65px" @change="changePageSize">
            <a-select-option value="5"> 5 </a-select-option>
            <a-select-option value="10"> 10 </a-select-option>
            <a-select-option value="15"> 15 </a-select-option>
            <a-select-option value="20"> 20 </a-select-option>
          </a-select>
          条
        </span>
      </a-space>
      <a-pagination :page-size="page.pageSize" show-quick-jumper :default-current="1" :total="page.total" @change="changePageNum" />
    </div>
  </section>
</template>

<script>
import headTitle from '@/components/headTitle';
import search from '@/components/search';
import API from '@/constants/api/API';
import { getTableRow, setTableDataByRow } from '@/views/competition/competitionManagement/competitionRank/utils';
import { downloadFileWithToken } from '@/utils/file';
import { getResultStatusObj, ajaxTitleStyle } from '@/views/competition/competitionManagement/competitionRank/constants';

export default {
  components: {
    headTitle,
    search,
  },
  emits: ['getListTotal'],
  data() {
    const cid = this.$route.params.competitionId;
    return {
      ajaxTitleStyle,
      cid,
      scoreName: {},
      // noSearchResultEmptyImage: require('@/assets/image/empty2x.png'),
      tableData: [],
      columns: [
        {
          dataIndex: 'teamName',
          key: 'teamName',
          title: '团队名称',
          ellipsis: true,
          width: 200,
          fixed: 'left',
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          dataIndex: 'userName',
          key: 'userName',
          title: '提交人用户名',
          ellipsis: true,
          width: 150,
          fixed: 'left',
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          dataIndex: 'fullName',
          key: 'fullName',
          title: '提交人姓名',
          ellipsis: true,
          width: 100,
          fixed: 'left',
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
        {
          title: '状态',
          dataIndex: 'submitstatus',
          key: 'submitstatus',
          width: 100,
          slots: {
            filterDropdown: 'filterDropdown',
            customRender: 'submitstatus-slot',
            filterIcon: 'filterDropdownIcon',
          },
        },
        {
          title: '提交时间',
          key: 'createtime',
          dataIndex: 'createtime',
          sorter: true,
          width: 200,
          customRender: ({ text, record, index }) => {
            return text || '--';
          },
        },
      ],
      filter: false,
      loading: false,
      formData: {
        keyword: '',
        submitStatus: '',
        sortKey: '',
        sortRule: '',
      },
      page: {
        pageNum: 1,
        total: 0,
        pageSize: 10,
      },
    };
  },
  computed: {
    tableEmpty() {
      return this.tableData.length === 0;
    },
    emptyStatus() {
      if (this.formData.keyword || this.formData.submitStatus) {
        return {
          image: require('@/assets/image/empty2x.png'),
          title: '抱歉，没有找到相关提交记录',
          text: '您可以换一个关键词试试哦～',
        };
      } else {
        return {
          image: require('@/assets/image/emptys2x.png'),
          title: '暂无提交记录',
          text: '',
        };
      }
    },
  },
  created() {
    API.competition_model.getCompetitionGetScoreName({ cid: this.cid }).then((res) => {
      if (res.state === 'OK') {
        getTableRow({ scoreName: res.body, sorter: true }).then((_res) => {
          this.columns.splice(3, 0, ..._res);
          if (this.columns.filter((x) => x.isAjaxRow).length === 0) {
            this.columns
              .filter((x, i) => i > this.columns.length / 3)
              .map((x) => {
                delete x.width;
                delete x.fixed;
              });
          }
          this.getTableData().then((tableDataResult) => {
            this.$emit('getListTotal', tableDataResult.total);
          });
        });
      }
    });
  },
  methods: {
    getResultStatusObj,
    getTableData() {
      const params = { cid: this.cid, ...this.formData, ...this.page };
      this.loading = true;
      return new Promise((resolve) => {
        API.competition_model.fileResultList(params).then((res) => {
          this.loading = false;
          if (res.state === 'OK') {
            this.tableData = res.body.list;
            setTableDataByRow(this.tableData);
            this.page.total = res.body.total;
            resolve(res.body);
          }
        });
      });
    },
    handSearch(val) {
      this.formData.keyword = val;
      this.getTableData();
    },
    handleSelectClick(setSelectedKeys, selectedKeys, confirmCallBack, index) {
      confirmCallBack();
      this.formData.submitStatus = index;
      this.page.pageNum = 1;
      this.getTableData();
    },
    changePageSize(size) {
      this.page.pageSize = Number(size);
      this.page.pageNum = 1;
      this.getTableData();
    },
    changePageNum(num) {
      this.page.pageNum = num;
      this.getTableData();
    },
    tableChange(pagination, filters, sorter) {
      this.formData.sortKey = sorter.field;
      this.formData.sortRule = sorter.order == 'ascend' ? 'asc' : 'desc';
      this.getTableData();
    },
    download() {
      downloadFileWithToken({ url: `/competiton/web/manage/edit/resultList/export?cid=${this.cid}` });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import '~@/views/competition/competitionManagement/competitionRank/table.less';
section {
  margin-top: 43px;
  padding-bottom: 48px;
}
.subtitle {
  display: flex;
}
.search-input {
  width: 240px;
  margin-right: 9px;
  :deep(.ant-input) {
    padding-right: 20px;
  }
}
.button {
  width: 80px;
}
</style>
