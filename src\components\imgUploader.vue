<script setup>
import { UploadOutlined } from '@ant-design/icons-vue';
</script>
<template>
  <div class="container">
    <div class="viewer">
      <img :src="value || defaultUrl" alt="" />
    </div>
    <a-upload
      :show-upload-list="false"
      name="file"
      :multiple="false"
      :accept="acceptTypes"
      action="./object/web/storage/image/upload"
      :headers="{
        Authorization: 'Bearer ' + refreshToken,
      }"
      :before-upload="beforeUpload"
      :remove="handleRemove"
      @change="onChange"
    >
      <span class="required-icon">*</span>
      <a-button> <UploadOutlined /> 更换头像</a-button></a-upload
    >
    <p class="ant-upload-hint">支持jpg、jpeg、gif、png、bmp格式，大小不超过10MB，建议使用正方形图片</p>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { checkFileType } from '@/utils/utils';

export default {
  model: {
    prop: 'value', // 定义传递给v-model的那个变量，绑定到哪个属性值上
    event: 'change', // event:什么时候触发v-model行为
  },
  props: {
    value: String,
    required: Boolean,
    acceptTypes: {
      required: false,
      type: String,
      default: '.jpg,.jpeg,.gif,.png,.bmp,.JPG,.JPEG,.GIF,.PNG,.BMP',
    },
  },
  emits: ['change'],

  data() {
    return {
      defaultUrl: require('@/assets/image/avatar_big.png'),
    };
  },
  computed: {
    ...mapState(['refreshToken']),
  },
  methods: {
    handleRemove() {
      this.$emit('change', '');
    },
    onChange(info) {
      const file = info.file;
      if (file.status === 'done') {
        if (file.response && file.response.state === 'OK') {
          this.$message.success(`文件上传成功`);
          this.$emit('change', file.response.body.url);
        } else {
          this.$message.error(`文件上传失败:${file.response.body?.message || '未知错误'}`);
        }
      } else if (file.status === 'error') {
        this.$message.error(`文件上传失败`);
      }
    },
    beforeUpload(file) {
      if (!checkFileType(this.acceptTypes, file.name)) {
        this.$notification.error({
          message: '错误',
          description: '上传文件格式错误',
        });
        return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.ant-upload-hint {
  font-size: 12px;
  font-weight: 400;
  color: #a0a6ab;
  line-height: initial;
}
.viewer {
  margin-bottom: 24px;
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
  }
}
.required-icon {
  color: #ff454d;
  margin-right: 12px;
}
</style>
