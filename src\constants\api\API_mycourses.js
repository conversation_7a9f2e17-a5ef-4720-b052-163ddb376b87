import { GET } from '@/request';
// 控制台接口
export default {
  //课程管理页面，获取课程基本信息
  getCourseList: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getCourseList', data),
  getCourseDescription: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getCourseDescription', data),
  getCourseContent: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getCourseContent', data),
  getCourseResource: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getCourseResource', data),
  getTrackCourses: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getTrackCourses', data),
  getStudentsByCourse: (data) => GET('https://mobile-ms.uat.homecreditcfc.cn/mock/610ceee95a76d800278619d4/jtapi/getStudentsByCourse', data),
};
