<template>
  <div class="manage-header jt-box-shadow">
    <div class="manage-breadcrumb">
      <bread-crumb :value="breadCrumbs" :shadow="false"></bread-crumb>
    </div>
    <div class="manage-status" v-if="hasCompetition">
      <div class="status-left" :class="{ 'status-hidden': !currentManageCompetition.typeName }">
        <span class="competition-name">{{ currentManageCompetition.typeName }}</span>
        <a-space align="start" style="padding-top: 6px">
          <jt-tag :type="getCompetitionState(currentManageCompetition.flag)">{{ runningStatusMaps[currentManageCompetition.flag] }}</jt-tag>
          <jt-tag>{{ competitionTypeMaps[currentManageCompetition.typeId] }}</jt-tag>
          <jt-tag :type="currentManageCompetition.releaseSta === publishStatusKeys.PUBLISHED ? 'published' : 'unpublish'">{{ publishStatusMaps[currentManageCompetition.releaseSta] }}</jt-tag>
        </a-space>
      </div>
      <a-button v-if="currentManageCompetition.releaseSta === publishStatusKeys.PUBLISHED" class="route-to-main" @click="toOnlineCompetition()">查看线上比赛页<jt-icon type="iconbofang"></jt-icon></a-button>
    </div>
  </div>
</template>

<script>
import breadCrumb from '@/components/breadCrumb';
import JtTag from '@/components/tag';
import { competitionTypeMaps, publishStatusMaps, runningStatusMaps, publishStatusKeys, runningStatusKeys } from '../competitionConfig';
import { openInNewTab } from '@/utils/utils';
import { mapState } from 'vuex';
export default {
  name: 'manageHeader',
  components: {
    JtTag,
    breadCrumb,
  },
  computed: {
    ...mapState('competition', ['currentManageCompetition']),
    hasCompetition() {
      return Object.keys(this.currentManageCompetition).length > 0;
    },
  },
  data() {
    return {
      competitionTypeMaps,
      publishStatusMaps,
      runningStatusMaps,
      publishStatusKeys,
      runningStatusKeys,
      breadCrumbs: [{ name: '比赛', path: '/competition' }, { name: '我举办的比赛', path: '/competition/held-competition' }, { name: '比赛管理' }],
    };
  },
  methods: {
    toOnlineCompetition() {
      const id = this.$route.params.competitionId;
      openInNewTab(`./web#/competition/competition-detail?id=${id}`);
    },
    getCompetitionState(flag) {
      if (flag === runningStatusKeys.STARTING) {
        return 'tobegin';
      } else if (flag === runningStatusKeys.RUNNING) {
        return 'running';
      } else {
        return 'end';
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.manage-header {
  background-color: white;
  .manage-breadcrumb {
    width: 1200px;
    margin: auto;
    .ant-breadcrumb {
      height: 56px;
      line-height: 56px;
    }
  }
  .manage-status {
    display: flex;
    justify-content: space-between;
    width: 1200px;
    margin: 0px auto;
    min-height: 64px;

    .status-left {
      display: flex;
      line-height: 1;
      padding-top: 8px;
      &.status-hidden {
        visibility: hidden;
      }
      .competition-name {
        font-size: 24px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
        margin-right: 16px;
        padding-top: 6px;
        padding-bottom: 12px;
        max-width: 800px;
      }
    }
    .route-to-main {
      width: 138px;
      margin-top: 8px;
      background-color: #e5f3ff;
      color: #0082ff;
      border: none;
      &:hover {
        background-color: #dbeeff;
      }
      i {
        margin-left: 2px;
      }
    }
  }
}
</style>
